import { useEffect, useState } from "react";
import { Grid, Typography, Skeleton } from "@mui/material";
import dayjs from "dayjs";
// import utc from 'dayjs/plugin/utc'
// import timezone from 'dayjs/plugin/timezone'
import { displayCoordinates, userValues } from "../../../utils";
import axiosInstance from "../../../axios";
import gps_socket from "../../../gps_socket";
import { useApp } from "../../../hooks/AppHook";
import { useUser } from "../../../hooks/UserHook.jsx";

// dayjs.extend(utc)
// dayjs.extend(timezone)

const SensorDetails = ({ selectedStream }) => {
    // const [locationFetched, setLocationFetched] = useState(false);
    const { timezone } = useApp();
    const { user } = useUser();
    const [lastLocation, setLastLocation] = useState();
    const [isLoading, setIsLoading] = useState(true);
    // const [locationName, setLocationName] = useState('');

    console.log("MH", !!user?.use_MGRS);

    const key = displayCoordinates([lastLocation?.longitude, lastLocation?.latitude], !!user?.use_MGRS);

    // const fetchGeolocation = async (location) => {

    //     try {
    //         const name = await getLocation(location);
    //         setLocationName(name);
    //     } catch (err) {
    //         console.error('Error fetching geolocation:', err);
    //     }
    // };

    // useEffect(() => {
    //     if (!locationFetched && lastLocation) {
    //         setLocationFetched(true);
    //         fetchGeolocation({ lat: lastLocation.latitude, lng: lastLocation.longitude });
    //     }
    // }, [lastLocation])

    // useEffect(() => {
    //     let intervalId1 = null;
    //     let intervalId2 = null;
    //     if (locationFetched && lastLocation) {
    //         intervalId1 = setInterval(() => fetchGeolocation({ lat: lastLocation.latitude, lng: lastLocation.longitude }), 5 * 60 * 1000);
    //     }
    //     if (!lastLocation || !locationFetched) return;

    //     intervalId2 = setInterval(fetchGeolocation({ lat: lastLocation.latitude, lng: lastLocation.longitude }), 5 * 60 * 1000);

    //     return () => {
    //         intervalId1 && clearInterval(intervalId1);
    //         clearInterval(intervalId2);
    //     };
    // }, [locationFetched])

    useEffect(() => {
        setLastLocation();
        if (!selectedStream.StreamName) return;
        setIsLoading(true);
        axiosInstance
            .post(`/v2/vesselLocations/${selectedStream.VesselId}`, { lastKnown: 1 }, { meta: { showSnackbar: false } })
            .then((res) => {
                setLastLocation(res.data);
                // fetchGeolocation({ lat: res.data.latitude, lng: res.data.longitude });
            })
            .catch(console.error)
            .finally(() => setIsLoading(false));

        gps_socket.on(selectedStream.StreamName + "/gps", setLastLocation);
        return () => gps_socket.off(selectedStream.StreamName + "/gps", setLastLocation);
    }, [selectedStream]);

    return (
        <Grid container size={12} flexDirection={"column"} spacing={{ xs: 2, md: 1 }}>
            {[
                { field: "Position", value: key },
                { field: "Speed", value: !lastLocation?.groundSpeed ? "N/A" : (lastLocation?.groundSpeed / 0.514444).toFixed(3) + " Knots" },
                { field: "Time (Local)", value: dayjs(lastLocation?.timestamp).tz(timezone).format(userValues.dateTimeFormat(user)) },
            ].map(({ field, value }, index) => (
                <Grid
                    container
                    minWidth={"100%"}
                    size={12}
                    key={index}
                    justifyContent={"space-between"}
                    wrap="nowrap"
                    color={"primary.contrastText"}
                    bgcolor={{ xs: "transparent" }}
                    paddingY={{ xs: 0, lg: 0 }}
                    paddingX={{ xs: 0, lg: 0 }}
                >
                    <Grid>
                        <Typography fontSize={"12px"} fontWeight={{ xs: "700", lg: "400" }}>
                            {field}
                        </Typography>
                    </Grid>
                    <Grid>
                        {isLoading ? (
                            <Skeleton animation="wave" variant="text" height={15} width={200} />
                        ) : !lastLocation || !value ? (
                            <Typography textAlign={"right"} fontSize={"12px"} fontWeight={{ xs: "700", lg: "400" }}>
                                Not Available
                            </Typography>
                        ) : (
                            <Typography textAlign={"right"} fontSize={"12px"} fontWeight={{ xs: "700", lg: "400" }}>
                                {value}
                            </Typography>
                        )}
                    </Grid>
                </Grid>
            ))}
        </Grid>
    );
};

export default SensorDetails;
