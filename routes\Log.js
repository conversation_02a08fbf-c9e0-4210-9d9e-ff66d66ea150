const express = require("express");
const { validateError } = require("../utils/functions");
const { default: mongoose, isValidObjectId } = require("mongoose");
const { permissions } = require("../utils/permissions");
const hasPermission = require("../middlewares/hasPermission");
const { validateData } = require("../middlewares/validator");
const { param } = require("express-validator");
const SessionLog = require("../models/SessionLog");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

const buildQuery = (req, sepSearch = false) => {
    const query = {};
    const sorting = req.query.sorting;
    const search = {};

    if (req.query.status) {
        if (req.query.status === "online") {
            query.disconnect_timestamp = { $eq: null };
        } else if (req.query.status === "offline") {
            query.disconnect_timestamp = { $ne: null };
        }
    }

    if (req.query.created_after) {
        const date = new Date(Number(req.query.created_after));
        query.connect_timestamp = { $gte: date };
    }

    if (req.query.full_name_or_browser_or_device) {
        if (sepSearch) {
            search.$or = [
                // Use $ifNull to prevent errors if fields are missing, and to make empty regex match
                { $regexMatch: { input: { $ifNull: ["$browser", ""] }, regex: req.query.full_name_or_browser_or_device, options: "i" } },
                { $regexMatch: { input: { $ifNull: ["$device", ""] }, regex: req.query.full_name_or_browser_or_device, options: "i" } },
                {
                    $regexMatch: {
                        input: { $ifNull: ["$user.name", ""] },
                        regex: req.query.full_name_or_browser_or_device,
                        options: "i",
                    },
                },
            ];
        } else {
            query.$or = [
                { browser: { $regex: req.query.full_name_or_browser_or_device, $options: "i" } },
                { device: { $regex: req.query.full_name_or_browser_or_device, $options: "i" } },
                { "user.name": { $regex: req.query.full_name_or_browser_or_device, $options: "i" } },
            ];
        }
    }

    if (sorting) {
        for (let key in sorting) {
            if (sorting[key] === "NONE") {
                delete sorting[key];
                continue;
            }

            sorting[key] = sorting[key] === "ASC" ? 1 : -1;
        }
    }

    return { query, sorting: sorting && Object.keys(sorting).length > 0 ? sorting : null, search };
};

router.get(
    "/sessions",
    assignEndpointId.bind(this, endpointIds.FETCH_SESSION_LOGS),
    isAuthenticated,
    hasPermission.bind(this, [permissions.viewSessionLogs]),
    async (req, res) => {
        try {
            let page = Math.max(1, Number(req.query.page) || 1);
            let limit = Math.min(50, Number(req.query.rowsPerPage) || 10);
            const { query, sorting } = buildQuery(req);

            const skip = (page - 1) * limit;

            const pipeline = [
                {
                    $match: {
                        environment: process.env.NODE_ENV,
                    },
                },
                {
                    $project: {
                        user_id: 1,
                        connect_timestamp: 1,
                        device: 1,
                        browser: 1,
                        disconnect_timestamp: 1,
                    },
                },
                {
                    $sort: { connect_timestamp: -1 }, // Ensure most recent sessions come first
                },
                {
                    $group: {
                        _id: "$user_id",
                        lastLog: { $first: "$$ROOT" }, // Pick the latest log per user
                    },
                },
                {
                    $replaceRoot: { newRoot: "$lastLog" }, // Flatten the structure
                },
                // Now join user info for only the latest log per user
                {
                    $lookup: {
                        from: "users",
                        localField: "user_id",
                        foreignField: "_id",
                        as: "user",
                        pipeline: [{ $project: { name: 1, username: 1, _id: 0 } }],
                    },
                },
                {
                    $unwind: {
                        path: "$user",
                        preserveNullAndEmptyArrays: true,
                    },
                },
            ];
            if (query && Object.keys(query).length > 0) {
                pipeline.push({ $match: query });
            }
            if (sorting) {
                pipeline.push({ $sort: sorting });
            }
            pipeline.push({
                $facet: {
                    metadata: [{ $count: "total" }],
                    data: [
                        { $skip: skip },
                        { $limit: limit },
                        {
                            $project: {
                                "user.password": 0,
                                "user.reset_password_token": 0,
                                "user.reset_password_expire": 0,
                            },
                        },
                    ],
                },
            });
            pipeline.push({
                $project: {
                    total: { $ifNull: [{ $arrayElemAt: ["$metadata.total", 0] }, 0] },
                    data: "$data",
                },
            });

            const logs = await SessionLog.aggregate(pipeline, { collation: { locale: "en", caseFirst: "off" } });

            const totalCount = (logs[0] && logs[0].total) || 0;
            const totalPages = Math.ceil(totalCount / limit);

            res.json({ logs: (logs[0] && logs[0].data) || [], totalPages, currentPage: page, totalCount });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/sessions/user",
    assignEndpointId.bind(this, endpointIds.FETCH_SESSION_LOGS_BY_USER),
    isAuthenticated,
    hasPermission.bind(this, [permissions.viewSessionLogs]),
    async (req, res) => {
        try {
            const userId = req.query.userId;
            if (!userId) {
                return res.status(400).send({ message: "User ID is required" });
            }

            const { query } = buildQuery(req);

            const logs = await SessionLog.aggregate([
                {
                    $match: {
                        user_id: mongoose.Types.ObjectId(userId),
                        environment: process.env.NODE_ENV,
                    },
                },
                {
                    $sort: { createdAt: -1 },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "user_id",
                        foreignField: "_id",
                        as: "user",
                        pipeline: [{ $project: { name: 1, username: 1, _id: 0 } }],
                    },
                },
                {
                    $unwind: {
                        path: "$user",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $match: query,
                },
                {
                    $project: {
                        "user.password": 0,
                        "user.reset_password_token": 0,
                        "user.reset_password_expire": 0,
                    },
                },
            ]);
            res.json(logs);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/sessions/:id",
    assignEndpointId.bind(this, endpointIds.FETCH_SESSION_LOG_BY_ID),
    isAuthenticated,
    hasPermission.bind(this, [permissions.viewSessionLogs]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const logs = await SessionLog.aggregate([
                {
                    $match: {
                        _id: req.params.id,
                        environment: process.env.NODE_ENV,
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "user_id",
                        foreignField: "_id",
                        as: "user",
                        pipeline: [{ $project: { name: 1, username: 1, _id: 0 } }],
                    },
                },
                {
                    $unwind: {
                        path: "$user",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $project: {
                        "user.password": 0,
                        "user.reset_password_token": 0,
                        "user.reset_password_expire": 0,
                    },
                },
            ]);

            if (logs.length === 0) return res.status(404).send({ message: "Log does not exist with that id" });

            const log = logs[0];

            res.json(log);
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
