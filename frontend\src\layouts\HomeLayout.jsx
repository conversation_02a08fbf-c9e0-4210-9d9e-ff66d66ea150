import { useEffect, useRef, useState } from "react";
import { Outlet } from "react-router-dom";
import { Grid } from "@mui/material";
import theme from "../theme";
import { jwtDecode } from "jwt-decode";
import { Navigate } from "react-router-dom";

const HomeLayout = () => {
    const [error, setError] = useState("");

    const timeout = useRef();

    useEffect(() => {
        if (error) {
            clearTimeout(timeout.current);
            timeout.current = setTimeout(() => setError(""), 3000);
        }
    }, [error]);

    const checkTokenExpiry = (token) => {
        if (!token) return true;
        try {
            const decodedToken = jwtDecode(token);
            const currentTime = Date.now() / 1000;
            return decodedToken.exp < currentTime;
        } catch (error) {
            console.error("Invalid Token", error);
            return true;
        }
    };
    const token = localStorage.getItem("jwt_token");
    const isExpired = checkTokenExpiry(token);

    if (!isExpired) {
        return <Navigate to="/dashboard/stream" />;
    }
    return (
        <Grid
            container
            overflow={"auto"}
            bgcolor={"primary.main"}
            flexDirection={{ lg: "row", xs: "column-reverse" }}
            minHeight={"100vh"}
            justifyContent={"start"}
        >
            <Grid
                container
                display={{ xs: "none", sm: "none", md: "none", lg: "flex" }}
                justifyContent={"flex-end"}
                alignItems={"center"}
                flexDirection={"column"}
                gap={{ xs: 2, sm: 3 }}
                paddingBottom={6}
                borderRadius={"0 20px 0 0"}
                sx={{
                    background: `
                        linear-gradient(
                            180.03deg, 
                            rgba(0, 0, 0, 0) 51.41%, 
                            rgba(28, 38, 55, 0.929461) 84.58%, 
                            ${theme.palette.primary.main} 99.97%
                        ), 
                        url(/home-layout-bg.jpg)
                    `,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                }}
                size={{
                    xs: "grow",
                    lg: 5,
                }}
            >
                <Grid width={{ xs: 100, sm: 200, md: 300 }}>
                    <img src="/quartermaster-logo-home.svg" alt="Quartermaster Logo" width={"100%"} />
                </Grid>
                <Grid width={{ xs: 150, sm: 250, md: 400 }}>
                    <img src="/quartermaster-text-home.png" alt="Quartermaster Logo" width={"100%"} />
                </Grid>
            </Grid>
            <Grid
                container
                justifyContent={{ lg: "flex-start", xs: "center" }}
                alignItems={"center"}
                position={"relative"}
                paddingY={5}
                paddingLeft={{ lg: 15, md: 0 }}
                size={{
                    xs: "grow",
                    lg: 7,
                }}
            >
                <Grid width={{ xs: 300 }} position={"absolute"} bottom={0} right={0}>
                    <img
                        src="/quartermaster-watermark.png"
                        style={{ position: "absolute", bottom: 0, left: 0, pointerEvents: "none" }}
                        width={"100%"}
                    />
                </Grid>
                <Grid
                    size={{
                        xs: 9,
                        sm: 8,
                    }}
                >
                    <Outlet />
                </Grid>
            </Grid>
        </Grid>
    );
};

export default HomeLayout;
