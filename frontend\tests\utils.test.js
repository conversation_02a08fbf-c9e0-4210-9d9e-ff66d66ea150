import axiosInstance from "../src/axios";
import {
    defaultValues,
    permissions,
    roles,
    getDayByIndex,
    generateRandomColorRGBA,
    getDatesArrayBetweenDateRange,
    sortObject,
    getLocation,
    haversine,
    isEnvironment,
    cacheLocations,
} from "../src/utils";
import dayjs from "dayjs";

jest.mock("../src/axios", () => ({
    get: jest.fn(),
}));

jest.mock("../environment", () => ({
    VITE_NODE_ENV: "dev",
}));

describe("defaultValues", () => {
    it("should have journeyStart 3 days ago", () => {
        const threeDaysAgo = dayjs().subtract(3, "days").startOf("day");
        expect(defaultValues.journeyStart.isSame(threeDaysAgo, "day")).toBe(true);
    });

    it("should have journeyEnd as the current date", () => {
        const now = dayjs().startOf("day");
        expect(defaultValues.journeyEnd.isSame(now, "day")).toBe(true);
    });

    it("should correctly format date based on options", () => {
        let formattedDate = defaultValues.dateTimeFormat();
        expect(formattedDate).toBe("DD-MMM-YYYY HH:mm:ss");

        formattedDate = defaultValues.dateTimeFormat({ exclude_hours: true, exclude_minutes: true, exclude_seconds: true });
        expect(formattedDate).toBe("DD-MMM-YYYY");

        formattedDate = defaultValues.dateTimeFormat({ exclude_minutes: true });
        expect(formattedDate).toBe("DD-MMM-YYYY HH:ss");

        formattedDate = defaultValues.dateTimeFormat({ exclude_seconds: true });
        expect(formattedDate).toBe("DD-MMM-YYYY HH:mm");

        formattedDate = defaultValues.dateTimeFormat({ exclude_hours: true, exclude_minutes: true, exclude_seconds: true });
        expect(formattedDate).toBe("DD-MMM-YYYY");

        formattedDate = defaultValues.dateTimeFormat({ exclude_hours: true, exclude_seconds: true });
        expect(formattedDate).toBe("DD-MMM-YYYY:mm");

        formattedDate = defaultValues.dateTimeFormat({ exclude_minutes: true, exclude_seconds: true });
        expect(formattedDate).toBe("DD-MMM-YYYY HH");

        formattedDate = defaultValues.dateTimeFormat({ exclude_hours: true, exclude_minutes: true, exclude_seconds: true });
        expect(formattedDate).toBe("DD-MMM-YYYY");
    });
});

describe("permissions and roles", () => {
    it("should have correct permissions values", () => {
        expect(permissions.manageRoles).toBe(100);
        expect(permissions.manageUsers).toBe(200);
        expect(permissions.accessAllVessels).toBe(300);
        expect(permissions.viewSessionLogs).toBe(400);
        expect(permissions.manageApiKeys).toBe(500);
        expect(permissions.viewStatistics).toBe(600);
    });

    it("should have correct roles values", () => {
        expect(roles.super_admin).toBe(1);
    });
});

describe("Utility functions", () => {
    it("should return the correct day name for an index", () => {
        expect(getDayByIndex(0)).toBe("Sunday");
        expect(getDayByIndex(1)).toBe("Monday");
        expect(getDayByIndex(6)).toBe("Saturday");
    });

    it("should generate random RGBA color from seed", () => {
        const color1 = generateRandomColorRGBA(1, 0.5);
        const color2 = generateRandomColorRGBA(2);

        expect(color1).toMatch(/^rgba\(\d+, \d+, \d+, 0.5\)$/);
        expect(color2).toMatch(/^rgba\(\d+, \d+, \d+, 1\)$/);
        expect(color1).not.toBe(color2);
    });

    it("should generate the correct array of dates between two dates", () => {
        const startDate = "2024-11-01";
        const endDate = "2024-11-03";
        const dates = getDatesArrayBetweenDateRange(startDate, endDate);

        expect(dates).toEqual(["2024-11-01", "2024-11-02", "2024-11-03"]);
    });

    it("should sort an object correctly by value", () => {
        const obj = { a: 5, b: 3, c: 8 };
        const sortedObj = sortObject(obj);
        expect(Object.keys(sortedObj)).toEqual(["c", "a", "b"]);
        expect(sortedObj.c).toBe(8);
        expect(sortedObj.a).toBe(5);
        expect(sortedObj.b).toBe(3);
    });
});

describe("isEnvironment", () => {
    it("should return false if environment not matches", () => {
        expect(isEnvironment(["production", "staging"])).toBe(false);
    });
});

describe("haversine", () => {
    it("should calculate the correct distance", () => {
        const distance = haversine(52.52, 13.405, 48.8566, 2.3522);
        expect(distance).toBeLessThan(878);
    });

    it("should return 0 for the same coordinates", () => {
        const distance = haversine(40.7128, -74.006, 40.7128, -74.006);
        expect(distance).toBe(0);
    });
});

describe("getLocation", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it("should return cached location if within 5km", async () => {
        cacheLocations["40.7128,-74.0060"] = "New York";
        const result = await getLocation({ lat: 40.713, lng: -74.0065 });
        expect(result).toBe("New York");
    });

    it("should reject on API error", async () => {
        axiosInstance.get.mockRejectedValueOnce(new Error("API call failed"));
        await expect(getLocation({ lat: 34.0522, lng: -118.2437 })).rejects.toThrow("API call failed");
    });

    it("should fetch from API if not in cache", async () => {
        axiosInstance.get.mockResolvedValueOnce({ data: { name: "Los Angeles" } });
        const result = await getLocation({ lat: 34.0522, lng: -118.2437 });
        expect(result).toBe("Los Angeles");
    });
});
