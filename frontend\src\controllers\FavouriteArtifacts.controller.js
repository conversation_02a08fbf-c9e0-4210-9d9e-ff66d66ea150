import axiosInstance from "../axios";

class FavouriteArtifactsController {
    async addFavouriteArtifact(data) {
        try {
            const res = await axiosInstance.post(`/artifactFavourites`, data, { meta: { showSnackbar: true } });
            return res;
        } catch (error) {
            console.log("Error Favourite Artifact Create " + error);
            return error.response.data;
        }
    }

    async removeFavouriteArtifact(data) {
        try {
            const res = await axiosInstance.delete(`/artifactFavourites`, { data, meta: { showSnackbar: true } });
            return res;
        } catch (error) {
            console.log("Error Favourite Artifact Removed " + error);
            return error.response.data;
        }
    }

    async getUserFavouriteArtifacts() {
        try {
            const res = await axiosInstance.get(`/artifactFavourites`);
            return res.data;
        } catch (error) {
            console.log("Error Get All Favourite Artifacts " + error);
            return error.response.data;
        }
    }
}

const favouriteArtifactsController = new FavouriteArtifactsController();

export default favouriteArtifactsController;
