require("dotenv").config();
// const mongoose = require("mongoose");
const Statistics = require("../models/Statistics");
const Vessel = require("../models/Vessel");

// Configuration
const dryRun = process.argv.includes("--dry-run");

// Progress tracking utilities
function createProgressBar(current, total, length = 30) {
    const percentage = Math.round((current / total) * 100);
    const filled = Math.round((current / total) * length);
    const empty = length - filled;
    return `[${"█".repeat(filled)}${" ".repeat(empty)}] ${percentage}% (${current}/${total})`;
}

function writeLine(message) {
    process.stdout.write(message + "\n");
}

function writeProgress(message) {
    process.stdout.write("\r" + message);
}

// Statistics field names that use unit_id as keys
const UNIT_ID_FIELDS = [
    "totalVesselsDetectedbySensors",
    "totalSensorsOnlineDuration",
    "totalSensorsDurationAtSea",
    "totalSmartmastsDistanceTraveled",
];

function convertStatsUnitIdsToVesselIds(stats, unitToVesselMap, fromTimestamp, toTimestamp) {
    const convertedStats = { ...stats };
    let totalConversions = 0;
    const unmappedUnits = new Set();

    const fromDate = new Date(fromTimestamp);
    const toDate = new Date(toTimestamp);

    UNIT_ID_FIELDS.forEach((fieldName) => {
        if (stats[fieldName] && typeof stats[fieldName] === "object") {
            const originalField = stats[fieldName];
            const convertedField = {};

            Object.keys(originalField).forEach((unitId) => {
                const vesselMappings = unitToVesselMap.get(unitId);
                if (vesselMappings) {
                    let found = false;
                    for (const mapping of vesselMappings) {
                        // First check if this unit_id is currently assigned to this vessel (no unmount_timestamp)
                        const hasCurrentUnit = mapping.unit_id === unitId;
                        if (hasCurrentUnit) {
                            convertedField[mapping.vessel_id] = originalField[unitId];
                            totalConversions++;
                            found = true;
                            break;
                        }

                        // If not currently assigned, check historical assignments
                        const matchingHistory = mapping.units_history.find((history) => {
                            if (history.unit_id !== unitId) return false;
                            const mountDate = new Date(history.mount_timestamp);
                            const unmountDate = history.unmount_timestamp ? new Date(history.unmount_timestamp) : new Date();
                            return mountDate <= toDate && unmountDate >= fromDate;
                        });

                        if (matchingHistory) {
                            convertedField[mapping.vessel_id] = originalField[unitId];
                            totalConversions++;
                            found = true;
                            break;
                        }
                    }

                    if (!found) {
                        unmappedUnits.add(unitId);
                    }
                } else {
                    unmappedUnits.add(unitId);
                }
            });

            convertedStats[fieldName] = convertedField;
        }
    });

    return {
        convertedStats,
        totalConversions,
        unmappedUnits: Array.from(unmappedUnits),
    };
}

async function migrateStatistics(unitToVesselMap) {
    writeLine("\n📊 Migrating statistics...");
    const statistics = await Statistics.find({});
    let updated = 0;
    let totalConversions = 0;
    const allUnmappedUnits = new Set();
    const updatedStatIds = [];
    const unmappedStats = new Map(); // Map to store stats with unmapped units

    for (let i = 0; i < statistics.length; i++) {
        const stat = statistics[i];
        const progress = createProgressBar(i + 1, statistics.length);
        writeProgress(`📊 Processing statistics... ${progress}`);

        try {
            const {
                convertedStats,
                totalConversions: conversions,
                unmappedUnits,
            } = convertStatsUnitIdsToVesselIds(stat.stats, unitToVesselMap, stat.fromTimestamp, stat.toTimestamp);

            // Track unmapped units and their stats
            if (unmappedUnits.length > 0) {
                unmappedUnits.forEach((unit) => {
                    allUnmappedUnits.add(unit);
                    if (!unmappedStats.has(unit)) {
                        unmappedStats.set(unit, []);
                    }
                    unmappedStats.get(unit).push({
                        id: stat._id.toString(),
                        type: stat.type,
                        from: stat.fromTimestamp,
                        to: stat.toTimestamp,
                    });
                });
            }
            totalConversions += conversions;

            // Only update if we have conversions to make
            if (conversions > 0) {
                if (!dryRun) {
                    await Statistics.updateOne({ _id: stat._id }, { $set: { stats: convertedStats } });
                }
                updated++;
                updatedStatIds.push({
                    id: stat._id.toString(),
                    from: stat.fromTimestamp,
                    type: stat.type,
                });
            }
        } catch (error) {
            writeLine(`❌ Error migrating statistics ${stat._id}: ${error.message}`);
        }
    }

    writeLine(`\n✅ Statistics migration completed: ${updated} updated, ${totalConversions} unit_ids converted`);

    if (allUnmappedUnits.size > 0) {
        writeLine(`\n⚠️  Unmapped unit_ids and their statistics:`);
        writeLine("=".repeat(50));

        // Sort units for consistent output
        const sortedUnits = Array.from(allUnmappedUnits).sort();

        for (const unitId of sortedUnits) {
            const stats = unmappedStats.get(unitId);
            writeLine(`\n📡 Unit ID: ${unitId}`);
            writeLine("   Statistics records with this unit:");

            // Group by type and sort by date
            const byType = stats.reduce((acc, stat) => {
                const key = stat.type;
                if (!acc[key]) acc[key] = [];
                acc[key].push(stat);
                return acc;
            }, {});

            for (const [type, typeStats] of Object.entries(byType)) {
                writeLine(`   ${type.toUpperCase()}:`);
                typeStats
                    .sort((a, b) => new Date(a.from) - new Date(b.from))
                    .forEach((stat) => {
                        const fromDate = new Date(stat.from).toISOString().split("T")[0];
                        const toDate = new Date(stat.to).toISOString().split("T")[0];
                        writeLine(`      ${fromDate} to ${toDate}: ${stat.id}`);
                    });
            }
        }
        writeLine("=".repeat(50));
    }

    // Group and display updated statistics by type and date
    if (updatedStatIds.length > 0) {
        writeLine("\n📊 Updated Statistics Records:");
        writeLine("=".repeat(50));

        // Group by type
        const byType = updatedStatIds.reduce((acc, stat) => {
            const dateStr = new Date(stat.from).toISOString().split("T")[0];
            const key = `${stat.type}`;
            if (!acc[key]) acc[key] = [];
            acc[key].push({ id: stat.id, date: dateStr });
            return acc;
        }, {});

        // Display grouped results
        for (const [type, stats] of Object.entries(byType)) {
            writeLine(`\n${type.toUpperCase()} Statistics:`);
            stats.sort((a, b) => a.date.localeCompare(b.date)); // Sort by date
            stats.forEach((stat) => {
                writeLine(`   ${stat.date}: ${stat.id}`);
            });
        }
        writeLine("=".repeat(50));
    }

    return { updated, totalConversions, unmappedUnits: Array.from(allUnmappedUnits) };
}

async function migrateStatisticsUnitIdsToVesselIds() {
    try {
        writeLine("🚀 Starting migration from unit_ids to vessel_ids in statistics...");
        if (dryRun) {
            writeLine("🔍 DRY RUN MODE - No changes will be made");
        }

        // Get all vessels with their unit history
        writeProgress("📋 Fetching vessels with unit history...");
        const vessels = await Vessel.find({}, { _id: 1, unit_id: 1, name: 1, units_history: 1 });

        const unitToVesselMap = new Map();
        vessels.forEach((vessel) => {
            // Add current unit_id mapping
            if (vessel.unit_id) {
                if (!unitToVesselMap.has(vessel.unit_id)) {
                    unitToVesselMap.set(vessel.unit_id, []);
                }
                unitToVesselMap.get(vessel.unit_id).push({
                    vessel_id: vessel._id.toString(),
                    unit_id: vessel.unit_id,
                    units_history: vessel.units_history || [],
                });
            }

            // Add historical unit_id mappings
            if (vessel.units_history && vessel.units_history.length > 0) {
                vessel.units_history.forEach((history) => {
                    if (!unitToVesselMap.has(history.unit_id)) {
                        unitToVesselMap.set(history.unit_id, []);
                    }
                    // Only add if this vessel isn't already mapped for this unit_id
                    if (!unitToVesselMap.get(history.unit_id).some((m) => m.vessel_id === vessel._id.toString())) {
                        unitToVesselMap.get(history.unit_id).push({
                            vessel_id: vessel._id.toString(),
                            unit_id: vessel.unit_id, // current unit_id
                            units_history: vessel.units_history || [],
                        });
                    }
                });
            }
        });

        writeLine(`✅ Created mapping for ${unitToVesselMap.size} unique unit IDs`);

        // Run migration
        const { updated, totalConversions, unmappedUnits } = await migrateStatistics(unitToVesselMap);

        // Final summary
        writeLine("\n📊 Migration Summary:");
        writeLine("=".repeat(50));
        writeLine(`📈 Statistics Records: ${updated} updated`);
        writeLine(`🔄 Unit ID Conversions: ${totalConversions} converted`);
        writeLine(`⚠️  Unmapped Unit IDs: ${unmappedUnits.length}`);
        if (unmappedUnits.length > 0) {
            writeLine(`   ${unmappedUnits.join(", ")}`);
        }
        writeLine("=".repeat(50));

        if (dryRun) {
            writeLine("🔍 DRY RUN COMPLETED - No actual changes were made");
        } else {
            writeLine("✅ MIGRATION COMPLETED SUCCESSFULLY!");
        }
    } catch (error) {
        writeLine(`❌ Migration failed: ${error.message}`);
        process.exit(1);
    } finally {
        process.exit(0);
    }
}

migrateStatisticsUnitIdsToVesselIds();
