import { useEffect, useState } from "react";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { Button, Grid, Modal } from "@mui/material";
import ModalContainer from "../../../../components/ModalContainer";
import SortableItem from "./SortableItem";
import axiosInstance from "../../../../axios";

const ReorderRolesModal = ({ open, onClose, roles, onReorder, fetchRoles, user }) => {
    onReorder;
    fetchRoles;

    const [roleList, setRoleList] = useState(roles);

    useEffect(() => {
        if (open) {
            setRoleList(roles);
        }
    }, [roles, open]);

    const handleDragEnd = ({ active, over }) => {
        if (!over || active.id === over.id) return;

        const activeIndex = roleList.findIndex((role) => role._id === active.id);
        const overIndex = roleList.findIndex((role) => role._id === over.id);

        const activeRole = roleList[activeIndex];
        const overRole = roleList[overIndex];

        // Check if the active role has a higher hierarchy than the over role
        if (user.role.hierarchy_number >= overRole.hierarchy_number) {
            return; // Do not allow dragging to higher or equal hierarchy
        }

        // Only allow dragging within the same lower-hierarchy group
        if (activeRole.hierarchy_number > user.role.hierarchy_number && overRole.hierarchy_number > user.role.hierarchy_number) {
            const newOrder = arrayMove(roleList, activeIndex, overIndex);
            setRoleList(newOrder);
        }
    };

    const handleSave = async () => {
        const reorderedRoles = roleList.map((role, index) => ({
            _id: role._id,
            hierarchy_number: index + 1,
        }));

        try {
            await axiosInstance.patch("/roles/reorder", { roles: reorderedRoles }, { meta: { showSnackbar: true } });
            // const newRoles = await fetchRoles();
            // onReorder(newRoles);
        } catch (error) {
            console.error("Error updating hierarchy numbers:", error);
        }
        onClose();
    };

    const canReorder = (role) => user.role.hierarchy_number < role.hierarchy_number;

    const handleCancel = () => {
        setRoleList(roles);
        onClose();
    };

    return (
        <Modal open={open} onClose={onClose}>
            <ModalContainer title="Reorder Roles" onClose={onClose}>
                <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                    <SortableContext items={roleList?.map((role) => role._id)}>
                        <Grid container flexDirection="column" gap={1} width={{ xs: 300, lg: 500 }}>
                            {roleList?.map((role) => (
                                <SortableItem key={role._id} role={role} isDraggable={canReorder(role)} />
                            ))}
                        </Grid>
                    </SortableContext>
                </DndContext>
                <Grid container justifyContent="center" spacing={2} mt={2}>
                    <Grid>
                        <Button onClick={handleCancel} variant="outlined" sx={{ mx: 1 }}>
                            Cancel
                        </Button>
                    </Grid>
                    <Grid>
                        <Button onClick={handleSave} variant="contained" sx={{ mx: 1 }}>
                            Save Order
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default ReorderRolesModal;
