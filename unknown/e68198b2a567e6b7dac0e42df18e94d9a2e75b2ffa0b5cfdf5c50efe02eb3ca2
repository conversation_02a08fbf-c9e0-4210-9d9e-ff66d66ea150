const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Role = require('../../models/Role');
const { default: mongoose } = require('mongoose');
const crypto = require('crypto');
const { sendEmail } = require('../../modules/email');
const Region = require('../../models/Region');
const { regionsList } = require('../data/Regions');
const { permissionsList } = require('../data/Permissions');
const Permission = require('../../models/Permission');
const { streamsList } = require('../data/Kinesis');
const { s3 } = require('../../modules/awsS3');
const request = require("supertest");
const app = require('../../server');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

jest.mock('../../modules/awsS3', () => ({
    s3: {
        getSignedUrl: jest.fn(),
    },
}));

describe('POST /api/s3/signedUrl', () => {
    const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
        const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
        const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
        const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
        const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

        describe(`${authMethod} authentication`, () => {

            beforeEach(() => {
                jest.resetAllMocks();
            });

            it('should return 400 if both bucket_name and key are missing', async () => {
                mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                const res = await request(app).post('/api/s3/signedUrl').set('Authorization', authToken).send({});

                expect(res.status).toBe(400);
                expect(res.body.message).toBe("Missing required parameters");
            });

            it('should return 400 if bucket_name is missing', async () => {
                mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                const res = await request(app).post('/api/s3/signedUrl').set('Authorization', authToken).send({
                    key: 'testKey'
                });

                expect(res.status).toBe(400);
                expect(res.body.message).toBe("Missing required parameters");
            });

            it('should return 400 if key is missing', async () => {
                mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                const res = await request(app).post('/api/s3/signedUrl').set('Authorization', authToken).send({
                    bucket_name: 'testBucket'
                });

                expect(res.status).toBe(400);
                expect(res.body.message).toBe("Missing required parameters");
            });

            it('should return 200 and provide signed URL if valid bucket_name and key are provided', async () => {
                mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                const signedUrl = 'https://example.com/signed-url';
                jest.spyOn(s3, 'getSignedUrl').mockReturnValue(signedUrl);

                const res = await request(app).post('/api/s3/signedUrl').set('Authorization', authToken).send({
                    bucket_name: 'testBucket',
                    key: 'testKey'
                });

                expect(res.status).toBe(200);
                expect(res.body.signedUrl).toBe(signedUrl);
            });

            it('should return 200 and provide signed URLs for batch request', async () => {
                mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                const signedUrls = [
                    { Bucket: 'testBucket1', Key: 'testKey1', signedUrl: 'https://example.com/signed-url-1' },
                    { Bucket: 'testBucket2', Key: 'testKey2', signedUrl: 'https://example.com/signed-url-2' }
                ];
                jest.spyOn(s3, 'getSignedUrl').mockReturnValueOnce(signedUrls[0].signedUrl)
                    .mockReturnValueOnce(signedUrls[1].signedUrl);

                const res = await request(app).post('/api/s3/signedUrl').set('Authorization', authToken).send({
                    batch: [
                        { bucket_name: 'testBucket1', key: 'testKey1' },
                        { bucket_name: 'testBucket2', key: 'testKey2' }
                    ]
                });

                expect(res.status).toBe(200);
                expect(res.body.signedUrls).toEqual(signedUrls);
            });

            it('should return 500 if an error occurs while generating the signed URL', async () => {
                mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                jest.spyOn(s3, 'getSignedUrl').mockImplementationOnce(() => { throw new Error('S3 error'); });

                const res = await request(app).post('/api/s3/signedUrl').set('Authorization', authToken).send({
                    bucket_name: 'testBucket',
                    key: 'testKey'
                });

                expect(res.status).toBe(500);
            });
        });
    }

    runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
    runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
});
