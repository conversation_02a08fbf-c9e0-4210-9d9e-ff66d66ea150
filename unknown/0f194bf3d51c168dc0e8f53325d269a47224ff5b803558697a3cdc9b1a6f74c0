import { Button, Grid, Modal, Typography, Chip, alpha } from "@mui/material";
import { useEffect, useState } from "react";
import ModalContainer from "../../../../components/ModalContainer";
import theme from "../../../../theme";

const FilterRoleModal = ({ showFilterModal, setShowFilterModal, permissions, roles, setFilteredRoles, setPage }) => {
    const [selectedPermissons, setSelectedPermissons] = useState([]);
    const [changed, setChanged] = useState({
        isChanged: false,
        changedPermissions: [],
    });

    const handleClose = () => {
        // if isChanged is true, remove from selected permissions if not applied
        if (changed.isChanged) {
            const newPermissions = selectedPermissons.filter((permission) => !changed.changedPermissions.includes(permission));
            setSelectedPermissons(newPermissions);
            setChanged({
                isChanged: false,
                changedPermissions: [],
            });
        }
        setShowFilterModal(false);
    };

    const handleClear = () => {
        setSelectedPermissons([]);
        setFilteredRoles(roles);
        setPage(1);
        setChanged({
            isChanged: false,
            changedPermissions: [],
        });
    };

    const togglePermissionSelection = (permission_id) => {
        setSelectedPermissons((prev) =>
            prev.includes(permission_id) ? prev.filter((permission) => permission !== permission_id) : [...prev, permission_id],
        );
        setChanged({
            isChanged: true,
            changedPermissions: [...changed.changedPermissions, permission_id],
        });
    };

    const handleSubmit = () => {
        let filteredRoles = [...roles];
        if (selectedPermissons.length > 0) {
            filteredRoles = filteredRoles.filter((role) => selectedPermissons.every((permission) => !role.denied_permissions.includes(permission)));
        }
        setFilteredRoles(filteredRoles);
        setChanged({ isChanged: false, changedPermissions: [] });
        setShowFilterModal(false);
    };

    useEffect(() => {
        handleSubmit();
    }, [roles, permissions]);

    return (
        <Modal open={Boolean(showFilterModal)} onClose={handleClose}>
            <ModalContainer title={"Filter"} onClose={handleClose} showDivider={true}>
                <Grid
                    container
                    flexDirection={"column"}
                    gap={2}
                    width={{ xs: 300, sm: 500 }}
                    maxHeight={"70vh"}
                    overflow={"auto"}
                    flexWrap={"nowrap"}
                >
                    <Grid item>
                        <Typography variant="h6" fontSize={"16px !important"} marginBottom={2} fontWeight={500}>
                            Permissions
                        </Typography>
                        <Grid container spacing={1}>
                            {permissions.map((permission) => (
                                <Grid item key={permission.permission_id}>
                                    <Chip
                                        label={permission.permission_name}
                                        disableRipple
                                        onClick={() => togglePermissionSelection(permission.permission_id)}
                                        sx={{
                                            border: `1px solid ${selectedPermissons.includes(permission.permission_id) ? alpha("#FFFFFF", 0.5) : theme.palette.custom.borderColor}`,
                                            borderRadius: "8px",
                                            color: selectedPermissons.includes(permission.permission_id) ? "#FFFFFF" : "#737791",
                                            backgroundColor: "transparent",
                                            "&:hover": { backgroundColor: "transparent" },
                                        }}
                                    />
                                </Grid>
                            ))}
                        </Grid>
                    </Grid>
                </Grid>
                <Grid container gap={2} justifyContent={"space-between"} marginTop={2}>
                    <Grid item>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                textTransform: "none",
                            }}
                            onClick={handleClear}
                        >
                            Clear filters
                        </Button>
                    </Grid>
                    <Grid item>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                backgroundColor: theme.palette.custom.mainBlue,
                                "&:hover": {
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                            variant="contained"
                            onClick={handleSubmit}
                        >
                            Apply
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default FilterRoleModal;
