import { render, screen, waitFor } from "@testing-library/react";
import SensorDetails from "../../src/pages/Dashboard/Stream/SensorDetails";
import axiosInstance from "../../src/axios";

jest.mock('../../src/axios', () => ({
    post: jest.fn(),
}));
jest.mock('../../src/gps_socket', () => ({
    on: jest.fn(),
    off: jest.fn()
}));

describe("SensorDetails", () => {
    const mockStream = { StreamName: "TestStream" };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders 'N/A' for groundSpeed when no speed data is available", async () => {
        const mockLocationData = {
            latitude: 12.34,
            longitude: 56.78,
            groundSpeed: null,
            timestamp: "2024-11-21T12:00:00Z"
        };

        axiosInstance.post.mockResolvedValueOnce({ data: mockLocationData });

        render(<SensorDetails selectedStream={mockStream} />);

        await waitFor(() => {
            expect(screen.getByText("Speed")).toBeInTheDocument();
            expect(screen.getByText("N/A")).toBeInTheDocument();
        });
    });

    it("renders '23.676 Knots' groundSpeed when speed data is available", async () => {
        const mockLocationData = {
            latitude: 12.34,
            longitude: 56.78,
            groundSpeed: 12.18,
            timestamp: "2024-11-21T12:00:00Z"
        };

        axiosInstance.post.mockResolvedValueOnce({ data: mockLocationData });

        render(<SensorDetails selectedStream={mockStream} />);

        await waitFor(() => {
            expect(screen.getByText("Speed")).toBeInTheDocument();
            expect(screen.getByText("23.676 Knots")).toBeInTheDocument();
        });
    });

    it("should not render any values if StreamName is null", async () => {
        const mockLocationData = {
            latitude: 12.34,
            longitude: 56.78,
            groundSpeed: 12.18,
            timestamp: "2024-11-21T12:00:00Z"
        };

        axiosInstance.post.mockResolvedValueOnce({ data: mockLocationData });

        render(<SensorDetails selectedStream={{ StreamName: null }} />);

        await waitFor(() => {
            expect(screen.getByText("Speed")).toBeInTheDocument();
            expect(screen.getByText("Time (Local)")).toBeInTheDocument();
            expect(screen.getByText("Position")).toBeInTheDocument();
        });
    });
});
