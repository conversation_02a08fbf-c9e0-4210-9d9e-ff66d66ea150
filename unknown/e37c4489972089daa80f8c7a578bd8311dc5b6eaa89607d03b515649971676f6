import { render, screen, fireEvent } from "@testing-library/react";
import Filters from "../../src/pages/Dashboard/Stream/Filters";

jest.mock('../../src/components/DateTimeSlider', () => ({ range, onChangeCommitted }) => (
    <input data-testid="datetime-slider" onChange={(e) => onChangeCommitted(e, range)} />
));

jest.mock('@mui/material', () => ({
    ...jest.requireActual('@mui/material'),
    TextField: ({ children, onChange, InputProps: { startAdornment } }) => {
        if (onChange) onChange({ target: { value: 'test' } });
        return (
            <div>
                {startAdornment}
                {children}
            </div>
        )
    }
}));

describe("Filters", () => {
    const mockSetCategory = jest.fn();
    const mockSetStartTimestamp = jest.fn();
    const mockSetEndTimestamp = jest.fn();

    const categories = ["Category 1", "Category 2", "Category 3"];
    const category = "Category 1";

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the correct icon in the search field", () => {
        render(
            <Filters
                category={category}
                setCategory={mockSetCategory}
                categories={categories}
                setStartTimestamp={mockSetStartTimestamp}
                setEndTimestamp={mockSetEndTimestamp}
            />
        );

        const searchIcon = screen.getByTestId("SearchIcon");
        expect(searchIcon).toBeInTheDocument();
    });
});
