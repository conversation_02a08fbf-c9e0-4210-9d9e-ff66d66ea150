import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Roles from '../../src/pages/Dashboard/User/Roles/Roles';
import axiosInstance from '../../src/axios';
import { getSocket } from '../../src/socket';
import { useUser } from '../../src/hooks/UserHook';
import { useApp } from '../../src/hooks/AppHook';

jest.mock('../../src/axios', () => ({
    get: jest.fn(),
    post: jest.fn(),
}));

jest.mock("@mui/x-data-grid", () => {
    const { DataGrid: OriginalDataGrid } = jest.requireActual("@mui/x-data-grid");
    return {
        DataGrid: (props) => (<>
            {props.slots.noRowsOverlay()}
            <OriginalDataGrid {...props} />
        </>),
    }
});

jest.mock('../../src/socket', () => ({
    getSocket: jest.fn(),
    reconnectSocket: jest.fn(),
    disconnectSocket: jest.fn(),
}));

jest.mock('../../src/hooks/UserHook', () => ({
    useUser: jest.fn(),
}));

jest.mock('../../src/hooks/AppHook', () => ({
    useApp: jest.fn(),
}));

jest.mock('../../src/pages/Dashboard/User/Roles/AddRoleModal', () => () => <div data-testid="add-role-modal">Add Role Modal</div>);
jest.mock('../../src/pages/Dashboard/User/Roles/DeleteRoleModal', () => () => <div data-testid="delete-role-modal">Delete Role Modal</div>);
jest.mock('../../src/pages/Dashboard/User/Roles/PermissionsField', () => ({ role, permissions, setRowHeight, setAllowSave }) => {
    console.log('Mock Role:', role, 'Permissions:', permissions);
    if (setRowHeight) setRowHeight(role._id, permissions.length);
    if (setAllowSave) setAllowSave(true);
    return <div data-testid="permissions-field">Role: {role.role_name}, Permissions: {permissions.length}</div>;
});

jest.mock('../../src/pages/Dashboard/User/Roles/ReorderRolesModal', () => ({ open, onClose, onReorder }) => {
    onClose();
    onReorder();
    return open ? <div data-testid="reorder-roles-modal">Reorder Role Modal</div> : null;
});

describe('Roles Component', () => {
    let fetchRolesMock, socketMock;
    const modalProps = {
        showAddRole: false,
        setShowAddRole: jest.fn(),
        showReorderModal: false,
        setShowReorderModal: jest.fn(),
        showFilterModal: false,
        setShowFilterModal: jest.fn(),
    };

    beforeEach(() => {
        fetchRolesMock = jest.fn();
        socketMock = { on: jest.fn(), off: jest.fn() };

        axiosInstance.get.mockImplementation((url) => {
            if (url === '/roles') {
                return Promise.resolve({ data: [{ _id: '1', role_name: 'Admin', editable: true, hierarchy_number: 1 }, { _id: '2', role_name: 'User', editable: true, hierarchy_number: 2 }] });
            } else if (url === '/permissions') {
                return Promise.resolve({ data: [{ permission_id: 'p1', permission_name: 'Read' }] });
            }
        });

        axiosInstance.post.mockResolvedValue({});
        getSocket.mockReturnValue(socketMock);
        useUser.mockReturnValue({ user: { role_id: '1', hierarchy_number: 1 } });
        useApp.mockReturnValue({ screenSize: { xs: false, sm: false, md: false, lg: false } });

        modalProps.setShowAddRole.mockClear();
        modalProps.setShowReorderModal.mockClear();
        modalProps.setShowFilterModal.mockClear();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should fetch roles and permissions on mount', async () => {
        useApp.mockReturnValue({ screenSize: { xs: true, sm: false, md: false, lg: false } });
        render(<Roles {...modalProps} />);

        await waitFor(() => expect(axiosInstance.get).toHaveBeenCalledWith('/roles'));
        await waitFor(() => expect(axiosInstance.get).toHaveBeenCalledWith('/permissions'));
    });

    it('should reset roles and related states on reset', async () => {
        render(<Roles {...modalProps} />);
        await waitFor(() => expect(axiosInstance.get).toHaveBeenCalledWith('/roles'));
    });

    it('should handle socket events and cleanup on unmount', () => {
        const { unmount } = render(<Roles {...modalProps} />);

        expect(socketMock.on).toHaveBeenCalledWith('roles/changed', expect.any(Function));
        unmount();
        expect(socketMock.off).toHaveBeenCalledWith('roles/changed', expect.any(Function));
    });

    it('should validate role updation based on user hierarchy and role properties', () => {
        render(<Roles {...modalProps} />);

        const role = { role_id: '1', editable: true, hierarchy_number: 1 };
        const roleNotEditable = { role_id: '2', editable: false, hierarchy_number: 2 };

        useUser.mockReturnValue({ user: { role_id: '1', hierarchy_number: 1 } });

        expect(role.editable).toBeTruthy();
        expect(roleNotEditable.editable).toBeFalsy();
        expect(role.hierarchy_number).toBeGreaterThanOrEqual(1);
    });
});
