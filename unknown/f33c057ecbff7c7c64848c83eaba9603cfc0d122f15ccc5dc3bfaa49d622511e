import { render, screen, waitFor, act } from '@testing-library/react';
import Sessions from '../../src/pages/Dashboard/LogsPage/Sessions';
import axiosInstance from '../../src/axios';
import { useApp } from '../../src/hooks/AppHook';
import { getSocket } from '../../src/socket';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

jest.mock('../../src/hooks/AppHook', () => ({
    useApp: jest.fn().mockReturnValue(),
}));

jest.mock('../../src/axios', () => ({
    get: jest.fn().mockResolvedValue({
        data: [
            {
                _id: '1',
                user: {
                    name: 'Test User',
                    role_id: 'admin',
                },
                timestamp: '2024-11-14T10:00:00Z',
                type: 'connect',
                device: 'Mobile',
                browser: 'Chrome',
                socket_id: '1',
            },
            {
                _id: '2',
                user: {
                    name: 'Test User',
                    role_id: 'admin',
                },
                timestamp: '2024-11-14T11:00:00Z',
                type: 'connect',
                device: 'Desktop',
                browser: 'Firefox',
                socket_id: '2',
            },
            {
                _id: '3',
                user: {
                    name: 'Test User',
                    role_id: 'admin',
                },
                timestamp: '2024-11-14T11:00:00Z',
                type: 'undefined',
                device: 'Desktop',
                browser: 'Firefox',
                socket_id: '2',
            },
            {
                _id: '4',
                user: {
                    name: 'Test User',
                    role_id: 'admin',
                },
                timestamp: '2024-11-14T11:00:00Z',
                type: 'disconnect',
                device: 'Desktop',
                browser: 'Firefox',
                socket_id: '2',
            },
        ],
    }),
}));

jest.mock('../../src/socket', () => ({
    getSocket: jest.fn(),
}));

describe('Sessions Component', () => {
    const mockSocket = {
        on: jest.fn(),
        off: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
        useApp.mockReturnValue({ 
            screenSize: { xs: false },
            isMobile: false 
        });
        getSocket.mockReturnValue(mockSocket);
    });

    it('should fetch and display logs on render', async () => {
        render(<Sessions />);
        await waitFor(() => {
            expect(axiosInstance.get).toHaveBeenCalledWith('/logs/sessions');
            expect(screen.getByText('Name')).toBeInTheDocument();
        });
    });

    it('should handle search query filtering', async () => {
        render(<Sessions searchQuery="Test User" />);
        
        await waitFor(() => {
            const userCells = screen.getAllByRole('cell', { name: /Test User/i });
            expect(userCells[0]).toBeInTheDocument();
        });
    });

    it('should cleanup socket listeners on unmount', async () => {
        const { unmount } = render(<Sessions />);
        
        await waitFor(() => {
            expect(axiosInstance.get).toHaveBeenCalled();
        });
        
        unmount();
        expect(mockSocket.off).toHaveBeenCalled();
    });

    it('should handle error state when fetching logs fails', async () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
        axiosInstance.get.mockRejectedValueOnce(new Error('Failed to fetch'));

        render(<Sessions />);

        await waitFor(() => {
            expect(screen.getByText('No data available')).toBeInTheDocument();
        });

        consoleSpy.mockRestore();
    });
});
