import { render, screen, fireEvent } from '@testing-library/react';
import CustomSwitch from '../../src/components/CustomSwitch';
import { ThemeProvider } from '@mui/material';
import theme from '../../src/theme';
import { useApp } from '../../src/hooks/AppHook';

jest.mock('../../src/hooks/AppHook');

describe('CustomSwitch', () => {
    const mockAction = jest.fn();
    const props = {
        initialState: false,
        textOn: 'Enabled',
        textOff: 'Disabled',
        iconOn: '/icon-on.png',
        iconOff: '/icon-off.png',
        action: mockAction,
    };

    beforeEach(() => {
        useApp.mockReturnValue({ screenSize: { xs: true } });
        jest.clearAllMocks();
    });

    it('should render with the initial state', () => {
        useApp.mockReturnValue({ screenSize: { xs: false } });
        render(
            <ThemeProvider theme={theme}>
                <CustomSwitch {...props} />
            </ThemeProvider>
        );

        expect(screen.getByText('Disabled')).toBeInTheDocument();
        expect(screen.getByAltText('Switch Icon')).toHaveAttribute('src', '/icon-off.png');
    });

    it('should toggle state when clicked', () => {
        render(
            <ThemeProvider theme={theme}>
                <CustomSwitch {...props} action={null} />
            </ThemeProvider>
        );

        const switchElement = screen.getByText('Disabled').closest('div');
        fireEvent.click(switchElement);

        expect(screen.getByText('Enabled')).toBeInTheDocument();
        expect(screen.getByAltText('Switch Icon')).toHaveAttribute('src', '/icon-on.png');
    });

    it('should call the action function on toggle', () => {
        render(
            <ThemeProvider theme={theme}>
                <CustomSwitch {...props} />
            </ThemeProvider>
        );

        const switchElement = screen.getByText('Disabled').closest('div');
        fireEvent.click(switchElement);

        expect(mockAction).toHaveBeenCalledTimes(1);
    });

    it('should update when initialState changes', () => {
        const { rerender } = render(
            <ThemeProvider theme={theme}>
                <CustomSwitch {...props} initialState={false} />
            </ThemeProvider>
        );

        expect(screen.getByText('Disabled')).toBeInTheDocument();

        rerender(
            <ThemeProvider theme={theme}>
                <CustomSwitch {...props} initialState={true} />
            </ThemeProvider>
        );

        expect(screen.getByText('Enabled')).toBeInTheDocument();
    });

    it('should update when initialState changes', () => {
        const { rerender } = render(
            <ThemeProvider theme={theme}>
                <CustomSwitch {...props} initialState={false} />
            </ThemeProvider>
        );

        expect(screen.getByText('Disabled')).toBeInTheDocument();

        rerender(
            <ThemeProvider theme={theme}>
                <CustomSwitch {...props} initialState={true} />
            </ThemeProvider>
        );

        expect(screen.getByText('Enabled')).toBeInTheDocument();
    });
});
