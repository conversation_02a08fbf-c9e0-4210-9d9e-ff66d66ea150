import React from "react";
import { Grid, <PERSON><PERSON>, Ty<PERSON><PERSON>, Button } from "@mui/material";
import ModalContainer from "./ModalContainer";

const ConfirmModal = ({ title, message, initialState, onClose, onConfirm, isDanger = false }) => {
    return (
        <Modal open={initialState} onClose={onClose}>
            <ModalContainer title={title} onClose={onClose}>
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: "auto" }}>
                    <Grid>
                        <Typography fontWeight={"100"}>{message}</Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="outlined" onClick={onClose}>
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" color={isDanger ? "error" : undefined} onClick={onConfirm}>
                                Confirm
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default ConfirmModal;
