import { useState, useEffect, useRef } from "react";
import { DatePicker } from "@mui/x-date-pickers";
import theme from "../theme";
import { userValues } from "../utils.js";
import { useUser } from "../hooks/UserHook.jsx";

const CustomDatePicker = ({ value, onChange, minDate, maxDate, slots, sx, iconPosition = "end" }) => {
    const ref = useRef(null);
    const [open, setOpen] = useState(false);
    const { user } = useUser();

    const defaultStyle = {
        width: "100%",
        backgroundColor: "transparent",
        borderRadius: "40px",
        // border: '1px solid #E0E2E9',
        "& .MuiButtonBase-root": {
            padding: 0,
            margin: 0,
        },
        "& .MuiSvgIcon-root": {
            color: theme.palette.custom.mediumGrey,
            fontSize: "18px",
        },
        fontSize: "12px",
        height: "40px",
        "& .MuiInputBase-root": {
            display: "flex",
            flexDirection: iconPosition === "end" ? "row" : "row-reverse",
            padding: 0,
            gap: iconPosition === "end" ? 0 : "10px",
            border: "none",
            fontSize: "12px",
            color: theme.palette.custom.mediumGrey,
            justifyContent: "space-between",
            "& input": {
                padding: 0,
                maxWidth: "80px",
                cursor: "pointer",
            },
        },
    };

    const handleOpen = () => setOpen(true);

    useEffect(() => {
        if (ref.current) {
            ref.current.addEventListener("click", handleOpen);
        }

        return () => {
            if (ref.current) {
                ref.current.removeEventListener("click", handleOpen);
            }
        };
    }, []);

    const combinedStyles = { ...defaultStyle, ...sx };

    return (
        <DatePicker
            value={value}
            onChange={onChange}
            minDate={minDate}
            maxDate={maxDate}
            open={open}
            onOpen={handleOpen}
            onClose={() => setOpen(false)}
            slots={slots}
            sx={combinedStyles}
            format={userValues.dateTimeFormat(user, { exclude_hours: true })}
            inputRef={ref}
        />
    );
};

export default CustomDatePicker;
