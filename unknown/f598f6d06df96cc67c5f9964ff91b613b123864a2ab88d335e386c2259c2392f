const mongoose = require('mongoose');
const User = require('./User');
const db = require('../modules/db');

const TourGuideSchema = new mongoose.Schema({
    user_id: { type: mongoose.Schema.Types.ObjectId, ref: User,  required: true },
    maps: { type: mongoose.Schema.Types.Boolean, default: false },
    streams: { type: mongoose.Schema.Types.Boolean, default: false },
    events: { type: mongoose.Schema.Types.Boolean, default: false },
    notifications: { type: mongoose.Schema.Types.Boolean, default: false },
});

const TourGuide = db.qm.model('TourGuide', TourGuideSchema);
module.exports = TourGuide;
