// Import required libraries
import { PolarArea } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, RadialLinearScale } from 'chart.js';

// Register components in Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, RadialLinearScale, Title, Tooltip, Legend);

export default function PolarAreaChart({ data, options }) {

    return <PolarArea data={data} options={options} />;
}