const mongoose = require('mongoose');
const { authorizedUser } = require('../data/Auth');
const User = require('../../models/User');
const { getUser } = require('../../queries/User');
const mockingoose = require('mockingoose');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('getUser', () => {
    const validUserId = mongoose.Types.ObjectId()

    beforeEach(() => {
        jest.clearAllMocks(); // Clear mocks before each test
    });

    test('should resolve with user details when valid user_id is provided', async () => {
        User.aggregate.mockResolvedValue([authorizedUser]); // Mock Mongoose aggregate success

        console.log('getuser is', getUser)
        const result = await getUser({ user_id: validUserId });

        console.log('the result is ', result)

        expect(result).toBeInstanceOf(Object);
        ['_id', 'name', 'username', 'role_id', 'deletable', 'creation_timestamp', 'is_deleted', 'role', 'permissions'].forEach(prop => {
            expect(result).toHaveProperty(prop);
        });
    });

    test('should reject with error when user_id is not provided', async () => {
        try {
            await getUser({}); // Call without user_id
        } catch (error) {
            expect(error).toEqual({ message: 'getUser failed. No id provided' }); // Validate the error message
        }
    });

    test('should reject with an error when aggregate fails', async () => {
        const error = new Error('Database error')
        User.aggregate.mockRejectedValue(error); // Mock Mongoose aggregate failure

        try {
            await getUser({ user_id: validUserId });
        } catch (err) {
            expect(err).toBe(error); // Validate that the error is the same
        }

        expect(User.aggregate).toHaveBeenCalledTimes(1); // Ensure that the aggregate was called
    });
});
