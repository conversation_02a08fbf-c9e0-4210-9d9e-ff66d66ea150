import { useState } from "react";
import axiosInstance from "../../../../axios";
import { Button, Grid, Modal, TextField } from "@mui/material";
import ModalContainer from "../../../../components/ModalContainer";

const AddOrganizationModal = ({ showAddOrganization, setShowAddOrganization, setAdding, onSuccess }) => {
    const [organizationName, setOrganizationName] = useState("");
    const [domain, setDomain] = useState("");

    const handleClose = () => {
        setShowAddOrganization(false);
        setOrganizationName("");
        setDomain("");
    };

    const onAdd = () => {
        setAdding(true);
        axiosInstance
            .post("/organizations", { name: organizationName, domain }, { meta: { showSnackbar: true } })
            .then(() => {
                onSuccess && onSuccess();
                setOrganizationName("");
                setDomain("");
                handleClose();
            })
            .catch(console.error)
            .finally(() => setAdding(false));
    };

    return (
        <Modal open={Boolean(showAddOrganization)} onClose={handleClose}>
            <ModalContainer title={"Add New Organization"} onClose={handleClose}>
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: 500 }}>
                    <Grid>
                        <TextField
                            value={organizationName}
                            onChange={(e) => setOrganizationName(e.target.value)}
                            label="Organization Name"
                            variant="filled"
                            fullWidth
                        />
                    </Grid>
                    <Grid>
                        <TextField value={domain} onChange={(e) => setDomain(e.target.value)} label="Domain" variant="filled" fullWidth />
                    </Grid>
                    <Grid justifyContent={"center"} display={"flex"}>
                        <Button disabled={!organizationName || !domain} variant="contained" onClick={onAdd}>
                            Submit
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default AddOrganizationModal;
