import React, { useEffect, useMemo, useState } from "react";
import Joyride, { STATUS } from "react-joyride";
import { useLocation } from "react-router-dom";
import axiosInstance from "../axios.js";

const desktopDashboardSteps = [
    {
        target: ".dashboard-step-1",
        content: "Displaying your username, profile picture, and a logout button.",
        placement: "left",
    },
    {
        target: ".dashboard-step-2",
        content: "<PERSON><PERSON> will appear here",
        placement: "right",
    },
    {
        target: ".dashboard-step-3",
        content: "Displaying selected sensor name.",
        placement: "bottom",
    },
    {
        target: ".dashboard-step-4",
        content: "Displaying sensor status live or replay",
        placement: "bottom",
    },
    {
        target: ".dashboard-step-5",
        content: "Click here to see Replays.",
        placement: "right",
    },
    {
        target: ".dashboard-step-6",
        content: "Sensor details will be shown here, including current position, speed, and time (according to your local area).",
        placement: "left",
    },
    {
        target: ".dashboard-step-7",
        content: "Click here to see Sen<PERSON>.",
        placement: "right",
        spotlightClicks: true,
    },
    {
        target: ".dashboard-step-8",
        content: "The list of sensors will be displayed here, along with their status (live/offline).",
        placement: "right",
    },
    {
        target: ".dashboard-step-9",
        content: "Click here to see Map.",
        placement: "right",
        spotlightClicks: true,
    },
    {
        target: ".dashboard-step-10",
        content: "The current position can be located on this map, which will refresh every 10 seconds.",
        placement: "right",
    },
    {
        target: ".dashboard-step-11",
        content: "Click here to see Events.",
        placement: "right",
        spotlightClicks: true,
    },
    {
        target: ".dashboard-step-12",
        content: "The artifacts captured by the sensor will be displayed here.",
        placement: "top",
    },
    {
        target: ".dashboard-step-13",
        content: "The Replays options will be displayed here.",
        placement: "right",
    },
    {
        target: ".dashboard-step-14",
        content: "This button will help you to save part of the video",
        placement: "right",
    },
    {
        target: ".dashboard-step-15",
        content: "This button will help you to take screenshot from a current video",
        placement: "right",
    },
];

const mobileDashboardSteps = [
    {
        target: ".mobile-step-1",
        content: "Menus will appear here",
        placement: "right",
    },
    {
        target: ".dashboard-step-3",
        content: "Displaying selected sensor name.",
        placement: "bottom",
    },
    {
        target: ".dashboard-step-4",
        content: "Displaying sensor status live or replay",
        placement: "bottom",
    },
    {
        target: ".dashboard-step-5",
        content: "Displaying show replay/live button.",
        placement: "left",
    },
    {
        target: ".mobile-step-6",
        content: "Sensor details will be shown here, including current position, speed, and time (according to your local area).",
        placement: "left",
    },
    {
        target: ".dashboard-step-7",
        content: "The list of sensors will be displayed here, along with their status (live/offline).",
        placement: "right",
    },
    {
        target: ".dashboard-step-9",
        content: "The current position can be located on this map, which will refresh every 10 seconds",
        placement: "right",
    },
    {
        target: ".dashboard-step-11",
        content: "The artifacts captured by the sensor within the last 24 hours will be displayed here.",
        placement: "top",
    },
];

const mapSteps = [
    {
        target: ".map-step-1",
        content: "Click here to see Vessels.",
        placement: "right",
        spotlightClicks: true,
    },
    {
        target: ".map-step-2",
        content:
            "This panel allows users to view and interact with all available vessels on the map, providing options to control which vessels are displayed and focused on.",
        placement: "left",
    },
    {
        target: ".map-step-3",
        content: "Click here to see Filters.",
        placement: "right",
        spotlightClicks: true,
    },
    {
        target: ".map-step-4",
        content:
            "This panel allows users to refine the vessel history displayed on the map, offering flexible date and category filters as well as display options.",
        placement: "left",
    },
    {
        target: ".map-step-5",
        content: "Click here to see Advanced Settings.",
        placement: "right",
        spotlightClicks: true,
    },
    {
        target: ".map-step-6",
        content:
            "This panel allows users to fine-tune the display of data points to optimize performance and device load by adjusting the level of detail shown on the map.",
        placement: "left",
    },
];

const mobileMapSteps = [
    {
        target: ".map-step-2",
        content:
            "This panel allows users to view and interact with all available vessels on the map, providing options to control which vessels are displayed and focused on.",
        placement: "left",
    },
    {
        target: ".map-step-4",
        content:
            "This panel allows users to refine the vessel history displayed on the map, offering flexible date and category filters as well as display options.",
        placement: "left",
    },
    {
        target: ".map-step-6",
        content:
            "This panel allows users to fine-tune the display of data points to optimize performance and device load by adjusting the level of detail shown on the map.",
        placement: "left",
    },
];

const desktopEventSteps = [
    {
        target: ".events-step-1",
        content: "You can filter events based on different parameters, Click here to see Filters.",
        placement: "bottom",
        spotlightClicks: true,
    },
    {
        target: ".events-step-2",
        content: "This gives you a more detailed view about the artifacts, Click on an artifact to see more details.",
        placement: "left",
    },
];

const mobileEventSteps = [
    {
        target: ".events-step-1",
        content: "You can filter events based on different parameters, Click here to see Filters.",
        placement: "bottom",
    },
    {
        target: ".events-step-2",
        content: "This gives you a more detailed view about the artifacts, Click on an artifact to see more details.",
        placement: "top",
    },
];

const mobileNotificationSteps = [
    {
        target: ".notification-step-1",
        content: "Here you can switch between notifications and summary reports.",
        placement: "bottom",
    },
];

const desktopNotificationSteps = [
    {
        target: ".notification-step-1",
        content: "Here you can switch between notifications and summary reports.",
        placement: "bottom",
    },
    {
        target: ".notification-step-2",
        content: "Click here to create alerts.",
        placement: "bottom",
    },
    {
        target: ".notification-step-3",
        content: "Click here to create summary reports.",
        placement: "bottom",
    },
];

const stepsConfig = {
    "/stream": {
        mobile: mobileDashboardSteps,
        desktop: desktopDashboardSteps,
    },
    "/map": {
        mobile: mobileMapSteps,
        desktop: mapSteps,
    },
    "/events": {
        mobile: mobileEventSteps,
        desktop: desktopEventSteps,
    },
    "/notification": {
        mobile: mobileNotificationSteps,
        desktop: desktopNotificationSteps,
    },
};

export default function TourGuide({ isMobile }) {
    const { pathname } = useLocation();
    const [run, setRun] = useState(false);
    const [stepIndex, setStepIndex] = useState(null);
    const [, setTourGuide] = useState(null);

    const { localStorageKey, steps } = useMemo(() => {
        for (const path in stepsConfig) {
            if (pathname.includes(path)) {
                const welcomeStep = {
                    target: "body",
                    content: (
                        <div style={{ textAlign: "center" }}>
                            <h2>Welcome to Quartermaster</h2>
                            <p>We are excited to give you a quick tour to show you around.</p>
                        </div>
                    ),
                    placement: "center",
                    disableBeacon: true,
                    isFixed: true,
                    styles: {
                        options: {
                            zIndex: 10000,
                            width: "100%",
                            height: "100%",
                            overlayColor: "rgba(0, 0, 0, 0.9)",
                        },
                    },
                };

                const allSteps = isMobile ? [welcomeStep, ...stepsConfig[path].mobile] : [welcomeStep, ...stepsConfig[path].desktop];
                return {
                    localStorageKey: path.includes("/stream")
                        ? "dashboardTourGuide"
                        : path.includes("/map")
                          ? "mapTourGuide"
                          : path.includes("/notification")
                            ? "notificationTourGuide"
                            : "eventsTourGuide",
                    steps: allSteps,
                };
            }
        }
        return { localStorageKey: "", steps: [] };
    }, [pathname, isMobile]);

    const checkForInitialTour = async () => {
        if (localStorageKey && !localStorage.getItem(localStorageKey)) {
            setRun(true);
            setStepIndex(0);
        }
    };

    const handleCallback = (data) => {
        const { status, index } = data;
        if (isMobile && index === steps.length - 1) {
            let lastStepTarget;

            if (pathname.includes("/map")) {
                lastStepTarget = document.querySelector(".map-step-3");
            } else if (pathname.includes("/stream")) {
                lastStepTarget = document.querySelector(".dashboard-step-9");
            } else if (pathname.includes("/events")) {
                lastStepTarget = document.querySelector(".events-step-4");
            } else if (pathname.includes("/notification")) {
                lastStepTarget = document.querySelector(".notification-step-4");
            }

            if (lastStepTarget) {
                lastStepTarget.scrollIntoView({ behavior: "smooth", block: "center" });
            }
        }

        if (localStorageKey && (status === STATUS.FINISHED || status === STATUS.SKIPPED)) {
            localStorage.setItem(localStorageKey, "true");
            const payload = {};
            if (localStorageKey === "dashboardTourGuide") {
                payload.streams = true;
            }

            if (localStorageKey === "mapTourGuide") {
                payload.maps = true;
            }
            if (localStorageKey === "eventsTourGuide") {
                payload.events = true;
            }

            if (localStorageKey === "notificationTourGuide") {
                payload.notifications = true;
            }

            axiosInstance.patch(`/tourGuides/`, payload).then((res) => {
                setTourGuide(res.data);
            });
            setRun(false);
            setStepIndex(0);
        }
    };

    useEffect(() => {
        axiosInstance
            .get("/tourGuides")
            .then((res) => {
                if (res.data.length === 0) {
                    axiosInstance
                        .post("/tourGuides", {
                            events: Boolean(localStorage.getItem("eventsTourGuide")) || false,
                            maps: Boolean(localStorage.getItem("mapTourGuide")) || false,
                            streams: Boolean(localStorage.getItem("dashboardTourGuide")) || false,
                            notifications: Boolean(localStorage.getItem("notificationTourGuide")),
                        })
                        .then((res) => {
                            setTourGuide(res.data);
                        })
                        .catch((err) => {
                            console.error(err);
                        });
                }
                if (res.data.length > 0) {
                    setTourGuide(res.data[0]);
                    if (!localStorage.getItem("mapTourGuide") && res.data[0].maps === true) {
                        localStorage.setItem("mapTourGuide", "true");
                    }
                    if (!localStorage.getItem("dashboardTourGuide") && res.data[0].streams === true) {
                        localStorage.setItem("dashboardTourGuide", "true");
                    }

                    if (!localStorage.getItem("eventsTourGuide") && res.data[0].events === true) {
                        localStorage.setItem("eventsTourGuide", "true");
                    }

                    if (!localStorage.getItem("notificationTourGuide") && res.data[0].notifications === true) {
                        localStorage.setItem("notificationTourGuide", "true");
                    }

                    const payload = {};

                    if (localStorage.getItem("mapTourGuide") === "true" && res.data[0].maps === false) {
                        payload.maps = true;
                    }
                    if (localStorage.getItem("dashboardTourGuide") === "true" && res.data[0].streams === false) {
                        payload.streams = true;
                    }
                    if (localStorage.getItem("eventsTourGuide") === "true" && res.data[0].events === false) {
                        payload.events = true;
                    }

                    if (localStorage.getItem("notificationTourGuide") === "true" && res.data[0].notifications === false) {
                        payload.notifications = true;
                    }
                    if (Object.keys(payload).length > 0) {
                        axiosInstance
                            .patch(`/tourGuides/`, payload)
                            .then((res) => {
                                setTourGuide(res.data);
                            })
                            .catch((err) => {
                                console.error(err);
                            });
                    }
                }
            })
            .catch((err) => {
                console.error(err);
            });
        const timer = setTimeout(checkForInitialTour, 5000);
        return () => clearTimeout(timer);
    }, [pathname]);

    return (
        <Joyride
            steps={steps}
            stepIndex={stepIndex}
            callback={handleCallback}
            run={run}
            continuous
            hideCloseButton
            scrollToFirstStep
            hideBackButton
            disableCloseOnEsc
            disableOverlayClose
            disableScrolling={false}
            showProgress
            showSkipButton
            locale={{ last: "✔" }}
            styles={{
                options: {
                    zIndex: 10000,
                    primaryColor: "red",
                    overlayColor: "rgba(0, 0, 0, 0.9)",
                },
                overlay: {
                    backgroundColor: "rgba(0, 0, 0, 0.5)",
                    mixBlendMode: "hard-light",
                },
                tooltip: { width: "300px" },
                buttonNext: {
                    backgroundColor: "rgb(21, 25, 30)",
                    padding: "10px",
                    color: "#FFFFFF",
                    border: "none",
                    borderRadius: "10px",
                },
            }}
        />
    );
}
