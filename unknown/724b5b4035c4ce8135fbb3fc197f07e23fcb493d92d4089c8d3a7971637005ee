import { render, screen, fireEvent, waitFor, act, within } from "@testing-library/react";
import ApiKeys from "../../src/pages/Dashboard/ApiKeys/ApiKeys";
import axiosInstance from "../../src/axios";
import { getSocket } from "../../src/socket";
import { useApp } from "../../src/hooks/AppHook";
import { useToaster } from "../../src/hooks/ToasterHook";
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

jest.mock("../../src/axios", () => ({
    get: jest.fn(),
    patch: jest.fn(),
}));
jest.mock("../../src/socket", () => ({
    getSocket: jest.fn(() => ({
        on: jest.fn(),
        off: jest.fn(),
    })),
}));
jest.mock("../../src/hooks/AppHook", () => ({
    useApp: jest.fn(),
}));
jest.mock("../../src/pages/Dashboard/ApiKeys//AddKeyModal", () => jest.fn(() => <div>Add Key Modal</div>));
jest.mock("../../src/pages/Dashboard/ApiKeys//DeleteKeyModal", () => jest.fn(() => <div>Delete Key Modal</div>));
jest.mock("../../src/pages/Dashboard/ApiKeys//RevokeKeyModal", () => jest.fn(() => <div>Revoke Key Modal</div>));
jest.mock("../../src/pages/Dashboard/ApiKeys//ApiAccessModal", () => jest.fn(() => <div>API Access Modal</div>));
jest.mock('../../src/hooks/ToasterHook', () => ({
    useToaster: jest.fn()
}));

describe("ApiKeys", () => {
    const mockToaster = jest.fn();
    const mockSocket = { on: jest.fn(), off: jest.fn() };
    const mockKeys = [
        { _id: "1", serial: 1, api_key: "test-key-1", description: "Test 1", is_revoked: false },
        { _id: "2", serial: 2, api_key: "test-key-2", description: "Test 2", is_revoked: true }
    ];

    beforeEach(() => {
        jest.clearAllMocks();
        useApp.mockReturnValue({ screenSize: { xs: false }, isMobile: false });
        useToaster.mockReturnValue(mockToaster);
        getSocket.mockReturnValue(mockSocket);

        axiosInstance.get.mockImplementation((url) => {
            if (url === '/apiKeys') {
                return Promise.resolve({ data: mockKeys });
            }
            if (url === '/apiEndpoints') {
                return Promise.resolve({ data: [] });
            }
            return Promise.reject(new Error('Not found'));
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("fetches and displays API keys on mount", async () => {
        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        expect(axiosInstance.get).toHaveBeenCalledWith('/apiKeys');
        expect(screen.getByText('Test 1')).toBeInTheDocument();
    });

    it("handles search filtering", async () => {
        await act(async () => {
            render(<ApiKeys searchQuery="Test 1" />);
        });

        expect(screen.getByText('Test 1')).toBeInTheDocument();
    });

    it("handles API key visibility toggle", async () => {
        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        const visibilityButton = screen.getAllByRole('button')[0];
        await act(async () => {
            fireEvent.click(visibilityButton);
        });

        expect(screen.getByText('test-key-1')).toBeInTheDocument();
    });

    it("handles mobile view and row expansion", async () => {
        useApp.mockReturnValue({ screenSize: { xs: true }, isMobile: true });

        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        const rows = screen.getAllByRole('row');
        const firstRow = rows[0];

        const expandButton = within(firstRow).getByTestId('ExpandMoreIcon');
        await act(async () => {
            fireEvent.click(expandButton);
        });

        const expandedSection = screen.getByRole('cell', { name: /Description/i }).closest('tr');
        const descriptionLabel = within(expandedSection).getByText('Description');

        expect(descriptionLabel).toBeInTheDocument();
    });

    it("handles API error gracefully", async () => {
        axiosInstance.get.mockRejectedValueOnce(new Error("API Error"));

        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        expect(mockToaster).toHaveBeenCalledWith('Something went wrong', { variant: 'error' });
    });

    it("handles modal states correctly", async () => {
        const setShowAddKey = jest.fn();

        await act(async () => {
            render(
                <ApiKeys
                    searchQuery=""
                    showAddKey={true}
                    setShowAddKey={setShowAddKey}
                />
            );
        });

        expect(screen.getByText('Add Key Modal')).toBeInTheDocument();

        await act(async () => {
            setShowAddKey(false);
        });
    });

    it("cleans up socket listeners", async () => {
        const { unmount } = render(<ApiKeys searchQuery="" />);

        await act(async () => {
            unmount();
        });

        expect(mockSocket.off).toHaveBeenCalledWith('apiKeys/changed', expect.any(Function));
    });

    it("should update key access when keys change", async () => {
        const updatedKeys = [
            { _id: "1", api_key: "updated-key-1", description: "Updated Test 1" }
        ];

        let rendered;
        await act(async () => {
            rendered = render(<ApiKeys searchQuery="" />);
        });

        fireEvent.click(screen.getAllByText('Manage Permissions')[0]);

        await act(async () => {
            axiosInstance.get.mockImplementationOnce((url) => {
                if (url === '/apiKeys') {
                    return Promise.resolve({ data: updatedKeys });
                }
                return Promise.resolve({ data: [] });
            });
            const socket = getSocket();
            socket.on.mock.calls[0][1]();
        });

        expect(screen.getByText('Updated Test 1')).toBeInTheDocument();
    });

    it("should filter keys based on search query", async () => {
        const searchableKeys = [
            { _id: "1", serial: 1, description: "Searchable Key", requests: 100, api_key: "key1" },
            { _id: "2", serial: 2, description: "Another Key", requests: 200, api_key: "key2" },
            { _id: "3", serial: 3, description: '', requests: null, api_key: "key3" }
        ];

        axiosInstance.get.mockImplementation((url) => {
            if (url === '/apiKeys') {
                return Promise.resolve({ data: searchableKeys });
            }
            return Promise.resolve({ data: [] });
        });

        await act(async () => {
            render(<ApiKeys searchQuery="Searchable" />);
        });

        await waitFor(() => {
            expect(screen.getByText((content) =>
                content.includes("Searchable Key")
            )).toBeInTheDocument();
        });

        expect(screen.queryByText((content) =>
            content.includes("Another Key")
        )).toBeInTheDocument();
    });

    it("should handle empty API keys response", async () => {
        axiosInstance.get.mockImplementation((url) => {
            if (url === '/apiKeys') {
                return Promise.resolve({ data: [] });
            }
            return Promise.resolve({ data: [] });
        });

        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        expect(mockToaster).toHaveBeenCalledWith('No data found for keys', { variant: 'warning' });
    });

    it("should handle successful endpoints fetch", async () => {
        const mockEndpoints = [
            { path: "/test1", method: "GET" },
            { path: "/test2", method: "POST" }
        ];

        axiosInstance.get.mockImplementation((url) => {
            if (url === '/apiEndpoints') {
                return Promise.resolve({ data: mockEndpoints });
            }
            return Promise.resolve({ data: mockKeys });
        });

        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        expect(axiosInstance.get).toHaveBeenCalledWith('/apiEndpoints');

        const managePermissionsButtons = screen.getAllByText('Manage Permissions');
        fireEvent.click(managePermissionsButtons[0]);

        await waitFor(() => {
            expect(screen.getByText('API Access Modal')).toBeInTheDocument();
        });
    });

    it("should handle empty endpoints response", async () => {
        axiosInstance.get.mockImplementation((url) => {
            if (url === '/apiEndpoints') {
                return Promise.resolve({ data: [] });
            }
            return Promise.resolve({ data: mockKeys });
        });

        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        expect(mockToaster).toHaveBeenCalledWith('No data found for endpoints', { variant: 'warning' });
    });

    it("should handle error in endpoints fetch", async () => {
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => { });

        axiosInstance.get.mockImplementation((url) => {
            if (url === '/apiEndpoints') {
                return Promise.reject(new Error('Endpoint fetch failed'));
            }
            return Promise.resolve({ data: mockKeys });
        });

        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        expect(mockToaster).toHaveBeenCalledWith('Something went wrong', { variant: 'error' });
        expect(consoleErrorSpy).toHaveBeenCalledWith(
            'An error occurred while fetching endpoints on the ApiKeys Page',
            expect.any(Error)
        );

        consoleErrorSpy.mockRestore();
    });

    it("should handle initial row expansion when no previous row", async () => {
        useApp.mockReturnValue({ screenSize: { xs: true }, isMobile: true });

        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        const rows = screen.getAllByRole('row');
        const firstRow = rows[0];
        const expandButton = within(firstRow).getByTestId('ExpandMoreIcon');

        await act(async () => {
            fireEvent.click(expandButton);
        });

        const expandedRow = firstRow.nextElementSibling;
        const descriptionCell = within(expandedRow).getByText('Description');
        expect(descriptionCell).toBeInTheDocument();

        const visibleDescriptions = screen.getAllByText('Description')
            .filter(el => el.closest('tr') === expandedRow);
        expect(visibleDescriptions).toHaveLength(1);
    });

    it("should handle pagination and page size changes", async () => {
        const manyKeys = Array.from({ length: 25 }, (_, i) => ({
            _id: `key${i}`,
            serial: i + 1,
            api_key: `test-key-${i}`,
            description: `Test Description ${i}`,
            is_revoked: false,
            requests: i
        }));

        axiosInstance.get.mockImplementation((url) => {
            if (url === '/apiKeys') {
                return Promise.resolve({ data: manyKeys });
            }
            return Promise.resolve({ data: [] });
        });

        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        await waitFor(() => {
            expect(screen.getByText((content) =>
                content.includes('Test Description 0')
            )).toBeInTheDocument();
        });

        const rowsPerPageSelect = screen.getByRole('combobox');
        const paginationNav = screen.getByRole('navigation');

        await act(async () => {
            fireEvent.mouseDown(rowsPerPageSelect);
        });

        const listbox = screen.getByRole('listbox');
        await act(async () => {
            fireEvent.click(within(listbox).getByText(/20/));
        });

        await waitFor(() => {
            expect(screen.getByText((content) =>
                content.includes('Test Description 19')
            )).toBeInTheDocument();
        });

        const nextPageButton = within(paginationNav).getByRole('button', { name: /go to next page/i });
        await act(async () => {
            fireEvent.click(nextPageButton);
        });

        const currentPage = within(paginationNav).getByRole('button', {
            name: (content) => content.includes('page 2')
        });

        expect(currentPage).not.toHaveClass('Mui-selected');
    });

    it("should handle revoke and delete button states and clicks", async () => {
        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        await waitFor(() => {
            expect(screen.getByText('Test 1')).toBeInTheDocument();
        });

        expect(screen.getByText('Revoke Key Modal')).toBeInTheDocument();
        expect(screen.getByText('Delete Key Modal')).toBeInTheDocument();
    });

    it("should handle revoke and delete button clicks in mobile view", async () => {
        useApp.mockReturnValue({ screenSize: { xs: true }, isMobile: true });

        await act(async () => {
            render(<ApiKeys searchQuery="" />);
        });

        const firstRow = screen.getAllByRole('row')[0];
        const expandButton = within(firstRow).getByTestId('ExpandMoreIcon');
        await act(async () => {
            fireEvent.click(expandButton);
        });

        const expandedRow = firstRow.nextElementSibling;
        const revokeButton = within(expandedRow).getByText('Revoke');
        await act(async () => {
            fireEvent.click(revokeButton);
        });
        expect(screen.getByText('Revoke Key Modal')).toBeInTheDocument();

        const deleteButton = within(expandedRow).getByText('Delete');
        await act(async () => {
            fireEvent.click(deleteButton);
        });
        expect(screen.getByText('Delete Key Modal')).toBeInTheDocument();
    });
});
