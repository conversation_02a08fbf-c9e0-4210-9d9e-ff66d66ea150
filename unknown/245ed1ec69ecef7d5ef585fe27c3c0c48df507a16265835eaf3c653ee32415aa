import { useState, useEffect } from "react";
import {
    Button,
    <PERSON>alog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Box, // Use Box for simple layout needs
} from "@mui/material";

// Generate week options (1 to 24)
const MAX_WEEKS = 24;
const weekOptions = Array.from({ length: MAX_WEEKS }, (_, i) => i + 1); // Generates [1, 2, ..., 24]

function ReportDurationModal({ open, onClose, onSubmit, initialValue = "0" }) {
    const [selectedDuration, setSelectedDuration] = useState(initialValue);

    // Reset selection when the modal is opened if initialValue changes or just to ensure clean state
    useEffect(() => {
        if (open) {
            setSelectedDuration(initialValue);
        }
    }, [open, initialValue]);

    const handleChange = (event) => {
        setSelectedDuration(event.target.value);
    };

    const handleCancel = () => {
        onClose();
    };

    const handleSubmit = () => {
        onSubmit(selectedDuration);
    };

    return (
        <Dialog
            sx={{ "& .MuiDialog-paper": { backgroundColor: "#020716" } }}
            open={open}
            onClose={handleCancel} // Allow closing by clicking backdrop or pressing Esc
            aria-labelledby="report-duration-dialog-title"
            fullWidth
            maxWidth="xs" // Sets a max width (e.g., 'xs', 'sm')
        >
            <DialogTitle sx={{ color: "#FFFFFF" }} id="report-duration-dialog-title">
                Choose Report Coverage
            </DialogTitle>
            <DialogContent>
                <Box component="form" noValidate autoComplete="off" sx={{ mt: 1 }}>
                    <FormControl fullWidth required>
                        <InputLabel sx={{ color: "#FFFFFF" }} id="duration-select-label">
                            Weeks of Coverage
                        </InputLabel>
                        <Select
                            labelId="duration-select-label"
                            id="duration-select"
                            value={selectedDuration}
                            label="Weeks of Coverage" // Required for InputLabel to float correctly
                            onChange={handleChange}
                        >
                            {/* All Time Option */}
                            <MenuItem value="0">All Time</MenuItem>

                            {/* Dynamic Week Options */}
                            {weekOptions.map((week) => (
                                <MenuItem key={week} value={week}>
                                    {week} Week{week > 1 ? "s" : ""} {/* Handle pluralization */}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Box>
            </DialogContent>
            <DialogActions sx={{ padding: "16px 24px" }}>
                <Button
                    onClick={handleCancel}
                    sx={{
                        color: "#FFFFFF",
                        textTransform: "none",
                        border: "1px solid grey",
                        padding: "10px",
                    }}
                >
                    Cancel
                </Button>
                <Button onClick={handleSubmit} variant="contained" color="primary">
                    Submit
                </Button>
            </DialogActions>
        </Dialog>
    );
}

export default ReportDurationModal;
