import { Button, Grid, Modal, Typography } from "@mui/material";
import ModalContainer from "../../../../components/ModalContainer";

const DeleteConfirmationModal = ({ showDeleteModal, setShowDeleteModal, onDelete }) => {
    const handleClose = () => {
        setShowDeleteModal(false);
    };

    const handleConfirm = () => {
        onDelete();
        handleClose();
    };

    return (
        <Modal open={Boolean(showDeleteModal)} onClose={handleClose}>
            <ModalContainer title={"Confirm Deletion"} onClose={handleClose}>
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: 500 }}>
                    <Grid>
                        <Typography fontWeight={400} textAlign={"center"}>
                            Are you sure you want to delete this organization? This action cannot be undone.
                        </Typography>
                    </Grid>
                    <Grid container justifyContent={"center"} gap={2}>
                        <Button variant="outlined" onClick={handleClose}>
                            Cancel
                        </Button>
                        <Button variant="contained" color="error" onClick={handleConfirm}>
                            Delete
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default DeleteConfirmationModal;
