const mockingoose = require('mockingoose');
const ioEmitter = require('../../modules/ioEmitter');
const { usersList } = require('../data/Users');
const User = require('../../models/User');

// Mock the ioEmitter
jest.mock('../../modules/ioEmitter', () => ({
    emit: jest.fn(),
}));

describe('SessionLog Model', () => {
    it('should create the document', async () => {
        const doc = usersList[0]

        mockingoose(User).toReturn(doc, 'create');

        const result = await User.create({
            name: doc.name,
            username: doc.user,
            email: '<EMAIL>',
            password: 'hashed-password',
            role_id: 2
        });

        expect(result).toBeInstanceOf(Object)
        Object.keys(doc).forEach(prop => {
            expect(result).toHaveProperty(prop)
        })
    });

    it('should return the doc with findById', async () => {
        const doc = { ...usersList[0], password: 'hashed-password' }

        mockingoose(User).toReturn(doc, 'findOne');

        const result = await User.findById({ _id: doc._id });

        expect(result).toBeInstanceOf(Object)
        Object.keys(doc).forEach(prop => {
            expect(result).toHaveProperty(prop)
        })
    });

    it('should return the doc with find', async () => {
        const docs = usersList

        mockingoose(User).toReturn(docs, 'find');

        const result = await User.find();

        expect(result).toBeInstanceOf(Array)
        Object.keys(docs[0]).forEach(prop => {
            expect(result[0]).toHaveProperty(prop)
        })
    });

    it('should trigger post save hook and emit an event', async () => {
        const doc = { ...usersList[0], password: 'hashed-password' }

        // Mocking the save operation
        mockingoose(User).toReturn(doc, 'save');

        const user = new User(doc);
        await user.save(); // This should trigger the `post('save')` hook

        // Check if the ioEmitter was called
        expect(ioEmitter.emit).toHaveBeenCalled();
    });

    it('should trigger post findOneAndDelete hook and emit an event', async () => {
        const doc = usersList[0]

        // Mocking the save operation
        mockingoose(User).toReturn(doc, 'findOneAndDelete');

        const deletedDoc = await User.findOneAndDelete({ _id: doc._id })

        User.emitChangedEvent(deletedDoc)

        // ApiKey.schema.post('findOneAndDelete')(deletedDoc);

        // Check if the ioEmitter was called
        expect(ioEmitter.emit).toHaveBeenCalled();
    });
});
