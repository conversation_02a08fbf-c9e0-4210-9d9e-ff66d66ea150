.react-datepicker__input-container input {
    background-color: transparent;
    border: 1px solid white;
    border-radius: 10px;
    cursor: pointer;
    padding: 10px;
    font-size: 14px;
    width: 100%;
    color: white;
}

.react-datepicker__input-container .react-datepicker__calendar-icon {
    right: 2px;
}

svg {
    fill: white;
}


.react-datepicker__day--selected {
    background-color: black;
    color: #fff;
}

.react-datepicker__month-container {
    border: 1px solid #343B44;
    background-color: #343B44;
    color: white;

}

.react-datepicker__header {
    background-color: #343B44;
    color: white;
}

.react-datepicker__current-month,
.react-datepicker__day,
.react-datepicker__day-name {
    color: white;
}

.react-datepicker__day:hover {
    background-color: white;
    color: black;
    border-radius: 50%;
}

.react-datepicker__day-name:hover {
    color: grey;
}