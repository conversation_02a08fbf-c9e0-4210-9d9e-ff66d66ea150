import { render, screen, fireEvent } from '@testing-library/react';
import StatisticsManagement from '../../src/pages/Dashboard/Statistics/StatisticsManagement';
import { useUser } from '../../src/hooks/UserHook';

jest.mock('../../src/axios', () => ({
    get: jest.fn(),
    post: jest.fn(),
}));
jest.mock('../../src/hooks/UserHook', () => ({
    useUser: jest.fn(),
}));
jest.mock('../../src/pages/Dashboard/Statistics/StatisticsPastWeeks', () => () => <div>StatisticsPastWeeks Component</div>);
jest.mock('../../src/pages/Dashboard/Statistics/StatisticsPastDaylights', () => () => <div>StatisticsPastDaylights Component</div>);


describe('StatisticsManagement', () => {
    it('should render tabs and components based on permissions', () => {
        useUser.mockReturnValue({
            user: {
                hasPermissions: jest.fn().mockReturnValue(true),
            },
        });

        render(<StatisticsManagement />);

        expect(screen.getByText('Total')).toBeInTheDocument();
    });

    it('should not render the component if the user has no permissions', () => {
        useUser.mockReturnValue({
            user: {
                hasPermissions: jest.fn().mockReturnValue(false),
            },
        });

        render(<StatisticsManagement />);

        expect(screen.queryByText('Past 24hrs')).not.toBeInTheDocument();
        expect(screen.queryByText('Total')).not.toBeInTheDocument();
    });

    it('should switch between tabs correctly', () => {
        useUser.mockReturnValue({
            user: {
                hasPermissions: jest.fn().mockReturnValue(true),
            },
        });

        render(<StatisticsManagement />);

        const totalTab = screen.getByText('Total');
        fireEvent.click(totalTab);

        expect(totalTab).toHaveAttribute('aria-selected', 'true');
    });
});
