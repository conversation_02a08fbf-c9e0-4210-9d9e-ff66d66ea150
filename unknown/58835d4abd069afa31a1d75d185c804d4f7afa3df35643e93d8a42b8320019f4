import "video.js/dist/video-js.css";
import { <PERSON>rid, Tooltip, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { displayCoordinates } from "../../../utils";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import s3Controller from "../../../controllers/S3.controller.js";

const Detection = ({ artifact, favouriteArtifacts }) => {
    const [src, setSrc] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);
    // const [locationName, setLocationName] = useState('Loading...');
    const {
        location: { coordinates },
        video_path,
        others,
    } = artifact;
    const key = displayCoordinates([coordinates[0], coordinates[1]]);
    // const location = { lat: coordinates[1], lng: coordinates[0] };

    // const fetchGeolocation = async () => {
    //     if (coordinates.length === 2) {
    //         try {
    //             const name = await getLocation(location);
    //             setLocationName(name);
    //         } catch (err) {
    //             console.error('Error fetching geolocation:', err);
    //         }
    //     }
    // };

    useEffect(() => {
        setSrc(s3Controller.fetchUrl(artifact, artifact.video_path ? "video" : "image"));
        setThumbnail(s3Controller.fetchPreviewUrl(artifact));
    }, [artifact]);

    // useEffect(() => {
    //     fetchGeolocation();
    // }, []);

    return (
        <Grid container color="#FFFFFF" flexDirection={{ xs: "column", sm: "row" }} gap={1} sx={{ backgroundColor: "primary.main" }} padding={1}>
            <Grid item xs position="relative">
                <PreviewMedia
                    thumbnailLink={thumbnail} // here will be thumbnail
                    originalLink={src} // put here link on original image
                    showVideoModal={true}
                    cardId={artifact._id}
                    favouriteArtifacts={favouriteArtifacts}
                    isImage={!video_path}
                    style={{ borderRadius: 8 }}
                    skeletonStyle={{ height: "100%", borderRadius: "8px" }}
                    showFullscreenIcon={true}
                />
            </Grid>
            <Grid item container xs flexDirection="column" gap={1}>
                <Grid item fontWeight={500} fontSize="14px" sx={{ color: theme.palette.custom.mainBlue }}>
                    Location
                </Grid>
                <Grid item fontWeight={500} fontSize="14px">
                    {/* <Tooltip enterDelay={300} title={key} placement="bottom"> */}
                    <Typography fontSize="14px" fontWeight={400}>
                        {key}
                    </Typography>
                    {/* </Tooltip> */}
                </Grid>
                <Grid item fontWeight={500} fontSize="14px" sx={{ color: theme.palette.custom.mainBlue }}>
                    Description
                </Grid>
                <Grid item fontWeight={500} fontSize="14px">
                    <Tooltip enterDelay={300} title={others?.length > 45 ? others : ""} placement="bottom">
                        {others ? (others.length > 45 ? `${others.slice(0, 45)}...` : others) : "--"}
                    </Tooltip>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default Detection;
