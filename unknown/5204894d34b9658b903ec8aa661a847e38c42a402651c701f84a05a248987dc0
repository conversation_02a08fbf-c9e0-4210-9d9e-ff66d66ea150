const otpService = require('../../modules/otpService');

describe('OTP Service', () => {
    const email = '<EMAIL>';
    let sendEmail;
    let mockSetTimeout;
    let mockClearTimeout;

    beforeEach(() => {
        jest.useFakeTimers();
        jest.clearAllMocks();
        otpService.otpStore.length = 0;
        sendEmail = jest.fn();

        mockSetTimeout = jest.fn((callback, delay) => setTimeout(callback, delay));
        mockClearTimeout = jest.fn((timeout) => clearTimeout(timeout));
        otpService.cleanupTimeout = null;
    });

    afterEach(() => {
        jest.clearAllTimers();
        otpService.stopCleanupTimeout(mockClearTimeout);
    });

    describe('sendOtp', () => {
        it('should generate and send OTP', async () => {
            const response = await otpService.sendOtp(email, sendEmail);

            expect(response).toEqual({ message: 'OTP sent successfully' });
            expect(sendEmail).toHaveBeenCalledWith({
                to: email,
                subject: 'Your OTP Code',
                text: expect.stringMatching(/\d{6}/),
            });
            expect(otpService.otpStore.length).toBe(1);
        });

        it('should handle errors during OTP sending', async () => {
            sendEmail.mockRejectedValueOnce(new Error('Failed to send OTP'));

            await expect(otpService.sendOtp(email, sendEmail)).rejects.toThrow('Failed to send OTP');
        });
    });

    describe('verifyOtp', () => {
        it('should verify OTP correctly', () => {
            otpService.otpStore.push({
                email,
                otp: 100000,
                expiresAt: Date.now() + 60000,
            });
            const result = otpService.verifyOtp(email, 100000);
            expect(result).toEqual({ valid: true });
        });

        it('should return invalid for incorrect OTP', () => {
            const result = otpService.verifyOtp(email, 100010);
            expect(result).toEqual({ valid: false, message: 'Invalid OTP or email' });
        });

        it('should return OTP expired if OTP has expired', () => {
            otpService.otpStore.push({
                email,
                otp: 100000,
                expiresAt: Date.now() - 60000,
            });
            const result = otpService.verifyOtp(email, 100000);
            expect(result).toEqual({ valid: false, message: 'OTP expired' });
        });
    });

    describe('cleanupExpiredOtps', () => {
        // it('should remove expired OTPs from the store', () => {
        //     otpService.otpStore.push({
        //         email,
        //         otp: 100000,
        //         expiresAt: Date.now() - 60000,
        //     });
        //     otpService.cleanupExpiredOtps();

        //     expect(otpService.otpStore.length).toEqual(0);
        // });

        it('should keep valid OTPs in store after cleanup', () => {
            otpService.otpStore.push({
                email,
                otp: 100000,
                expiresAt: Date.now() + 60000,
            });
            otpService.cleanupExpiredOtps();

            expect(otpService.otpStore.length).toEqual(1);
        });
    });

    describe('startCleanupTimeout', () => {
        it('should start cleanup timeout only once', () => {
            otpService.startCleanupTimeout(mockSetTimeout);
            otpService.startCleanupTimeout(mockSetTimeout);

            expect(mockSetTimeout).toHaveBeenCalledTimes(1);
        });

        it('should reschedule cleanup timeout if OTPs remain after cleanup', () => {
            otpService.otpStore.push({
                email,
                otp: 123456,
                expiresAt: Date.now() + 60000,
            });
            otpService.startCleanupTimeout(mockSetTimeout);

            jest.runOnlyPendingTimers();

            expect(mockSetTimeout).toHaveBeenCalled();
        });

        it('should not reschedule cleanup if OTP store is empty after cleanup', () => {
            otpService.otpStore.push({
                email,
                otp: 123456,
                expiresAt: Date.now() - 60000,
            });

            otpService.startCleanupTimeout(mockSetTimeout);
            otpService.cleanupExpiredOtps();

            jest.runOnlyPendingTimers();
            expect(mockSetTimeout).toHaveBeenCalledTimes(1);
        });
    });

    describe('stopCleanupTimeout', () => {
        it('should stop cleanup timeout when using default clearTimeout', () => {
            otpService.startCleanupTimeout(mockSetTimeout);
            otpService.stopCleanupTimeout();

            expect(otpService.cleanupTimeout).toBeNull();
        });
    });

});
