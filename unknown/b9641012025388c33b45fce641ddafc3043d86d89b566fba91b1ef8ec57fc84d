const { fetchGeolocation, googleMapsClient } = require('../../modules/geolocation');

jest.mock('@googlemaps/google-maps-services-js', () => {
    return {
        Client: jest.fn(() => ({
            reverseGeocode: jest.fn(),
        })),
    };
});

describe('fetchGeolocation', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return the correct address when valid data is returned', async () => {
        const mockResponse = {
            data: {
                results: [
                    { formatted_address: '123 Main St', types: ['street_address'] },
                    { formatted_address: '456 Other St', types: ['plus_code'] },
                ],
            },
            status: 200,
        };

        googleMapsClient.reverseGeocode.mockResolvedValueOnce(mockResponse);

        const result = await fetchGeolocation(40.7128, -74.0060);

        expect(result).toBe('123 Main St');
        expect(googleMapsClient.reverseGeocode).toHaveBeenCalled();
    });

    it('should return the first address if no valid address is found', async () => {
        const mockResponse = {
            data: {
                results: [
                    { formatted_address: '123 Main St', types: ['street_address'] },
                    { formatted_address: '456 Other St', types: ['plus_code'] },
                ],
            },
            status: 200,
        };

        googleMapsClient.reverseGeocode.mockResolvedValueOnce(mockResponse);

        const result = await fetchGeolocation(40.7128, -74.0060);

        expect(result).toBe('123 Main St');
    });

    it('should throw an error if no valid address is found', async () => {
        const mockResponse = {
            data: {
                results: [],
            },
            status: 200,
        };

        googleMapsClient.reverseGeocode.mockResolvedValueOnce(mockResponse);

        await expect(fetchGeolocation(40.7128, -74.0060)).rejects.toThrow('No valid address found.');
        expect(googleMapsClient.reverseGeocode).toHaveBeenCalled();
    });

    it('should throw an error when the Google Maps API fails', async () => {
        googleMapsClient.reverseGeocode.mockRejectedValueOnce(new Error('API call failed'));

        await expect(fetchGeolocation(40.7128, -74.0060)).rejects.toThrow('Google Maps Geocoding Error: API call failed');
        expect(googleMapsClient.reverseGeocode).toHaveBeenCalled();
    });

    it('should return the first available address when no non-plus_code address is found', async () => {
        const mockResponse = {
            data: {
                results: [
                    { formatted_address: '456 Other St', types: ['plus_code'] },
                ],
            },
            status: 200,
        };

        googleMapsClient.reverseGeocode.mockResolvedValueOnce(mockResponse);

        const result = await fetchGeolocation(40.7128, -74.0060);

        expect(result).toBe('456 Other St');
        expect(googleMapsClient.reverseGeocode).toHaveBeenCalled();
    });
});
