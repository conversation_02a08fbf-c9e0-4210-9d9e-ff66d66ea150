import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { config } from "dotenv";
import compression from "vite-plugin-compression";
import { visualizer } from "rollup-plugin-visualizer";

config();
// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
    optimizeDeps: {
        include: [
            '@emotion/react',
            '@emotion/styled',
            '@mui/material/Tooltip',
            '@mui/material/Slider',
        ],
    },
    plugins: [
        react(),
        compression({
            algorithm: "gzip",
            ext: ".gz",
        }),
        mode === "analyze" &&
        visualizer({
            filename: "dist/stats.html",
            open: true,
            gzipSize: true,
            brotliSize: true,
        }),
    ].filter(Boolean),
    server: {
        port: 3000,
        open: true,
        host: true,
    },
    define: {
        global: {},
        // eslint-disable-next-line no-undef
        "process.env": process.env,
    },
    build: {
        target: "esnext",
        minify: "terser",
        terserOptions: {
            compress: {
                drop_console: true,
                drop_debugger: true,
            },
        },
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ["react", "react-dom", "react-router-dom", "@mui/material", "@mui/icons-material", "@emotion/react", "@emotion/styled"],
                    charts: ["chart.js", "echarts-for-react"],
                    maps: ["@react-google-maps/api", "@googlemaps/markerclusterer"],
                    utils: ["date-fns", "dayjs", "formik", "yup"],
                },
            },
        },
        chunkSizeWarningLimit: 1000,
        sourcemap: false,
        cssCodeSplit: true,
        assetsInlineLimit: 4096,
    },
}));
