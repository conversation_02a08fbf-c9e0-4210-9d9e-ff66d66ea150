import { Modal, Grid, Typography, Button } from "@mui/material";
import ModalContainer from "../../../components/ModalContainer";
import axiosInstance from "../../../axios";

const EnableNotificationModal = ({ enableKey, setEnableKey, setEnable }) => {
    const handleClose = () => {
        setEnableKey();
    };

    const onEnableDisable = async () => {
        handleClose();
        setEnable(enableKey._id);
        const route = enableKey?.is_enabled ? "disable" : "enable";
        await axiosInstance
            .patch(`/notificationsAlerts/${enableKey._id}/${route}`, { meta: { showSnackbar: true } })
            .catch(console.error)
            .finally(() => setEnable());
    };

    return (
        <Modal open={enableKey ? true : false} onClose={handleClose}>
            <ModalContainer title={enableKey?.is_enabled ? "Disable Notification Alert" : "Enable Notification Alert"} headerPosition="center">
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: "auto" }}>
                    <Grid>
                        <Typography fontWeight={"500"} fontSize={"15px"}>
                            Are you sure you want to {enableKey?.is_enabled ? "Disable" : "Enable"} Notification Alert ?
                        </Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" className="btn-cancel" onClick={handleClose}>
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button
                                variant="contained"
                                color={enableKey?.is_enabled ? "error" : "success"}
                                onClick={onEnableDisable}
                                sx={{ textTransform: "none", padding: "10px 24px" }}
                            >
                                {enableKey?.is_enabled ? "Disable" : "Enable"}
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default EnableNotificationModal;
