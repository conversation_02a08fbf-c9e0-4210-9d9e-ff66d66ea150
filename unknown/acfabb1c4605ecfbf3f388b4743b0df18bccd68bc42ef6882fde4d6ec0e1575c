const regionsList = [
    {
        "_id": "66c4a658077e3ad6e537d418",
        "name": "US East (Ohio)",
        "value": "us-east-2",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d417",
        "name": "US East (N. Virginia)",
        "value": "us-east-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d419",
        "name": "US West (N. California)",
        "value": "us-west-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d41c",
        "name": "Asia Pacific (Osaka)",
        "value": "ap-northeast-3",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d41d",
        "name": "Asia Pacific (Seoul)",
        "value": "ap-northeast-2",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d41e",
        "name": "Asia Pacific (Singapore)",
        "value": "ap-southeast-1",
        "__v": 0,
        "default": true
    },
    {
        "_id": "66c4a658077e3ad6e537d41f",
        "name": "Asia Pacific (Sydney)",
        "value": "ap-southeast-2",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d420",
        "name": "Asia Pacific (Tokyo)",
        "value": "ap-northeast-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d421",
        "name": "Canada (Central)",
        "value": "ca-central-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d422",
        "name": "Europe (Frankfurt)",
        "value": "eu-central-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d41a",
        "name": "US West (Oregon)",
        "value": "us-west-2",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d424",
        "name": "Europe (London)",
        "value": "eu-west-2",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d41b",
        "name": "Asia Pacific (Mumbai)",
        "value": "ap-south-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d426",
        "name": "Europe (Stockholm)",
        "value": "eu-north-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d427",
        "name": "South America (São Paulo)",
        "value": "sa-east-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d428",
        "name": "Africa (Cape Town)",
        "value": "af-south-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d429",
        "name": "Asia Pacific (Hong Kong)",
        "value": "ap-east-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d42a",
        "name": "Asia Pacific (Hyderabad)",
        "value": "ap-south-2",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d42b",
        "name": "Asia Pacific (Jakarta)",
        "value": "ap-southeast-3",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d42c",
        "name": "Asia Pacific (Melbourne)",
        "value": "ap-southeast-4",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d42d",
        "name": "Canada (Calgary)",
        "value": "ca-west-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d42e",
        "name": "Europe (Milan)",
        "value": "eu-south-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d42f",
        "name": "Europe (Spain)",
        "value": "eu-south-2",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d430",
        "name": "Europe (Zurich)",
        "value": "eu-central-2",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d431",
        "name": "Middle East (Bahrain)",
        "value": "me-south-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d432",
        "name": "Middle East (UAE)",
        "value": "me-central-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d423",
        "name": "Europe (Ireland)",
        "value": "eu-west-1",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d425",
        "name": "Europe (Paris)",
        "value": "eu-west-3",
        "__v": 0,
        "default": false
    },
    {
        "_id": "66c4a658077e3ad6e537d433",
        "name": "Israel (Tel Aviv)",
        "value": "il-central-1",
        "__v": 0,
        "default": false
    }
]

module.exports = {
    regionsList
}