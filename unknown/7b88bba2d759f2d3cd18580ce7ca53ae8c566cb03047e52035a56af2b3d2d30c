import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter, Route, Routes, useNavigate, useParams } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material';
import ResetPassword from '../../src/pages/ResetPassword/ResetPassword';
import axiosInstance from '../../src/axios';

jest.mock('../../src/axios', () => ({
  post: jest.fn(),
}));

const theme = createTheme();

describe('ResetPassword Component', () => {
  const mockNavigate = jest.fn();

  beforeEach(() => {
    jest.useFakeTimers();
    mockNavigate.mockReset();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  it('should display error if passwords do not match', async () => {
    render(
      <MemoryRouter initialEntries={['/reset-password/token123']}>
        <ThemeProvider theme={theme}>
          <Routes>
            <Route path="/reset-password/:token" element={<ResetPassword />} />
          </Routes>
        </ThemeProvider>
      </MemoryRouter>
    );

    fireEvent.change(screen.getByPlaceholderText('New Password'), { target: { value: 'newpassword' } });
    fireEvent.change(screen.getByPlaceholderText('Confirm New Password'), { target: { value: 'differentpassword' } });

    fireEvent.click(screen.getByRole('button', { name: /Reset Password/i }));

    await waitFor(() => expect(screen.getByText('Passwords do not match.')).toBeInTheDocument());
  });

  it('should call the API and navigate to login page on successful password reset', async () => {
    axiosInstance.post.mockResolvedValue({});

    render(
      <MemoryRouter initialEntries={['/reset-password/token123']}>
        <ThemeProvider theme={theme}>
          <Routes>
            <Route path="/reset-password/:token" element={<ResetPassword />} />
          </Routes>
        </ThemeProvider>
      </MemoryRouter>
    );

    fireEvent.change(screen.getByPlaceholderText('New Password'), { target: { value: 'newpassword' } });
    fireEvent.change(screen.getByPlaceholderText('Confirm New Password'), { target: { value: 'newpassword' } });

    fireEvent.click(screen.getByRole('button', { name: /Reset Password/i }));

    await waitFor(() => expect(axiosInstance.post).toHaveBeenCalled());
    await waitFor(() => expect(screen.getByText('Password has been reset successfully.')).toBeInTheDocument());
    jest.advanceTimersByTime(2000);
    expect(mockNavigate).not.toHaveBeenCalledWith('/login');
  });

  it('should show an error message if the password reset fails', async () => {
    axiosInstance.post.mockRejectedValue({
      response: {
        data: {
          message: 'Password reset failed.',
        },
      },
    });

    render(
      <MemoryRouter initialEntries={['/reset-password/token123']}>
        <ThemeProvider theme={theme}>
          <Routes>
            <Route path="/reset-password/:token" element={<ResetPassword />} />
          </Routes>
        </ThemeProvider>
      </MemoryRouter>
    );

    fireEvent.change(screen.getByPlaceholderText('New Password'), { target: { value: 'newpassword' } });
    fireEvent.change(screen.getByPlaceholderText('Confirm New Password'), { target: { value: 'newpassword' } });

    fireEvent.click(screen.getByRole('button', { name: /Reset Password/i }));

    await waitFor(() => expect(axiosInstance.post).toHaveBeenCalled());
    await waitFor(() => expect(screen.getByText('Password reset failed.')).toBeInTheDocument());
  });

  it('should show an error message if the password reset fails with err.message', async () => {
    axiosInstance.post.mockRejectedValue({
      response: {
        data: {},
      },
      message: 'Password reset failed.',
    });

    render(
      <MemoryRouter initialEntries={['/reset-password/token123']}>
        <ThemeProvider theme={theme}>
          <Routes>
            <Route path="/reset-password/:token" element={<ResetPassword />} />
          </Routes>
        </ThemeProvider>
      </MemoryRouter>
    );

    fireEvent.change(screen.getByPlaceholderText('New Password'), { target: { value: 'newpassword' } });
    fireEvent.change(screen.getByPlaceholderText('Confirm New Password'), { target: { value: 'newpassword' } });

    fireEvent.click(screen.getByRole('button', { name: /Reset Password/i }));

    await waitFor(() => expect(axiosInstance.post).toHaveBeenCalled());
    await waitFor(() => expect(screen.getByText('Password reset failed.')).toBeInTheDocument());
  });

  it('should disable the reset password button when submitting', async () => {
    render(
      <MemoryRouter initialEntries={['/reset-password/token123']}>
        <ThemeProvider theme={theme}>
          <Routes>
            <Route path="/reset-password/:token" element={<ResetPassword />} />
          </Routes>
        </ThemeProvider>
      </MemoryRouter>
    );

    fireEvent.change(screen.getByPlaceholderText('New Password'), { target: { value: 'newpassword' } });
    fireEvent.change(screen.getByPlaceholderText('Confirm New Password'), { target: { value: 'newpassword' } });

    fireEvent.click(screen.getByRole('button', { name: /Reset Password/i }));

    expect(screen.getByRole('button', { name: /Reset Password/i })).toBeDisabled();
    await waitFor(() => expect(screen.getByRole('button', { name: /Reset Password/i })).not.toBeDisabled());
  });
});
