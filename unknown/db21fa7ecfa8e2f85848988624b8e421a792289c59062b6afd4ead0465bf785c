import { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { TextField, Button, Typography, Grid, CircularProgress } from "@mui/material";
import axiosInstance from "../../axios";

const ResetPassword = () => {
    const { token } = useParams();
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [message, setMessage] = useState("");
    const [messageColor, setMessageColor] = useState("error");
    const [submitting, setSubmitting] = useState(false);
    const navigate = useNavigate();

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSubmitting(true);
        setMessage("");
        if (password !== confirmPassword) {
            setMessageColor("error");
            setMessage("Passwords do not match.");
            return;
        }
        try {
            await axiosInstance.post(`/users/reset-password/${token}`, { password }, { meta: { showSnackbar: false } });
            setMessageColor("success.main");
            setMessage("Password has been reset successfully.");
            setTimeout(() => {
                navigate("/login");
            }, 2000);
            setSubmitting(false);
        } catch (err) {
            setMessageColor("error");
            setMessage(err.response.data?.message || err.message);
            setSubmitting(false);
        }
    };

    return (
        <Grid container flexDirection={"column"} maxWidth={591} gap={4}>
            <Grid container flexDirection={"column"} color={"#FFFFFF"}>
                <Grid>
                    <Typography variant="h3" fontWeight={"bold"}>{`Reset Password`}</Typography>
                </Grid>
            </Grid>
            <Grid container flexDirection={"column"} component={"form"} onSubmit={handleSubmit} gap={4}>
                <Grid>
                    <TextField
                        className="input-login"
                        type="password"
                        placeholder="New Password"
                        variant="outlined"
                        fullWidth
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                    />
                </Grid>
                <Grid>
                    <TextField
                        className="input-login"
                        type="password"
                        placeholder="Confirm New Password"
                        variant="outlined"
                        fullWidth
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        required
                    />
                </Grid>
                <Grid display={message ? "block" : "none"}>
                    <Typography color={messageColor}>{message}</Typography>
                </Grid>
                <Grid>
                    <Button
                        className="btn-login"
                        type="submit"
                        variant="contained"
                        color="primary"
                        fullWidth
                        disabled={submitting}
                        endIcon={submitting && <CircularProgress />}
                    >
                        Reset Password
                    </Button>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default ResetPassword;
