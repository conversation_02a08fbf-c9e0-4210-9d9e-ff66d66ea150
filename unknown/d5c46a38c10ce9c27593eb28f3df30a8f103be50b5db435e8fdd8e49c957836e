const { validationResult } = require('express-validator');

/**
 * Middleware for data validation.
 *
 * @param {import('express').Request} req - The Express request object.
 * @param {import('express').Response} res - The Express response object.
 * @param {import('express').NextFunction} next - The next middleware function.
 * @returns {Promise<void>}
 */
const validateData = async (rules, req, res, next) => {
    // Run validation rules and gather validation errors
    return Promise.all(rules.map(rule => rule.run(req))).then(() => {
        const errors = validationResult(req);

        // If there are validation errors, respond with a 400 Bad Request status and error messages
        if (errors.isEmpty()) {
            next();
        } else {
            console.log(errors.array())
            return res.status(400).json({ message: errors.array()[0].msg });
        }
    });
};

module.exports = {
    validateData,
};