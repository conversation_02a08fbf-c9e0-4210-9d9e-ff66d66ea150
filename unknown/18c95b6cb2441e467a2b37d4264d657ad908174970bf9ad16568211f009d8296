import { Modal, Grid, Typography, Button } from "@mui/material";
import apiKeyController from "../../../controllers/ApiKey.controller";
import ModalContainer from "../../../components/ModalContainer";

const DeleteKeyModal = ({ deleteKey, setDeleteKey, setDeleting, fetchKeys }) => {
    const handleClose = () => {
        setDeleteKey();
    };

    const onDelete = async () => {
        try {
            setDeleting(deleteKey._id);
            await apiKeyController.delete({ id: deleteKey._id });
            fetchKeys();
            handleClose();
        } catch (error) {
            console.error("Error deleting API key:", error);
        } finally {
            setDeleting(null);
        }
    };

    return (
        <Modal open={deleteKey ? true : false} onClose={handleClose}>
            <ModalContainer title={"Delete API Key"} headerPosition="center">
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: "auto" }}>
                    <Grid>
                        <Typography fontWeight={"500"} fontSize={"15px"}>
                            Are you sure you want to delete API key #{deleteKey?.serial}?
                        </Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" className="btn-cancel" onClick={handleClose}>
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" color="error" onClick={onDelete} sx={{ textTransform: "none", padding: "10px 24px" }}>
                                Delete
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default DeleteKeyModal;
