import { useUser } from '../../src/hooks/UserHook';
import { useContext } from 'react';

jest.mock('../../src/contexts/UserContext', () => ({
    UserContext: 'UserContext',
}));
jest.mock('react', () => ({
    useContext: jest.fn(),
}));

describe('useUser Hook', () => {
    it('should return user context', () => {
        const mockContextValue = {
            user: { name: '<PERSON>' },
            userFetched: true,
            fetchUser: jest.fn(),
            sessionExpired: false,
            setSessionExpired: jest.fn(),
            login: jest.fn(),
            logout: jest.fn(),
        };
        
        useContext.mockReturnValue(mockContextValue);
        
        const result = useUser();
        
        expect(result).toBe(mockContextValue);
    });

    it('should throw error when used outside of UserProvider', () => {
        useContext.mockReturnValue(undefined);
        
        expect(() => useUser()).toThrowError('useUser must be used within a UserProvider');
    });
});
