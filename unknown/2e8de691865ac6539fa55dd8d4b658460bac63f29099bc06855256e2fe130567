import React from 'react';
import { render, screen } from '@testing-library/react';
import HeatmapChart from '../../../src/components/Charts/HeatmapChart';
// import Chart from 'react-apexcharts';

jest.mock('react-apexcharts', () => ({
    __esModule: true,
    default: () => <div data-testid="heatmap-chart" />,
}));

describe('HeatmapChart Component', () => {
    const mockChartSeries = [
        {
            name: 'Metric 1',
            data: [
                { x: 'Jan', y: 30 },
                { x: 'Feb', y: 40 },
                { x: 'Mar', y: 35 },
                { x: 'Apr', y: 0 },
            ],
        },
        {
            name: 'Metric 2',
            data: [
                { x: 'Jan', y: 20 },
                { x: 'Feb', y: 60 },
                { x: 'Mar', y: 70 },
                { x: 'Apr', y: 30 },
            ],
        },
        {
            name: 'Metric 3',
            data: [
                { x: 'Jan', y: 50 },
                { x: 'Feb', y: 30 },
                { x: 'Mar', y: 20 },
                { x: 'Apr', y: 60 },
            ],
        },
    ];

    it('renders the HeatmapChart component', () => {
        render(<HeatmapChart chartSeries={mockChartSeries} />);
        
        const chartElement = screen.getByTestId('heatmap-chart');
        expect(chartElement).toBeInTheDocument();
    });

    it('displays the correct chart data', () => {
        render(<HeatmapChart chartSeries={mockChartSeries} />);
        
        const chartElement = screen.getByTestId('heatmap-chart');
        expect(chartElement).toBeInTheDocument();
    });
});
