import { useEffect, useRef, useState } from "react";
import { GoogleMap } from "@react-google-maps/api";
import { Skeleton, Typography } from "@mui/material";
import dayjs from "dayjs";
import { useApp } from "../hooks/AppHook";
import { displayCoordinates, defaultValues, userValues } from "../utils";
import gps_socket from "../gps_socket";
import { useUser } from "../hooks/UserHook.jsx";

const InsetMap = ({ vessel = {}, newCoordinate = {}, initialZoom }) => {
    const { google, timezone } = useApp();
    const { user } = useUser();
    const [map, setMap] = useState(null);
    const [coordinate, setCoordinate] = useState();
    const [zoom, setZoom] = useState(initialZoom || defaultValues.zoom);
    const [center, setCenter] = useState();
    const markerRef = useRef(null);
    const infoWindowRef = useRef(null);
    const centerSetRef = useRef(false);
    const coordinateRef = useRef(coordinate);

    const onLoad = (mapInstance) => {
        setMap(mapInstance);
    };

    const onUnmount = () => {
        google.maps.event.clearListeners(markerRef.current, "mouseover");
        google.maps.event.clearListeners(markerRef.current, "mouseout");
        setMap(null);
    };

    useEffect(() => {
        if (!vessel.id && !newCoordinate) return;
        setCoordinate();
        setCenter();
        centerSetRef.current = false;
        setCoordinate(newCoordinate);

        const updateCoordinate = (data) => {
            if (!data.latitude) return;
            const newCoordinates = { ...data, lat: data.latitude, lng: data.longitude };
            setCoordinate(newCoordinates);

            if (!centerSetRef.current && newCoordinates) {
                setCenter(newCoordinates);
                centerSetRef.current = true;
            }
        };

        gps_socket.on(vessel.id + "/gps", updateCoordinate);
        return () => gps_socket.off(vessel.id + "/gps", updateCoordinate);
    }, [vessel]);

    const setMarker = (coordinate) => {
        if (!coordinate || !coordinate.lat || !coordinate.lng || !infoWindowRef.current) return;
        const content = `
            <div style="align-items: center; padding: 10px; font-size: 11px;">
                <strong>Location:</strong> ${displayCoordinates([coordinate.lng, coordinate.lat], !!user?.use_MGRS)}<br/>
                <strong>Time:</strong> ${dayjs(coordinate.timestamp).tz(timezone).format(userValues.dateTimeFormat(user))}<br/>
                <style>
                    .gm-style-iw-chr {
                        display: none !important; /* Hide the close button */;
                    }
                    .gm-style-iw-tc {
                        display: none !important; /* Hide the close button */;
                    }
                    .gm-style .gm-style-iw-c  {
                        background-color: #343B44 !important;
                        outline: none;
                        padding: 0;
                    }
                    .gm-style .gm-style-iw-d {
                        overflow:auto !important;
                    }
                    p {
                        margin: 0;
                        color:white
                    }
                    strong {
                        color:white
                    }
                </style>
            </div>
        `;

        if (!markerRef.current) {
            markerRef.current = new google.maps.Marker({
                position: coordinate,
                map: map,
                icon: {
                    path: defaultValues.icons.location,
                    fillColor: "#FF0000",
                    fillOpacity: 1,
                    strokeColor: "#000000",
                    strokeOpacity: 1,
                    scale: 1,
                },
            });

            infoWindowRef.current.setContent(content);

            const handleMouseOver = () => infoWindowRef.current.open(map, markerRef.current);
            const handleMouseOut = () => infoWindowRef.current.close();

            markerRef.current.addListener("mouseover", handleMouseOver);
            markerRef.current.addListener("mouseout", handleMouseOut);
        } else {
            markerRef.current.setPosition(coordinate);
            markerRef.current.setMap(map);
            infoWindowRef.current.setContent(content);
        }
    };

    useEffect(() => {
        if (google) {
            infoWindowRef.current = new google.maps.InfoWindow({ disableAutoPan: true });
        }
    }, [google]);

    useEffect(() => {
        if (!map || !coordinate) return;
        if (!centerSetRef.current) {
            setCenter(coordinate);
        }
        coordinateRef.current = coordinate;

        setMarker(coordinate);
    }, [map, coordinate]);

    useEffect(() => {
        const intervalId = setInterval(
            () => {
                if (!coordinateRef.current) return;
                setCenter(coordinateRef.current);
            },
            5 * 60 * 1000,
        ); // 5 minutes

        return () => clearInterval(intervalId);
    }, []);

    return (
        <div data-testid="inset-map-container" style={{ color: "#FFFFFF", width: "100%", height: "100%" }}>
            {!vessel?.id || !coordinate ? (
                <Skeleton data-testid="loading-skeleton" animation="wave" variant="rectangular" height={"100%"} />
            ) : !coordinate.latitude ? (
                <Typography data-testid="no-coordinates" color={"#FFFFFF"}>
                    No coordinates found for this vessel
                </Typography>
            ) : (
                google && (
                    <GoogleMap
                        data-testid="google-map-component"
                        mapContainerStyle={{
                            width: "100%",
                            height: "100%",
                        }}
                        center={center}
                        zoom={zoom}
                        onZoomChanged={() => map && setZoom && setZoom(map?.getZoom())}
                        onLoad={onLoad}
                        onUnmount={onUnmount}
                        options={{
                            disableDefaultUI: true,
                            zoomControl: false,
                            streetViewControl: false,
                            mapTypeControl: false,
                            fullscreenControl: false,
                        }}
                    />
                )
            )}
        </div>
    );
};

export default InsetMap;
