export default {
    "testEnvironment": "jsdom",
    "moduleNameMapper": {
        "^.+\\.svg$": "jest-svg-transformer",
        "^.+\\.(css|less|scss)$": "identity-obj-proxy"
    },
    "setupFilesAfterEnv": [
        "<rootDir>/setupTests.js"
    ],
    "coverageReporters": ['html', 'text'],
    "testMatch": [
        "<rootDir>/tests/**/*.(test|spec).(js|jsx|ts|tsx)",
    ],
    "collectCoverageFrom": [
        "src/**/*.{js,jsx,ts,tsx}",
        "!src/**/*.d.ts",
    ],
    "transform": {
        '^.+\\.jsx?$': 'babel-jest',
        '^.+\\.tsx?$': 'babel-jest',
    },
};
