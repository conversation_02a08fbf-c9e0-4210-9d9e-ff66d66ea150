const redis = require("redis");

const redisClient = redis.createClient({
    url: `redis://${process.env.REDIS_HOST}:${Number(process.env.REDIS_PORT || 6379)}`,
    return_buffers: true,
    connect_timeout: 30000, // 30 seconds
});

redisClient.connect().catch((err) => {
    console.error("Error connecting to Redis:", err);
});

// Set memory limit and eviction policy dynamically
const setMemoryLimit = async () => {
    try {
        await redisClient.sendCommand(["CONFIG", "SET", "maxmemory", "2gb"]);
        console.log("Maxmemory set to 2gb");

        // INFO:
        // noeviction: Returns an error when the memory limit is reached (default).
        // allkeys-lru: Evicts the least recently used keys.
        // volatile-lru: Evicts the least recently used keys with an expiration set.
        // allkeys-random: Randomly evicts keys.
        // volatile-random: Randomly evicts keys with an expiration set.

        await redisClient.sendCommand(["CONFIG", "SET", "maxmemory-policy", "allkeys-lru"]);
        console.log("Maxmemory policy set to allkeys-lru");
    } catch (err) {
        console.error("Error setting configuration:", err);
    }
};

redisClient.on("error", (err) => {
    console.error("Redis error:", err);
});

redisClient.on("connect", () => {
    console.log("Connected to Redis");
    // Call the function to set the configuration
    setMemoryLimit();
});

module.exports = redisClient;
