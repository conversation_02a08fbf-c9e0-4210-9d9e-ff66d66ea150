const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Role = require('../../models/Role');
const { default: mongoose } = require('mongoose');
const crypto = require('crypto');
const { sendEmail } = require('../../modules/email');
const Region = require('../../models/Region');
const { regionsList } = require('../data/Regions');
const { permissionsList } = require('../data/Permissions');
const Permission = require('../../models/Permission');
const { streamsList } = require('../data/Kinesis');
const { s3 } = require('../../modules/awsS3');
const { sessionLogsList } = require('../data/Logs');
const SessionLog = require('../../models/SessionLog');
const request = require("supertest");
const app = require('../../server');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('Logs API', () => {

    describe('GET /api/logs/sessions', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/logs/sessions');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if user does not have the required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);

                    const res = await request(app)
                        .get('/api/logs/sessions')
                        .set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 200 and fetch session logs if the user is authorized', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    SessionLog.aggregate.mockResolvedValue(sessionLogsList);

                    const res = await request(app)
                        .get('/api/logs/sessions')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                    ['_id', 'socket_id', 'type', 'device', 'browser', 'user_id', 'timestamp', 'environment', 'user'].forEach(prop => {
                        expect(res.body[0]).toHaveProperty(prop);
                    });
                });

                it('should return 500 if an error occurs while fetching session logs', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    SessionLog.aggregate.mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .get('/api/logs/sessions')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('GET /api/logs/sessions/:id', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validObjectId = mongoose.Types.ObjectId();

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get(`/api/logs/sessions/${validObjectId}`);
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);

                    const res = await request(app)
                        .get(`/api/logs/sessions/${validObjectId}`)
                        .set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 400 if the provided id is invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    const invalidId = '12345'; // Invalid ObjectId format

                    const res = await request(app)
                        .get(`/api/logs/sessions/${invalidId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 404 if no session log is found with the provided id', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    SessionLog.aggregate.mockResolvedValue([]);

                    const res = await request(app)
                        .get(`/api/logs/sessions/${validObjectId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                });

                it('should return 200 and fetch the session log if the user is authorized', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    SessionLog.aggregate.mockResolvedValue([sessionLogsList[0]]);

                    const res = await request(app)
                        .get(`/api/logs/sessions/${validObjectId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    ['_id', 'socket_id', 'type', 'device', 'browser', 'user_id', 'timestamp', 'environment', 'user'].forEach(prop => {
                        expect(res.body).toHaveProperty(prop);
                    });
                });

                it('should return 500 if an error occurs while fetching the session log', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    SessionLog.aggregate.mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .get(`/api/logs/sessions/${validObjectId}`)
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

});
