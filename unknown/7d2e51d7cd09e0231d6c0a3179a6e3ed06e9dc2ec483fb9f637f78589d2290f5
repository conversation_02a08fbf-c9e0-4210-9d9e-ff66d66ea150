// Import required libraries
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement, PointElement, LineElement } from 'chart.js';
import theme from '../../theme';

// Register components in Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, PointElement, LineElement, Title, Tooltip, Legend);

export default function LineChart({ data, options }) {
    const defaultOptions = {
        responsive: true,
        scales: {
            x: {
                grid: {
                    color: '#282C39',
                },
                ticks: {
                    color: '#FFFFFF',
                },
            },
            y: {
                grid: {
                    color: '#282C39',
                },
                ticks: {
                    color: '#FFFFFF',
                },
            },
        },
        plugins: {
            legend: {
                labels: {
                    color: '#FFFFFF',
                    usePointStyle: true,
                    pointStyle: 'circle',
                },
            },
        },
        elements: {
            line: {
                borderColor: theme.palette.custom.mainBlue,
                borderWidth: 1,
            },
            point: {
                radius: 4,
            },
        },
    };

    const mergedOptions = { ...defaultOptions, ...options };

    return <Line data={data} options={mergedOptions} />;
}