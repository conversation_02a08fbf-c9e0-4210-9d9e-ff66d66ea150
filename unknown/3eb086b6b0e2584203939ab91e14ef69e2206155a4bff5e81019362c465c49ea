import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import StatisticsPastDaylights from '../../src/pages/Dashboard/Statistics/StatisticsPastDaylights';
import axiosInstance from '../../src/axios';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

jest.mock('../../src/axios');
jest.mock('../../src/components/KPICard', () => "KPICard");
jest.mock('../../src/components/Charts/BarChart', () => "BarChart");

describe('StatisticsPastDaylights', () => {
    let allStats;
    let vesselsInfo;

    beforeEach(() => {
        allStats = [
            {
                _id: '1',
                fromTimestamp: 1625097600000,
                toTimestamp: 1625184000000,
                stats: {
                    totalVesselsDetected: 100,
                    totalSmartmastsAtSea: 50,
                    totalSmartmastsOnline: 45,
                    totalVesselsDetectedbySensors: { unit1: 30, unit2: 70 },
                    totalVesselsSuperCategorized: { cat1: 40, cat2: 60 },
                    totalVesselsSubCategorized: { sub1: 20, sub2: 80 },
                    experimental: {
                        totalVesselsSuperCategorizedWithBoundingBoxOccupancy5: { cat1: 10, cat2: 20 },
                    },
                },
            },
            {
                _id: '2',
                fromTimestamp: 1625184000000,
                toTimestamp: 1625270400000,
                stats: {
                    totalVesselsDetected: 200,
                    totalSmartmastsAtSea: 60,
                    totalSmartmastsOnline: 55,
                    totalVesselsDetectedbySensors: { unit1: 80, unit2: 120 },
                    totalVesselsSuperCategorized: { cat1: 90, cat2: 110 },
                    totalVesselsSubCategorized: { sub1: 40, sub2: 160 },
                },
            },
        ];

        vesselsInfo = [
            { unit_id: 'unit1', name: 'Vessel 1' },
            { unit_id: 'unit2', name: 'Vessel 2' },
        ];

        axiosInstance.get.mockImplementation((url) => {
            if (url === '/vessels/info') {
                return Promise.resolve({ data: vesselsInfo });
            }
            if (url === '/statistics') {
                return Promise.resolve({ data: allStats });
            }
        });
    });

    it('should handle stats change with "increment"', async () => {
        render(<StatisticsPastDaylights />);
        await waitFor(() => {
            expect(
                screen.getByText((content, element) =>
                    content.includes('Data for the time period from:')
                )
            ).toBeInTheDocument();
        });

        fireEvent.click(screen.getByTestId('ArrowForwardIosIcon'));
        fireEvent.click(screen.getByTestId('ArrowBackIosIcon'));
    });

    it('should not call map when stats are missing', async () => {
        axiosInstance.get.mockImplementation((url) => {
            if (url === '/statistics') {
                return Promise.resolve({
                    data: [{
                        _id: '3',
                        fromTimestamp: 1625270400000,
                        toTimestamp: 1625356800000,
                        stats: {
                            totalVesselsDetected: 0,
                            totalSmartmastsAtSea: 0,
                            totalSmartmastsOnline: 0,
                            totalVesselsDetectedbySensors: {},
                            totalVesselsSuperCategorized: {},
                            totalVesselsSubCategorized: {},
                        },
                    }]
                });
            }
            return Promise.resolve({ data: vesselsInfo });
        });

        render(<StatisticsPastDaylights />);
        await waitFor(() => expect(screen.queryByText('Data for the time period')).not.toBeInTheDocument());
    });

    it('should handle transformKeys with missing vessel info', async () => {
        const modifiedStats = {
            ...allStats[0],
            stats: {
                ...allStats[0].stats,
                totalVesselsDetectedbySensors: {
                    unit1: 30,
                    unknown_vessel: 50,
                    unit2: 70
                }
            }
        };

        axiosInstance.get.mockImplementation((url) => {
            if (url === '/statistics') {
                return Promise.resolve({ data: [modifiedStats] });
            }
            return Promise.resolve({ data: vesselsInfo });
        });

        render(<StatisticsPastDaylights />);
        await waitFor(() => {
            expect(screen.getByText((content) =>
                content.includes('Data for the time period from:')
            )).toBeInTheDocument();
        });
    });
});
