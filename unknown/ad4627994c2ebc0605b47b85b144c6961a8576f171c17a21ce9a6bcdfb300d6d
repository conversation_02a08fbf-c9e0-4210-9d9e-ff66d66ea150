import { render, screen, fireEvent, waitFor, renderHook, act } from '@testing-library/react';
import { UserProvider } from '../../src/providers/UserProvider';
import { useUser } from '../../src/hooks/UserHook';
import axiosInstance from '../../src/axios';
import { getSocket, reconnectSocket, disconnectSocket } from '../../src/socket';
import { jwtDecode } from 'jwt-decode';

jest.mock('../../src/axios', () => ({
    get: jest.fn(),
}));

jest.mock('../../src/socket', () => ({
    getSocket: jest.fn().mockReturnValue({
        on: jest.fn(),
        off: jest.fn()
    }),
    reconnectSocket: jest.fn(),
    disconnectSocket: jest.fn(),
}));

jest.mock('jwt-decode');

const TestingComponent = () => {
    const { user, userFetched, login, logout, fetchUser } = useUser();
    fetchUser().then(() => { }).catch(() => { });
    return (
        <>
            <p data-testid="user-name">{user?.name}</p>
            <p data-testid="user-fetched">{userFetched ? 'true' : 'false'}</p>
            <button data-testid="logout" onClick={() => logout(() => { })} />
        </>
    );
};

describe('UserProvider', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        localStorage.clear();
    });

    it('should handle successful login', async () => {
        const mockToken = 'test-token';
        const mockUser = { _id: '123', name: 'Test User' };

        jwtDecode.mockReturnValueOnce({ exp: Math.floor(Date.now() / 1000) + 3600 });
        axiosInstance.get
            .mockResolvedValueOnce({ data: { jwt_token: mockToken } })
            .mockResolvedValueOnce({ data: mockUser });

        const { result } = renderHook(() => useUser(), {
            wrapper: UserProvider
        });

        await result.current.login({ username: 'test', password: 'test' });

        expect(localStorage.getItem('jwt_token')).toBe(mockToken);
        expect(reconnectSocket).toHaveBeenCalled();
        expect(axiosInstance.get).toHaveBeenCalledWith(
            '/users/auth',
            expect.any(Object)
        );
    });

    it('should handle login failure', async () => {
        const error = new Error('Invalid credentials');
        jwtDecode.mockReturnValueOnce({ exp: Math.floor(Date.now() / 1000) + 3600 });
        axiosInstance.get.mockRejectedValueOnce(error);

        const { result } = renderHook(() => useUser(), {
            wrapper: UserProvider
        });

        await expect(result.current.login({
            username: 'test',
            password: 'wrong'
        })).rejects.toEqual(error);
    });

    it('should handle fetchUser with missing token', async () => {
        const { result } = renderHook(() => useUser(), {
            wrapper: UserProvider
        });

        await expect(result.current.fetchUser()).rejects.toEqual('No token found');
    });

    it('should handle fetchUser API error', async () => {
        localStorage.setItem('jwt_token', 'valid-token');
        jwtDecode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
        const error = new Error('API Error');
        axiosInstance.get.mockRejectedValueOnce(error);

        const { result } = renderHook(() => useUser(), {
            wrapper: UserProvider
        });

        await expect(result.current.fetchUser()).rejects.toEqual(error);
        expect(localStorage.getItem('jwt_token')).toBeNull();
    });

    it('should handle user change event', async () => {
        const mockUser = { _id: '123', name: 'Initial' };
        const updatedUser = { _id: '123', name: 'Updated' };
        let userChangeCallback;

        localStorage.setItem('jwt_token', 'valid-token');
        jwtDecode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });

        const socketMock = {
            on: (event, cb) => {
                if (event === 'users/changed') userChangeCallback = cb;
            },
            off: jest.fn()
        };
        getSocket.mockReturnValue(socketMock);

        axiosInstance.get
            .mockImplementation((url) => {
                if (url === '/users/user') {
                    return Promise.resolve({
                        data: {
                            ...mockUser,
                            hasPermissions: jest.fn()
                        }
                    });
                }
                return Promise.resolve({ data: mockUser });
            });

        const { result } = renderHook(() => useUser(), {
            wrapper: UserProvider
        });

        await act(async () => {
            await result.current.fetchUser();
        });

        await waitFor(() => {
            expect(result.current.user).toEqual(expect.objectContaining(mockUser));
            expect(typeof result.current.user.hasPermissions).toBe('function');
        });

        axiosInstance.get.mockImplementationOnce(() =>
            Promise.resolve({
                data: {
                    ...updatedUser,
                    hasPermissions: jest.fn()
                }
            })
        );

        await act(async () => {
            userChangeCallback({ _id: '124' });
            userChangeCallback({ _id: '123' });
        });

        await waitFor(() => {
            expect(result.current.user).toEqual(expect.objectContaining(updatedUser));
            expect(typeof result.current.user.hasPermissions).toBe('function');
            expect(axiosInstance.get).toHaveBeenCalledTimes(2);
        });
    });

    it('should handle role change event', async () => {
        const mockUser = { _id: '123', role_id: 'role1' };
        const updatedUser = { _id: '123', role_id: 'role1', name: 'Updated' };
        let socketCallback;

        localStorage.setItem('jwt_token', 'valid-token');
        jwtDecode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });

        const socketMock = {
            on: (event, cb) => { if (event === 'roles/changed') socketCallback = cb; },
            off: jest.fn()
        };

        getSocket.mockReturnValue(socketMock);
        axiosInstance.get
            .mockImplementation((url) => {
                if (url === '/users/user') {
                    return Promise.resolve({
                        data: {
                            ...mockUser,
                            hasPermissions: jest.fn()
                        }
                    });
                }
                return Promise.resolve({ data: mockUser });
            });

        const { result } = renderHook(() => useUser(), {
            wrapper: UserProvider
        });

        await act(async () => {
            await result.current.fetchUser();
        });

        await waitFor(() => {
            expect(result.current.user).toEqual(expect.objectContaining(mockUser));
            expect(typeof result.current.user.hasPermissions).toBe('function');
        });

        await act(async () => {
            socketCallback({ role_id: 'role1' });
            socketCallback({ role_id: 'role2' });
        });

        await waitFor(() => {
            expect(axiosInstance.get).toHaveBeenCalledTimes(2);
        });
    });

    it('should handle expired token correctly', async () => {
        const mockToken = 'expired-token';
        localStorage.setItem('jwt_token', mockToken);
        jwtDecode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) - 3600 });
        axiosInstance.get.mockResolvedValueOnce({ data: {} });
        getSocket.mockReturnValue({ on: jest.fn(), off: jest.fn() });

        render(
            <UserProvider>
                <TestingComponent />
            </UserProvider>
        );

        await waitFor(() => expect(screen.getByTestId('user-fetched').textContent).toBe('true'));
    });

    it('should fetch user and set userFetched to true on successful login', async () => {
        const mockUser = { _id: '123', name: 'John Doe', permissions: [] };
        const mockToken = 'valid-token';
        localStorage.setItem('jwt_token', mockToken);
        axiosInstance.get.mockResolvedValueOnce({ data: mockUser });
        jwtDecode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
        getSocket.mockReturnValue({ on: jest.fn(), off: jest.fn() });

        render(
            <UserProvider>
                <TestingComponent />
            </UserProvider>
        );

        await waitFor(() => expect(screen.getByTestId('user-fetched').textContent).toBe('true'));
        expect(screen.getByTestId('user-name').textContent).toBe('John Doe');
    });

    it('should logout user correctly', async () => {
        const mockUser = { _id: '123', name: 'John Doe', permissions: [] };
        const mockToken = 'valid-token';
        localStorage.setItem('jwt_token', mockToken);
        jest.spyOn(global, 'setTimeout').mockImplementationOnce((cb, a) => cb())
        axiosInstance.get.mockResolvedValueOnce({ data: mockUser });
        jwtDecode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });

        render(
            <UserProvider>
                <TestingComponent />
            </UserProvider>
        );

        const logoutButton = screen.getByTestId('logout');
        fireEvent.click(logoutButton);

        await waitFor(() => expect(localStorage.getItem('jwt_token')).toBeNull());
        expect(screen.getByTestId('user-fetched').textContent).toBe('true');
    });

    it('should call socket listeners on mount and clean up on unmount', () => {
        const socketMock = { on: jest.fn(), off: jest.fn() };
        getSocket.mockReturnValue(socketMock);

        const { unmount } = render(
            <UserProvider>
                <TestingComponent />
            </UserProvider>
        );

        expect(socketMock.on).toHaveBeenCalledWith('users/changed', expect.any(Function));
        expect(socketMock.on).toHaveBeenCalledWith('roles/changed', expect.any(Function));

        unmount();

        expect(socketMock.off).toHaveBeenCalledWith('users/changed', expect.any(Function));
        expect(socketMock.off).toHaveBeenCalledWith('roles/changed', expect.any(Function));
    });

    it('should trigger socket event listeners when user or role changes', () => {
        const socketMock = { on: jest.fn(), off: jest.fn() };
        getSocket.mockReturnValue(socketMock);

        const mockUser = { _id: '123', role_id: '456' };
        axiosInstance.get.mockResolvedValueOnce({ data: mockUser });
        jwtDecode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });

        render(
            <UserProvider>
                <TestingComponent />
            </UserProvider>
        );

        expect(socketMock.on).toHaveBeenCalledWith('users/changed', expect.any(Function));
        expect(socketMock.on).toHaveBeenCalledWith('roles/changed', expect.any(Function));
    });

    it('should check permissions with AND operator correctly', async () => {
        const mockUser = {
            _id: '123',
            permissions: [
                { permission_id: 'p1' },
                { permission_id: 'p2' },
                { permission_id: 'p3' }
            ]
        };

        localStorage.setItem('jwt_token', 'valid-token');
        jwtDecode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
        axiosInstance.get.mockResolvedValueOnce({ data: mockUser });

        const { result } = renderHook(() => useUser(), {
            wrapper: UserProvider
        });

        await act(async () => {
            await result.current.fetchUser();
        });

        result.current.user['permissions'] = mockUser.permissions;

        expect(result.current.user.hasPermissions(['p1', 'p2'])).toBe(true);
        expect(result.current.user.hasPermissions(['p1', 'p2', 'p3'])).toBe(true);
        expect(result.current.user.hasPermissions(['p1', 'p4'])).toBe(false);
        expect(result.current.user.hasPermissions(['p4', 'p5'])).toBe(false);
    });

    it('should check permissions with OR operator correctly', async () => {
        const mockUser = {
            _id: '123',
            permissions: [
                { permission_id: 'p1' },
                { permission_id: 'p2' }
            ]
        };

        localStorage.setItem('jwt_token', 'valid-token');
        jwtDecode.mockReturnValue({ exp: Math.floor(Date.now() / 1000) + 3600 });
        axiosInstance.get.mockResolvedValueOnce({ data: mockUser });

        const { result } = renderHook(() => useUser(), {
            wrapper: UserProvider
        });

        await act(async () => {
            await result.current.fetchUser();
        });

        result.current.user['permissions'] = mockUser.permissions;

        expect(result.current.user.hasPermissions(['p1', 'p3'], 'OR')).toBe(true);
        expect(result.current.user.hasPermissions(['p2', 'p4'], 'OR')).toBe(true);
        expect(result.current.user.hasPermissions(['p3', 'p4'], 'OR')).toBe(false);
    });

});
