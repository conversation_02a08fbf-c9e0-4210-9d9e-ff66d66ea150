const ApiEndpoint = require("../models/ApiEndpoint");
const ApiKey = require("../models/ApiKey");
const Vessel = require("../models/Vessel");
const { isValidObjectId } = require("mongoose");

class ApiKeyService {
    async fetchAll() {
        const apiKeys = await ApiKey.aggregate([
            {
                $match: { is_deleted: false },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "created_by",
                    foreignField: "_id",
                    as: "created_by",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                username: 1,
                            },
                        },
                    ],
                },
            },
            {
                $addFields: {
                    created_by: { $arrayElemAt: ["$created_by", 0] },
                },
            },
        ]);
        return apiKeys;
    }

    async findById({ id }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const apiKey = await Api<PERSON>ey.findOne({
            _id: id,
            is_deleted: false,
        });

        if (!apiKey) return null;
        return apiKey;
    }

    async create({ description, email, created_by }) {
        const apiKey = await Api<PERSON>ey.create({ description, email, created_by });
        return await this.findById({ id: apiKey._id });
    }

    async update({ id, description, email }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const data = { description, email };
        Object.keys(data).forEach((key) => {
            if (data[key] === undefined) delete data[key];
        });

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, data, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id });
    }

    async updateAllowedEndpoints({ id, allowed_endpoints }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const apiEndpoints = await ApiEndpoint.find();
        if (allowed_endpoints.some((e_id) => !apiEndpoints.find((e) => e.endpoint_id === e_id))) throw new Error("Invalid endpoint provided");

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { allowed_endpoints }, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id });
    }

    async updateAllowedVessels({ id, allowed_vessels }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const inActiveVessels = await Vessel.find({ _id: { $in: allowed_vessels }, is_active: false });
        if (inActiveVessels.length > 0) {
            throw new Error("Cannot assign inactive vessels to API key");
        }

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { allowed_vessels }, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id });
    }

    async updateRevocationStatus({ id, is_revoked }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { is_revoked }, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id });
    }

    async delete({ id }) {
        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { is_deleted: true }, { new: true });
        return apiKey !== null;
    }
}

const apiKeyService = new ApiKeyService();

module.exports = apiKeyService;
