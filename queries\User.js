const { default: mongoose } = require("mongoose");
const User = require("../models/User");

function getUser({ user_id, includeUnprojected = false }) {
    return new Promise((resolve, reject) => {
        if (!user_id) return reject({ message: "getUser failed. No id provided" });
        User.aggregate(
            [
                {
                    $match: {
                        _id: mongoose.Types.ObjectId(user_id),
                    },
                },
                {
                    $lookup: {
                        from: "roles",
                        localField: "role_id",
                        foreignField: "role_id",
                        as: "role",
                    },
                },
                {
                    $unwind: {
                        path: "$role",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "permissions",
                        let: { denied_permissions: "$role.denied_permissions" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $not: { $in: ["$permission_id", "$$denied_permissions"] },
                                    },
                                },
                            },
                        ],
                        as: "permissions",
                    },
                },
                {
                    $lookup: {
                        from: "organizations",
                        localField: "organization_id",
                        foreignField: "_id",
                        as: "organization",
                    },
                },
                {
                    $unwind: {
                        path: "$organization",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                includeUnprojected
                    ? undefined
                    : {
                          $project: {
                              jwt_tokens: 0,
                              password: 0,
                              reset_password_expire: 0,
                              reset_password_token: 0,
                          },
                      },
            ].filter((v) => v),
        )
            .then((result) => {
                resolve(result[0]);
            })
            .catch(reject);
    });
}

module.exports = {
    getUser,
};
