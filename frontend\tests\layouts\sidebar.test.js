import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useUser } from '../../src/hooks/UserHook';
import { useApp } from '../../src/hooks/AppHook';
import { getSocket } from '../../src/socket';
import { permissions } from '../../src/utils';
import Sidebar from '../../src/layouts/Sidebar';
import { ThemeProvider } from '@mui/material';
import theme from '../../src/theme';

jest.mock('react-router-dom', () => ({
    useLocation: jest.fn(),
    useNavigate: jest.fn()
}));

jest.mock('../../src/hooks/UserHook');
jest.mock('../../src/hooks/AppHook');
jest.mock('../../src/socket');
jest.mock('../../src/layouts/RegionsMenu', () => (props) => <div data-testid="regions-menu" {...props}>RegionsMenu</div>);
jest.mock('../../src/layouts/ProfileMenu', () => ({ logoutOnly }) => <div data-testid="profile-menu" data-logout-only={logoutOnly}>ProfileMenu</div>);

describe('Sidebar', () => {
    const mockSocket = {
        on: jest.fn(),
        off: jest.fn()
    };
    const mockNavigate = jest.fn();
    const mockSetDrawerOpen = jest.fn();
    const mockUser = {
        hasPermissions: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
        useLocation.mockReturnValue({ pathname: '/dashboard/stream' });
        useNavigate.mockReturnValue(mockNavigate);
        useUser.mockReturnValue({ user: mockUser });
        useApp.mockReturnValue({ screenSize: { xs: false, sm: false, md: false, lg: true } });
        getSocket.mockReturnValue(mockSocket);
    });

    const renderSidebar = (props = {}) => {
        return render(
            <ThemeProvider theme={theme}>
                <Sidebar {...props} />
            </ThemeProvider>
        );
    };

    it('should call both onClick and navigate for settings', () => {
        useApp.mockReturnValue({ screenSize: { xs: true } });
        renderSidebar();

        const settingsIcon = screen.getByTestId('SettingsIcon').closest('div');
        fireEvent.click(settingsIcon);

        expect(mockNavigate).toHaveBeenCalledWith('/dashboard/settings');
        expect(mockNavigate).toHaveBeenCalledTimes(1);
    });

    // it('should handle direct path navigation and onClick together', () => {
    //     useApp.mockReturnValue({ screenSize: { xs: true } });
    //     const mockOnClick = jest.fn();
    //     renderSidebar();

    //     // Find the settings menu item and simulate click
    //     const settingsItem = screen.getByText('Settings').closest('div');
    //     fireEvent.click(settingsItem);

    //     // Both navigation and onClick should have been triggered
    //     expect(mockNavigate).toHaveBeenCalledWith('/dashboard/settings');
    // });

    it('should apply flex-end justification for items without labels', () => {
        renderSidebar();

        const upgradeIcon = screen.getByTestId('UpgradeIcon').closest('.MuiGrid-container');
        expect(upgradeIcon).toHaveStyle({ justifyContent: 'flex-end' });
    });

    it('should apply flex-start justification for items with labels', () => {
        renderSidebar();

        const menuItem = screen.getByText('Streams').closest('.MuiGrid-container');
        expect(menuItem).toHaveStyle({ justifyContent: 'flex-start' });
    });

    it('should highlight menu item when pathname matches item path', () => {
        useLocation.mockReturnValue({ pathname: '/dashboard/settings' });
        renderSidebar();

        const settingsItem = screen.getByTestId('SettingsIcon')
            .closest('.MuiGrid-container');

        expect(settingsItem).toHaveStyle({
            backgroundColor: theme.palette.custom.mainBlue
        });
    });

    it('should not highlight menu items when pathname doesnt match', () => {
        useLocation.mockReturnValue({ pathname: '/dashboard/other' });
        renderSidebar();

        const menuItem = screen.getByText('Streams')
            .closest('.MuiGrid-container');

        expect(menuItem).toHaveStyle({
            backgroundColor: 'transparent'
        });
    });

    it('should handle multiple path matches correctly', () => {
        const { rerender } = renderSidebar();

        useLocation.mockReturnValue({ pathname: '/dashboard/stream' });
        rerender(
            <ThemeProvider theme={theme}>
                <Sidebar />
            </ThemeProvider>
        );
        expect(screen.getByText('Streams').closest('.MuiGrid-container'))
            .toHaveStyle({ backgroundColor: theme.palette.custom.mainBlue });

        useLocation.mockReturnValue({ pathname: '/dashboard/map' });
        rerender(
            <ThemeProvider theme={theme}>
                <Sidebar />
            </ThemeProvider>
        );
        expect(screen.getByText('Map').closest('.MuiGrid-container'))
            .toHaveStyle({ backgroundColor: theme.palette.custom.mainBlue });
    });

    it('should navigate when menu item is clicked', () => {
        mockUser.hasPermissions.mockReturnValue(true);
        renderSidebar();

        fireEvent.click(screen.getByText('Streams'));
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard/stream');
    });

    it('should close drawer on navigation if in mobile', () => {
        renderSidebar({ setDrawerOpen: mockSetDrawerOpen });

        fireEvent.click(screen.getByText('Streams'));
        expect(mockSetDrawerOpen).toHaveBeenCalledWith(false);
    });

    it('should highlight current route', () => {
        useLocation.mockReturnValue({ pathname: '/dashboard/map' });
        renderSidebar();

        const mapItem = screen.getByText('Map').closest('div');
        expect(mapItem).toHaveClass('MuiGrid-root');
    });

    it('should show items based on permissions', () => {
        mockUser.hasPermissions.mockImplementation((perms) =>
            perms.includes(permissions.manageUsers)
        );

        renderSidebar();
        expect(screen.getByText('Users')).toBeInTheDocument();
        expect(screen.queryByText('Api Keys')).not.toBeInTheDocument();
    });

    it('should always show non-permission items', () => {
        mockUser.hasPermissions.mockReturnValue(false);
        renderSidebar();

        expect(screen.getByText('Streams')).toBeInTheDocument();
        expect(screen.getByText('Map')).toBeInTheDocument();
    });

    it('should show offline indicator when disconnected', async () => {
        renderSidebar();

        const disconnectHandler = mockSocket.on.mock.calls.find(call => call[0] === 'disconnect')[1];
        disconnectHandler();

        await waitFor(() => {
            expect(screen.getByTestId('WifiOffIcon')).toBeInTheDocument();
        });
    });

    it('should hide offline indicator when connected', async () => {
        renderSidebar();

        const connectHandler = mockSocket.on.mock.calls.find(call => call[0] === 'connect')[1];
        connectHandler();

        await waitFor(() => {
            expect(screen.queryByTestId('WifiOffIcon')).toBeInTheDocument();
        });
    });

    it('should cleanup socket listeners on unmount', () => {
        const { unmount } = renderSidebar();
        unmount();

        expect(mockSocket.off).toHaveBeenCalledWith('connect', expect.any(Function));
        expect(mockSocket.off).toHaveBeenCalledWith('disconnect', expect.any(Function));
    });

    it('should render mobile menu items', () => {
        useApp.mockReturnValue({ screenSize: { xs: true } });
        renderSidebar();

        expect(screen.getByTestId('SettingsIcon')).toBeInTheDocument();
        expect(screen.getByTestId('profile-menu')).toBeInTheDocument();
    });

    it('should handle menu expansion toggle', () => {
        renderSidebar();

        const expandButton = screen.getByTestId('UpgradeIcon').parentElement;
        fireEvent.click(expandButton);

        expect(screen.getByText('Streams')).toHaveClass('MuiTypography-root');
    });

    it('should open regions menu when clicked', () => {
        mockUser.hasPermissions.mockReturnValue(true);
        renderSidebar();

        fireEvent.click(screen.getByText('Regions'));
        const regionsMenu = screen.getByTestId('regions-menu');
        expect(regionsMenu).toHaveAttribute('data-testid', 'regions-menu');
    });

    it('should navigate to settings when settings icon is clicked', () => {
        useApp.mockReturnValue({ screenSize: { xs: true } });
        renderSidebar();

        fireEvent.click(screen.getByTestId('SettingsIcon'));
        expect(mockNavigate).toHaveBeenCalledWith('/dashboard/settings');
    });

    it('should render all SVG icons correctly', () => {
        mockUser.hasPermissions.mockReturnValue(true);
        renderSidebar();

        expect(screen.getByAltText('Events Icon')).toHaveAttribute('alt', 'Events Icon');
    });
});
