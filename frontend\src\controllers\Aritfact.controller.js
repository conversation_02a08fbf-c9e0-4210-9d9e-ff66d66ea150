import axiosInstance from "../axios";

class ArtifactController {
    async getArtifactTimeSeries(startTimestamp, endTimestamp, interval) {
        try {
            const res = await axiosInstance.get(
                `/artifacts/hoursAggregatedCount?startTimestamp=${startTimestamp}&endTimestamp=${endTimestamp}&interval=${interval}`,
            );
            return res;
        } catch (error) {
            console.log("Error Artifact record for scrub bar notches " + error);
            return error.response.data;
        }
    }
}

const artifactController = new ArtifactController();

export default artifactController;
