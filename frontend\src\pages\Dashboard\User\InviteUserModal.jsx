import { useMemo, useEffect, useState } from "react";
import axiosInstance from "../../../axios";
import { alpha, Button, Grid, MenuItem, Modal, TextField, useTheme } from "@mui/material";
import { Field, Form, Formik } from "formik";
import ModalContainer from "../../../components/ModalContainer";
import { inviteUserSchema } from "../../../validation-schemas";
import { useUser } from "../../../hooks/UserHook";
import MultiSelect from "../../../components/MultiSelect";
import { permissions } from "../../../utils";
import useVesselInfo from "../../../hooks/VesselInfoHook";

const InviteUserModal = ({ showAddUser, setShowAddUser, onSuccess, roles, organizations, regionGroups }) => {
    const { user } = useUser();
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();
    const [vessels, setVessels] = useState([]);
    const theme = useTheme();

    const canManageOrganizations = user?.hasPermissions([permissions.manageOrganizations]);

    useEffect(() => {
        if (vesselInfo && Array.isArray(vesselInfo)) {
            setVessels(vesselInfo);
        } else {
            fetchVesselsInfo();
        }
    }, [vesselInfo, fetchVesselsInfo]);

    const vesselsByRegionGroup = useMemo(() => {
        return (
            vessels
                .filter((v) => v.region_group_id)
                .map((v) => ({ ...v, region_group_object: regionGroups.find((rg) => rg._id === v.region_group_id) }))
                // Sort vessels within each group based  due to MUI sorting behavior
                .sort((a, b) => {
                    const groupA = a.region_group_object?.name?.toLowerCase() || "";
                    const groupB = b.region_group_object?.name?.toLowerCase() || "";
                    // Sort by group name first
                    if (groupA < groupB) return -1;
                    if (groupA > groupB) return 1;
                    // Then sort within the group based on region_group_object.vessel_ids order
                    // const vesselIds = a.region_group_object?.vessel_ids || [];
                    // const indexA = vesselIds.indexOf(a.vessel_id);
                    // const indexB = vesselIds.indexOf(b.vessel_id);
                    // // Fallback to alphabetical if not found
                    // if (indexA === -1 || indexB === -1) {
                    const nameA = a.name?.toLowerCase() || a.vessel_id;
                    const nameB = b.name?.toLowerCase() || b.vessel_id;
                    return nameA.localeCompare(nameB);

                    // }

                    // return indexA - indexB;
                })
        );
    }, [vessels, regionGroups]);

    const handleClose = () => {
        setShowAddUser(false);
    };

    const onAdd = (values, { setSubmitting, resetForm }) => {
        setSubmitting(true);
        const data = {};
        Object.keys(values).forEach((key) => {
            const value = values[key];
            if (value !== "") data[key] = value;
        });

        data.allowed_vessels = data.allowed_vessels
            .map((vessel_id) => {
                const vessel = vessels.find((v) => v.vessel_id === vessel_id);
                return vessel ? vessel.vessel_id : null;
            })
            .filter(Boolean);

        axiosInstance
            .post("/users/invite", data, { meta: { showSnackbar: true } })
            .then(() => {
                onSuccess && onSuccess();
                resetForm();
            })
            .catch(console.error)
            .finally(() => setSubmitting(false));
    };

    const checkRoleUpdation = (role, values) => {
        if (user.role.hierarchy_number >= role.hierarchy_number) return true;
        if (
            organizations.find((org) => org._id == values.organization_id)?.is_miscellaneous &&
            !role.denied_permissions.includes(permissions.manageUsers)
        )
            return true;
        return false;
    };

    const getInitialOrgId = () => {
        if (canManageOrganizations) {
            return "";
        }

        if (user && user.organization) {
            return user.organization._id;
        }

        return "";
    };

    return (
        <Modal open={showAddUser} onClose={() => {}}>
            <ModalContainer title={"Invite New User"} onClose={handleClose}>
                <Formik
                    initialValues={{
                        email: "",
                        role_id: "",
                        organization_id: getInitialOrgId(),
                        allowed_vessels: [],
                    }}
                    validationSchema={inviteUserSchema}
                    onSubmit={onAdd}
                >
                    {({ errors, touched, isSubmitting, values, setValues, setFieldTouched, setErrors }) => (
                        <Form>
                            <Grid
                                container
                                flexDirection={"column"}
                                gap={2}
                                width={{ xs: 300, lg: 500 }}
                                sx={{
                                    "& .MuiFormHelperText-root": {
                                        color: alpha("#FFFFFF", 0.5) + " !important",
                                    },
                                    "& .MuiFormLabel-root": {
                                        color: "#FFFFFF !important",
                                    },
                                }}
                            >
                                <Grid>
                                    <Field
                                        as={TextField}
                                        required
                                        select
                                        name={"organization_id"}
                                        value={values.organization_id}
                                        label="Organization"
                                        variant="filled"
                                        size="small"
                                        error={touched.organization_id && Boolean(errors.organization_id)}
                                        helperText={touched.organization_id && errors.organization_id}
                                        fullWidth
                                        disabled={!canManageOrganizations}
                                        sx={{
                                            "& .Mui-disabled": {
                                                color: "white !important",
                                                cursor: "not-allowed !important",
                                                WebkitTextFillColor: "rgba(255, 255, 255, 0.7) !important",
                                            },
                                        }}
                                    >
                                        {organizations.map((org) => (
                                            <MenuItem
                                                key={org._id}
                                                value={org._id}
                                                disabled={
                                                    org.is_miscellaneous &&
                                                    values.role_id !== "" &&
                                                    !roles
                                                        .find((role) => role.role_id === values.role_id)
                                                        ?.denied_permissions.includes(permissions.manageUsers)
                                                }
                                            >
                                                {org.name}
                                            </MenuItem>
                                        ))}
                                    </Field>
                                </Grid>
                                <Grid>
                                    <Field
                                        as={TextField}
                                        required
                                        select
                                        name={"role_id"}
                                        value={values.role_id}
                                        label="Role"
                                        variant="filled"
                                        size="small"
                                        disabled={!values.organization_id}
                                        error={touched.role_id && Boolean(errors.role_id)}
                                        helperText={!values.organization_id ? "Please select organization first" : touched.role_id && errors.role_id}
                                        fullWidth
                                    >
                                        {roles
                                            .sort((a, b) => a.hierarchy_number - b.hierarchy_number)
                                            .map((role) => (
                                                <MenuItem key={role.role_id} value={role.role_id} disabled={checkRoleUpdation(role, values)}>
                                                    {role.role_name}
                                                </MenuItem>
                                            ))}
                                    </Field>
                                </Grid>
                                <Grid>
                                    <Field
                                        as={TextField}
                                        required={!values.username}
                                        name={"email"}
                                        value={values.email}
                                        type="email"
                                        label="Email"
                                        variant="filled"
                                        error={touched.email && Boolean(errors.email)}
                                        helperText={touched.email && errors.email}
                                        size="small"
                                        fullWidth
                                        sx={{
                                            backgroundColor: "transparent",
                                            "& .MuiFilledInput-root": {
                                                backgroundColor: "transparent",
                                                borderBottom: "none",
                                                border: (theme) => `1px solid ${theme.palette.custom.borderColor}`,
                                                borderRadius: "8px",
                                            },
                                            "& .MuiInputBase-input": {
                                                padding: 1.5,
                                            },
                                            "& .MuiFilledInput-root::after,.MuiFilledInput-root::before": {
                                                border: "none !important",
                                            },
                                            "& .MuiInputBase-root": {
                                                backgroundColor: "transparent",
                                                borderBottom: "none",
                                                border: (theme) => `1px solid ${theme.palette.custom.borderColor}`,
                                                borderRadius: "8px",
                                            },
                                            "& .MuiInputLabel-shrink": {
                                                display: "none",
                                            },
                                        }}
                                    />
                                </Grid>
                                <Grid>
                                    <MultiSelect
                                        loading={vessels.length === 0}
                                        options={vesselsByRegionGroup}
                                        value={values.allowed_vessels}
                                        multiple
                                        groupBy={(o) => o.region_group_object?.name}
                                        disableCloseOnSelect
                                        label={
                                            values.allowed_vessels.length === 0 ? "Select Vessels *" : `${values.allowed_vessels.length} selected *`
                                        }
                                        getOptionLabel={(o) => o.name || o.vessel_id}
                                        isOptionEqualToValue={(o, v) => v.includes(o.vessel_id)}
                                        renderTags={() => null}
                                        onChange={(_, newValue) => {
                                            console.log("UserInvite: newValue", newValue);
                                            setValues((v) => ({
                                                ...v,
                                                allowed_vessels: newValue.map((o) => (typeof o === "string" ? o : o.vessel_id)),
                                            }));
                                        }}
                                        backgroundColor={theme.palette.custom.darkBlue}
                                        sx={{
                                            ".MuiInputBase-input": {
                                                height: 24,
                                            },
                                        }}
                                        borderRadius={8}
                                        InputLabelStyle={{ fontSize: 16, opacity: 0.5 }}
                                        TextFieldProps={{
                                            error: touched.allowed_vessels && Boolean(errors.allowed_vessels),
                                            helperText: touched.allowed_vessels && errors.allowed_vessels,
                                            onBlur: () => {
                                                if (!touched.allowed_vessels) {
                                                    setFieldTouched("allowed_vessels", true, true);
                                                }
                                                if (values.allowed_vessels.length === 0) {
                                                    setErrors({ ...errors, allowed_vessels: "At least one vessel is required" });
                                                }
                                            },
                                        }}
                                    />
                                </Grid>

                                <Grid justifyContent={"center"} display={"flex"}>
                                    <Button
                                        type="submit"
                                        variant="contained"
                                        disabled={
                                            !values.email ||
                                            !values.role_id ||
                                            !values.organization_id ||
                                            values.allowed_vessels.length === 0 ||
                                            isSubmitting
                                        }
                                        sx={{
                                            backgroundColor: (theme) => theme.palette.custom.mainBlue,
                                            color: "#FFFFFF",
                                            padding: "10px 24px",
                                        }}
                                    >
                                        Submit
                                    </Button>
                                </Grid>
                            </Grid>
                        </Form>
                    )}
                </Formik>
            </ModalContainer>
        </Modal>
    );
};

export default InviteUserModal;
