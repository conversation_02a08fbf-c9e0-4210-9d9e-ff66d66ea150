// Backup original console.warn
const originalConsoleError = console.error;
import ReactDOM from "react-dom/client";
import "./index.css";
import Router from "./Router.jsx";
import { isEnvironment } from "./utils.js";
import environment from "../environment.js";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import dayjs from "dayjs";

dayjs.extend(utc);
dayjs.extend(timezone);

// Remove the splash screen once the app is rendered
function removeSplashScreen() {
    const splash = document.getElementById("splash");
    if (splash) {
        splash.style.transition = "opacity 0.5s";
        splash.style.opacity = "0";
        // setTimeout(() => {
        splash.remove();
        // }, 250); // Delay removal to allow fade-out
    }
}

if (isEnvironment(environment.production)) {
    console.log = () => {};
    console.error = function (...args) {
        const suppressMessages = ["MUI X: useResizeContainer - The parent DOM element of the Data Grid has an empty height"];

        const message = args[0];
        if (typeof message === "string" && suppressMessages.some((s) => message.includes(s))) {
            return;
        }
        originalConsoleError.apply(console, args);
    };
}

// Inject Microsoft Clarity
function injectClarity() {
    let Key = "";
    if (isEnvironment(["portal"])) Key = "sbslb655o6";
    else if (isEnvironment(["dev"])) Key = "sbaw0nicou";
    // else if (isEnvironment(['staging'])) Key = '';
    (function (c, l, a, r, i, t, y) {
        c[a] =
            c[a] ||
            function () {
                (c[a].q = c[a].q || []).push(arguments);
            };
        t = l.createElement(r);
        t.async = 1;
        t.src = "https://www.clarity.ms/tag/" + i;
        y = l.getElementsByTagName(r)[0];
        y.parentNode.insertBefore(t, y);
    })(window, document, "clarity", "script", Key);
}

// Inject Google Analytics
function injectGoogleAnalytics() {
    let gaKey = "";
    if (isEnvironment(["portal"])) gaKey = "G-7STH6DSMLK";
    else if (isEnvironment(["dev"])) gaKey = "G-N0QMBKZ3LX";
    // else if (isEnvironment(['staging'])) gaKey = '';

    if (gaKey) {
        const gtagScript = document.createElement("script");
        gtagScript.async = true;
        gtagScript.src = `https://www.googletagmanager.com/gtag/js?id=${gaKey}`;
        document.head.appendChild(gtagScript);

        window.dataLayer = window.dataLayer || [];
        function gtag() {
            window.dataLayer.push(arguments);
        }
        window.gtag = gtag;
        gtag("js", new Date());
        gtag("config", gaKey);
    }
}

// Call integrations before rendering
injectClarity();
injectGoogleAnalytics();

ReactDOM.createRoot(document.getElementById("root")).render(
    <Router />,
    removeSplashScreen(), // Remove splash screen after rendering the app
);
