import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { TextField, Button, Typography, Link, Grid, CircularProgress, InputAdornment, IconButton } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { version } from "../../../package.json";
import { useUser } from "../../hooks/UserHook.jsx";

const Login = () => {
    const navigate = useNavigate();

    const [username, setUsername] = useState("");
    const [password, setPassword] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const [error, setError] = useState("");

    const [submitting, setSubmitting] = useState(false);

    const timeout = useRef();
    const { login } = useUser();

    useEffect(() => {
        if (error) {
            clearTimeout(timeout.current);
            timeout.current = setTimeout(() => setError(""), 3000);
        }
    }, [error]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSubmitting(true);
        try {
            const response = await login({ username, password });
            if (response) {
                const eventPath = sessionStorage.getItem("eventPath");
                if (eventPath) {
                    navigate(eventPath);
                } else {
                    navigate("/dashboard/stream");
                }
            }
        } catch (err) {
            console.error(err);
            if (err.response?.status === 302) {
                navigate("/otp", { state: { username, password } });
            } else {
                setError("Login failed: " + (err.response?.data?.message || err.message || JSON.stringify(err)));
            }
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Grid container flexDirection={"column"} gap={4}>
            <Grid container flexDirection={"column"} color={"#FFFFFF"}>
                <Grid>
                    <Typography
                        variant="h3"
                        fontWeight={"600"}
                        textAlign={"center"}
                        sx={{ whiteSpace: "pre-line" }}
                    >{`SmartMast \n Dashboard Login`}</Typography>
                </Grid>
            </Grid>
            <Grid container flexDirection={"column"} component={"form"} onSubmit={handleSubmit} gap={4}>
                <Grid>
                    <TextField
                        className="input-login"
                        type="text"
                        inputProps={{
                            autoComplete: "on",
                        }}
                        autoComplete="on"
                        placeholder="Email or Username"
                        variant="outlined"
                        fullWidth
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        required
                    />
                </Grid>
                <Grid>
                    <TextField
                        className="input-login"
                        placeholder="Password"
                        variant="outlined"
                        fullWidth
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        required
                        type={showPassword ? "text" : "password"}
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <IconButton
                                        onClick={() => setShowPassword((v) => !v)}
                                        sx={{ color: "primary.contrastText" }}
                                        data-testid="toggle-visibility-button"
                                        // edge="end"
                                    >
                                        {showPassword ? <VisibilityOff /> : <Visibility />}
                                    </IconButton>
                                </InputAdornment>
                            ),
                        }}
                    />
                </Grid>
                <Grid display={error ? "block" : "none"}>
                    <Typography color="error">{error}</Typography>
                </Grid>
                <Grid>
                    <Button
                        className="btn-login"
                        type="submit"
                        variant="contained"
                        color="primary"
                        fullWidth
                        disabled={submitting}
                        endIcon={submitting && <CircularProgress />}
                    >
                        Sign in
                    </Button>
                </Grid>
            </Grid>
            <Grid>
                <Typography fontSize="18px" fontWeight={600}>
                    <Link
                        href="/forgot-password"
                        color="#FFFFFF"
                        fontWeight="bold"
                        sx={{ textDecoration: "none", ":hover": { textDecoration: "underline" } }}
                    >
                        Forgot Password?
                    </Link>
                </Typography>
            </Grid>
            <Grid color={"#FFFFFF"}>
                <Typography fontSize="18px" lineHeight="30px" fontWeight={"400"}>
                    Request an Account:{" "}
                    <Link
                        href="mailto:<EMAIL>"
                        color="#FFFFFF"
                        fontWeight="bold"
                        sx={{ textDecoration: "none", ":hover": { textDecoration: "underline" } }}
                    >
                        <EMAIL>
                    </Link>
                </Typography>
            </Grid>
            <Grid color={"#FFFFFF"}>
                <Typography fontWeight={"light"}>Version: {version}</Typography>
            </Grid>
        </Grid>
    );
};

export default Login;
