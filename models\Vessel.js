const mongoose = require("mongoose");
const ioEmitter = require("../modules/ioEmitter");
const db = require("../modules/db");
const User = require("./User");

const vesselSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    thumbnail_s3_key: {
        type: String,
        required: false,
        default: null,
    },
    thumbnail_compressed_s3_key: {
        type: String,
        required: false,
        default: null,
    },
    unit_id: {
        type: String,
        required: false,
        unique: true,
        sparse: true,
    },
    region_group_id: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
    },
    is_active: {
        type: Boolean,
        required: true,
        default: true,
    },
    units_history: [
        {
            _id: false,
            unit_id: {
                type: String,
                required: true,
            },
            mount_timestamp: {
                type: Date,
                required: true,
            },
            unmount_timestamp: {
                type: Date,
                required: false,
            },
        },
    ],
    creation_timestamp: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
    created_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: User,
        required: true,
    },
});

vesselSchema.index({ unit_id: 1 });

vesselSchema.pre("save", function (next) {
    if (this.isNew) {
        // For new vessels, only add to history if unit_id is provided
        if (this.unit_id) {
            this.units_history = [
                {
                    unit_id: this.unit_id,
                    mount_timestamp: this.creation_timestamp || new Date(),
                    unmount_timestamp: null,
                },
            ];
        } else {
            this.units_history = [];
        }
    } else if (this.isModified("unit_id")) {
        // If unit_id is being changed, update the history
        const now = new Date();

        // Close the previous unit's history if it exists
        if (this.units_history && this.units_history.length > 0) {
            const lastEntry = this.units_history[this.units_history.length - 1];
            if (!lastEntry) {
                throw new Error("Unexpected error: Last entry in units_history is undefined");
            }
            if (!lastEntry.unmount_timestamp) {
                lastEntry.unmount_timestamp = now;
            }
        }

        // Only add new unit to history if unit_id is not null/undefined
        if (this.unit_id) {
            this.units_history.push({
                unit_id: this.unit_id,
                mount_timestamp: now,
                unmount_timestamp: null,
            });
        }
    }
    next();
});

vesselSchema.post("save", emitChangedEvent);
vesselSchema.post("findOneAndUpdate", emitChangedEvent);
vesselSchema.post("findOneAndDelete", emitChangedEvent);

function emitChangedEvent(vessel) {
    ioEmitter.emit("notifyAll", { name: `vessel/changed`, data: vessel.toObject() });
}

const Vessel = db.qmShared.model("Vessel", vesselSchema, "vessels");

module.exports = Vessel;
module.exports.emitChangedEvent = process.env.NODE_ENV === "test" && emitChangedEvent;
