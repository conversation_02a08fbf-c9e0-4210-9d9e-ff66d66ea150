import { useEffect, useState } from "react";
import { useUser } from "../hooks/UserHook";
import { GroupRegionContext } from "../contexts/GroupRegionContext";
import regionGroupController from "../controllers/RegionGroup.controller";

export const GroupRegionProvider = ({ children }) => {
    const [regions, setRegions] = useState([]);
    const { user } = useUser();
    const fetchRegions = async () => {
        try {
            const allRegions = await regionGroupController.fetchAll();
            setRegions(allRegions);
        } catch (err) {
            console.error("An error occurred while fetching allRegions in the GroupRegionProvider :", err);
        }
    };
    useEffect(() => {
        if (user) {
            fetchRegions();
        }
    }, [user]);
    return <GroupRegionContext.Provider value={{ regions, fetchRegions }}>{children}</GroupRegionContext.Provider>;
};
