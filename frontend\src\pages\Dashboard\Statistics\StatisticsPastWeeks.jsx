import { Box, CircularProgress, Grid, IconButton, Typography } from "@mui/material";
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from "react";
import axiosInstance from "../../../axios";
import {
    generateRandomColorRGBA,
    getDatesArrayBetweenDateRange,
    getDayByIndex,
    sortObject,
    permissions,
    isEnvironment,
    userValues,
    defaultValues,
} from "../../../utils";
import dayjs from "dayjs";
import { ArrowBackIos, ArrowForwardIos } from "@mui/icons-material";
import KPICard from "../../../components/KPICard";
import Bar<PERSON>hart from "../../../components/Charts/BarChart";
import PieChart from "../../../components/Charts/PieChart";
import LineChart from "../../../components/Charts/LineChart";
import HeatmapChart from "../../../components/Charts/HeatmapChart";
import { DataGrid } from "@mui/x-data-grid";
import theme from "../../../theme";
import { useApp } from "../../../hooks/AppHook";
import environment from "../../../../environment";
import { useUser } from "../../../hooks/UserHook";
import DoughnutChart from "../../../components/Charts/DoughnutChart";
import useVesselInfo from "../../../hooks/VesselInfoHook";

const graphsMinHeight = { xs: 200, sm: 300, lg: 400 };

const StatisticsPastWeeks = forwardRef((props, ref) => {
    const { devMode, timezone, showIDs } = useApp();
    const { user } = useUser();
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();
    const [focusedStats, setFocusedStats] = useState();
    const [allStats, setAllStats] = useState();
    const [vesselsInfo, setVesselsInfo] = useState();

    useEffect(() => {
        fetchVessels();
    }, [vesselInfo]);

    const fetchVessels = async () => {
        if (vesselInfo && Array.isArray(vesselInfo)) {
            setVesselsInfo(vesselInfo);
        } else {
            fetchVesselsInfo();
        }
    };

    useEffect(() => {
        axiosInstance
            .get("/statistics", { params: { type: "weekly" } })
            .then((res) => setAllStats(res.data))
            .catch(console.error);
    }, []);

    useImperativeHandle(ref, () => ({
        getAllStats() {
            return allStats;
        },
    }));

    useEffect(() => {
        if (!allStats || allStats.length === 0) return;
        setFocusedStats(allStats[0]);
    }, [allStats]);

    const handleStatsChange = (op) => {
        setFocusedStats((focusedStats) => {
            var index = allStats.findIndex((o) => o._id === focusedStats._id);
            if (op === "increment") index += 1;
            else if (op === "decrement") index -= 1;
            if (!allStats[index]) return focusedStats;
            return allStats[index];
        });
    };

    const transformKeys = (data) => {
        return Object.fromEntries(
            Object.entries(data).map(([key, value]) => {
                const vessel = vesselsInfo.find((e) => e.vessel_id === key);
                if (vessel) {
                    if ((devMode || showIDs) && vessel.unit_id) {
                        return [`${vessel.name} (${vessel.unit_id})`, value];
                    } else {
                        return [vessel.name, value];
                    }
                }
                return [key, value];
            }),
        );
    };

    const stats = useMemo(() => {
        if (!focusedStats || !vesselsInfo || !user) return null;
        let stats = focusedStats.stats;

        /** remove dev units */
        if (!devMode) {
            vesselsInfo.forEach((vessel) => {
                if (!vessel.is_active && vessel.vessel_id) {
                    delete stats.totalSensorsOnlineDuration?.[vessel.vessel_id];
                    delete stats.totalSensorsDurationAtSea?.[vessel.vessel_id];
                    delete stats.totalSmartmastsDistanceTraveled?.[vessel.vessel_id];
                    delete stats.totalVesselsDetectedbySensors?.[vessel.vessel_id];
                }
            });
        }

        /** hide unwanted vessels */
        if (!user.hasPermissions([permissions.accessAllVessels])) {
            let personalizedStats = {
                totalSensorsOnlineDuration: {},
                totalSensorsDurationAtSea: {},
                totalSmartmastsDistanceTraveled: {},
                totalVesselsDetectedbySensors: {},
            };

            const userVesselIds = user.allowed_vessels || [];

            vesselsInfo.forEach((vessel) => {
                const hasAccess = userVesselIds.some((allowedVessel) => allowedVessel.toString() === vessel.vessel_id);

                if (hasAccess && vessel.vessel_id && stats.totalVesselsDetectedbySensors?.[vessel.vessel_id]) {
                    personalizedStats.totalSensorsOnlineDuration[vessel.vessel_id] = stats.totalSensorsOnlineDuration?.[vessel.vessel_id];
                    personalizedStats.totalSensorsDurationAtSea[vessel.vessel_id] = stats.totalSensorsDurationAtSea?.[vessel.vessel_id];
                    personalizedStats.totalSmartmastsDistanceTraveled[vessel.vessel_id] = stats.totalSmartmastsDistanceTraveled?.[vessel.vessel_id];
                    personalizedStats.totalVesselsDetectedbySensors[vessel.vessel_id] = stats.totalVesselsDetectedbySensors?.[vessel.vessel_id];
                }
            });
            stats = { ...stats, ...personalizedStats };
        }

        stats.totalVesselsByHoursLocal = {};
        Object.keys(stats.totalVesselsByHoursUTC).forEach((hour) => {
            const localHour = (Number(hour) + 8) % 24;
            stats.totalVesselsByHoursLocal[localHour] = stats.totalVesselsByHoursUTC[hour];
        });

        stats.totalSensorsOnlineDurationHours = {};
        Object.keys(stats.totalSensorsOnlineDuration || {}).forEach((unit) => {
            stats.totalSensorsOnlineDurationHours[unit] = parseFloat((stats.totalSensorsOnlineDuration[unit] / 1000 / 60 / 60).toFixed(2));
        });

        stats.totalSensorsDurationAtSeaHours = {};
        Object.keys(stats.totalSensorsDurationAtSea || {}).forEach((unit) => {
            stats.totalSensorsDurationAtSeaHours[unit] = parseFloat((stats.totalSensorsDurationAtSea[unit] / 1000 / 60 / 60).toFixed(2));
        });

        stats.totalSmartmastsDistanceTraveledMiles = {};
        Object.keys(stats.totalSmartmastsDistanceTraveled || {}).forEach((unit) => {
            stats.totalSmartmastsDistanceTraveledMiles[unit] = parseFloat((stats.totalSmartmastsDistanceTraveled[unit] * 0.00062137).toFixed(2));
        });

        stats.totalSmartmastsDistanceTraveledbyAllMiles = parseFloat(
            Object.values(stats.totalSmartmastsDistanceTraveled || {}).reduce((sum, value) => (sum += value), 0) * 0.00062137,
        ).toFixed(2);

        stats.listOfTextsExtractedArray = stats.listOfTextsExtracted.map((value, id) => ({ id, value }));

        stats.totalVesselsByWeekDayHoursLocal = {};

        Object.keys(stats.totalVesselsByWeekDayHoursUTC).forEach((date) => {
            const newDate = new Date(new Date(date).getTime() + 1000 * 60 * 60 * 8).toISOString().replace("Z", "+08:00");
            stats.totalVesselsByWeekDayHoursLocal[newDate] = stats.totalVesselsByWeekDayHoursUTC[date];
        });

        stats.totalVesselsSuperCategorized = sortObject(stats.totalVesselsSuperCategorized);
        stats.totalVesselsSubCategorized = sortObject(stats.totalVesselsSubCategorized);
        stats.totalVesselsDetectedbySensors = sortObject(stats.totalVesselsDetectedbySensors || {});
        stats.totalSensorsOnlineDurationHours = sortObject(stats.totalSensorsOnlineDurationHours);
        stats.totalSmartmastsDistanceTraveledMiles = sortObject(stats.totalSmartmastsDistanceTraveledMiles);

        return stats;
    }, [focusedStats, vesselsInfo, devMode, user]);

    return !stats ? (
        <CircularProgress />
    ) : (
        <Grid container spacing={2} color={"#FFFFFF"} flexDirection={"column"} wrap="nowrap" overflow={"auto"} width={"100%"}>
            <Grid
                container
                alignItems={"center"}
                justifyContent={"space-between"}
                wrap="nowrap"
                bgcolor={"primary.main"}
                borderRadius={"20px"}
                padding={2}
            >
                <Grid>
                    <IconButton sx={{ p: 0 }} disabled={focusedStats._id === allStats[0]._id} onClick={() => handleStatsChange("decrement")}>
                        <ArrowBackIos />
                    </IconButton>
                </Grid>
                <Grid>
                    <Typography sx={{ typography: { xs: "caption", sm: "body1" } }} fontWeight={"bold"} textAlign={"center"} display={"flex"}>
                        Data for the time period from:{" "}
                        {dayjs(focusedStats.fromTimestamp)
                            .tz(timezone)
                            .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}{" "}
                        to:{" "}
                        {dayjs(focusedStats.toTimestamp)
                            .tz(timezone)
                            .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}{" "}
                        ({timezone})
                    </Typography>
                </Grid>
                <Grid>
                    <IconButton
                        sx={{ p: 0 }}
                        disabled={focusedStats._id === allStats[allStats.length - 1]._id}
                        onClick={() => handleStatsChange("increment")}
                    >
                        <ArrowForwardIos />
                    </IconButton>
                </Grid>
            </Grid>
            <Grid container spacing={2}>
                <Grid
                    size={{
                        xs: 12,
                        md: 6,
                        lg: 6,
                    }}
                >
                    <KPICard
                        title={"Artifacts with At Least One Vessel"}
                        values={[
                            { title: "AI Confidence > 40%", value: stats.totalArtifactsWithAtleastOneVessel.confidenceAbove40 },
                            { title: "AI Confidence > 80%", value: stats.totalArtifactsWithAtleastOneVessel.confidenceAbove80 },
                        ]}
                        icon={"/stats-icon-4.svg"}
                    />
                </Grid>

                <Grid
                    size={{
                        xs: 12,
                        md: 6,
                        lg: 6,
                    }}
                >
                    <KPICard
                        title={"Distance Traveled by All Smartmasts (miles)"}
                        value={stats.totalSmartmastsDistanceTraveledbyAllMiles}
                        icon={"/stats-icon-3.svg"}
                    />
                </Grid>
            </Grid>

            <Grid container spacing={2} columns={12}>
                <Grid
                    display={isEnvironment(environment.stagingAndProduction) && !devMode ? "none" : "flex"}
                    flexDirection={"column"}
                    minHeight={graphsMinHeight}
                    justifyContent={"center"}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3 }}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                        Total Vessels Categorized by Super Category
                    </Typography>
                    {Object.keys(stats.totalVesselsSuperCategorized).length > 0 ? (
                        <BarChart
                            data={{
                                labels: Object.keys(stats.totalVesselsSuperCategorized),
                                datasets: [
                                    {
                                        label: "Total Vessels",
                                        data: Object.values(stats.totalVesselsSuperCategorized),
                                        backgroundColor: theme.palette.custom.mainBlue,
                                    },
                                ].filter((o) => o),
                            }}
                            options={{
                                responsive: true,
                            }}
                        />
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                height: "93%",
                            }}
                        >
                            <Typography>No data available for specified time range</Typography>
                        </Box>
                    )}
                </Grid>
                <Grid
                    display={isEnvironment(environment.stagingAndProduction) && !devMode ? "none" : "flex"}
                    flexDirection={"column"}
                    minHeight={graphsMinHeight}
                    justifyContent={"center"}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3 }}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                        Total Vessels Categorized by Sub Category
                    </Typography>
                    {Object.keys(stats.totalVesselsSubCategorized).length > 0 ? (
                        <BarChart
                            data={{
                                labels: Object.keys(stats.totalVesselsSubCategorized),
                                datasets: [
                                    {
                                        label: "Total Vessels",
                                        data: Object.values(stats.totalVesselsSubCategorized),
                                        backgroundColor: theme.palette.custom.mainBlue,
                                    },
                                ],
                            }}
                            options={{
                                responsive: true,
                            }}
                        />
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                height: "93%",
                            }}
                        >
                            <Typography>No data available for specified time range</Typography>
                        </Box>
                    )}
                </Grid>
                <Grid
                    display={isEnvironment(environment.stagingAndProduction) && !devMode ? "none" : "flex"}
                    flexDirection={"column"}
                    minHeight={{ xs: 400, sm: 600, lg: 550 }}
                    maxHeight={graphsMinHeight}
                    justifyContent={"center"}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3, height: "330 !important" }}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                        Total Vessels Categorized by Country Flag
                    </Typography>
                    {Object.keys(stats.totalVesselsWithCountryFlag).length > 0 ? (
                        <Grid justifyContent={"center"} alignItems={"center"} display={"flex"} height={"100%"} width={"100%"}>
                            <PieChart
                                data={{
                                    labels: Object.keys(stats.totalVesselsWithCountryFlag),
                                    datasets: [
                                        {
                                            label: "Total Vessels",
                                            data: Object.values(stats.totalVesselsWithCountryFlag),
                                            backgroundColor: Array.from({ length: Object.keys(stats.totalVesselsWithCountryFlag).length }).map(
                                                (_, i) => generateRandomColorRGBA(10 + i, 1),
                                            ),
                                            borderColor: Array.from({ length: Object.keys(stats.totalVesselsWithCountryFlag).length }).map((_, i) =>
                                                generateRandomColorRGBA(10 + i, 1),
                                            ),
                                            borderWidth: 1,
                                        },
                                    ],
                                }}
                                options={{
                                    responsive: true,
                                }}
                            />
                        </Grid>
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                height: "93% !important",
                            }}
                        >
                            <Typography>No data available for specified time range</Typography>
                        </Box>
                    )}
                </Grid>
                <Grid
                    flexDirection={"column"}
                    minHeight={{ xs: 400, sm: 600, lg: 550 }}
                    maxHeight={"none"}
                    height={"auto"}
                    justifyContent={"center"}
                    display={"flex"}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3, gap: 3 }}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                        Total Vessels Detected by Sensors
                    </Typography>
                    {Object.keys(stats.totalVesselsDetectedbySensors).length > 0 ? (
                        <Grid justifyContent={"center"} alignItems={"center"} display={"flex"} height={"90%"} width={"100%"}>
                            <DoughnutChart
                                data={{
                                    labels: Object.keys(transformKeys(stats.totalVesselsDetectedbySensors)),
                                    datasets: [
                                        {
                                            label: "Total Vessels",
                                            data: Object.values(stats.totalVesselsDetectedbySensors),
                                            backgroundColor: Array.from({ length: Object.keys(stats.totalVesselsDetectedbySensors).length }).map(
                                                (_, i) => generateRandomColorRGBA(10 + i, 1),
                                            ),
                                            borderColor: Array.from({ length: Object.keys(stats.totalVesselsDetectedbySensors).length }).map((_, i) =>
                                                generateRandomColorRGBA(10 + i, 1),
                                            ),
                                            borderWidth: 1,
                                        },
                                    ],
                                }}
                                options={{
                                    responsive: true,
                                }}
                            />
                        </Grid>
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                height: "93%",
                            }}
                        >
                            <Typography>No data available for specified time range</Typography>
                        </Box>
                    )}
                </Grid>
                <Grid
                    display={isEnvironment(environment.stagingAndProduction) && !devMode ? "none" : "flex"}
                    flexDirection={"column"}
                    minHeight={graphsMinHeight}
                    justifyContent={"center"}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3 }}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                        Total Vessels Observed by Hour of Day
                    </Typography>
                    {Object.keys(stats.totalVesselsByHoursLocal).length > 0 ? (
                        <LineChart
                            data={{
                                labels: Object.keys(stats.totalVesselsByHoursLocal),
                                datasets: [
                                    {
                                        label: "Total Vessels",
                                        data: Object.values(stats.totalVesselsByHoursLocal),
                                        backgroundColor: theme.palette.custom.mainBlue,
                                        stepped: true,
                                    },
                                ],
                            }}
                            options={{
                                responsive: true,
                            }}
                        />
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                height: "93%",
                            }}
                        >
                            <Typography>No data available for specified time range</Typography>
                        </Box>
                    )}
                </Grid>
                <Grid
                    flexDirection={"column"}
                    minHeight={graphsMinHeight}
                    justifyContent={"center"}
                    display={"flex"}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3 }}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                        Smartmasts Activity Time (hours)
                    </Typography>
                    {Object.keys(stats.totalSensorsOnlineDurationHours).length > 0 ? (
                        <BarChart
                            data={{
                                labels: Object.keys(transformKeys(stats.totalSensorsOnlineDurationHours)),
                                datasets: [
                                    {
                                        label: "Total Time Online",
                                        data: Object.values(stats.totalSensorsOnlineDurationHours),
                                        backgroundColor: theme.palette.custom.mainBlue,
                                    },
                                    {
                                        label: "Total Time at Sea",
                                        data: Object.keys(stats.totalSensorsOnlineDurationHours).map(
                                            (unit) => stats.totalSensorsDurationAtSeaHours[unit] || 0,
                                        ),
                                        backgroundColor: "#10B981",
                                    },
                                ].filter((o) => o),
                            }}
                            options={{
                                responsive: true,
                            }}
                        />
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                height: "93%",
                            }}
                        >
                            <Typography>No data available for specified time range</Typography>
                        </Box>
                    )}
                </Grid>
                <Grid
                    flexDirection={"column"}
                    minHeight={graphsMinHeight}
                    justifyContent={"center"}
                    display={"flex"}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3 }}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                        Smartmasts Distance Traveled (miles)
                    </Typography>
                    {Object.keys(stats.totalSmartmastsDistanceTraveledMiles).length > 0 ? (
                        <BarChart
                            data={{
                                labels: Object.keys(transformKeys(stats.totalSmartmastsDistanceTraveledMiles)),
                                datasets: [
                                    {
                                        label: "Total Distance",
                                        data: Object.values(stats.totalSmartmastsDistanceTraveledMiles),
                                        backgroundColor: theme.palette.custom.mainBlue,
                                    },
                                ].filter((o) => o),
                            }}
                            options={{
                                responsive: true,
                            }}
                        />
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                height: "93%",
                            }}
                        >
                            <Typography>No data available for specified time range</Typography>
                        </Box>
                    )}
                </Grid>
                <Grid
                    display={isEnvironment(environment.stagingAndProduction) && !devMode ? "none" : "flex"}
                    flexDirection={"column"}
                    minHeight={graphsMinHeight}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3 }}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                        Vessels Observation Heatmap
                    </Typography>
                    <Box height={"100%"} width={"100%"} display={"flex"} justifyContent={"center"} alignItems={"center"}>
                        {Object.keys(stats.totalVesselsByWeekDayHoursLocal).length > 0 ? (
                            <Box width={"100%"}>
                                <HeatmapChart
                                    chartSeries={getDatesArrayBetweenDateRange(
                                        dayjs(focusedStats.fromTimestamp)
                                            .tz(timezone)
                                            .format(
                                                defaultValues.dateTimeFormat({ exclude_hours: true, exclude_minutes: true, exclude_seconds: true }),
                                            ),
                                        dayjs(focusedStats.toTimestamp)
                                            .tz(timezone)
                                            .format(
                                                defaultValues.dateTimeFormat({ exclude_hours: true, exclude_minutes: true, exclude_seconds: true }),
                                            ),
                                    )
                                        .sort((a, b) => new Date(b) - new Date(a))
                                        .map((date) => ({
                                            name: getDayByIndex(new Date(date).getUTCDay()),
                                            data: Array.from({ length: 24 }).map((_, i) => ({
                                                x: i,
                                                y:
                                                    stats.totalVesselsByWeekDayHoursLocal[
                                                        `${date}T${i.toString().padStart(2, "0")}:00:00.000+08:00`
                                                    ] || 0,
                                            })),
                                            // Object.keys(stats.totalVesselsByWeekDayHoursUTC[dayIndex]).map(x => ({ x, y: stats.totalVesselsByWeekDayHoursUTC[dayIndex][x] }))
                                        }))}
                                />
                            </Box>
                        ) : (
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    height: "93%",
                                }}
                            >
                                <Typography>No data available for specified time range</Typography>
                            </Box>
                        )}
                    </Box>
                </Grid>
                <Grid
                    display={isEnvironment(environment.stagingAndProduction) && !devMode ? "none" : "flex"}
                    maxHeight={400}
                    justifyContent={"center"}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3 }}
                    minHeight={400}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    {Object.keys(stats.totalVesselsByWeekDayHoursLocal).length > 0 ? (
                        <DataGrid
                            disableRowSelectionOnClick
                            rows={stats.listOfTextsExtractedArray}
                            columns={[{ field: "value", headerName: "List of Texts Extracted", flex: 1 }]}
                            // getRowId={(row) => v}
                            // slots={{
                            //     footer: CustomFooter,
                            // }}
                            // getRowHeight={getRowHeight}
                            sx={{
                                backgroundColor: theme.palette.custom.darkBlue,
                                height: 340,
                            }}
                        />
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                height: "93%",
                            }}
                        >
                            <Typography>No data available for specified time range</Typography>
                        </Box>
                    )}
                </Grid>
            </Grid>
            {/* <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total artifacts with at least one vessel
                        </Typography>
                    </Grid>
                    <Grid item>
                        <Typography>
                            Confidence above 40: {stats.totalArtifactsWithAtleastOneVessel.confidenceAbove40}
                        </Typography>
                    </Grid>
                    <Grid item>
                        <Typography>
                            Confidence above 80: {stats.totalArtifactsWithAtleastOneVessel.confidenceAbove80}
                        </Typography>
                    </Grid>
                </Grid> */}
            {/* <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total vessels categorized by super category
                        </Typography>
                    </Grid>
                    <Grid item container columnGap={2}>
                        {Object.keys(stats.totalVesselsSuperCategorized).map(cat => (
                            <Grid item>
                                <Typography>
                                    {cat}: {stats.totalVesselsSuperCategorized[cat]}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
                {stats.experimental?.totalVesselsSuperCategorizedWithBoundingBoxOccupancy5 && (
                    <Grid item container flexDirection={'column'}>
                        <Grid item>
                            <Typography>
                                [Experimental] Total vessels categorized by super category (Bounding Box Occupancy {'>'} 5%)
                            </Typography>
                        </Grid>
                        <Grid item container columnGap={2}>
                            {Object.keys(stats.experimental.totalVesselsSuperCategorizedWithBoundingBoxOccupancy5).map(cat => (
                                <Grid item>
                                    <Typography>
                                        {cat}: {stats.experimental.totalVesselsSuperCategorizedWithBoundingBoxOccupancy5[cat]}
                                    </Typography>
                                </Grid>
                            ))}
                        </Grid>
                    </Grid>
                )}
                <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total vessels categorized by sub category
                        </Typography>
                    </Grid>
                    <Grid item container columnGap={2}>
                        {Object.keys(stats.totalVesselsSubCategorized).map(cat => (
                            <Grid item>
                                <Typography>
                                    {cat}: {stats.totalVesselsSubCategorized[cat]}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                </Grid> */}
            {/* <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            List of texts extracted
                        </Typography>
                    </Grid>
                    <Grid item>
                        <Typography>
                            {stats.listOfTextsExtracted.join(', ')}
                        </Typography>
                    </Grid>
                </Grid> */}
            {/* <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total vessels categorized by country flag
                        </Typography>
                    </Grid>
                    <Grid item container columnGap={2}>
                        {Object.keys(stats.totalVesselsWithCountryFlag).map(cat => (
                            <Grid item>
                                <Typography>
                                    {cat}: {stats.totalVesselsWithCountryFlag[cat]}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                </Grid> */}
            {/* <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total vessels detected by sensors
                        </Typography>
                    </Grid>
                    <Grid item container gap={2}>
                        {Object.keys(stats.totalVesselsDetectedbySensors).map(unit => (
                            <Grid item>
                                <Typography>
                                    {unit}: {stats.totalVesselsDetectedbySensors[unit]}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                </Grid> */}
            {/* <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total vessels observed by hours (UTC):
                        </Typography>
                    </Grid>
                    <Grid item container gap={2}>
                        {Object.keys(stats.totalVesselsByHoursUTC).map(hr => (
                            <Grid item>
                                <Typography>
                                    {hr}: {stats.totalVesselsByHoursUTC[hr]}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                </Grid> */}
            {/* <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total vessels observed by hours/day (UTC)
                        </Typography>
                    </Grid>
                    <Grid item container flexDirection={'column'}>
                        {Object.keys(stats.totalVesselsByWeekDayHoursUTC).map((dayIndex, index) => (
                            <Grid key={index} item container gap={2}>
                                <Grid item minWidth={100}>
                                    <Typography>
                                        {getDayByIndex(dayIndex)}:
                                    </Typography>
                                </Grid>
                                {Object.keys(stats.totalVesselsByWeekDayHoursUTC[dayIndex]).map((hr, i) => (
                                    <Grid key={i} item>
                                        <Typography>
                                            {hr}: {stats.totalVesselsByWeekDayHoursUTC[dayIndex][hr]}
                                        </Typography>
                                    </Grid>
                                ))}
                            </Grid>
                        ))}
                    </Grid>
                </Grid> */}
            {/* <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total time of SmartMasts at the sea (hours)
                        </Typography>
                    </Grid>
                    <Grid item container columnGap={2}>
                        {Object.keys(stats.totalSensorsDurationAtSea).map(unit => (
                            <Grid item>
                                <Typography>
                                    {unit}: {stats.totalSensorsDurationAtSea[unit]}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
                <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total online duration of SmartMasts (hours)
                        </Typography>
                    </Grid>
                    <Grid item container columnGap={2}>
                        {Object.keys(stats.totalSensorsOnlineDuration).map(unit => (
                            <Grid item>
                                <Typography>
                                    {unit}: {stats.totalSensorsOnlineDuration[unit]}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                </Grid> */}
            {/* <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total distance traveled by SmartMasts (miles)
                        </Typography>
                    </Grid>
                    <Grid item container columnGap={2}>
                        {Object.keys(stats.totalSmartmastsDistanceTraveled).map(unit => (
                            <Grid item>
                                <Typography>
                                    {unit}: {parseFloat((stats.totalSmartmastsDistanceTraveled[unit] * 0.00062137).toFixed(2))}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
                <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total distance traveled by all SmartMasts (miles)
                        </Typography>
                    </Grid>
                    <Grid item container columnGap={2}>
                        {parseFloat(Object.values(stats.totalSmartmastsDistanceTraveled).reduce((sum, value) => sum += value, 0) * 0.00062137).toFixed(2)}
                    </Grid>
                </Grid> */}
        </Grid>
    );
});

StatisticsPastWeeks.displayName = "StatisticsPastWeeks";

export default StatisticsPastWeeks;
