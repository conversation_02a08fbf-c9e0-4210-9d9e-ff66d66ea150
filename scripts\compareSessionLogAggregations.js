// scripts/compareSessionLogAggregations.js
require("dotenv").config();
const SessionLog = require("../models/SessionLog");

async function run() {
    const environment = "portal";
    const page = 1;
    const limit = 10;
    const skip = (page - 1) * limit;

    // --- OLD PIPELINE ---
    const oldPipeline = [
        { $match: { environment } },
        {
            $lookup: {
                from: "users",
                localField: "user_id",
                foreignField: "_id",
                as: "user",
                pipeline: [{ $project: { name: 1, username: 1, _id: 0 } }],
            },
        },
        { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
        { $sort: { connect_timestamp: -1 } },
        {
            $group: {
                _id: "$user_id",
                lastLog: { $first: "$$ROOT" },
            },
        },
        { $replaceRoot: { newRoot: "$lastLog" } },
        {
            $facet: {
                metadata: [{ $count: "total" }],
                data: [
                    { $skip: skip },
                    { $limit: limit },
                    {
                        $project: {
                            "user.password": 0,
                            "user.reset_password_token": 0,
                            "user.reset_password_expire": 0,
                        },
                    },
                ],
            },
        },
        {
            $project: {
                total: { $ifNull: [{ $arrayElemAt: ["$metadata.total", 0] }, 0] },
                data: "$data",
            },
        },
    ];

    // --- NEW PIPELINE ---
    const newPipeline = [
        { $match: { environment } },
        {
            $project: {
                user_id: 1,
                connect_timestamp: 1,
                device: 1,
                browser: 1,
                disconnect_timestamp: 1,
            },
        },
        { $sort: { connect_timestamp: -1 } },
        {
            $group: {
                _id: "$user_id",
                lastLog: { $first: "$$ROOT" },
            },
        },
        { $replaceRoot: { newRoot: "$lastLog" } },
        {
            $lookup: {
                from: "users",
                localField: "user_id",
                foreignField: "_id",
                as: "user",
                pipeline: [{ $project: { name: 1, username: 1, _id: 0 } }],
            },
        },
        { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
        {
            $facet: {
                metadata: [{ $count: "total" }],
                data: [
                    { $skip: skip },
                    { $limit: limit },
                    {
                        $project: {
                            "user.password": 0,
                            "user.reset_password_token": 0,
                            "user.reset_password_expire": 0,
                        },
                    },
                ],
            },
        },
        {
            $project: {
                total: { $ifNull: [{ $arrayElemAt: ["$metadata.total", 0] }, 0] },
                data: "$data",
            },
        },
    ];

    console.log("Running OLD pipeline...");
    await SessionLog.aggregate(oldPipeline);
    console.log("OLD pipeline complete.");

    console.log("Running NEW pipeline...");
    await SessionLog.aggregate(newPipeline);
    console.log("NEW pipeline complete.");

    process.exit(0);
}

run().catch((e) => {
    console.error(e);
    process.exit(1);
});
