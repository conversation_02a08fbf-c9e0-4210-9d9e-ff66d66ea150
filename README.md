# Quartermaster Web Application
## Table of Contents
- [Quartermaster Web Application](#quartermaster-web-application)
  - [Table of Contents](#table-of-contents)
  - [Overview](#overview)
  - [Features](#features)
  - [Architecture Overview](#architecture-overview)
  - [Running the Code](#running-the-code)
    - [Prerequisites](#prerequisites)
    - [Cloning the Repository](#cloning-the-repository)
    - [Project Structure](#project-structure)
    - [Installation](#installation)
    - [Environment Variables](#environment-variables)
    - [Running the backend](#running-the-backend)
    - [Running the frontend](#running-the-frontend)
    - [Building the project (optional)](#building-the-project-optional)
  - [API Documentation](#api-documentation)
  - [Testing](#testing)
    - [Backend](#backend)
    - [Frontend](#frontend)
## Overview
The Quartermaster Web Application is a comprehensive system built to manage and streamline maritime operations. It utilizes a modern tech stack, consisting of a backend built with Node.js and a frontend powered by Vite and React. The application supports real-time communication, authentication, video handling, and mapping features.
## Features
- **Real-Time Video Streaming**: The platform streams live video from SmartMast-enabled vessels, allowing users to monitor activities as they happen. This functionality supports both manual and automated control of camera systems, including the ability to pan, tilt, and zoom.
- **Gimbal-Controlled Camera Operations**: Users can remotely control cameras mounted on the vessels through an integrated gimbal system. This allows for precise adjustments of the camera's pan, tilt, and zoom functions, ensuring that operators can capture high-resolution images and video of vessels from various angles, even in challenging sea conditions.
- **GPS Tracking and Vessel Monitoring**: Quartermaster integrates GPS coordinates to provide real-time tracking of vessels. Users can view the position, movement, and behavior of vessels.
- **AI-based Event Detections**: The onboard sensors utilize AI and high-bandwidth satellite connectivity to deliver real-time, high-definition detections of maritime activity. 
- **Data Visualisation and Reporting**: The platform provides immediate alerts when activities are detected, ensuring timely response. Additionally, users can view and analyze data collected from sensors, including vessel detections, GPS tracking, and video footages, using an intuitive dashboard with real-time updates.
- **User Management and Permissions**: The platform features a robust user management system with role-based access control, ensuring secure access for customers, operators, and administrators.
- **API Integration and Authentication**: The platform also offers fully fledged and documented Application Programming Interface (API), allowing third-party developers to securely and easily build custom solutions.
## Architecture Overview
[<img alt="quartermaster_web_architecture" src="frontend/public/quartermaster-architectural-design.jpg" />]()
## Running the Code
### Prerequisites
Make sure you have the following installed:

- **Node.js**: v18.x or higher
- **npm**: v8.x or higher
- **MongoDB**: v7.x or higher
- **Git**: v2.x or higher

Node.js download link: https://nodejs.org/en/download/package-manager\
MongoDB download link: https://www.mongodb.com/docs/manual/installation\
Git download link: https://git-scm.com/downloads
### Cloning the Repository
1. Acquire the **Personal Access Token (PAT)** from the respository owner in order to clone this repository
2. Clone the repository using the following git command:
```bash
git clone https://<personal-access-token>@github.com/Quartermaster-AI/quartermaster-webapp.git
```
- Ensure to replace `<personal-access-token>` with your acquired token (without angular brackets)
### Project Structure
This project is structured as a monorepo with the **Express backend** residing in the root directory and the **React frontend** residing inside the `frontend` folder.
```bash
📦 Project Root
├── 📁 frontend                 # React frontend application
│   ├── 📁 dist                 # Build folder
│   ├── 📁 public               # Public assets
│   ├── 📁 src                  # Source files for the React app
│   │   ├── 📁 components       # Reusable React components
│   │   ├── 📁 contexts         # Context variables
│   │   ├── 📁 hooks            # Custom hooks for the app
│   │   ├── 📁 layouts          # Layout components for pages
│   │   ├── 📁 pages            # Page components
│   │   ├── 📁 providers        # Context providers for global state
│   │   ├── 📄 axios.js         # Axios instance
│   │   ├── 📄 gps_socket.js    # GPS microservice WebSocket instance
│   │   ├── 📄 indexedDB.js     # Indexed DB functions
│   │   ├── 📄 main.js          # Entry point for the React app
│   │   ├── 📄 Router.jsx       # Frontend routes
│   │   ├── 📄 socket.js        # WebSocket instance
│   │   ├── 📄 theme.js         # MUI theme config file
│   │   └── 📄 utils.js         # Utility variables and functions
│   ├── 📄 .env                 # Environment variables
│   ├── 📄 .env.example         # Sample .env file
│   ├── 📄 .eslintrc.cjs        # Es-lint config file
│   ├── 📄 index.html           # HTML layout file
│   ├── 📄 package.json         # Frontend dependencies and scripts
│   └── 📄 vite.config.js       # Vite config file
├── 📁 middlewares              # Middleware functions
├── 📁 models                   # Database models
├── 📁 modules                  # Server modules
├── 📁 queries                  # Database queries
├── 📁 routes                   # API routes for the backend
├── 📁 tests                    # Backend unit test cases
├── 📁 utils                    # Utility functions
├── 📄 .env                     # Environment variables
├── 📄 .env.example             # Sample .env file
├── 📄 jest.config.js           # Jest config file
├── 📄 package.json             # Backend dependencies
└── 📄 server.js                # Server entry point
```
### Installation
1. Run the install command to install backend dependencies:
```bash
npm install
```
2. Run the following commands to install frontend dependencies:
```bash
cd frontend
npm install
```
### Environment Variables
1. Make a copy of `.env.example` file in the root folder and rename the new file to `.env`
2. Similary, make a copy of `.env.example` file in the frontend folder and rename it to `.env`
3. Assign values to the variables, or contact the repository owner for assistance
### Running the backend
Navigate to the `root` folder and run the following command:
```bash
npm run dev
```
### Running the frontend
Navigate to the `frontend` folder and run the following command:
```bash
npm run dev
```
Now you can visit http://localhost:3000/ to view the website in your browser
### Building the project (optional)
If you would like to build the project, you can run the following commands:
```bash
cd frontend
npm run build
cd ..
npm start
```
Your built project will now be hosted at http://localhost:5000/
## API Documentation
The backend API has been documented using Swagger, and can be accessed at https://staging.quartermaster.us/api/docs
## Testing
### Backend
Jest is used for testing the backend. To run tests, use the following command in `root` folder:
```bash
npm run test
```
### Frontend
Jest is used for testing the frontend. To run tests, use the following commands:
```bash
cd frontend
npm run test
```