{"version": 3, "sources": ["../../geodesy/dms.js", "../../geodesy/latlon-spherical.js"], "sourcesContent": ["/* - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */\n/* Geodesy representation conversion functions                        (c) <PERSON> 2002-2020  */\n/*                                                                                   MIT Licence  */\n/* www.movable-type.co.uk/scripts/latlong.html                                                    */\n/* www.movable-type.co.uk/scripts/js/geodesy/geodesy-library.html#dms                             */\n/* - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */\n\n/* eslint no-irregular-whitespace: [2, { skipComments: true }] */\n\n\n/**\n * Latitude/longitude points may be represented as decimal degrees, or subdivided into sexagesimal\n * minutes and seconds. This module provides methods for parsing and representing degrees / minutes\n * / seconds.\n *\n * @module dms\n */\n\n\n/* Degree-minutes-seconds (& cardinal directions) separator character */\nlet dmsSeparator = '\\u202f'; // U+202F = 'narrow no-break space'\n\n\n/**\n * Functions for parsing and representing degrees / minutes / seconds.\n */\nclass Dms {\n\n    // note Unicode Degree = U+00B0. Prime = U+2032, Double prime = U+2033\n\n    /**\n     * Separator character to be used to separate degrees, minutes, seconds, and cardinal directions.\n     *\n     * Default separator is U+202F ‘narrow no-break space’.\n     *\n     * To change this (e.g. to empty string or full space), set Dms.separator prior to invoking\n     * formatting.\n     *\n     * @example\n     *   import LatLon, { Dms } from '/js/geodesy/latlon-spherical.js';\n     *   const p = new LatLon(51.2, 0.33).toString('dms');  // 51° 12′ 00″ N, 000° 19′ 48″ E\n     *   Dms.separator = '';                                // no separator\n     *   const pʹ = new LatLon(51.2, 0.33).toString('dms'); // 51°12′00″N, 000°19′48″E\n     */\n    static get separator()     { return dmsSeparator; }\n    static set separator(char) { dmsSeparator = char; }\n\n\n    /**\n     * Parses string representing degrees/minutes/seconds into numeric degrees.\n     *\n     * This is very flexible on formats, allowing signed decimal degrees, or deg-min-sec optionally\n     * suffixed by compass direction (NSEW); a variety of separators are accepted. Examples -3.62,\n     * '3 37 12W', '3°37′12″W'.\n     *\n     * Thousands/decimal separators must be comma/dot; use Dms.fromLocale to convert locale-specific\n     * thousands/decimal separators.\n     *\n     * @param   {string|number} dms - Degrees or deg/min/sec in variety of formats.\n     * @returns {number}        Degrees as decimal number.\n     *\n     * @example\n     *   const lat = Dms.parse('51° 28′ 40.37″ N');\n     *   const lon = Dms.parse('000° 00′ 05.29″ W');\n     *   const p1 = new LatLon(lat, lon); // 51.4779°N, 000.0015°W\n     */\n    static parse(dms) {\n        // check for signed decimal degrees without NSEW, if so return it directly\n        if (!isNaN(parseFloat(dms)) && isFinite(dms)) return Number(dms);\n\n        // strip off any sign or compass dir'n & split out separate d/m/s\n        const dmsParts = String(dms).trim().replace(/^-/, '').replace(/[NSEW]$/i, '').split(/[^0-9.,]+/);\n        if (dmsParts[dmsParts.length-1]=='') dmsParts.splice(dmsParts.length-1);  // from trailing symbol\n\n        if (dmsParts == '') return NaN;\n\n        // and convert to decimal degrees...\n        let deg = null;\n        switch (dmsParts.length) {\n            case 3:  // interpret 3-part result as d/m/s\n                deg = dmsParts[0]/1 + dmsParts[1]/60 + dmsParts[2]/3600;\n                break;\n            case 2:  // interpret 2-part result as d/m\n                deg = dmsParts[0]/1 + dmsParts[1]/60;\n                break;\n            case 1:  // just d (possibly decimal) or non-separated dddmmss\n                deg = dmsParts[0];\n                // check for fixed-width unseparated format eg 0033709W\n                //if (/[NS]/i.test(dmsParts)) deg = '0' + deg;  // - normalise N/S to 3-digit degrees\n                //if (/[0-9]{7}/.test(deg)) deg = deg.slice(0,3)/1 + deg.slice(3,5)/60 + deg.slice(5)/3600;\n                break;\n            default:\n                return NaN;\n        }\n        if (/^-|[WS]$/i.test(dms.trim())) deg = -deg; // take '-', west and south as -ve\n\n        return Number(deg);\n    }\n\n\n    /**\n     * Converts decimal degrees to deg/min/sec format\n     *  - degree, prime, double-prime symbols are added, but sign is discarded, though no compass\n     *    direction is added.\n     *  - degrees are zero-padded to 3 digits; for degrees latitude, use .slice(1) to remove leading\n     *    zero.\n     *\n     * @private\n     * @param   {number} deg - Degrees to be formatted as specified.\n     * @param   {string} [format=d] - Return value as 'd', 'dm', 'dms' for deg, deg+min, deg+min+sec.\n     * @param   {number} [dp=4|2|0] - Number of decimal places to use – default 4 for d, 2 for dm, 0 for dms.\n     * @returns {string} Degrees formatted as deg/min/secs according to specified format.\n     */\n    static toDms(deg, format='d', dp=undefined) {\n        if (isNaN(deg)) return null;  // give up here if we can't make a number from deg\n        if (typeof deg == 'string' && deg.trim() == '') return null;\n        if (typeof deg == 'boolean') return null;\n        if (deg == Infinity) return null;\n        if (deg == null) return null;\n\n        // default values\n        if (dp === undefined) {\n            switch (format) {\n                case 'd':   case 'deg':         dp = 4; break;\n                case 'dm':  case 'deg+min':     dp = 2; break;\n                case 'dms': case 'deg+min+sec': dp = 0; break;\n                default:          format = 'd'; dp = 4; break; // be forgiving on invalid format\n            }\n        }\n\n        deg = Math.abs(deg);  // (unsigned result ready for appending compass dir'n)\n\n        let dms = null, d = null, m = null, s = null;\n        switch (format) {\n            default: // invalid format spec!\n            case 'd': case 'deg':\n                d = deg.toFixed(dp);                       // round/right-pad degrees\n                if (d<100) d = '0' + d;                    // left-pad with leading zeros (note may include decimals)\n                if (d<10) d = '0' + d;\n                dms = d + '°';\n                break;\n            case 'dm': case 'deg+min':\n                d = Math.floor(deg);                       // get component deg\n                m = ((deg*60) % 60).toFixed(dp);           // get component min & round/right-pad\n                if (m == 60) { m = (0).toFixed(dp); d++; } // check for rounding up\n                d = ('000'+d).slice(-3);                   // left-pad with leading zeros\n                if (m<10) m = '0' + m;                     // left-pad with leading zeros (note may include decimals)\n                dms = d + '°'+Dms.separator + m + '′';\n                break;\n            case 'dms': case 'deg+min+sec':\n                d = Math.floor(deg);                       // get component deg\n                m = Math.floor((deg*3600)/60) % 60;        // get component min\n                s = (deg*3600 % 60).toFixed(dp);           // get component sec & round/right-pad\n                if (s == 60) { s = (0).toFixed(dp); m++; } // check for rounding up\n                if (m == 60) { m = 0; d++; }               // check for rounding up\n                d = ('000'+d).slice(-3);                   // left-pad with leading zeros\n                m = ('00'+m).slice(-2);                    // left-pad with leading zeros\n                if (s<10) s = '0' + s;                     // left-pad with leading zeros (note may include decimals)\n                dms = d + '°'+Dms.separator + m + '′'+Dms.separator + s + '″';\n                break;\n        }\n\n        return dms;\n    }\n\n\n    /**\n     * Converts numeric degrees to deg/min/sec latitude (2-digit degrees, suffixed with N/S).\n     *\n     * @param   {number} deg - Degrees to be formatted as specified.\n     * @param   {string} [format=d] - Return value as 'd', 'dm', 'dms' for deg, deg+min, deg+min+sec.\n     * @param   {number} [dp=4|2|0] - Number of decimal places to use – default 4 for d, 2 for dm, 0 for dms.\n     * @returns {string} Degrees formatted as deg/min/secs according to specified format.\n     *\n     * @example\n     *   const lat = Dms.toLat(-3.62, 'dms'); // 3°37′12″S\n     */\n    static toLat(deg, format, dp) {\n        const lat = Dms.toDms(Dms.wrap90(deg), format, dp);\n        return lat===null ? '–' : lat.slice(1) + Dms.separator + (deg<0 ? 'S' : 'N');  // knock off initial '0' for lat!\n    }\n\n\n    /**\n     * Convert numeric degrees to deg/min/sec longitude (3-digit degrees, suffixed with E/W).\n     *\n     * @param   {number} deg - Degrees to be formatted as specified.\n     * @param   {string} [format=d] - Return value as 'd', 'dm', 'dms' for deg, deg+min, deg+min+sec.\n     * @param   {number} [dp=4|2|0] - Number of decimal places to use – default 4 for d, 2 for dm, 0 for dms.\n     * @returns {string} Degrees formatted as deg/min/secs according to specified format.\n     *\n     * @example\n     *   const lon = Dms.toLon(-3.62, 'dms'); // 3°37′12″W\n     */\n    static toLon(deg, format, dp) {\n        const lon = Dms.toDms(Dms.wrap180(deg), format, dp);\n        return lon===null ? '–' : lon + Dms.separator + (deg<0 ? 'W' : 'E');\n    }\n\n\n    /**\n     * Converts numeric degrees to deg/min/sec as a bearing (0°..360°).\n     *\n     * @param   {number} deg - Degrees to be formatted as specified.\n     * @param   {string} [format=d] - Return value as 'd', 'dm', 'dms' for deg, deg+min, deg+min+sec.\n     * @param   {number} [dp=4|2|0] - Number of decimal places to use – default 4 for d, 2 for dm, 0 for dms.\n     * @returns {string} Degrees formatted as deg/min/secs according to specified format.\n     *\n     * @example\n     *   const lon = Dms.toBrng(-3.62, 'dms'); // 356°22′48″\n     */\n    static toBrng(deg, format, dp) {\n        const brng =  Dms.toDms(Dms.wrap360(deg), format, dp);\n        return brng===null ? '–' : brng.replace('360', '0');  // just in case rounding took us up to 360°!\n    }\n\n\n    /**\n     * Converts DMS string from locale thousands/decimal separators to JavaScript comma/dot separators\n     * for subsequent parsing.\n     *\n     * Both thousands and decimal separators must be followed by a numeric character, to facilitate\n     * parsing of single lat/long string (in which whitespace must be left after the comma separator).\n     *\n     * @param   {string} str - Degrees/minutes/seconds formatted with locale separators.\n     * @returns {string} Degrees/minutes/seconds formatted with standard Javascript separators.\n     *\n     * @example\n     *   const lat = Dms.fromLocale('51°28′40,12″N');                          // '51°28′40.12″N' in France\n     *   const p = new LatLon(Dms.fromLocale('51°28′40,37″N, 000°00′05,29″W'); // '51.4779°N, 000.0015°W' in France\n     */\n    static fromLocale(str) {\n        const locale = (123456.789).toLocaleString();\n        const separator = { thousands: locale.slice(3, 4), decimal: locale.slice(7, 8) };\n        return str.replace(separator.thousands, '⁜').replace(separator.decimal, '.').replace('⁜', ',');\n    }\n\n\n    /**\n     * Converts DMS string from JavaScript comma/dot thousands/decimal separators to locale separators.\n     *\n     * Can also be used to format standard numbers such as distances.\n     *\n     * @param   {string} str - Degrees/minutes/seconds formatted with standard Javascript separators.\n     * @returns {string} Degrees/minutes/seconds formatted with locale separators.\n     *\n     * @example\n     *   const Dms.toLocale('123,456.789');                   // '123.456,789' in France\n     *   const Dms.toLocale('51°28′40.12″N, 000°00′05.31″W'); // '51°28′40,12″N, 000°00′05,31″W' in France\n     */\n    static toLocale(str) {\n        const locale = (123456.789).toLocaleString();\n        const separator = { thousands: locale.slice(3, 4), decimal: locale.slice(7, 8) };\n        return str.replace(/,([0-9])/, '⁜$1').replace('.', separator.decimal).replace('⁜', separator.thousands);\n    }\n\n\n    /**\n     * Returns compass point (to given precision) for supplied bearing.\n     *\n     * @param   {number} bearing - Bearing in degrees from north.\n     * @param   {number} [precision=3] - Precision (1:cardinal / 2:intercardinal / 3:secondary-intercardinal).\n     * @returns {string} Compass point for supplied bearing.\n     *\n     * @example\n     *   const point = Dms.compassPoint(24);    // point = 'NNE'\n     *   const point = Dms.compassPoint(24, 1); // point = 'N'\n     */\n    static compassPoint(bearing, precision=3) {\n        if (![ 1, 2, 3 ].includes(Number(precision))) throw new RangeError(`invalid precision ‘${precision}’`);\n        // note precision could be extended to 4 for quarter-winds (eg NbNW), but I think they are little used\n\n        bearing = Dms.wrap360(bearing); // normalise to range 0..360°\n\n        const cardinals = [\n            'N', 'NNE', 'NE', 'ENE',\n            'E', 'ESE', 'SE', 'SSE',\n            'S', 'SSW', 'SW', 'WSW',\n            'W', 'WNW', 'NW', 'NNW' ];\n        const n = 4 * 2**(precision-1); // no of compass points at req’d precision (1=>4, 2=>8, 3=>16)\n        const cardinal = cardinals[Math.round(bearing*n/360)%n * 16/n];\n\n        return cardinal;\n    }\n\n\n    /**\n     * Constrain degrees to range -90..+90 (for latitude); e.g. -91 => -89, 91 => 89.\n     *\n     * @private\n     * @param {number} degrees\n     * @returns degrees within range -90..+90.\n     */\n    static wrap90(degrees) {\n        if (-90<=degrees && degrees<=90) return degrees; // avoid rounding due to arithmetic ops if within range\n\n        // latitude wrapping requires a triangle wave function; a general triangle wave is\n        //     f(x) = 4a/p ⋅ | (x-p/4)%p - p/2 | - a\n        // where a = amplitude, p = period, % = modulo; however, JavaScript '%' is a remainder operator\n        // not a modulo operator - for modulo, replace 'x%n' with '((x%n)+n)%n'\n        const x = degrees, a = 90, p = 360;\n        return 4*a/p * Math.abs((((x-p/4)%p)+p)%p - p/2) - a;\n    }\n\n    /**\n     * Constrain degrees to range -180..+180 (for longitude); e.g. -181 => 179, 181 => -179.\n     *\n     * @private\n     * @param {number} degrees\n     * @returns degrees within range -180..+180.\n     */\n    static wrap180(degrees) {\n        if (-180<=degrees && degrees<=180) return degrees; // avoid rounding due to arithmetic ops if within range\n\n        // longitude wrapping requires a sawtooth wave function; a general sawtooth wave is\n        //     f(x) = (2ax/p - p/2) % p - a\n        // where a = amplitude, p = period, % = modulo; however, JavaScript '%' is a remainder operator\n        // not a modulo operator - for modulo, replace 'x%n' with '((x%n)+n)%n'\n        const x = degrees, a = 180, p = 360;\n        return (((2*a*x/p - p/2)%p)+p)%p - a;\n    }\n\n    /**\n     * Constrain degrees to range 0..360 (for bearings); e.g. -1 => 359, 361 => 1.\n     *\n     * @private\n     * @param {number} degrees\n     * @returns degrees within range 0..360.\n     */\n    static wrap360(degrees) {\n        if (0<=degrees && degrees<360) return degrees; // avoid rounding due to arithmetic ops if within range\n\n        // bearing wrapping requires a sawtooth wave function with a vertical offset equal to the\n        // amplitude and a corresponding phase shift; this changes the general sawtooth wave function from\n        //     f(x) = (2ax/p - p/2) % p - a\n        // to\n        //     f(x) = (2ax/p) % p\n        // where a = amplitude, p = period, % = modulo; however, JavaScript '%' is a remainder operator\n        // not a modulo operator - for modulo, replace 'x%n' with '((x%n)+n)%n'\n        const x = degrees, a = 180, p = 360;\n        return (((2*a*x/p)%p)+p)%p;\n    }\n\n}\n\n\n// Extend Number object with methods to convert between degrees & radians\nNumber.prototype.toRadians = function() { return this * Math.PI / 180; };\nNumber.prototype.toDegrees = function() { return this * 180 / Math.PI; };\n\n/* - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */\n\nexport default Dms;\n", "/* - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */\n/* Latitude/longitude spherical geodesy tools                         (c) <PERSON> 2002-2021  */\n/*                                                                                   MIT Licence  */\n/* www.movable-type.co.uk/scripts/latlong.html                                                    */\n/* www.movable-type.co.uk/scripts/geodesy-library.html#latlon-spherical                           */\n/* - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */\n\nimport Dms from './dms.js';\n\nconst π = Math.PI;\n\n\n/**\n * Library of geodesy functions for operations on a spherical earth model.\n *\n * Includes distances, bearings, destinations, etc, for both great circle paths and rhumb lines,\n * and other related functions.\n *\n * All calculations are done using simple spherical trigonometric formulae.\n *\n * @module latlon-spherical\n */\n\n// note greek letters (e.g. φ, λ, θ) are used for angles in radians to distinguish from angles in\n// degrees (e.g. lat, lon, brng)\n\n\n/* LatLonSpherical - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */\n\n\n/**\n * Latitude/longitude points on a spherical model earth, and methods for calculating distances,\n * bearings, destinations, etc on (orthodromic) great-circle paths and (loxodromic) rhumb lines.\n */\nclass LatLonSpherical {\n\n    /**\n     * Creates a latitude/longitude point on the earth’s surface, using a spherical model earth.\n     *\n     * @param  {number} lat - Latitude (in degrees).\n     * @param  {number} lon - Longitude (in degrees).\n     * @throws {TypeError} Invalid lat/lon.\n     *\n     * @example\n     *   import LatLon from '/js/geodesy/latlon-spherical.js';\n     *   const p = new LatLon(52.205, 0.119);\n     */\n    constructor(lat, lon) {\n        if (isNaN(lat)) throw new TypeError(`invalid lat ‘${lat}’`);\n        if (isNaN(lon)) throw new TypeError(`invalid lon ‘${lon}’`);\n\n        this._lat = Dms.wrap90(Number(lat));\n        this._lon = Dms.wrap180(Number(lon));\n    }\n\n\n    /**\n     * Latitude in degrees north from equator (including aliases lat, latitude): can be set as\n     * numeric or hexagesimal (deg-min-sec); returned as numeric.\n     */\n    get lat()       { return this._lat; }\n    get latitude()  { return this._lat; }\n    set lat(lat) {\n        this._lat = isNaN(lat) ? Dms.wrap90(Dms.parse(lat)) : Dms.wrap90(Number(lat));\n        if (isNaN(this._lat)) throw new TypeError(`invalid lat ‘${lat}’`);\n    }\n    set latitude(lat) {\n        this._lat = isNaN(lat) ? Dms.wrap90(Dms.parse(lat)) : Dms.wrap90(Number(lat));\n        if (isNaN(this._lat)) throw new TypeError(`invalid latitude ‘${lat}’`);\n    }\n\n    /**\n     * Longitude in degrees east from international reference meridian (including aliases lon, lng,\n     * longitude): can be set as numeric or hexagesimal (deg-min-sec); returned as numeric.\n     */\n    get lon()       { return this._lon; }\n    get lng()       { return this._lon; }\n    get longitude() { return this._lon; }\n    set lon(lon) {\n        this._lon = isNaN(lon) ? Dms.wrap180(Dms.parse(lon)) : Dms.wrap180(Number(lon));\n        if (isNaN(this._lon)) throw new TypeError(`invalid lon ‘${lon}’`);\n    }\n    set lng(lon) {\n        this._lon = isNaN(lon) ? Dms.wrap180(Dms.parse(lon)) : Dms.wrap180(Number(lon));\n        if (isNaN(this._lon)) throw new TypeError(`invalid lng ‘${lon}’`);\n    }\n    set longitude(lon) {\n        this._lon = isNaN(lon) ? Dms.wrap180(Dms.parse(lon)) : Dms.wrap180(Number(lon));\n        if (isNaN(this._lon)) throw new TypeError(`invalid longitude ‘${lon}’`);\n    }\n\n\n    /** Conversion factors; 1000 * LatLon.metresToKm gives 1. */\n    static get metresToKm()            { return 1/1000; }\n    /** Conversion factors; 1000 * LatLon.metresToMiles gives 0.621371192237334. */\n    static get metresToMiles()         { return 1/1609.344; }\n    /** Conversion factors; 1000 * LatLon.metresToMiles gives 0.5399568034557236. */\n    static get metresToNauticalMiles() { return 1/1852; }\n\n\n    /**\n     * Parses a latitude/longitude point from a variety of formats.\n     *\n     * Latitude & longitude (in degrees) can be supplied as two separate parameters, as a single\n     * comma-separated lat/lon string, or as a single object with { lat, lon } or GeoJSON properties.\n     *\n     * The latitude/longitude values may be numeric or strings; they may be signed decimal or\n     * deg-min-sec (hexagesimal) suffixed by compass direction (NSEW); a variety of separators are\n     * accepted. Examples -3.62, '3 37 12W', '3°37′12″W'.\n     *\n     * Thousands/decimal separators must be comma/dot; use Dms.fromLocale to convert locale-specific\n     * thousands/decimal separators.\n     *\n     * @param   {number|string|Object} lat|latlon - Latitude (in degrees) or comma-separated lat/lon or lat/lon object.\n     * @param   {number|string}        [lon]      - Longitude (in degrees).\n     * @returns {LatLon} Latitude/longitude point.\n     * @throws  {TypeError} Invalid point.\n     *\n     * @example\n     *   const p1 = LatLon.parse(52.205, 0.119);                                    // numeric pair (≡ new LatLon)\n     *   const p2 = LatLon.parse('52.205', '0.119');                                // numeric string pair (≡ new LatLon)\n     *   const p3 = LatLon.parse('52.205, 0.119');                                  // single string numerics\n     *   const p4 = LatLon.parse('52°12′18.0″N', '000°07′08.4″E');                  // DMS pair\n     *   const p5 = LatLon.parse('52°12′18.0″N, 000°07′08.4″E');                    // single string DMS\n     *   const p6 = LatLon.parse({ lat: 52.205, lon: 0.119 });                      // { lat, lon } object numeric\n     *   const p7 = LatLon.parse({ lat: '52°12′18.0″N', lng: '000°07′08.4″E' });    // { lat, lng } object DMS\n     *   const p8 = LatLon.parse({ type: 'Point', coordinates: [ 0.119, 52.205] }); // GeoJSON\n     */\n    static parse(...args) {\n        if (args.length == 0) throw new TypeError('invalid (empty) point');\n        if (args[0]===null || args[1]===null) throw new TypeError('invalid (null) point');\n\n        let lat=undefined, lon=undefined;\n\n        if (args.length == 2) { // regular (lat, lon) arguments\n            [ lat, lon ] = args;\n            lat = Dms.wrap90(Dms.parse(lat));\n            lon = Dms.wrap180(Dms.parse(lon));\n            if (isNaN(lat) || isNaN(lon)) throw new TypeError(`invalid point ‘${args.toString()}’`);\n        }\n\n        if (args.length == 1 && typeof args[0] == 'string') { // single comma-separated lat,lon string\n            [ lat, lon ] = args[0].split(',');\n            lat = Dms.wrap90(Dms.parse(lat));\n            lon = Dms.wrap180(Dms.parse(lon));\n            if (isNaN(lat) || isNaN(lon)) throw new TypeError(`invalid point ‘${args[0]}’`);\n        }\n\n        if (args.length == 1 && typeof args[0] == 'object') { // single { lat, lon } object\n            const ll = args[0];\n            if (ll.type == 'Point' && Array.isArray(ll.coordinates)) { // GeoJSON\n                [ lon, lat ] = ll.coordinates;\n            } else { // regular { lat, lon } object\n                if (ll.latitude  != undefined) lat = ll.latitude;\n                if (ll.lat       != undefined) lat = ll.lat;\n                if (ll.longitude != undefined) lon = ll.longitude;\n                if (ll.lng       != undefined) lon = ll.lng;\n                if (ll.lon       != undefined) lon = ll.lon;\n                lat = Dms.wrap90(Dms.parse(lat));\n                lon = Dms.wrap180(Dms.parse(lon));\n            }\n            if (isNaN(lat) || isNaN(lon)) throw new TypeError(`invalid point ‘${JSON.stringify(args[0])}’`);\n        }\n\n        if (isNaN(lat) || isNaN(lon)) throw new TypeError(`invalid point ‘${args.toString()}’`);\n\n        return new LatLonSpherical(lat, lon);\n    }\n\n\n    /**\n     * Returns the distance along the surface of the earth from ‘this’ point to destination point.\n     *\n     * Uses haversine formula: a = sin²(Δφ/2) + cosφ1·cosφ2 · sin²(Δλ/2); d = 2 · atan2(√a, √(a-1)).\n     *\n     * @param   {LatLon} point - Latitude/longitude of destination point.\n     * @param   {number} [radius=6371e3] - Radius of earth (defaults to mean radius in metres).\n     * @returns {number} Distance between this point and destination point, in same units as radius.\n     * @throws  {TypeError} Invalid radius.\n     *\n     * @example\n     *   const p1 = new LatLon(52.205, 0.119);\n     *   const p2 = new LatLon(48.857, 2.351);\n     *   const d = p1.distanceTo(p2);       // 404.3×10³ m\n     *   const m = p1.distanceTo(p2, 3959); // 251.2 miles\n     */\n    distanceTo(point, radius=6371e3) {\n        if (!(point instanceof LatLonSpherical)) point = LatLonSpherical.parse(point); // allow literal forms\n        if (isNaN(radius)) throw new TypeError(`invalid radius ‘${radius}’`);\n\n        // a = sin²(Δφ/2) + cos(φ1)⋅cos(φ2)⋅sin²(Δλ/2)\n        // δ = 2·atan2(√(a), √(1−a))\n        // see mathforum.org/library/drmath/view/51879.html for derivation\n\n        const R = radius;\n        const φ1 = this.lat.toRadians(),  λ1 = this.lon.toRadians();\n        const φ2 = point.lat.toRadians(), λ2 = point.lon.toRadians();\n        const Δφ = φ2 - φ1;\n        const Δλ = λ2 - λ1;\n\n        const a = Math.sin(Δφ/2)*Math.sin(Δφ/2) + Math.cos(φ1)*Math.cos(φ2) * Math.sin(Δλ/2)*Math.sin(Δλ/2);\n        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n        const d = R * c;\n\n        return d;\n    }\n\n\n    /**\n     * Returns the initial bearing from ‘this’ point to destination point.\n     *\n     * @param   {LatLon} point - Latitude/longitude of destination point.\n     * @returns {number} Initial bearing in degrees from north (0°..360°).\n     *\n     * @example\n     *   const p1 = new LatLon(52.205, 0.119);\n     *   const p2 = new LatLon(48.857, 2.351);\n     *   const b1 = p1.initialBearingTo(p2); // 156.2°\n     */\n    initialBearingTo(point) {\n        if (!(point instanceof LatLonSpherical)) point = LatLonSpherical.parse(point); // allow literal forms\n        if (this.equals(point)) return NaN; // coincident points\n\n        // tanθ = sinΔλ⋅cosφ2 / cosφ1⋅sinφ2 − sinφ1⋅cosφ2⋅cosΔλ\n        // see mathforum.org/library/drmath/view/55417.html for derivation\n\n        const φ1 = this.lat.toRadians();\n        const φ2 = point.lat.toRadians();\n        const Δλ = (point.lon - this.lon).toRadians();\n\n        const x = Math.cos(φ1) * Math.sin(φ2) - Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);\n        const y = Math.sin(Δλ) * Math.cos(φ2);\n        const θ = Math.atan2(y, x);\n\n        const bearing = θ.toDegrees();\n\n        return Dms.wrap360(bearing);\n    }\n\n\n    /**\n     * Returns final bearing arriving at destination point from ‘this’ point; the final bearing will\n     * differ from the initial bearing by varying degrees according to distance and latitude.\n     *\n     * @param   {LatLon} point - Latitude/longitude of destination point.\n     * @returns {number} Final bearing in degrees from north (0°..360°).\n     *\n     * @example\n     *   const p1 = new LatLon(52.205, 0.119);\n     *   const p2 = new LatLon(48.857, 2.351);\n     *   const b2 = p1.finalBearingTo(p2); // 157.9°\n     */\n    finalBearingTo(point) {\n        if (!(point instanceof LatLonSpherical)) point = LatLonSpherical.parse(point); // allow literal forms\n\n        // get initial bearing from destination point to this point & reverse it by adding 180°\n\n        const bearing = point.initialBearingTo(this) + 180;\n\n        return Dms.wrap360(bearing);\n    }\n\n\n    /**\n     * Returns the midpoint between ‘this’ point and destination point.\n     *\n     * @param   {LatLon} point - Latitude/longitude of destination point.\n     * @returns {LatLon} Midpoint between this point and destination point.\n     *\n     * @example\n     *   const p1 = new LatLon(52.205, 0.119);\n     *   const p2 = new LatLon(48.857, 2.351);\n     *   const pMid = p1.midpointTo(p2); // 50.5363°N, 001.2746°E\n     */\n    midpointTo(point) {\n        if (!(point instanceof LatLonSpherical)) point = LatLonSpherical.parse(point); // allow literal forms\n\n        // φm = atan2( sinφ1 + sinφ2, √( (cosφ1 + cosφ2⋅cosΔλ)² + cos²φ2⋅sin²Δλ ) )\n        // λm = λ1 + atan2(cosφ2⋅sinΔλ, cosφ1 + cosφ2⋅cosΔλ)\n        // midpoint is sum of vectors to two points: mathforum.org/library/drmath/view/51822.html\n\n        const φ1 = this.lat.toRadians();\n        const λ1 = this.lon.toRadians();\n        const φ2 = point.lat.toRadians();\n        const Δλ = (point.lon - this.lon).toRadians();\n\n        // get cartesian coordinates for the two points\n        const A = { x: Math.cos(φ1), y: 0, z: Math.sin(φ1) }; // place point A on prime meridian y=0\n        const B = { x: Math.cos(φ2)*Math.cos(Δλ), y: Math.cos(φ2)*Math.sin(Δλ), z: Math.sin(φ2) };\n\n        // vector to midpoint is sum of vectors to two points (no need to normalise)\n        const C = { x: A.x + B.x, y: A.y + B.y, z: A.z + B.z };\n\n        const φm = Math.atan2(C.z, Math.sqrt(C.x*C.x + C.y*C.y));\n        const λm = λ1 + Math.atan2(C.y, C.x);\n\n        const lat = φm.toDegrees();\n        const lon = λm.toDegrees();\n\n        return new LatLonSpherical(lat, lon);\n    }\n\n\n    /**\n     * Returns the point at given fraction between ‘this’ point and given point.\n     *\n     * @param   {LatLon} point - Latitude/longitude of destination point.\n     * @param   {number} fraction - Fraction between the two points (0 = this point, 1 = specified point).\n     * @returns {LatLon} Intermediate point between this point and destination point.\n     *\n     * @example\n     *   const p1 = new LatLon(52.205, 0.119);\n     *   const p2 = new LatLon(48.857, 2.351);\n     *   const pInt = p1.intermediatePointTo(p2, 0.25); // 51.3721°N, 000.7073°E\n     */\n    intermediatePointTo(point, fraction) {\n        if (!(point instanceof LatLonSpherical)) point = LatLonSpherical.parse(point); // allow literal forms\n        if (this.equals(point)) return new LatLonSpherical(this.lat, this.lon); // coincident points\n\n        const φ1 = this.lat.toRadians(), λ1 = this.lon.toRadians();\n        const φ2 = point.lat.toRadians(), λ2 = point.lon.toRadians();\n\n        // distance between points\n        const Δφ = φ2 - φ1;\n        const Δλ = λ2 - λ1;\n        const a = Math.sin(Δφ/2) * Math.sin(Δφ/2)\n            + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ/2) * Math.sin(Δλ/2);\n        const δ = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n\n        const A = Math.sin((1-fraction)*δ) / Math.sin(δ);\n        const B = Math.sin(fraction*δ) / Math.sin(δ);\n\n        const x = A * Math.cos(φ1) * Math.cos(λ1) + B * Math.cos(φ2) * Math.cos(λ2);\n        const y = A * Math.cos(φ1) * Math.sin(λ1) + B * Math.cos(φ2) * Math.sin(λ2);\n        const z = A * Math.sin(φ1) + B * Math.sin(φ2);\n\n        const φ3 = Math.atan2(z, Math.sqrt(x*x + y*y));\n        const λ3 = Math.atan2(y, x);\n\n        const lat = φ3.toDegrees();\n        const lon = λ3.toDegrees();\n\n        return new LatLonSpherical(lat, lon);\n    }\n\n\n    /**\n     * Returns the destination point from ‘this’ point having travelled the given distance on the\n     * given initial bearing (bearing normally varies around path followed).\n     *\n     * @param   {number} distance - Distance travelled, in same units as earth radius (default: metres).\n     * @param   {number} bearing - Initial bearing in degrees from north.\n     * @param   {number} [radius=6371e3] - (Mean) radius of earth (defaults to radius in metres).\n     * @returns {LatLon} Destination point.\n     *\n     * @example\n     *   const p1 = new LatLon(51.47788, -0.00147);\n     *   const p2 = p1.destinationPoint(7794, 300.7); // 51.5136°N, 000.0983°W\n     */\n    destinationPoint(distance, bearing, radius=6371e3) {\n        // sinφ2 = sinφ1⋅cosδ + cosφ1⋅sinδ⋅cosθ\n        // tanΔλ = sinθ⋅sinδ⋅cosφ1 / cosδ−sinφ1⋅sinφ2\n        // see mathforum.org/library/drmath/view/52049.html for derivation\n\n        const δ = distance / radius; // angular distance in radians\n        const θ = Number(bearing).toRadians();\n\n        const φ1 = this.lat.toRadians(), λ1 = this.lon.toRadians();\n\n        const sinφ2 = Math.sin(φ1) * Math.cos(δ) + Math.cos(φ1) * Math.sin(δ) * Math.cos(θ);\n        const φ2 = Math.asin(sinφ2);\n        const y = Math.sin(θ) * Math.sin(δ) * Math.cos(φ1);\n        const x = Math.cos(δ) - Math.sin(φ1) * sinφ2;\n        const λ2 = λ1 + Math.atan2(y, x);\n\n        const lat = φ2.toDegrees();\n        const lon = λ2.toDegrees();\n\n        return new LatLonSpherical(lat, lon);\n    }\n\n\n    /**\n     * Returns the point of intersection of two paths defined by point and bearing.\n     *\n     * @param   {LatLon}      p1 - First point.\n     * @param   {number}      brng1 - Initial bearing from first point.\n     * @param   {LatLon}      p2 - Second point.\n     * @param   {number}      brng2 - Initial bearing from second point.\n     * @returns {LatLon|null} Destination point (null if no unique intersection defined).\n     *\n     * @example\n     *   const p1 = new LatLon(51.8853, 0.2545), brng1 = 108.547;\n     *   const p2 = new LatLon(49.0034, 2.5735), brng2 =  32.435;\n     *   const pInt = LatLon.intersection(p1, brng1, p2, brng2); // 50.9078°N, 004.5084°E\n     */\n    static intersection(p1, brng1, p2, brng2) {\n        if (!(p1 instanceof LatLonSpherical)) p1 = LatLonSpherical.parse(p1); // allow literal forms\n        if (!(p2 instanceof LatLonSpherical)) p2 = LatLonSpherical.parse(p2); // allow literal forms\n        if (isNaN(brng1)) throw new TypeError(`invalid brng1 ‘${brng1}’`);\n        if (isNaN(brng2)) throw new TypeError(`invalid brng2 ‘${brng2}’`);\n\n        // see www.edwilliams.org/avform.htm#Intersection\n\n        const φ1 = p1.lat.toRadians(), λ1 = p1.lon.toRadians();\n        const φ2 = p2.lat.toRadians(), λ2 = p2.lon.toRadians();\n        const θ13 = Number(brng1).toRadians(), θ23 = Number(brng2).toRadians();\n        const Δφ = φ2 - φ1, Δλ = λ2 - λ1;\n\n        // angular distance p1-p2\n        const δ12 = 2 * Math.asin(Math.sqrt(Math.sin(Δφ/2) * Math.sin(Δφ/2)\n            + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ/2) * Math.sin(Δλ/2)));\n        if (Math.abs(δ12) < Number.EPSILON) return new LatLonSpherical(p1.lat, p1.lon); // coincident points\n\n        // initial/final bearings between points\n        const cosθa = (Math.sin(φ2) - Math.sin(φ1)*Math.cos(δ12)) / (Math.sin(δ12)*Math.cos(φ1));\n        const cosθb = (Math.sin(φ1) - Math.sin(φ2)*Math.cos(δ12)) / (Math.sin(δ12)*Math.cos(φ2));\n        const θa = Math.acos(Math.min(Math.max(cosθa, -1), 1)); // protect against rounding errors\n        const θb = Math.acos(Math.min(Math.max(cosθb, -1), 1)); // protect against rounding errors\n\n        const θ12 = Math.sin(λ2-λ1)>0 ? θa : 2*π-θa;\n        const θ21 = Math.sin(λ2-λ1)>0 ? 2*π-θb : θb;\n\n        const α1 = θ13 - θ12; // angle 2-1-3\n        const α2 = θ21 - θ23; // angle 1-2-3\n\n        if (Math.sin(α1) == 0 && Math.sin(α2) == 0) return null; // infinite intersections\n        if (Math.sin(α1) * Math.sin(α2) < 0) return null;        // ambiguous intersection (antipodal/360°)\n\n        const cosα3 = -Math.cos(α1)*Math.cos(α2) + Math.sin(α1)*Math.sin(α2)*Math.cos(δ12);\n\n        const δ13 = Math.atan2(Math.sin(δ12)*Math.sin(α1)*Math.sin(α2), Math.cos(α2) + Math.cos(α1)*cosα3);\n\n        const φ3 = Math.asin(Math.min(Math.max(Math.sin(φ1)*Math.cos(δ13) + Math.cos(φ1)*Math.sin(δ13)*Math.cos(θ13), -1), 1));\n\n        const Δλ13 = Math.atan2(Math.sin(θ13)*Math.sin(δ13)*Math.cos(φ1), Math.cos(δ13) - Math.sin(φ1)*Math.sin(φ3));\n        const λ3 = λ1 + Δλ13;\n\n        const lat = φ3.toDegrees();\n        const lon = λ3.toDegrees();\n\n        return new LatLonSpherical(lat, lon);\n    }\n\n\n    /**\n     * Returns (signed) distance from ‘this’ point to great circle defined by start-point and\n     * end-point.\n     *\n     * @param   {LatLon} pathStart - Start point of great circle path.\n     * @param   {LatLon} pathEnd - End point of great circle path.\n     * @param   {number} [radius=6371e3] - (Mean) radius of earth (defaults to radius in metres).\n     * @returns {number} Distance to great circle (-ve if to left, +ve if to right of path).\n     *\n     * @example\n     *   const pCurrent = new LatLon(53.2611, -0.7972);\n     *   const p1 = new LatLon(53.3206, -1.7297);\n     *   const p2 = new LatLon(53.1887, 0.1334);\n     *   const d = pCurrent.crossTrackDistanceTo(p1, p2);  // -307.5 m\n     */\n    crossTrackDistanceTo(pathStart, pathEnd, radius=6371e3) {\n        if (!(pathStart instanceof LatLonSpherical)) pathStart = LatLonSpherical.parse(pathStart); // allow literal forms\n        if (!(pathEnd instanceof LatLonSpherical)) pathEnd = LatLonSpherical.parse(pathEnd);       // allow literal forms\n        const R = radius;\n\n        if (this.equals(pathStart)) return 0;\n\n        const δ13 = pathStart.distanceTo(this, R) / R;\n        const θ13 = pathStart.initialBearingTo(this).toRadians();\n        const θ12 = pathStart.initialBearingTo(pathEnd).toRadians();\n\n        const δxt = Math.asin(Math.sin(δ13) * Math.sin(θ13 - θ12));\n\n        return δxt * R;\n    }\n\n\n    /**\n     * Returns how far ‘this’ point is along a path from from start-point, heading towards end-point.\n     * That is, if a perpendicular is drawn from ‘this’ point to the (great circle) path, the\n     * along-track distance is the distance from the start point to where the perpendicular crosses\n     * the path.\n     *\n     * @param   {LatLon} pathStart - Start point of great circle path.\n     * @param   {LatLon} pathEnd - End point of great circle path.\n     * @param   {number} [radius=6371e3] - (Mean) radius of earth (defaults to radius in metres).\n     * @returns {number} Distance along great circle to point nearest ‘this’ point.\n     *\n     * @example\n     *   const pCurrent = new LatLon(53.2611, -0.7972);\n     *   const p1 = new LatLon(53.3206, -1.7297);\n     *   const p2 = new LatLon(53.1887,  0.1334);\n     *   const d = pCurrent.alongTrackDistanceTo(p1, p2);  // 62.331 km\n     */\n    alongTrackDistanceTo(pathStart, pathEnd, radius=6371e3) {\n        if (!(pathStart instanceof LatLonSpherical)) pathStart = LatLonSpherical.parse(pathStart); // allow literal forms\n        if (!(pathEnd instanceof LatLonSpherical)) pathEnd = LatLonSpherical.parse(pathEnd);       // allow literal forms\n        const R = radius;\n\n        if (this.equals(pathStart)) return 0;\n\n        const δ13 = pathStart.distanceTo(this, R) / R;\n        const θ13 = pathStart.initialBearingTo(this).toRadians();\n        const θ12 = pathStart.initialBearingTo(pathEnd).toRadians();\n\n        const δxt = Math.asin(Math.sin(δ13) * Math.sin(θ13-θ12));\n\n        const δat = Math.acos(Math.cos(δ13) / Math.abs(Math.cos(δxt)));\n\n        return δat*Math.sign(Math.cos(θ12-θ13)) * R;\n    }\n\n\n    /**\n     * Returns maximum latitude reached when travelling on a great circle on given bearing from\n     * ‘this’ point (‘Clairaut’s formula’). Negate the result for the minimum latitude (in the\n     * southern hemisphere).\n     *\n     * The maximum latitude is independent of longitude; it will be the same for all points on a\n     * given latitude.\n     *\n     * @param   {number} bearing - Initial bearing.\n     * @returns {number} Maximum latitude reached.\n     */\n    maxLatitude(bearing) {\n        const θ = Number(bearing).toRadians();\n\n        const φ = this.lat.toRadians();\n\n        const φMax = Math.acos(Math.abs(Math.sin(θ) * Math.cos(φ)));\n\n        return φMax.toDegrees();\n    }\n\n\n    /**\n     * Returns the pair of meridians at which a great circle defined by two points crosses the given\n     * latitude. If the great circle doesn't reach the given latitude, null is returned.\n     *\n     * @param   {LatLon}      point1 - First point defining great circle.\n     * @param   {LatLon}      point2 - Second point defining great circle.\n     * @param   {number}      latitude - Latitude crossings are to be determined for.\n     * @returns {Object|null} Object containing { lon1, lon2 } or null if given latitude not reached.\n     */\n    static crossingParallels(point1, point2, latitude) {\n        if (point1.equals(point2)) return null; // coincident points\n\n        const φ = Number(latitude).toRadians();\n\n        const φ1 = point1.lat.toRadians();\n        const λ1 = point1.lon.toRadians();\n        const φ2 = point2.lat.toRadians();\n        const λ2 = point2.lon.toRadians();\n\n        const Δλ = λ2 - λ1;\n\n        const x = Math.sin(φ1) * Math.cos(φ2) * Math.cos(φ) * Math.sin(Δλ);\n        const y = Math.sin(φ1) * Math.cos(φ2) * Math.cos(φ) * Math.cos(Δλ) - Math.cos(φ1) * Math.sin(φ2) * Math.cos(φ);\n        const z = Math.cos(φ1) * Math.cos(φ2) * Math.sin(φ) * Math.sin(Δλ);\n\n        if (z * z > x * x + y * y) return null; // great circle doesn't reach latitude\n\n        const λm = Math.atan2(-y, x);               // longitude at max latitude\n        const Δλi = Math.acos(z / Math.sqrt(x*x + y*y)); // Δλ from λm to intersection points\n\n        const λi1 = λ1 + λm - Δλi;\n        const λi2 = λ1 + λm + Δλi;\n\n        const lon1 = λi1.toDegrees();\n        const lon2 = λi2.toDegrees();\n\n        return {\n            lon1: Dms.wrap180(lon1),\n            lon2: Dms.wrap180(lon2),\n        };\n    }\n\n\n    /* Rhumb - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */\n\n\n    /**\n     * Returns the distance travelling from ‘this’ point to destination point along a rhumb line.\n     *\n     * @param   {LatLon} point - Latitude/longitude of destination point.\n     * @param   {number} [radius=6371e3] - (Mean) radius of earth (defaults to radius in metres).\n     * @returns {number} Distance in km between this point and destination point (same units as radius).\n     *\n     * @example\n     *   const p1 = new LatLon(51.127, 1.338);\n     *   const p2 = new LatLon(50.964, 1.853);\n     *   const d = p1.distanceTo(p2); //  40.31 km\n     */\n    rhumbDistanceTo(point, radius=6371e3) {\n        if (!(point instanceof LatLonSpherical)) point = LatLonSpherical.parse(point); // allow literal forms\n\n        // see www.edwilliams.org/avform.htm#Rhumb\n\n        const R = radius;\n        const φ1 = this.lat.toRadians();\n        const φ2 = point.lat.toRadians();\n        const Δφ = φ2 - φ1;\n        let Δλ = Math.abs(point.lon - this.lon).toRadians();\n        // if dLon over 180° take shorter rhumb line across the anti-meridian:\n        if (Math.abs(Δλ) > π) Δλ = Δλ > 0 ? -(2 * π - Δλ) : (2 * π + Δλ);\n\n        // on Mercator projection, longitude distances shrink by latitude; q is the 'stretch factor'\n        // q becomes ill-conditioned along E-W line (0/0); use empirical tolerance to avoid it (note ε is too small)\n        const Δψ = Math.log(Math.tan(φ2 / 2 + π / 4) / Math.tan(φ1 / 2 + π / 4));\n        const q = Math.abs(Δψ) > 10e-12 ? Δφ / Δψ : Math.cos(φ1);\n\n        // distance is pythagoras on 'stretched' Mercator projection, √(Δφ² + q²·Δλ²)\n        const δ = Math.sqrt(Δφ*Δφ + q*q * Δλ*Δλ); // angular distance in radians\n        const d = δ * R;\n\n        return d;\n    }\n\n\n    /**\n     * Returns the bearing from ‘this’ point to destination point along a rhumb line.\n     *\n     * @param   {LatLon}    point - Latitude/longitude of destination point.\n     * @returns {number}    Bearing in degrees from north.\n     *\n     * @example\n     *   const p1 = new LatLon(51.127, 1.338);\n     *   const p2 = new LatLon(50.964, 1.853);\n     *   const d = p1.rhumbBearingTo(p2); // 116.7°\n     */\n    rhumbBearingTo(point) {\n        if (!(point instanceof LatLonSpherical)) point = LatLonSpherical.parse(point); // allow literal forms\n        if (this.equals(point)) return NaN; // coincident points\n\n        const φ1 = this.lat.toRadians();\n        const φ2 = point.lat.toRadians();\n        let Δλ = (point.lon - this.lon).toRadians();\n        // if dLon over 180° take shorter rhumb line across the anti-meridian:\n        if (Math.abs(Δλ) > π) Δλ = Δλ > 0 ? -(2 * π - Δλ) : (2 * π + Δλ);\n\n        const Δψ = Math.log(Math.tan(φ2 / 2 + π / 4) / Math.tan(φ1 / 2 + π / 4));\n\n        const θ = Math.atan2(Δλ, Δψ);\n\n        const bearing = θ.toDegrees();\n\n        return Dms.wrap360(bearing);\n    }\n\n\n    /**\n     * Returns the destination point having travelled along a rhumb line from ‘this’ point the given\n     * distance on the given bearing.\n     *\n     * @param   {number} distance - Distance travelled, in same units as earth radius (default: metres).\n     * @param   {number} bearing - Bearing in degrees from north.\n     * @param   {number} [radius=6371e3] - (Mean) radius of earth (defaults to radius in metres).\n     * @returns {LatLon} Destination point.\n     *\n     * @example\n     *   const p1 = new LatLon(51.127, 1.338);\n     *   const p2 = p1.rhumbDestinationPoint(40300, 116.7); // 50.9642°N, 001.8530°E\n     */\n    rhumbDestinationPoint(distance, bearing, radius=6371e3) {\n        const φ1 = this.lat.toRadians(), λ1 = this.lon.toRadians();\n        const θ = Number(bearing).toRadians();\n\n        const δ = distance / radius; // angular distance in radians\n\n        const Δφ = δ * Math.cos(θ);\n        let φ2 = φ1 + Δφ;\n\n        // check for some daft bugger going past the pole, normalise latitude if so\n        if (Math.abs(φ2) > π / 2) φ2 = φ2 > 0 ? π - φ2 : -π - φ2;\n\n        const Δψ = Math.log(Math.tan(φ2 / 2 + π / 4) / Math.tan(φ1 / 2 + π / 4));\n        const q = Math.abs(Δψ) > 10e-12 ? Δφ / Δψ : Math.cos(φ1); // E-W course becomes ill-conditioned with 0/0\n\n        const Δλ = δ * Math.sin(θ) / q;\n        const λ2 = λ1 + Δλ;\n\n        const lat = φ2.toDegrees();\n        const lon = λ2.toDegrees();\n\n        return new LatLonSpherical(lat, lon);\n    }\n\n\n    /**\n     * Returns the loxodromic midpoint (along a rhumb line) between ‘this’ point and second point.\n     *\n     * @param   {LatLon} point - Latitude/longitude of second point.\n     * @returns {LatLon} Midpoint between this point and second point.\n     *\n     * @example\n     *   const p1 = new LatLon(51.127, 1.338);\n     *   const p2 = new LatLon(50.964, 1.853);\n     *   const pMid = p1.rhumbMidpointTo(p2); // 51.0455°N, 001.5957°E\n     */\n    rhumbMidpointTo(point) {\n        if (!(point instanceof LatLonSpherical)) point = LatLonSpherical.parse(point); // allow literal forms\n\n        // see mathforum.org/kb/message.jspa?messageID=148837\n\n        const φ1 = this.lat.toRadians(); let λ1 = this.lon.toRadians();\n        const φ2 = point.lat.toRadians(), λ2 = point.lon.toRadians();\n\n        if (Math.abs(λ2 - λ1) > π) λ1 += 2 * π; // crossing anti-meridian\n\n        const φ3 = (φ1 + φ2) / 2;\n        const f1 = Math.tan(π / 4 + φ1 / 2);\n        const f2 = Math.tan(π / 4 + φ2 / 2);\n        const f3 = Math.tan(π / 4 + φ3 / 2);\n        let λ3 = ((λ2 - λ1) * Math.log(f3) + λ1 * Math.log(f2) - λ2 * Math.log(f1)) / Math.log(f2 / f1);\n\n        if (!isFinite(λ3)) λ3 = (λ1 + λ2) / 2; // parallel of latitude\n\n        const lat = φ3.toDegrees();\n        const lon = λ3.toDegrees();\n\n        return new LatLonSpherical(lat, lon);\n    }\n\n\n    /* Area - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - */\n\n\n    /**\n     * Calculates the area of a spherical polygon where the sides of the polygon are great circle\n     * arcs joining the vertices.\n     *\n     * @param   {LatLon[]} polygon - Array of points defining vertices of the polygon.\n     * @param   {number}   [radius=6371e3] - (Mean) radius of earth (defaults to radius in metres).\n     * @returns {number}   The area of the polygon in the same units as radius.\n     *\n     * @example\n     *   const polygon = [new LatLon(0,0), new LatLon(1,0), new LatLon(0,1)];\n     *   const area = LatLon.areaOf(polygon); // 6.18e9 m²\n     */\n    static areaOf(polygon, radius=6371e3) {\n        // uses method due to Karney: osgeo-org.1560.x6.nabble.com/Area-of-a-spherical-polygon-td3841625.html;\n        // for each edge of the polygon, tan(E/2) = tan(Δλ/2)·(tan(φ₁/2)+tan(φ₂/2)) / (1+tan(φ₁/2)·tan(φ₂/2))\n        // where E is the spherical excess of the trapezium obtained by extending the edge to the equator\n        // (Karney's method is probably more efficient than the more widely known L’Huilier’s Theorem)\n\n        const R = radius;\n\n        // close polygon so that last point equals first point\n        const closed = polygon[0].equals(polygon[polygon.length-1]);\n        if (!closed) polygon.push(polygon[0]);\n\n        const nVertices = polygon.length - 1;\n\n        let S = 0; // spherical excess in steradians\n        for (let v=0; v<nVertices; v++) {\n            const φ1 = polygon[v].lat.toRadians();\n            const φ2 = polygon[v+1].lat.toRadians();\n            const Δλ = (polygon[v+1].lon - polygon[v].lon).toRadians();\n            const E = 2 * Math.atan2(Math.tan(Δλ/2) * (Math.tan(φ1/2)+Math.tan(φ2/2)), 1 + Math.tan(φ1/2)*Math.tan(φ2/2));\n            S += E;\n        }\n\n        if (isPoleEnclosedBy(polygon)) S = Math.abs(S) - 2*π;\n\n        const A = Math.abs(S * R*R); // area in units of R\n\n        if (!closed) polygon.pop(); // restore polygon to pristine condition\n\n        return A;\n\n        // returns whether polygon encloses pole: sum of course deltas around pole is 0° rather than\n        // normal ±360°: blog.element84.com/determining-if-a-spherical-polygon-contains-a-pole.html\n        function isPoleEnclosedBy(p) {\n            // TODO: any better test than this?\n            let ΣΔ = 0;\n            let prevBrng = p[0].initialBearingTo(p[1]);\n            for (let v=0; v<p.length-1; v++) {\n                const initBrng = p[v].initialBearingTo(p[v+1]);\n                const finalBrng = p[v].finalBearingTo(p[v+1]);\n                ΣΔ += (initBrng - prevBrng + 540) % 360 - 180;\n                ΣΔ += (finalBrng - initBrng + 540) % 360 - 180;\n                prevBrng = finalBrng;\n            }\n            const initBrng = p[0].initialBearingTo(p[1]);\n            ΣΔ += (initBrng - prevBrng + 540) % 360 - 180;\n            // TODO: fix (intermittant) edge crossing pole - eg (85,90), (85,0), (85,-90)\n            const enclosed = Math.abs(ΣΔ) < 90; // 0°-ish\n            return enclosed;\n        }\n    }\n\n\n    /* - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */\n\n\n    /**\n     * Checks if another point is equal to ‘this’ point.\n     *\n     * @param   {LatLon} point - Point to be compared against this point.\n     * @returns {bool}   True if points have identical latitude and longitude values.\n     *\n     * @example\n     *   const p1 = new LatLon(52.205, 0.119);\n     *   const p2 = new LatLon(52.205, 0.119);\n     *   const equal = p1.equals(p2); // true\n     */\n    equals(point) {\n        if (!(point instanceof LatLonSpherical)) point = LatLonSpherical.parse(point); // allow literal forms\n\n        if (Math.abs(this.lat - point.lat) > Number.EPSILON) return false;\n        if (Math.abs(this.lon - point.lon) > Number.EPSILON) return false;\n\n        return true;\n    }\n\n\n    /**\n     * Converts ‘this’ point to a GeoJSON object.\n     *\n     * @returns {Object} this point as a GeoJSON ‘Point’ object.\n     */\n    toGeoJSON() {\n        return { type: 'Point', coordinates: [ this.lon, this.lat ] };\n    }\n\n\n    /**\n     * Returns a string representation of ‘this’ point, formatted as degrees, degrees+minutes, or\n     * degrees+minutes+seconds.\n     *\n     * @param   {string} [format=d] - Format point as 'd', 'dm', 'dms', or 'n' for signed numeric.\n     * @param   {number} [dp=4|2|0] - Number of decimal places to use: default 4 for d, 2 for dm, 0 for dms.\n     * @returns {string} Comma-separated formatted latitude/longitude.\n     * @throws  {RangeError} Invalid format.\n     *\n     * @example\n     *   const greenwich = new LatLon(51.47788, -0.00147);\n     *   const d = greenwich.toString();                        // 51.4779°N, 000.0015°W\n     *   const dms = greenwich.toString('dms', 2);              // 51°28′40.37″N, 000°00′05.29″W\n     *   const [lat, lon] = greenwich.toString('n').split(','); // 51.4779, -0.0015\n     */\n    toString(format='d', dp=undefined) {\n        // note: explicitly set dp to undefined for passing through to toLat/toLon\n        if (![ 'd', 'dm', 'dms', 'n' ].includes(format)) throw new RangeError(`invalid format ‘${format}’`);\n\n        if (format == 'n') { // signed numeric degrees\n            if (dp == undefined) dp = 4;\n            return `${this.lat.toFixed(dp)},${this.lon.toFixed(dp)}`;\n        }\n        const lat = Dms.toLat(this.lat, format, dp);\n        const lon = Dms.toLon(this.lon, format, dp);\n        return `${lat}, ${lon}`;\n    }\n\n}\n\n\n/* - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */\n\nexport { LatLonSpherical as default, Dms };\n"], "mappings": ";;;AAoBA,IAAI,eAAe;AAMnB,IAAM,MAAN,MAAM,KAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBN,WAAW,YAAgB;AAAE,WAAO;AAAA,EAAc;AAAA,EAClD,WAAW,UAAU,MAAM;AAAE,mBAAe;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBlD,OAAO,MAAM,KAAK;AAEd,QAAI,CAAC,MAAM,WAAW,GAAG,CAAC,KAAK,SAAS,GAAG,EAAG,QAAO,OAAO,GAAG;AAG/D,UAAM,WAAW,OAAO,GAAG,EAAE,KAAK,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,YAAY,EAAE,EAAE,MAAM,WAAW;AAC/F,QAAI,SAAS,SAAS,SAAO,CAAC,KAAG,GAAI,UAAS,OAAO,SAAS,SAAO,CAAC;AAEtE,QAAI,YAAY,GAAI,QAAO;AAG3B,QAAI,MAAM;AACV,YAAQ,SAAS,QAAQ;AAAA,MACrB,KAAK;AACD,cAAM,SAAS,CAAC,IAAE,IAAI,SAAS,CAAC,IAAE,KAAK,SAAS,CAAC,IAAE;AACnD;AAAA,MACJ,KAAK;AACD,cAAM,SAAS,CAAC,IAAE,IAAI,SAAS,CAAC,IAAE;AAClC;AAAA,MACJ,KAAK;AACD,cAAM,SAAS,CAAC;AAIhB;AAAA,MACJ;AACI,eAAO;AAAA,IACf;AACA,QAAI,YAAY,KAAK,IAAI,KAAK,CAAC,EAAG,OAAM,CAAC;AAEzC,WAAO,OAAO,GAAG;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,OAAO,MAAM,KAAK,SAAO,KAAK,KAAG,QAAW;AACxC,QAAI,MAAM,GAAG,EAAG,QAAO;AACvB,QAAI,OAAO,OAAO,YAAY,IAAI,KAAK,KAAK,GAAI,QAAO;AACvD,QAAI,OAAO,OAAO,UAAW,QAAO;AACpC,QAAI,OAAO,SAAU,QAAO;AAC5B,QAAI,OAAO,KAAM,QAAO;AAGxB,QAAI,OAAO,QAAW;AAClB,cAAQ,QAAQ;AAAA,QACZ,KAAK;AAAA,QAAO,KAAK;AAAe,eAAK;AAAG;AAAA,QACxC,KAAK;AAAA,QAAO,KAAK;AAAe,eAAK;AAAG;AAAA,QACxC,KAAK;AAAA,QAAO,KAAK;AAAe,eAAK;AAAG;AAAA,QACxC;AAAkB,mBAAS;AAAK,eAAK;AAAG;AAAA,MAC5C;AAAA,IACJ;AAEA,UAAM,KAAK,IAAI,GAAG;AAElB,QAAI,MAAM,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI;AACxC,YAAQ,QAAQ;AAAA,MACZ;AAAA;AAAA,MACA,KAAK;AAAA,MAAK,KAAK;AACX,YAAI,IAAI,QAAQ,EAAE;AAClB,YAAI,IAAE,IAAK,KAAI,MAAM;AACrB,YAAI,IAAE,GAAI,KAAI,MAAM;AACpB,cAAM,IAAI;AACV;AAAA,MACJ,KAAK;AAAA,MAAM,KAAK;AACZ,YAAI,KAAK,MAAM,GAAG;AAClB,aAAM,MAAI,KAAM,IAAI,QAAQ,EAAE;AAC9B,YAAI,KAAK,IAAI;AAAE,cAAK,GAAG,QAAQ,EAAE;AAAG;AAAA,QAAK;AACzC,aAAK,QAAM,GAAG,MAAM,EAAE;AACtB,YAAI,IAAE,GAAI,KAAI,MAAM;AACpB,cAAM,IAAI,MAAI,KAAI,YAAY,IAAI;AAClC;AAAA,MACJ,KAAK;AAAA,MAAO,KAAK;AACb,YAAI,KAAK,MAAM,GAAG;AAClB,YAAI,KAAK,MAAO,MAAI,OAAM,EAAE,IAAI;AAChC,aAAK,MAAI,OAAO,IAAI,QAAQ,EAAE;AAC9B,YAAI,KAAK,IAAI;AAAE,cAAK,GAAG,QAAQ,EAAE;AAAG;AAAA,QAAK;AACzC,YAAI,KAAK,IAAI;AAAE,cAAI;AAAG;AAAA,QAAK;AAC3B,aAAK,QAAM,GAAG,MAAM,EAAE;AACtB,aAAK,OAAK,GAAG,MAAM,EAAE;AACrB,YAAI,IAAE,GAAI,KAAI,MAAM;AACpB,cAAM,IAAI,MAAI,KAAI,YAAY,IAAI,MAAI,KAAI,YAAY,IAAI;AAC1D;AAAA,IACR;AAEA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,OAAO,MAAM,KAAK,QAAQ,IAAI;AAC1B,UAAM,MAAM,KAAI,MAAM,KAAI,OAAO,GAAG,GAAG,QAAQ,EAAE;AACjD,WAAO,QAAM,OAAO,MAAM,IAAI,MAAM,CAAC,IAAI,KAAI,aAAa,MAAI,IAAI,MAAM;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,OAAO,MAAM,KAAK,QAAQ,IAAI;AAC1B,UAAM,MAAM,KAAI,MAAM,KAAI,QAAQ,GAAG,GAAG,QAAQ,EAAE;AAClD,WAAO,QAAM,OAAO,MAAM,MAAM,KAAI,aAAa,MAAI,IAAI,MAAM;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,OAAO,OAAO,KAAK,QAAQ,IAAI;AAC3B,UAAM,OAAQ,KAAI,MAAM,KAAI,QAAQ,GAAG,GAAG,QAAQ,EAAE;AACpD,WAAO,SAAO,OAAO,MAAM,KAAK,QAAQ,OAAO,GAAG;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,OAAO,WAAW,KAAK;AACnB,UAAM,SAAU,WAAY,eAAe;AAC3C,UAAM,YAAY,EAAE,WAAW,OAAO,MAAM,GAAG,CAAC,GAAG,SAAS,OAAO,MAAM,GAAG,CAAC,EAAE;AAC/E,WAAO,IAAI,QAAQ,UAAU,WAAW,GAAG,EAAE,QAAQ,UAAU,SAAS,GAAG,EAAE,QAAQ,KAAK,GAAG;AAAA,EACjG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,OAAO,SAAS,KAAK;AACjB,UAAM,SAAU,WAAY,eAAe;AAC3C,UAAM,YAAY,EAAE,WAAW,OAAO,MAAM,GAAG,CAAC,GAAG,SAAS,OAAO,MAAM,GAAG,CAAC,EAAE;AAC/E,WAAO,IAAI,QAAQ,YAAY,KAAK,EAAE,QAAQ,KAAK,UAAU,OAAO,EAAE,QAAQ,KAAK,UAAU,SAAS;AAAA,EAC1G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,OAAO,aAAa,SAAS,YAAU,GAAG;AACtC,QAAI,CAAC,CAAE,GAAG,GAAG,CAAE,EAAE,SAAS,OAAO,SAAS,CAAC,EAAG,OAAM,IAAI,WAAW,sBAAsB,SAAS,GAAG;AAGrG,cAAU,KAAI,QAAQ,OAAO;AAE7B,UAAM,YAAY;AAAA,MACd;AAAA,MAAK;AAAA,MAAO;AAAA,MAAM;AAAA,MAClB;AAAA,MAAK;AAAA,MAAO;AAAA,MAAM;AAAA,MAClB;AAAA,MAAK;AAAA,MAAO;AAAA,MAAM;AAAA,MAClB;AAAA,MAAK;AAAA,MAAO;AAAA,MAAM;AAAA,IAAM;AAC5B,UAAM,IAAI,IAAI,MAAI,YAAU;AAC5B,UAAM,WAAW,UAAU,KAAK,MAAM,UAAQ,IAAE,GAAG,IAAE,IAAI,KAAG,CAAC;AAE7D,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,OAAO,SAAS;AACnB,QAAI,OAAK,WAAW,WAAS,GAAI,QAAO;AAMxC,UAAM,IAAI,SAAS,IAAI,IAAI,IAAI;AAC/B,WAAO,IAAE,IAAE,IAAI,KAAK,MAAO,IAAE,IAAE,KAAG,IAAG,KAAG,IAAI,IAAE,CAAC,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,QAAQ,SAAS;AACpB,QAAI,QAAM,WAAW,WAAS,IAAK,QAAO;AAM1C,UAAM,IAAI,SAAS,IAAI,KAAK,IAAI;AAChC,aAAU,IAAE,IAAE,IAAE,IAAI,IAAE,KAAG,IAAG,KAAG,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,QAAQ,SAAS;AACpB,QAAI,KAAG,WAAW,UAAQ,IAAK,QAAO;AAStC,UAAM,IAAI,SAAS,IAAI,KAAK,IAAI;AAChC,YAAU,IAAE,IAAE,IAAE,IAAG,IAAG,KAAG;AAAA,EAC7B;AAEJ;AAIA,OAAO,UAAU,YAAY,WAAW;AAAE,SAAO,OAAO,KAAK,KAAK;AAAK;AACvE,OAAO,UAAU,YAAY,WAAW;AAAE,SAAO,OAAO,MAAM,KAAK;AAAI;AAIvE,IAAO,cAAQ;;;ACvVf,IAAM,IAAI,KAAK;AAyBf,IAAM,kBAAN,MAAM,iBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAalB,YAAY,KAAK,KAAK;AAClB,QAAI,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,gBAAgB,GAAG,GAAG;AAC1D,QAAI,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,gBAAgB,GAAG,GAAG;AAE1D,SAAK,OAAO,YAAI,OAAO,OAAO,GAAG,CAAC;AAClC,SAAK,OAAO,YAAI,QAAQ,OAAO,GAAG,CAAC;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,MAAY;AAAE,WAAO,KAAK;AAAA,EAAM;AAAA,EACpC,IAAI,WAAY;AAAE,WAAO,KAAK;AAAA,EAAM;AAAA,EACpC,IAAI,IAAI,KAAK;AACT,SAAK,OAAO,MAAM,GAAG,IAAI,YAAI,OAAO,YAAI,MAAM,GAAG,CAAC,IAAI,YAAI,OAAO,OAAO,GAAG,CAAC;AAC5E,QAAI,MAAM,KAAK,IAAI,EAAG,OAAM,IAAI,UAAU,gBAAgB,GAAG,GAAG;AAAA,EACpE;AAAA,EACA,IAAI,SAAS,KAAK;AACd,SAAK,OAAO,MAAM,GAAG,IAAI,YAAI,OAAO,YAAI,MAAM,GAAG,CAAC,IAAI,YAAI,OAAO,OAAO,GAAG,CAAC;AAC5E,QAAI,MAAM,KAAK,IAAI,EAAG,OAAM,IAAI,UAAU,qBAAqB,GAAG,GAAG;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,MAAY;AAAE,WAAO,KAAK;AAAA,EAAM;AAAA,EACpC,IAAI,MAAY;AAAE,WAAO,KAAK;AAAA,EAAM;AAAA,EACpC,IAAI,YAAY;AAAE,WAAO,KAAK;AAAA,EAAM;AAAA,EACpC,IAAI,IAAI,KAAK;AACT,SAAK,OAAO,MAAM,GAAG,IAAI,YAAI,QAAQ,YAAI,MAAM,GAAG,CAAC,IAAI,YAAI,QAAQ,OAAO,GAAG,CAAC;AAC9E,QAAI,MAAM,KAAK,IAAI,EAAG,OAAM,IAAI,UAAU,gBAAgB,GAAG,GAAG;AAAA,EACpE;AAAA,EACA,IAAI,IAAI,KAAK;AACT,SAAK,OAAO,MAAM,GAAG,IAAI,YAAI,QAAQ,YAAI,MAAM,GAAG,CAAC,IAAI,YAAI,QAAQ,OAAO,GAAG,CAAC;AAC9E,QAAI,MAAM,KAAK,IAAI,EAAG,OAAM,IAAI,UAAU,gBAAgB,GAAG,GAAG;AAAA,EACpE;AAAA,EACA,IAAI,UAAU,KAAK;AACf,SAAK,OAAO,MAAM,GAAG,IAAI,YAAI,QAAQ,YAAI,MAAM,GAAG,CAAC,IAAI,YAAI,QAAQ,OAAO,GAAG,CAAC;AAC9E,QAAI,MAAM,KAAK,IAAI,EAAG,OAAM,IAAI,UAAU,sBAAsB,GAAG,GAAG;AAAA,EAC1E;AAAA;AAAA,EAIA,WAAW,aAAwB;AAAE,WAAO,IAAE;AAAA,EAAM;AAAA;AAAA,EAEpD,WAAW,gBAAwB;AAAE,WAAO,IAAE;AAAA,EAAU;AAAA;AAAA,EAExD,WAAW,wBAAwB;AAAE,WAAO,IAAE;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BpD,OAAO,SAAS,MAAM;AAClB,QAAI,KAAK,UAAU,EAAG,OAAM,IAAI,UAAU,uBAAuB;AACjE,QAAI,KAAK,CAAC,MAAI,QAAQ,KAAK,CAAC,MAAI,KAAM,OAAM,IAAI,UAAU,sBAAsB;AAEhF,QAAI,MAAI,QAAW,MAAI;AAEvB,QAAI,KAAK,UAAU,GAAG;AAClB,OAAE,KAAK,GAAI,IAAI;AACf,YAAM,YAAI,OAAO,YAAI,MAAM,GAAG,CAAC;AAC/B,YAAM,YAAI,QAAQ,YAAI,MAAM,GAAG,CAAC;AAChC,UAAI,MAAM,GAAG,KAAK,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kBAAkB,KAAK,SAAS,CAAC,GAAG;AAAA,IAC1F;AAEA,QAAI,KAAK,UAAU,KAAK,OAAO,KAAK,CAAC,KAAK,UAAU;AAChD,OAAE,KAAK,GAAI,IAAI,KAAK,CAAC,EAAE,MAAM,GAAG;AAChC,YAAM,YAAI,OAAO,YAAI,MAAM,GAAG,CAAC;AAC/B,YAAM,YAAI,QAAQ,YAAI,MAAM,GAAG,CAAC;AAChC,UAAI,MAAM,GAAG,KAAK,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kBAAkB,KAAK,CAAC,CAAC,GAAG;AAAA,IAClF;AAEA,QAAI,KAAK,UAAU,KAAK,OAAO,KAAK,CAAC,KAAK,UAAU;AAChD,YAAM,KAAK,KAAK,CAAC;AACjB,UAAI,GAAG,QAAQ,WAAW,MAAM,QAAQ,GAAG,WAAW,GAAG;AACrD,SAAE,KAAK,GAAI,IAAI,GAAG;AAAA,MACtB,OAAO;AACH,YAAI,GAAG,YAAa,OAAW,OAAM,GAAG;AACxC,YAAI,GAAG,OAAa,OAAW,OAAM,GAAG;AACxC,YAAI,GAAG,aAAa,OAAW,OAAM,GAAG;AACxC,YAAI,GAAG,OAAa,OAAW,OAAM,GAAG;AACxC,YAAI,GAAG,OAAa,OAAW,OAAM,GAAG;AACxC,cAAM,YAAI,OAAO,YAAI,MAAM,GAAG,CAAC;AAC/B,cAAM,YAAI,QAAQ,YAAI,MAAM,GAAG,CAAC;AAAA,MACpC;AACA,UAAI,MAAM,GAAG,KAAK,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kBAAkB,KAAK,UAAU,KAAK,CAAC,CAAC,CAAC,GAAG;AAAA,IAClG;AAEA,QAAI,MAAM,GAAG,KAAK,MAAM,GAAG,EAAG,OAAM,IAAI,UAAU,kBAAkB,KAAK,SAAS,CAAC,GAAG;AAEtF,WAAO,IAAI,iBAAgB,KAAK,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,WAAW,OAAO,SAAO,QAAQ;AAC7B,QAAI,EAAE,iBAAiB,kBAAkB,SAAQ,iBAAgB,MAAM,KAAK;AAC5E,QAAI,MAAM,MAAM,EAAG,OAAM,IAAI,UAAU,mBAAmB,MAAM,GAAG;AAMnE,UAAM,IAAI;AACV,UAAM,KAAK,KAAK,IAAI,UAAU,GAAI,KAAK,KAAK,IAAI,UAAU;AAC1D,UAAM,KAAK,MAAM,IAAI,UAAU,GAAG,KAAK,MAAM,IAAI,UAAU;AAC3D,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK;AAEhB,UAAM,IAAI,KAAK,IAAI,KAAG,CAAC,IAAE,KAAK,IAAI,KAAG,CAAC,IAAI,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,KAAG,CAAC,IAAE,KAAK,IAAI,KAAG,CAAC;AAClG,UAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAE,CAAC,CAAC;AACrD,UAAM,IAAI,IAAI;AAEd,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,iBAAiB,OAAO;AACpB,QAAI,EAAE,iBAAiB,kBAAkB,SAAQ,iBAAgB,MAAM,KAAK;AAC5E,QAAI,KAAK,OAAO,KAAK,EAAG,QAAO;AAK/B,UAAM,KAAK,KAAK,IAAI,UAAU;AAC9B,UAAM,KAAK,MAAM,IAAI,UAAU;AAC/B,UAAM,MAAM,MAAM,MAAM,KAAK,KAAK,UAAU;AAE5C,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AACjF,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AACpC,UAAM,IAAI,KAAK,MAAM,GAAG,CAAC;AAEzB,UAAM,UAAU,EAAE,UAAU;AAE5B,WAAO,YAAI,QAAQ,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,eAAe,OAAO;AAClB,QAAI,EAAE,iBAAiB,kBAAkB,SAAQ,iBAAgB,MAAM,KAAK;AAI5E,UAAM,UAAU,MAAM,iBAAiB,IAAI,IAAI;AAE/C,WAAO,YAAI,QAAQ,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,WAAW,OAAO;AACd,QAAI,EAAE,iBAAiB,kBAAkB,SAAQ,iBAAgB,MAAM,KAAK;AAM5E,UAAM,KAAK,KAAK,IAAI,UAAU;AAC9B,UAAM,KAAK,KAAK,IAAI,UAAU;AAC9B,UAAM,KAAK,MAAM,IAAI,UAAU;AAC/B,UAAM,MAAM,MAAM,MAAM,KAAK,KAAK,UAAU;AAG5C,UAAM,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,EAAE,EAAE;AACnD,UAAM,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,EAAE,GAAG,GAAG,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,EAAE,GAAG,GAAG,KAAK,IAAI,EAAE,EAAE;AAGxF,UAAM,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,EAAE,IAAI,EAAE,EAAE;AAErD,UAAM,KAAK,KAAK,MAAM,EAAE,GAAG,KAAK,KAAK,EAAE,IAAE,EAAE,IAAI,EAAE,IAAE,EAAE,CAAC,CAAC;AACvD,UAAM,KAAK,KAAK,KAAK,MAAM,EAAE,GAAG,EAAE,CAAC;AAEnC,UAAM,MAAM,GAAG,UAAU;AACzB,UAAM,MAAM,GAAG,UAAU;AAEzB,WAAO,IAAI,iBAAgB,KAAK,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,oBAAoB,OAAO,UAAU;AACjC,QAAI,EAAE,iBAAiB,kBAAkB,SAAQ,iBAAgB,MAAM,KAAK;AAC5E,QAAI,KAAK,OAAO,KAAK,EAAG,QAAO,IAAI,iBAAgB,KAAK,KAAK,KAAK,GAAG;AAErE,UAAM,KAAK,KAAK,IAAI,UAAU,GAAG,KAAK,KAAK,IAAI,UAAU;AACzD,UAAM,KAAK,MAAM,IAAI,UAAU,GAAG,KAAK,MAAM,IAAI,UAAU;AAG3D,UAAM,KAAK,KAAK;AAChB,UAAM,KAAK,KAAK;AAChB,UAAM,IAAI,KAAK,IAAI,KAAG,CAAC,IAAI,KAAK,IAAI,KAAG,CAAC,IAClC,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,KAAG,CAAC,IAAI,KAAK,IAAI,KAAG,CAAC;AAClE,UAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAE,CAAC,CAAC;AAErD,UAAM,IAAI,KAAK,KAAK,IAAE,YAAU,CAAC,IAAI,KAAK,IAAI,CAAC;AAC/C,UAAM,IAAI,KAAK,IAAI,WAAS,CAAC,IAAI,KAAK,IAAI,CAAC;AAE3C,UAAM,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAC1E,UAAM,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAC1E,UAAM,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE;AAE5C,UAAM,KAAK,KAAK,MAAM,GAAG,KAAK,KAAK,IAAE,IAAI,IAAE,CAAC,CAAC;AAC7C,UAAM,KAAK,KAAK,MAAM,GAAG,CAAC;AAE1B,UAAM,MAAM,GAAG,UAAU;AACzB,UAAM,MAAM,GAAG,UAAU;AAEzB,WAAO,IAAI,iBAAgB,KAAK,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,iBAAiB,UAAU,SAAS,SAAO,QAAQ;AAK/C,UAAM,IAAI,WAAW;AACrB,UAAM,IAAI,OAAO,OAAO,EAAE,UAAU;AAEpC,UAAM,KAAK,KAAK,IAAI,UAAU,GAAG,KAAK,KAAK,IAAI,UAAU;AAEzD,UAAM,QAAQ,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AAClF,UAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,UAAM,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;AACjD,UAAM,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI;AACvC,UAAM,KAAK,KAAK,KAAK,MAAM,GAAG,CAAC;AAE/B,UAAM,MAAM,GAAG,UAAU;AACzB,UAAM,MAAM,GAAG,UAAU;AAEzB,WAAO,IAAI,iBAAgB,KAAK,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,OAAO,aAAa,IAAI,OAAO,IAAI,OAAO;AACtC,QAAI,EAAE,cAAc,kBAAkB,MAAK,iBAAgB,MAAM,EAAE;AACnE,QAAI,EAAE,cAAc,kBAAkB,MAAK,iBAAgB,MAAM,EAAE;AACnE,QAAI,MAAM,KAAK,EAAG,OAAM,IAAI,UAAU,kBAAkB,KAAK,GAAG;AAChE,QAAI,MAAM,KAAK,EAAG,OAAM,IAAI,UAAU,kBAAkB,KAAK,GAAG;AAIhE,UAAM,KAAK,GAAG,IAAI,UAAU,GAAG,KAAK,GAAG,IAAI,UAAU;AACrD,UAAM,KAAK,GAAG,IAAI,UAAU,GAAG,KAAK,GAAG,IAAI,UAAU;AACrD,UAAM,MAAM,OAAO,KAAK,EAAE,UAAU,GAAG,MAAM,OAAO,KAAK,EAAE,UAAU;AACrE,UAAM,KAAK,KAAK,IAAI,KAAK,KAAK;AAG9B,UAAM,MAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAG,CAAC,IAAI,KAAK,IAAI,KAAG,CAAC,IAC5D,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,KAAG,CAAC,IAAI,KAAK,IAAI,KAAG,CAAC,CAAC,CAAC;AACpE,QAAI,KAAK,IAAI,GAAG,IAAI,OAAO,QAAS,QAAO,IAAI,iBAAgB,GAAG,KAAK,GAAG,GAAG;AAG7E,UAAM,SAAS,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG,IAAE,KAAK,IAAI,EAAE;AACtF,UAAM,SAAS,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG,IAAE,KAAK,IAAI,EAAE;AACtF,UAAM,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,OAAO,EAAE,GAAG,CAAC,CAAC;AACrD,UAAM,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,OAAO,EAAE,GAAG,CAAC,CAAC;AAErD,UAAM,MAAM,KAAK,IAAI,KAAG,EAAE,IAAE,IAAI,KAAK,IAAE,IAAE;AACzC,UAAM,MAAM,KAAK,IAAI,KAAG,EAAE,IAAE,IAAI,IAAE,IAAE,KAAK;AAEzC,UAAM,KAAK,MAAM;AACjB,UAAM,KAAK,MAAM;AAEjB,QAAI,KAAK,IAAI,EAAE,KAAK,KAAK,KAAK,IAAI,EAAE,KAAK,EAAG,QAAO;AACnD,QAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,EAAG,QAAO;AAE5C,UAAM,QAAQ,CAAC,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,GAAG;AAEjF,UAAM,MAAM,KAAK,MAAM,KAAK,IAAI,GAAG,IAAE,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAE,KAAK;AAEjG,UAAM,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,GAAG,IAAE,KAAK,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;AAErH,UAAM,OAAO,KAAK,MAAM,KAAK,IAAI,GAAG,IAAE,KAAK,IAAI,GAAG,IAAE,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,EAAE,IAAE,KAAK,IAAI,EAAE,CAAC;AAC3G,UAAM,KAAK,KAAK;AAEhB,UAAM,MAAM,GAAG,UAAU;AACzB,UAAM,MAAM,GAAG,UAAU;AAEzB,WAAO,IAAI,iBAAgB,KAAK,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,qBAAqB,WAAW,SAAS,SAAO,QAAQ;AACpD,QAAI,EAAE,qBAAqB,kBAAkB,aAAY,iBAAgB,MAAM,SAAS;AACxF,QAAI,EAAE,mBAAmB,kBAAkB,WAAU,iBAAgB,MAAM,OAAO;AAClF,UAAM,IAAI;AAEV,QAAI,KAAK,OAAO,SAAS,EAAG,QAAO;AAEnC,UAAM,MAAM,UAAU,WAAW,MAAM,CAAC,IAAI;AAC5C,UAAM,MAAM,UAAU,iBAAiB,IAAI,EAAE,UAAU;AACvD,UAAM,MAAM,UAAU,iBAAiB,OAAO,EAAE,UAAU;AAE1D,UAAM,MAAM,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC;AAEzD,WAAO,MAAM;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,qBAAqB,WAAW,SAAS,SAAO,QAAQ;AACpD,QAAI,EAAE,qBAAqB,kBAAkB,aAAY,iBAAgB,MAAM,SAAS;AACxF,QAAI,EAAE,mBAAmB,kBAAkB,WAAU,iBAAgB,MAAM,OAAO;AAClF,UAAM,IAAI;AAEV,QAAI,KAAK,OAAO,SAAS,EAAG,QAAO;AAEnC,UAAM,MAAM,UAAU,WAAW,MAAM,CAAC,IAAI;AAC5C,UAAM,MAAM,UAAU,iBAAiB,IAAI,EAAE,UAAU;AACvD,UAAM,MAAM,UAAU,iBAAiB,OAAO,EAAE,UAAU;AAE1D,UAAM,MAAM,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,MAAI,GAAG,CAAC;AAEvD,UAAM,MAAM,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC;AAE7D,WAAO,MAAI,KAAK,KAAK,KAAK,IAAI,MAAI,GAAG,CAAC,IAAI;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,YAAY,SAAS;AACjB,UAAM,IAAI,OAAO,OAAO,EAAE,UAAU;AAEpC,UAAM,IAAI,KAAK,IAAI,UAAU;AAE7B,UAAM,OAAO,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC;AAE1D,WAAO,KAAK,UAAU;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,kBAAkB,QAAQ,QAAQ,UAAU;AAC/C,QAAI,OAAO,OAAO,MAAM,EAAG,QAAO;AAElC,UAAM,IAAI,OAAO,QAAQ,EAAE,UAAU;AAErC,UAAM,KAAK,OAAO,IAAI,UAAU;AAChC,UAAM,KAAK,OAAO,IAAI,UAAU;AAChC,UAAM,KAAK,OAAO,IAAI,UAAU;AAChC,UAAM,KAAK,OAAO,IAAI,UAAU;AAEhC,UAAM,KAAK,KAAK;AAEhB,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;AACjE,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC;AAC7G,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;AAEjE,QAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAG,QAAO;AAElC,UAAM,KAAK,KAAK,MAAM,CAAC,GAAG,CAAC;AAC3B,UAAM,MAAM,KAAK,KAAK,IAAI,KAAK,KAAK,IAAE,IAAI,IAAE,CAAC,CAAC;AAE9C,UAAM,MAAM,KAAK,KAAK;AACtB,UAAM,MAAM,KAAK,KAAK;AAEtB,UAAM,OAAO,IAAI,UAAU;AAC3B,UAAM,OAAO,IAAI,UAAU;AAE3B,WAAO;AAAA,MACH,MAAM,YAAI,QAAQ,IAAI;AAAA,MACtB,MAAM,YAAI,QAAQ,IAAI;AAAA,IAC1B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,gBAAgB,OAAO,SAAO,QAAQ;AAClC,QAAI,EAAE,iBAAiB,kBAAkB,SAAQ,iBAAgB,MAAM,KAAK;AAI5E,UAAM,IAAI;AACV,UAAM,KAAK,KAAK,IAAI,UAAU;AAC9B,UAAM,KAAK,MAAM,IAAI,UAAU;AAC/B,UAAM,KAAK,KAAK;AAChB,QAAI,KAAK,KAAK,IAAI,MAAM,MAAM,KAAK,GAAG,EAAE,UAAU;AAElD,QAAI,KAAK,IAAI,EAAE,IAAI,EAAG,MAAK,KAAK,IAAI,EAAE,IAAI,IAAI,MAAO,IAAI,IAAI;AAI7D,UAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AACvE,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI,QAAS,KAAK,KAAK,KAAK,IAAI,EAAE;AAGvD,UAAM,IAAI,KAAK,KAAK,KAAG,KAAK,IAAE,IAAI,KAAG,EAAE;AACvC,UAAM,IAAI,IAAI;AAEd,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,eAAe,OAAO;AAClB,QAAI,EAAE,iBAAiB,kBAAkB,SAAQ,iBAAgB,MAAM,KAAK;AAC5E,QAAI,KAAK,OAAO,KAAK,EAAG,QAAO;AAE/B,UAAM,KAAK,KAAK,IAAI,UAAU;AAC9B,UAAM,KAAK,MAAM,IAAI,UAAU;AAC/B,QAAI,MAAM,MAAM,MAAM,KAAK,KAAK,UAAU;AAE1C,QAAI,KAAK,IAAI,EAAE,IAAI,EAAG,MAAK,KAAK,IAAI,EAAE,IAAI,IAAI,MAAO,IAAI,IAAI;AAE7D,UAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AAEvE,UAAM,IAAI,KAAK,MAAM,IAAI,EAAE;AAE3B,UAAM,UAAU,EAAE,UAAU;AAE5B,WAAO,YAAI,QAAQ,OAAO;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,sBAAsB,UAAU,SAAS,SAAO,QAAQ;AACpD,UAAM,KAAK,KAAK,IAAI,UAAU,GAAG,KAAK,KAAK,IAAI,UAAU;AACzD,UAAM,IAAI,OAAO,OAAO,EAAE,UAAU;AAEpC,UAAM,IAAI,WAAW;AAErB,UAAM,KAAK,IAAI,KAAK,IAAI,CAAC;AACzB,QAAI,KAAK,KAAK;AAGd,QAAI,KAAK,IAAI,EAAE,IAAI,IAAI,EAAG,MAAK,KAAK,IAAI,IAAI,KAAK,CAAC,IAAI;AAEtD,UAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AACvE,UAAM,IAAI,KAAK,IAAI,EAAE,IAAI,QAAS,KAAK,KAAK,KAAK,IAAI,EAAE;AAEvD,UAAM,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI;AAC7B,UAAM,KAAK,KAAK;AAEhB,UAAM,MAAM,GAAG,UAAU;AACzB,UAAM,MAAM,GAAG,UAAU;AAEzB,WAAO,IAAI,iBAAgB,KAAK,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,gBAAgB,OAAO;AACnB,QAAI,EAAE,iBAAiB,kBAAkB,SAAQ,iBAAgB,MAAM,KAAK;AAI5E,UAAM,KAAK,KAAK,IAAI,UAAU;AAAG,QAAI,KAAK,KAAK,IAAI,UAAU;AAC7D,UAAM,KAAK,MAAM,IAAI,UAAU,GAAG,KAAK,MAAM,IAAI,UAAU;AAE3D,QAAI,KAAK,IAAI,KAAK,EAAE,IAAI,EAAG,OAAM,IAAI;AAErC,UAAM,MAAM,KAAK,MAAM;AACvB,UAAM,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC;AAClC,UAAM,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC;AAClC,UAAM,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC;AAClC,QAAI,OAAO,KAAK,MAAM,KAAK,IAAI,EAAE,IAAI,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,KAAK,IAAI,EAAE,KAAK,KAAK,IAAI,KAAK,EAAE;AAE9F,QAAI,CAAC,SAAS,EAAE,EAAG,OAAM,KAAK,MAAM;AAEpC,UAAM,MAAM,GAAG,UAAU;AACzB,UAAM,MAAM,GAAG,UAAU;AAEzB,WAAO,IAAI,iBAAgB,KAAK,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,OAAO,OAAO,SAAS,SAAO,QAAQ;AAMlC,UAAM,IAAI;AAGV,UAAM,SAAS,QAAQ,CAAC,EAAE,OAAO,QAAQ,QAAQ,SAAO,CAAC,CAAC;AAC1D,QAAI,CAAC,OAAQ,SAAQ,KAAK,QAAQ,CAAC,CAAC;AAEpC,UAAM,YAAY,QAAQ,SAAS;AAEnC,QAAI,IAAI;AACR,aAAS,IAAE,GAAG,IAAE,WAAW,KAAK;AAC5B,YAAM,KAAK,QAAQ,CAAC,EAAE,IAAI,UAAU;AACpC,YAAM,KAAK,QAAQ,IAAE,CAAC,EAAE,IAAI,UAAU;AACtC,YAAM,MAAM,QAAQ,IAAE,CAAC,EAAE,MAAM,QAAQ,CAAC,EAAE,KAAK,UAAU;AACzD,YAAM,IAAI,IAAI,KAAK,MAAM,KAAK,IAAI,KAAG,CAAC,KAAK,KAAK,IAAI,KAAG,CAAC,IAAE,KAAK,IAAI,KAAG,CAAC,IAAI,IAAI,KAAK,IAAI,KAAG,CAAC,IAAE,KAAK,IAAI,KAAG,CAAC,CAAC;AAC5G,WAAK;AAAA,IACT;AAEA,QAAI,iBAAiB,OAAO,EAAG,KAAI,KAAK,IAAI,CAAC,IAAI,IAAE;AAEnD,UAAM,IAAI,KAAK,IAAI,IAAI,IAAE,CAAC;AAE1B,QAAI,CAAC,OAAQ,SAAQ,IAAI;AAEzB,WAAO;AAIP,aAAS,iBAAiB,GAAG;AAEzB,UAAI,KAAK;AACT,UAAI,WAAW,EAAE,CAAC,EAAE,iBAAiB,EAAE,CAAC,CAAC;AACzC,eAAS,IAAE,GAAG,IAAE,EAAE,SAAO,GAAG,KAAK;AAC7B,cAAMA,YAAW,EAAE,CAAC,EAAE,iBAAiB,EAAE,IAAE,CAAC,CAAC;AAC7C,cAAM,YAAY,EAAE,CAAC,EAAE,eAAe,EAAE,IAAE,CAAC,CAAC;AAC5C,eAAOA,YAAW,WAAW,OAAO,MAAM;AAC1C,eAAO,YAAYA,YAAW,OAAO,MAAM;AAC3C,mBAAW;AAAA,MACf;AACA,YAAM,WAAW,EAAE,CAAC,EAAE,iBAAiB,EAAE,CAAC,CAAC;AAC3C,aAAO,WAAW,WAAW,OAAO,MAAM;AAE1C,YAAM,WAAW,KAAK,IAAI,EAAE,IAAI;AAChC,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,OAAO,OAAO;AACV,QAAI,EAAE,iBAAiB,kBAAkB,SAAQ,iBAAgB,MAAM,KAAK;AAE5E,QAAI,KAAK,IAAI,KAAK,MAAM,MAAM,GAAG,IAAI,OAAO,QAAS,QAAO;AAC5D,QAAI,KAAK,IAAI,KAAK,MAAM,MAAM,GAAG,IAAI,OAAO,QAAS,QAAO;AAE5D,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY;AACR,WAAO,EAAE,MAAM,SAAS,aAAa,CAAE,KAAK,KAAK,KAAK,GAAI,EAAE;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,SAAS,SAAO,KAAK,KAAG,QAAW;AAE/B,QAAI,CAAC,CAAE,KAAK,MAAM,OAAO,GAAI,EAAE,SAAS,MAAM,EAAG,OAAM,IAAI,WAAW,mBAAmB,MAAM,GAAG;AAElG,QAAI,UAAU,KAAK;AACf,UAAI,MAAM,OAAW,MAAK;AAC1B,aAAO,GAAG,KAAK,IAAI,QAAQ,EAAE,CAAC,IAAI,KAAK,IAAI,QAAQ,EAAE,CAAC;AAAA,IAC1D;AACA,UAAM,MAAM,YAAI,MAAM,KAAK,KAAK,QAAQ,EAAE;AAC1C,UAAM,MAAM,YAAI,MAAM,KAAK,KAAK,QAAQ,EAAE;AAC1C,WAAO,GAAG,GAAG,KAAK,GAAG;AAAA,EACzB;AAEJ;", "names": ["initBrng"]}