const express = require("express");
const Geolocation = require("../models/Geolocation");
const { fetchGeolocation } = require("../modules/geolocation");
const { validateData } = require("../middlewares/validator");
const { query } = require("express-validator");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_LOCATION),
    isAuthenticated,
    validateData.bind(this, [
        query("lat").notEmpty().withMessage("lat is required").isFloat().withMessage("Latitude must be a float"),
        query("lng").notEmpty().withMessage("lng is required").isFloat().withMessage("Longitude must be a float"),
    ]),
    async (req, res) => {
        const { lat, lng } = req.query;
        try {
            const radiusInKilometers = 30;
            const radiusInRadians = radiusInKilometers / 6371;

            const existingGeolocation = await Geolocation.findOne({
                location: {
                    $geoWithin: {
                        $centerSphere: [[parseFloat(lng), parseFloat(lat)], radiusInRadians],
                    },
                },
            });

            if (existingGeolocation) {
                return res.status(200).json({
                    lat,
                    lng,
                    name: existingGeolocation.name,
                });
            }

            let locationName;
            try {
                locationName = await fetchGeolocation(lat, lng);
            } catch (fetchError) {
                console.warn("Unable to fetch location name:", fetchError.message);
            }

            if (!locationName) {
                locationName = `Unknown Location`;
            }

            await Geolocation.create({
                location: { type: "Point", coordinates: [parseFloat(lng), parseFloat(lat)] },
                name: locationName,
            });

            return res.status(200).json({
                lat,
                lng,
                name: locationName,
            });
        } catch (err) {
            return res.status(500).json({
                message: "Error retrieving geolocation",
                error: err.message,
            });
        }
    },
);

module.exports = router;

/**
 * [temporarily removed from swagger]
 * tags:
 *   - name: Geolocations
 *     description: Operations related to geolocation fetching and storing.
 * components:
 *   schemas:
 *     Geolocation:
 *       type: object
 *       properties:
 *         lat:
 *           type: number
 *           description: Latitude of the location.
 *           example: 37.7749
 *         lng:
 *           type: number
 *           description: Longitude of the location.
 *           example: -122.4194
 *         name:
 *           type: string
 *           description: The resolved name of the location.
 *           example: San Francisco, CA, USA
 */

/**
 * [temporarily removed from swagger]
 * /geolocations:
 *   get:
 *     summary: Retrieve location details based on latitude and longitude.
 *     description: Checks for geolocation in the database within a 30 km radius. If not found, fetches from Google Maps API and stores it in the database. If the location is not found, returns "Unknown Location".
 *     tags: [Geolocations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: lat
 *         schema:
 *           type: number
 *           example: 37.7749
 *         required: true
 *         description: Latitude of the location.
 *       - in: query
 *         name: lng
 *         schema:
 *           type: number
 *           example: -122.4194
 *         required: true
 *         description: Longitude of the location.
 *     responses:
 *       200:
 *         description: Geolocation details retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Geolocation'
 *       400:
 *         description: Invalid request parameters.
 *       403:
 *         description: Forbidden - Error accessing Google Maps API.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message indicating forbidden access.
 *                   example: "Error retrieving geolocation"
 *                 error:
 *                   type: string
 *                   description: Detailed error message.
 *                   example: "Google Maps Geocoding Error: Request failed with status code 403"
 *       429:
 *         description: Too many requests.
 *       500:
 *         description: Internal server error.
 */
