// Import required libraries
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';

// Register components in Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, Title, Tooltip, Legend);

export default function <PERSON><PERSON>hart({ data, options }) {
    const defaultOptions = {
        responsive: true,
        plugins: {
            legend: {
                position: 'right',
                labels: {
                    color: '#FFFFFF',
                    usePointStyle: true,
                    pointStyle: 'circle',
                },
            },
        },
    };

    const mergedOptions = { ...defaultOptions, ...options };

    return <Pie data={data} options={mergedOptions} />;
}