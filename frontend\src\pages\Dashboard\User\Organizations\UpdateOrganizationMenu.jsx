import axiosInstance from "../../../../axios";
import { alpha, Menu, MenuItem } from "@mui/material";
import theme from "../../../../theme";
import { permissions } from "../../../../utils";

const UpdateOrganizationMenu = ({
    updateOrganizationAnchorEl,
    setUpdateOrganizationAnchorEl,
    organizations,
    roles,
    selectedOrganizationUser,
    setUpdatingOrganization,
    onSuccess,
}) => {
    const open = Boolean(updateOrganizationAnchorEl);

    const handleClick = async (organization) => {
        setUpdatingOrganization(selectedOrganizationUser._id);
        axiosInstance
            .patch(`/users/${selectedOrganizationUser._id}/organization`, { organization_id: organization._id }, { meta: { showSnackbar: true } })
            .then(() => onSuccess && onSuccess())
            .catch(console.error)
            .finally(() => setUpdatingOrganization());
        handleClose();
    };

    const handleClose = () => {
        setUpdateOrganizationAnchorEl(null);
    };

    const isUpdateable = (organization) => {
        const selectedUserRole = roles.find((role) => role.role_id == selectedOrganizationUser.role_id);
        return (
            organization._id === selectedOrganizationUser.organization_id ||
            (organization.is_miscellaneous && !selectedUserRole?.denied_permissions.includes(permissions.manageUsers))
        );
    };

    return (
        <>
            <Menu
                anchorEl={updateOrganizationAnchorEl}
                open={open}
                onClose={handleClose}
                sx={{
                    "& .MuiPaper-root": {
                        minWidth: updateOrganizationAnchorEl ? updateOrganizationAnchorEl.offsetWidth : "auto",
                        backgroundColor: theme.palette.primary.main,
                    },
                }}
                anchorOrigin={{
                    vertical: 35,
                    horizontal: 0,
                }}
            >
                {organizations.map((organization) => (
                    <MenuItem
                        key={organization._id}
                        disabled={isUpdateable(organization)}
                        onClick={() => handleClick(organization)}
                        sx={{
                            gap: 1,
                            "&:hover": {
                                backgroundColor: alpha(theme.palette.custom.darkBlue, 0.3) + " !important",
                            },
                        }}
                    >
                        {organization.name}
                    </MenuItem>
                ))}
            </Menu>
        </>
    );
};

export default UpdateOrganizationMenu;
