import { render, screen, fireEvent } from "@testing-library/react";
import DeleteKeyModal from "../../src/pages/Dashboard/ApiKeys/DeleteKeyModal";
import axiosInstance from "../../src/axios";

jest.mock("../../src/axios", () => ({
    delete: jest.fn(),
}));


describe("DeleteKeyModal Component", () => {
    const setDeleteKey = jest.fn();
    const setDeleting = jest.fn();

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("does not render when deleteKey is not provided", () => {
        render(
            <DeleteKeyModal
                deleteKey={null}
                setDeleteKey={setDeleteKey}
                setDeleting={setDeleting}
            />
        );

        expect(screen.queryByText("Delete Role")).not.toBeInTheDocument();
    });

    it("calls setDeleteKey with undefined when Cancel button is clicked", () => {
        render(
            <DeleteKeyModal
                deleteKey={{ _id: "123", serial: 1 }}
                setDeleteKey={setDeleteKey}
                setDeleting={setDeleting}
            />
        );

        const cancelButton = screen.getByText("Cancel");
        fireEvent.click(cancelButton);

        expect(setDeleteKey).toHaveBeenCalled();
    });

    it("calls axios.delete with the correct API key and updates deleting state when Delete button is clicked", async () => {
        axiosInstance.delete.mockResolvedValueOnce({});
        render(
            <DeleteKeyModal
                deleteKey={{ _id: "123", serial: 1 }}
                setDeleteKey={setDeleteKey}
                setDeleting={setDeleting}
            />
        );

        const deleteButton = screen.getByText("Delete");
        fireEvent.click(deleteButton);

        expect(setDeleteKey).toHaveBeenCalled();
        expect(setDeleting).toHaveBeenCalled();
        expect(axiosInstance.delete).toHaveBeenCalled();
    });
});
