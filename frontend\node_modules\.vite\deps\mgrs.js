import "./chunk-EWTE5DHJ.js";

// node_modules/mgrs/dist/mgrs.esm.js
var t = 65;
var e = 73;
var r = 79;
function n(n2, o2) {
  if (o2 = "number" == typeof o2 ? o2 : 5, !Array.isArray(n2)) throw new TypeError("forward did not receive an array");
  if ("string" == typeof n2[0] || "string" == typeof n2[1]) throw new TypeError("forward received an array of strings, but it only accepts an array of numbers.");
  const [a2, s2] = n2;
  if (a2 < -180 || a2 > 180) throw new TypeError("forward received an invalid longitude of " + a2);
  if (s2 < -90 || s2 > 90) throw new TypeError("forward received an invalid latitude of " + s2);
  if (s2 < -80 || s2 > 84) throw new TypeError(`forward received a latitude of ${s2}, but this library does not support conversions of points in polar regions below 80°S and above 84°N`);
  return function(n3, o3) {
    const a3 = "00000" + n3.easting, i2 = "00000" + n3.northing;
    return n3.zoneNumber + n3.zoneLetter + function(n4, o4, a4) {
      const i3 = f(a4), s3 = Math.floor(n4 / 1e5), c2 = Math.floor(o4 / 1e5) % 20;
      return function(n5, o5, a5) {
        const i4 = a5 - 1, s4 = "AJSAJS".charCodeAt(i4), c3 = "AFAFAF".charCodeAt(i4);
        let h2 = s4 + n5 - 1, f2 = c3 + o5, l2 = false;
        h2 > 90 && (h2 = h2 - 90 + t - 1, l2 = true);
        (h2 === e || s4 < e && h2 > e || (h2 > e || s4 < e) && l2) && h2++;
        (h2 === r || s4 < r && h2 > r || (h2 > r || s4 < r) && l2) && (h2++, h2 === e && h2++);
        h2 > 90 && (h2 = h2 - 90 + t - 1);
        f2 > 86 ? (f2 = f2 - 86 + t - 1, l2 = true) : l2 = false;
        (f2 === e || c3 < e && f2 > e || (f2 > e || c3 < e) && l2) && f2++;
        (f2 === r || c3 < r && f2 > r || (f2 > r || c3 < r) && l2) && (f2++, f2 === e && f2++);
        f2 > 86 && (f2 = f2 - 86 + t - 1);
        return String.fromCharCode(h2) + String.fromCharCode(f2);
      }(s3, c2, i3);
    }(n3.easting, n3.northing, n3.zoneNumber) + a3.substr(a3.length - 5, o3) + i2.substr(i2.length - 5, o3);
  }(function(t2) {
    const e2 = t2.lat, r2 = t2.lon, n3 = 6378137, o3 = i(e2), a3 = i(r2);
    let s3;
    s3 = Math.floor((r2 + 180) / 6) + 1, 180 === r2 && (s3 = 60);
    e2 >= 56 && e2 < 64 && r2 >= 3 && r2 < 12 && (s3 = 32);
    e2 >= 72 && e2 < 84 && (r2 >= 0 && r2 < 9 ? s3 = 31 : r2 >= 9 && r2 < 21 ? s3 = 33 : r2 >= 21 && r2 < 33 ? s3 = 35 : r2 >= 33 && r2 < 42 && (s3 = 37));
    const c2 = i(6 * (s3 - 1) - 180 + 3), f2 = n3 / Math.sqrt(1 - 669438e-8 * Math.sin(o3) * Math.sin(o3)), l2 = Math.tan(o3) * Math.tan(o3), u2 = 0.006739496752268451 * Math.cos(o3) * Math.cos(o3), b = Math.cos(o3) * (a3 - c2), M = n3 * (0.9983242984503243 * o3 - 0.002514607064228144 * Math.sin(2 * o3) + 2639046602129982e-21 * Math.sin(4 * o3) - 3418046101696858e-24 * Math.sin(6 * o3)), d = 0.9996 * f2 * (b + (1 - l2 + u2) * b * b * b / 6 + (5 - 18 * l2 + l2 * l2 + 72 * u2 - 0.39089081163157013) * b * b * b * b * b / 120) + 5e5;
    let g = 0.9996 * (M + f2 * Math.tan(o3) * (b * b / 2 + (5 - l2 + 9 * u2 + 4 * u2 * u2) * b * b * b * b / 24 + (61 - 58 * l2 + l2 * l2 + 600 * u2 - 2.2240339282485886) * b * b * b * b * b * b / 720));
    e2 < 0 && (g += 1e7);
    return { northing: Math.trunc(g), easting: Math.trunc(d), zoneNumber: s3, zoneLetter: h(e2) };
  }({ lat: s2, lon: a2 }), o2);
}
function o(t2) {
  const e2 = c(l(t2.toUpperCase()));
  return "number" == typeof e2.lat && "number" == typeof e2.lon ? [e2.lon, e2.lat, e2.lon, e2.lat] : [e2.left, e2.bottom, e2.right, e2.top];
}
function a(t2) {
  if ("" === t2) throw new TypeError("toPoint received a blank string");
  const e2 = c(l(t2.toUpperCase()));
  return "number" == typeof e2.lat && "number" == typeof e2.lon ? [e2.lon, e2.lat] : [(e2.left + e2.right) / 2, (e2.top + e2.bottom) / 2];
}
function i(t2) {
  return t2 * (Math.PI / 180);
}
function s(t2) {
  return t2 / Math.PI * 180;
}
function c(t2) {
  const e2 = t2.northing, r2 = t2.easting, { zoneLetter: n2, zoneNumber: o2 } = t2;
  if (o2 < 0 || o2 > 60) return null;
  const a2 = 6378137, i2 = (1 - Math.sqrt(0.99330562)) / (1 + Math.sqrt(0.99330562)), h2 = r2 - 5e5;
  let f2 = e2;
  n2 < "N" && (f2 -= 1e7);
  const l2 = 6 * (o2 - 1) - 180 + 3, u2 = f2 / 0.9996 / 6367449145945056e-9, b = u2 + (3 * i2 / 2 - 27 * i2 * i2 * i2 / 32) * Math.sin(2 * u2) + (21 * i2 * i2 / 16 - 55 * i2 * i2 * i2 * i2 / 32) * Math.sin(4 * u2) + 151 * i2 * i2 * i2 / 96 * Math.sin(6 * u2), M = a2 / Math.sqrt(1 - 669438e-8 * Math.sin(b) * Math.sin(b)), d = Math.tan(b) * Math.tan(b), g = 0.006739496752268451 * Math.cos(b) * Math.cos(b), w = 0.99330562 * a2 / Math.pow(1 - 669438e-8 * Math.sin(b) * Math.sin(b), 1.5), p = h2 / (0.9996 * M);
  let y = b - M * Math.tan(b) / w * (p * p / 2 - (5 + 3 * d + 10 * g - 4 * g * g - 0.06065547077041606) * p * p * p * p / 24 + (61 + 90 * d + 298 * g + 45 * d * d - 1.6983531815716497 - 3 * g * g) * p * p * p * p * p * p / 720);
  y = s(y);
  let m, A = (p - (1 + 2 * d + g) * p * p * p / 6 + (5 - 2 * g + 28 * d - 3 * g * g + 0.05391597401814761 + 24 * d * d) * p * p * p * p * p / 120) / Math.cos(b);
  if (A = l2 + s(A), "number" == typeof t2.accuracy) {
    const e3 = c({ northing: t2.northing + t2.accuracy, easting: t2.easting + t2.accuracy, zoneLetter: t2.zoneLetter, zoneNumber: t2.zoneNumber });
    m = { top: e3.lat, right: e3.lon, bottom: y, left: A };
  } else m = { lat: y, lon: A };
  return m;
}
function h(t2) {
  if (t2 <= 84 && t2 >= 72) return "X";
  if (t2 < 72 && t2 >= -80) {
    const e2 = 8, r2 = -80;
    return "CDEFGHJKLMNPQRSTUVWX"[Math.floor((t2 - r2) / e2)];
  }
  return t2 > 84 || t2 < -80 ? "Z" : void 0;
}
function f(t2) {
  let e2 = t2 % 6;
  return 0 === e2 && (e2 = 6), e2;
}
function l(n2) {
  if (n2 && 0 === n2.length) throw new TypeError("MGRSPoint coverting from nothing");
  n2 = n2.replace(/ /g, "");
  const { length: o2 } = n2;
  let a2, i2 = null, s2 = "", c2 = 0;
  for (; !/[A-Z]/.test(a2 = n2.charAt(c2)); ) {
    if (c2 >= 2) throw new Error("MGRSPoint bad conversion from: " + n2);
    s2 += a2, c2++;
  }
  const h2 = parseInt(s2, 10);
  if (0 === c2 || c2 + 3 > o2) throw new Error("MGRSPoint bad conversion from " + n2);
  const l2 = n2.charAt(c2++);
  if (l2 <= "A" || "B" === l2 || "Y" === l2 || l2 >= "Z" || "I" === l2 || "O" === l2) throw new Error(`MGRSPoint zone letter ${l2} not handled: ${n2}`);
  i2 = n2.substring(c2, c2 += 2);
  const b = f(h2), M = function(n3, o3) {
    let a3 = "AJSAJS".charCodeAt(o3 - 1), i3 = 1e5, s3 = false;
    for (; a3 !== n3.charCodeAt(0); ) {
      if (a3++, a3 === e && a3++, a3 === r && a3++, a3 > 90) {
        if (s3) throw new Error("Bad character: " + n3);
        a3 = t, s3 = true;
      }
      i3 += 1e5;
    }
    return i3;
  }(i2.charAt(0), b);
  let d = function(n3, o3) {
    if (n3 > "V") throw new TypeError("MGRSPoint given invalid Northing " + n3);
    let a3 = "AFAFAF".charCodeAt(o3 - 1), i3 = 0, s3 = false;
    for (; a3 !== n3.charCodeAt(0); ) {
      if (a3++, a3 === e && a3++, a3 === r && a3++, a3 > 86) {
        if (s3) throw new Error("Bad character: " + n3);
        a3 = t, s3 = true;
      }
      i3 += 1e5;
    }
    return i3;
  }(i2.charAt(1), b);
  for (; d < u(l2); ) d += 2e6;
  const g = o2 - c2;
  if (g % 2 != 0) throw new Error("MGRSPoint has to have an even number\nof digits after the zone letter and two 100km letters - front\nhalf for easting meters, second half for\nnorthing meters " + n2);
  const w = g / 2;
  let p, y, m, A = 0, k = 0;
  w > 0 && (p = 1e5 / Math.pow(10, w), y = n2.substring(c2, c2 + w), A = parseFloat(y) * p, m = n2.substring(c2 + w), k = parseFloat(m) * p);
  return { easting: A + M, northing: k + d, zoneLetter: l2, zoneNumber: h2, accuracy: p };
}
function u(t2) {
  let e2;
  switch (t2) {
    case "C":
      e2 = 11e5;
      break;
    case "D":
      e2 = 2e6;
      break;
    case "E":
      e2 = 28e5;
      break;
    case "F":
      e2 = 37e5;
      break;
    case "G":
      e2 = 46e5;
      break;
    case "H":
      e2 = 55e5;
      break;
    case "J":
      e2 = 64e5;
      break;
    case "K":
      e2 = 73e5;
      break;
    case "L":
      e2 = 82e5;
      break;
    case "M":
      e2 = 91e5;
      break;
    case "N":
      e2 = 0;
      break;
    case "P":
      e2 = 8e5;
      break;
    case "Q":
      e2 = 17e5;
      break;
    case "R":
      e2 = 26e5;
      break;
    case "S":
      e2 = 35e5;
      break;
    case "T":
      e2 = 44e5;
      break;
    case "U":
      e2 = 53e5;
      break;
    case "V":
      e2 = 62e5;
      break;
    case "W":
      e2 = 7e6;
      break;
    case "X":
      e2 = 79e5;
      break;
    default:
      e2 = -1;
  }
  if (e2 >= 0) return e2;
  throw new TypeError("Invalid zone letter: " + t2);
}
export {
  n as forward,
  h as getLetterDesignator,
  o as inverse,
  a as toPoint
};
//# sourceMappingURL=mgrs.js.map
