{"version": 3, "sources": ["../../mgrs/mgrs.js"], "sourcesContent": ["/**\n * UTM zones are grouped, and assigned to one of a group of 6\n * sets.\n *\n * {int} @private\n */\nconst NUM_100K_SETS = 6;\n\n/**\n * The column letters (for easting) of the lower left value, per\n * set.\n *\n * {string} @private\n */\nconst SET_ORIGIN_COLUMN_LETTERS = 'AJSAJS';\n\n/**\n * The row letters (for northing) of the lower left value, per\n * set.\n *\n * {string} @private\n */\nconst SET_ORIGIN_ROW_LETTERS = 'AFAFAF';\n\nconst A = 65; // A\nconst I = 73; // I\nconst O = 79; // O\nconst V = 86; // V\nconst Z = 90; // Z\n\n/**\n * First eccentricity squared\n * {number} @private\n */\nconst ECC_SQUARED = 0.00669438;\n\n/**\n * Scale factor along the central meridian\n * {number} @private\n */\nconst SCALE_FACTOR = 0.9996;\n\n/**\n * Semimajor axis (half the width of the earth) in meters\n * {number} @private\n */\nconst SEMI_MAJOR_AXIS = 6378137;\n\n/**\n * The easting of the central meridian of each UTM zone\n * {number} @private\n */\nconst EASTING_OFFSET = 500000;\n\n/**\n * The northing of the equator for southern hemisphere locations (in UTM)\n * {number} @private\n */\nconst NORTHING_OFFFSET = 10000000;\n\n/**\n * UTM zone width in degrees\n * {number} private\n */\nconst UTM_ZONE_WIDTH = 6;\n\n/**\n * Half the width of a UTM zone in degrees\n * {number} private\n */\nconst HALF_UTM_ZONE_WIDTH = UTM_ZONE_WIDTH / 2;\n\n/**\n * Convert lat/lon to MGRS.\n *\n * @param {[number, number]} ll Array with longitude and latitude on a\n *     WGS84 ellipsoid.\n * @param {number} [accuracy=5] Accuracy in digits (5 for 1 m, 4 for 10 m, 3 for\n *      100 m, 2 for 1 km, 1 for 10 km or 0 for 100 km). Optional, default is 5.\n * @return {string} the MGRS string for the given location and accuracy.\n */\nexport function forward(ll, accuracy) {\n  accuracy = typeof accuracy === 'number' ? accuracy : 5; // default accuracy 1m\n\n  if (!Array.isArray(ll)) {\n    throw new TypeError('forward did not receive an array');\n  }\n\n  if (typeof ll[0] === 'string' || typeof ll[1] === 'string') {\n    throw new TypeError('forward received an array of strings, but it only accepts an array of numbers.');\n  }\n\n  const [ lon, lat ] = ll;\n  if (lon < -180 || lon > 180) {\n    throw new TypeError(`forward received an invalid longitude of ${lon}`);\n  }\n  if (lat < -90 || lat > 90) {\n    throw new TypeError(`forward received an invalid latitude of ${lat}`);\n  }\n\n  if (lat < -80 || lat > 84) {\n    throw new TypeError(`forward received a latitude of ${lat}, but this library does not support conversions of points in polar regions below 80°S and above 84°N`);\n  }\n\n  return encode(LLtoUTM({ lat, lon }), accuracy);\n}\n\n/**\n * Convert MGRS to lat/lon bounding box.\n *\n * @param {string} mgrs MGRS string.\n * @return {[number,number,number,number]} An array with left (longitude),\n *    bottom (latitude), right\n *    (longitude) and top (latitude) values in WGS84, representing the\n *    bounding box for the provided MGRS reference.\n */\nexport function inverse(mgrs) {\n  const bbox = UTMtoLL(decode(mgrs.toUpperCase()));\n  if (typeof bbox.lat === 'number' && typeof bbox.lon === 'number') {\n    return [bbox.lon, bbox.lat, bbox.lon, bbox.lat];\n  }\n  return [bbox.left, bbox.bottom, bbox.right, bbox.top];\n}\n\nexport function toPoint(mgrs) {\n  if (mgrs === '') {\n    throw new TypeError('toPoint received a blank string');\n  }\n  const bbox = UTMtoLL(decode(mgrs.toUpperCase()));\n  if (typeof bbox.lat === 'number' && typeof bbox.lon === 'number') {\n    return [bbox.lon, bbox.lat];\n  }\n  return [(bbox.left + bbox.right) / 2, (bbox.top + bbox.bottom) / 2];\n}\n\n/**\n * Conversion from degrees to radians.\n *\n * @private\n * @param {number} deg the angle in degrees.\n * @return {number} the angle in radians.\n */\nfunction degToRad(deg) {\n  return (deg * (Math.PI / 180));\n}\n\n/**\n * Conversion from radians to degrees.\n *\n * @private\n * @param {number} rad the angle in radians.\n * @return {number} the angle in degrees.\n */\nfunction radToDeg(rad) {\n  return (180 * (rad / Math.PI));\n}\n\n/**\n * Converts a set of Longitude and Latitude co-ordinates to UTM\n * using the WGS84 ellipsoid.\n *\n * @private\n * @param {object} ll Object literal with lat and lon properties\n *     representing the WGS84 coordinate to be converted.\n * @return {object} Object literal containing the UTM value with easting,\n *     northing, zoneNumber and zoneLetter properties, and an optional\n *     accuracy property in digits. Returns null if the conversion failed.\n */\nfunction LLtoUTM(ll) {\n  const Lat = ll.lat;\n  const Long = ll.lon;\n  const a = SEMI_MAJOR_AXIS;\n  const LatRad = degToRad(Lat);\n  const LongRad = degToRad(Long);\n  let ZoneNumber;\n  // (int)\n  ZoneNumber = Math.floor((Long + 180) / 6) + 1;\n\n  //Make sure the longitude 180 is in Zone 60\n  if (Long === 180) {\n    ZoneNumber = 60;\n  }\n\n  // Special zone for Norway\n  if (Lat >= 56 && Lat < 64 && Long >= 3 && Long < 12) {\n    ZoneNumber = 32;\n  }\n\n  // Special zones for Svalbard\n  if (Lat >= 72 && Lat < 84) {\n    if (Long >= 0 && Long < 9) {\n      ZoneNumber = 31;\n    }\n    else if (Long >= 9 && Long < 21) {\n      ZoneNumber = 33;\n    }\n    else if (Long >= 21 && Long < 33) {\n      ZoneNumber = 35;\n    }\n    else if (Long >= 33 && Long < 42) {\n      ZoneNumber = 37;\n    }\n  }\n\n  // +HALF_UTM_ZONE_WIDTH puts origin in middle of zone\n  const LongOrigin = (ZoneNumber - 1) * UTM_ZONE_WIDTH - 180 + HALF_UTM_ZONE_WIDTH;\n\n  const LongOriginRad = degToRad(LongOrigin);\n\n  const eccPrimeSquared = (ECC_SQUARED) / (1 - ECC_SQUARED);\n\n  const N = a / Math.sqrt(1 - ECC_SQUARED * Math.sin(LatRad) * Math.sin(LatRad));\n  const T = Math.tan(LatRad) * Math.tan(LatRad);\n  const C = eccPrimeSquared * Math.cos(LatRad) * Math.cos(LatRad);\n  const A = Math.cos(LatRad) * (LongRad - LongOriginRad);\n\n  const M = a * ((1 - ECC_SQUARED / 4 - 3 * ECC_SQUARED * ECC_SQUARED / 64 - 5 * ECC_SQUARED * ECC_SQUARED * ECC_SQUARED / 256) * LatRad - (3 * ECC_SQUARED / 8 + 3 * ECC_SQUARED * ECC_SQUARED / 32 + 45 * ECC_SQUARED * ECC_SQUARED * ECC_SQUARED / 1024) * Math.sin(2 * LatRad) + (15 * ECC_SQUARED * ECC_SQUARED / 256 + 45 * ECC_SQUARED * ECC_SQUARED * ECC_SQUARED / 1024) * Math.sin(4 * LatRad) - (35 * ECC_SQUARED * ECC_SQUARED * ECC_SQUARED / 3072) * Math.sin(6 * LatRad));\n\n  const UTMEasting = (SCALE_FACTOR * N * (A + (1 - T + C) * A * A * A / 6 + (5 - 18 * T + T * T + 72 * C - 58 * eccPrimeSquared) * A * A * A * A * A / 120) + EASTING_OFFSET);\n\n  let UTMNorthing = (SCALE_FACTOR * (M + N * Math.tan(LatRad) * (A * A / 2 + (5 - T + 9 * C + 4 * C * C) * A * A * A * A / 24 + (61 - 58 * T + T * T + 600 * C - 330 * eccPrimeSquared) * A * A * A * A * A * A / 720)));\n  if (Lat < 0) {\n    UTMNorthing += NORTHING_OFFFSET;\n  }\n\n  return {\n    northing: Math.trunc(UTMNorthing),\n    easting: Math.trunc(UTMEasting),\n    zoneNumber: ZoneNumber,\n    zoneLetter: getLetterDesignator(Lat)\n  };\n}\n\n/**\n * Converts UTM coords to lat/long, using the WGS84 ellipsoid. This is a convenience\n * class where the Zone can be specified as a single string eg.\"60N\" which\n * is then broken down into the ZoneNumber and ZoneLetter.\n *\n * @private\n * @param {object} utm An object literal with northing, easting, zoneNumber\n *     and zoneLetter properties. If an optional accuracy property is\n *     provided (in meters), a bounding box will be returned instead of\n *     latitude and longitude.\n * @return {object} An object literal containing either lat and lon values\n *     (if no accuracy was provided), or top, right, bottom and left values\n *     for the bounding box calculated according to the provided accuracy.\n *     Returns null if the conversion failed.\n */\nfunction UTMtoLL(utm) {\n\n  const UTMNorthing = utm.northing;\n  const UTMEasting = utm.easting;\n  const { zoneLetter, zoneNumber } = utm;\n  // check the ZoneNummber is valid\n  if (zoneNumber < 0 || zoneNumber > 60) {\n    return null;\n  }\n\n  const a = SEMI_MAJOR_AXIS;\n  const e1 = (1 - Math.sqrt(1 - ECC_SQUARED)) / (1 + Math.sqrt(1 - ECC_SQUARED));\n\n  // remove 500,000 meter offset for longitude\n  const x = UTMEasting - EASTING_OFFSET;\n  let y = UTMNorthing;\n\n  // We must know somehow if we are in the Northern or Southern\n  // hemisphere, this is the only time we use the letter So even\n  // if the Zone letter isn't exactly correct it should indicate\n  // the hemisphere correctly\n  if (zoneLetter < 'N') {\n    y -= NORTHING_OFFFSET; // remove offset used for southern hemisphere\n  }\n\n  // +HALF_UTM_ZONE_WIDTH puts origin in middle of zone\n  const LongOrigin = (zoneNumber - 1) * UTM_ZONE_WIDTH - 180 + HALF_UTM_ZONE_WIDTH;\n\n  const eccPrimeSquared = (ECC_SQUARED) / (1 - ECC_SQUARED);\n\n  const M = y / SCALE_FACTOR;\n  const mu = M / (a * (1 - ECC_SQUARED / 4 - 3 * ECC_SQUARED * ECC_SQUARED / 64 - 5 * ECC_SQUARED * ECC_SQUARED * ECC_SQUARED / 256));\n\n  const phi1Rad = mu + (3 * e1 / 2 - 27 * e1 * e1 * e1 / 32) * Math.sin(2 * mu) + (21 * e1 * e1 / 16 - 55 * e1 * e1 * e1 * e1 / 32) * Math.sin(4 * mu) + (151 * e1 * e1 * e1 / 96) * Math.sin(6 * mu);\n  // double phi1 = ProjMath.radToDeg(phi1Rad);\n\n  const N1 = a / Math.sqrt(1 - ECC_SQUARED * Math.sin(phi1Rad) * Math.sin(phi1Rad));\n  const T1 = Math.tan(phi1Rad) * Math.tan(phi1Rad);\n  const C1 = eccPrimeSquared * Math.cos(phi1Rad) * Math.cos(phi1Rad);\n  const R1 = a * (1 - ECC_SQUARED) / Math.pow(1 - ECC_SQUARED * Math.sin(phi1Rad) * Math.sin(phi1Rad), 1.5);\n  const D = x / (N1 * SCALE_FACTOR);\n\n  let lat = phi1Rad - (N1 * Math.tan(phi1Rad) / R1) * (D * D / 2 - (5 + 3 * T1 + 10 * C1 - 4 * C1 * C1 - 9 * eccPrimeSquared) * D * D * D * D / 24 + (61 + 90 * T1 + 298 * C1 + 45 * T1 * T1 - 252 * eccPrimeSquared - 3 * C1 * C1) * D * D * D * D * D * D / 720);\n  lat = radToDeg(lat);\n\n  let lon = (D - (1 + 2 * T1 + C1) * D * D * D / 6 + (5 - 2 * C1 + 28 * T1 - 3 * C1 * C1 + 8 * eccPrimeSquared + 24 * T1 * T1) * D * D * D * D * D / 120) / Math.cos(phi1Rad);\n  lon = LongOrigin + radToDeg(lon);\n\n  let result;\n  if (typeof utm.accuracy === 'number') {\n    const topRight = UTMtoLL({\n      northing: utm.northing + utm.accuracy,\n      easting: utm.easting + utm.accuracy,\n      zoneLetter: utm.zoneLetter,\n      zoneNumber: utm.zoneNumber\n    });\n    result = {\n      top: topRight.lat,\n      right: topRight.lon,\n      bottom: lat,\n      left: lon\n    };\n  }\n  else {\n    result = {\n      lat,\n      lon\n    };\n  }\n  return result;\n}\n\n/**\n * Calculates the MGRS letter designator for the given latitude.\n *\n * @private (Not intended for public API, only exported for testing.)\n * @param {number} latitude The latitude in WGS84 to get the letter designator\n *     for.\n * @return {string} The letter designator.\n */\nexport function getLetterDesignator(latitude) {\n  if (latitude <= 84 && latitude >= 72) {\n    // the X band is 12 degrees high\n    return 'X';\n  } else if (latitude < 72 && latitude >= -80) {\n    // Latitude bands are lettered C through X, excluding I and O\n    const bandLetters = 'CDEFGHJKLMNPQRSTUVWX';\n    const bandHeight = 8;\n    const minLatitude = -80;\n    const index = Math.floor((latitude - minLatitude) / bandHeight);\n    return bandLetters[index];\n  } else if (latitude > 84 || latitude < -80) {\n    //This is here as an error flag to show that the Latitude is\n    //outside MGRS limits\n    return 'Z';\n  }\n}\n\n/**\n * Encodes a UTM location as MGRS string.\n *\n * @private\n * @param {object} utm An object literal with easting, northing,\n *     zoneLetter, zoneNumber\n * @param {number} accuracy Accuracy in digits (0-5).\n * @return {string} MGRS string for the given UTM location.\n */\nfunction encode(utm, accuracy) {\n  // prepend with leading zeroes\n  const seasting = '00000' + utm.easting,\n    snorthing = '00000' + utm.northing;\n\n  return utm.zoneNumber + utm.zoneLetter + get100kID(utm.easting, utm.northing, utm.zoneNumber) + seasting.substr(seasting.length - 5, accuracy) + snorthing.substr(snorthing.length - 5, accuracy);\n}\n\n/**\n * Get the two letter 100k designator for a given UTM easting,\n * northing and zone number value.\n *\n * @private\n * @param {number} easting\n * @param {number} northing\n * @param {number} zoneNumber\n * @return {string} the two letter 100k designator for the given UTM location.\n */\nfunction get100kID(easting, northing, zoneNumber) {\n  const setParm = get100kSetForZone(zoneNumber);\n  const setColumn = Math.floor(easting / 100000);\n  const setRow = Math.floor(northing / 100000) % 20;\n  return getLetter100kID(setColumn, setRow, setParm);\n}\n\n/**\n * Given a UTM zone number, figure out the MGRS 100K set it is in.\n *\n * @private\n * @param {number} i An UTM zone number.\n * @return {number} the 100k set the UTM zone is in.\n */\nfunction get100kSetForZone(i) {\n  let setParm = i % NUM_100K_SETS;\n  if (setParm === 0) {\n    setParm = NUM_100K_SETS;\n  }\n\n  return setParm;\n}\n\n/**\n * Get the two-letter MGRS 100k designator given information\n * translated from the UTM northing, easting and zone number.\n *\n * @private\n * @param {number} column the column index as it relates to the MGRS\n *        100k set spreadsheet, created from the UTM easting.\n *        Values are 1-8.\n * @param {number} row the row index as it relates to the MGRS 100k set\n *        spreadsheet, created from the UTM northing value. Values\n *        are from 0-19.\n * @param {number} parm the set block, as it relates to the MGRS 100k set\n *        spreadsheet, created from the UTM zone. Values are from\n *        1-60.\n * @return {string} two letter MGRS 100k code.\n */\nfunction getLetter100kID(column, row, parm) {\n  // colOrigin and rowOrigin are the letters at the origin of the set\n  const index = parm - 1;\n  const colOrigin = SET_ORIGIN_COLUMN_LETTERS.charCodeAt(index);\n  const rowOrigin = SET_ORIGIN_ROW_LETTERS.charCodeAt(index);\n\n  // colInt and rowInt are the letters to build to return\n  let colInt = colOrigin + column - 1;\n  let rowInt = rowOrigin + row;\n  let rollover = false;\n\n  if (colInt > Z) {\n    colInt = colInt - Z + A - 1;\n    rollover = true;\n  }\n\n  if (colInt === I || (colOrigin < I && colInt > I) || ((colInt > I || colOrigin < I) && rollover)) {\n    colInt++;\n  }\n\n  if (colInt === O || (colOrigin < O && colInt > O) || ((colInt > O || colOrigin < O) && rollover)) {\n    colInt++;\n\n    if (colInt === I) {\n      colInt++;\n    }\n  }\n\n  if (colInt > Z) {\n    colInt = colInt - Z + A - 1;\n  }\n\n  if (rowInt > V) {\n    rowInt = rowInt - V + A - 1;\n    rollover = true;\n  }\n  else {\n    rollover = false;\n  }\n\n  if (((rowInt === I) || ((rowOrigin < I) && (rowInt > I))) || (((rowInt > I) || (rowOrigin < I)) && rollover)) {\n    rowInt++;\n  }\n\n  if (((rowInt === O) || ((rowOrigin < O) && (rowInt > O))) || (((rowInt > O) || (rowOrigin < O)) && rollover)) {\n    rowInt++;\n\n    if (rowInt === I) {\n      rowInt++;\n    }\n  }\n\n  if (rowInt > V) {\n    rowInt = rowInt - V + A - 1;\n  }\n\n  const twoLetter = String.fromCharCode(colInt) + String.fromCharCode(rowInt);\n  return twoLetter;\n}\n\n/**\n * Decode the UTM parameters from a MGRS string.\n *\n * @private\n * @param {string} mgrsString an UPPERCASE coordinate string is expected.\n * @return {object} An object literal with easting, northing, zoneLetter,\n *     zoneNumber and accuracy (in meters) properties.\n */\nfunction decode(mgrsString) {\n\n  if (mgrsString && mgrsString.length === 0) {\n    throw new TypeError('MGRSPoint coverting from nothing');\n  }\n\n  //remove any spaces in MGRS String\n  mgrsString = mgrsString.replace(/ /g, '');\n\n  const { length } = mgrsString;\n\n  let hunK = null;\n  let sb = '';\n  let testChar;\n  let i = 0;\n\n  // get Zone number\n  while (!(/[A-Z]/).test(testChar = mgrsString.charAt(i))) {\n    if (i >= 2) {\n      throw new Error(`MGRSPoint bad conversion from: ${mgrsString}`);\n    }\n    sb += testChar;\n    i++;\n  }\n\n  const zoneNumber = parseInt(sb, 10);\n\n  if (i === 0 || i + 3 > length) {\n    // A good MGRS string has to be 4-5 digits long,\n    // ##AAA/#AAA at least.\n    throw new Error(`MGRSPoint bad conversion from ${mgrsString}`);\n  }\n\n  const zoneLetter = mgrsString.charAt(i++);\n\n  // Should we check the zone letter here? Why not.\n  if (zoneLetter <= 'A' || zoneLetter === 'B' || zoneLetter === 'Y' || zoneLetter >= 'Z' || zoneLetter === 'I' || zoneLetter === 'O') {\n    throw new Error(`MGRSPoint zone letter ${zoneLetter} not handled: ${mgrsString}`);\n  }\n\n  hunK = mgrsString.substring(i, i += 2);\n\n  const set = get100kSetForZone(zoneNumber);\n\n  const east100k = getEastingFromChar(hunK.charAt(0), set);\n  let north100k = getNorthingFromChar(hunK.charAt(1), set);\n\n  // We have a bug where the northing may be 2000000 too low.\n  // How\n  // do we know when to roll over?\n\n  while (north100k < getMinNorthing(zoneLetter)) {\n    north100k += 2000000;\n  }\n\n  // calculate the char index for easting/northing separator\n  const remainder = length - i;\n\n  if (remainder % 2 !== 0) {\n    throw new Error(`MGRSPoint has to have an even number\nof digits after the zone letter and two 100km letters - front\nhalf for easting meters, second half for\nnorthing meters ${mgrsString}`);\n  }\n\n  const sep = remainder / 2;\n\n  let sepEasting = 0;\n  let sepNorthing = 0;\n  let accuracyBonus, sepEastingString, sepNorthingString;\n  if (sep > 0) {\n    accuracyBonus = 100000 / Math.pow(10, sep);\n    sepEastingString = mgrsString.substring(i, i + sep);\n    sepEasting = parseFloat(sepEastingString) * accuracyBonus;\n    sepNorthingString = mgrsString.substring(i + sep);\n    sepNorthing = parseFloat(sepNorthingString) * accuracyBonus;\n  }\n\n  const easting = sepEasting + east100k;\n  const northing = sepNorthing + north100k;\n\n  return {\n    easting,\n    northing,\n    zoneLetter,\n    zoneNumber,\n    accuracy: accuracyBonus\n  };\n}\n\n/**\n * Given the first letter from a two-letter MGRS 100k zone, and given the\n * MGRS table set for the zone number, figure out the easting value that\n * should be added to the other, secondary easting value.\n *\n * @private\n * @param {string} e The first letter from a two-letter MGRS 100´k zone.\n * @param {number} set The MGRS table set for the zone number.\n * @return {number} The easting value for the given letter and set.\n */\nfunction getEastingFromChar(e, set) {\n  // colOrigin is the letter at the origin of the set for the\n  // column\n  let curCol = SET_ORIGIN_COLUMN_LETTERS.charCodeAt(set - 1);\n  let eastingValue = 100000;\n  let rewindMarker = false;\n\n  while (curCol !== e.charCodeAt(0)) {\n    curCol++;\n    if (curCol === I) {\n      curCol++;\n    }\n    if (curCol === O) {\n      curCol++;\n    }\n    if (curCol > Z) {\n      if (rewindMarker) {\n        throw new Error(`Bad character: ${e}`);\n      }\n      curCol = A;\n      rewindMarker = true;\n    }\n    eastingValue += 100000;\n  }\n\n  return eastingValue;\n}\n\n/**\n * Given the second letter from a two-letter MGRS 100k zone, and given the\n * MGRS table set for the zone number, figure out the northing value that\n * should be added to the other, secondary northing value. You have to\n * remember that Northings are determined from the equator, and the vertical\n * cycle of letters mean a 2000000 additional northing meters. This happens\n * approx. every 18 degrees of latitude. This method does *NOT* count any\n * additional northings. You have to figure out how many 2000000 meters need\n * to be added for the zone letter of the MGRS coordinate.\n *\n * @private\n * @param {string} n Second letter of the MGRS 100k zone\n * @param {number} set The MGRS table set number, which is dependent on the\n *     UTM zone number.\n * @return {number} The northing value for the given letter and set.\n */\nfunction getNorthingFromChar(n, set) {\n\n  if (n > 'V') {\n    throw new TypeError(`MGRSPoint given invalid Northing ${n}`);\n  }\n\n  // rowOrigin is the letter at the origin of the set for the\n  // column\n  let curRow = SET_ORIGIN_ROW_LETTERS.charCodeAt(set - 1);\n  let northingValue = 0;\n  let rewindMarker = false;\n\n  while (curRow !== n.charCodeAt(0)) {\n    curRow++;\n    if (curRow === I) {\n      curRow++;\n    }\n    if (curRow === O) {\n      curRow++;\n    }\n    // fixing a bug making whole application hang in this loop\n    // when 'n' is a wrong character\n    if (curRow > V) {\n      if (rewindMarker) { // making sure that this loop ends\n        throw new Error(`Bad character: ${n}`);\n      }\n      curRow = A;\n      rewindMarker = true;\n    }\n    northingValue += 100000;\n  }\n\n  return northingValue;\n}\n\n/**\n * The function getMinNorthing returns the minimum northing value of a MGRS\n * zone.\n *\n * Ported from Geotrans' c Lattitude_Band_Value structure table.\n *\n * @private\n * @param {string} zoneLetter The MGRS zone to get the min northing for.\n * @return {number}\n */\nfunction getMinNorthing(zoneLetter) {\n  let northing;\n  switch (zoneLetter) {\n  case 'C':\n    northing = 1100000;\n    break;\n  case 'D':\n    northing = 2000000;\n    break;\n  case 'E':\n    northing = 2800000;\n    break;\n  case 'F':\n    northing = 3700000;\n    break;\n  case 'G':\n    northing = 4600000;\n    break;\n  case 'H':\n    northing = 5500000;\n    break;\n  case 'J':\n    northing = 6400000;\n    break;\n  case 'K':\n    northing = 7300000;\n    break;\n  case 'L':\n    northing = 8200000;\n    break;\n  case 'M':\n    northing = 9100000;\n    break;\n  case 'N':\n    northing = 0;\n    break;\n  case 'P':\n    northing = 800000;\n    break;\n  case 'Q':\n    northing = 1700000;\n    break;\n  case 'R':\n    northing = 2600000;\n    break;\n  case 'S':\n    northing = 3500000;\n    break;\n  case 'T':\n    northing = 4400000;\n    break;\n  case 'U':\n    northing = 5300000;\n    break;\n  case 'V':\n    northing = 6200000;\n    break;\n  case 'W':\n    northing = 7000000;\n    break;\n  case 'X':\n    northing = 7900000;\n    break;\n  default:\n    northing = -1;\n  }\n  if (northing >= 0) {\n    return northing;\n  }\n  else {\n    throw new TypeError(`Invalid zone letter: ${zoneLetter}`);\n  }\n\n}\n"], "mappings": ";;;AAMA,IAkBMA,IAAI;AAlBV,IAmBMC,IAAI;AAnBV,IAoBMC,IAAI;AAuDH,SAASC,EAAQC,IAAIC,IAAAA;AAG1B,MAFAA,KAA+B,YAAA,OAAbA,KAAwBA,KAAW,GAAA,CAEhDC,MAAMC,QAAQH,EAAAA,EACjB,OAAM,IAAII,UAAU,kCAAA;AAGtB,MAAqB,YAAA,OAAVJ,GAAG,CAAA,KAAoC,YAAA,OAAVA,GAAG,CAAA,EACzC,OAAM,IAAII,UAAU,gFAAA;AAGtB,QAAA,CAAQC,IAAKC,EAAAA,IAAQN;AACrB,MAAIK,KAAAA,QAAcA,KAAM,IACtB,OAAM,IAAID,UAAU,8CAA4CC,EAAAA;AAElE,MAAIC,KAAAA,OAAaA,KAAM,GACrB,OAAM,IAAIF,UAAU,6CAA2CE,EAAAA;AAGjE,MAAIA,KAAAA,OAAaA,KAAM,GACrB,OAAM,IAAIF,UAAU,kCAAkCE,EAAAA,sGAAAA;AAGxD,SA2PF,SAAgBC,IAAKN,IAAAA;AAEnB,UAAMO,KAAW,UAAUD,GAAIE,SAC7BC,KAAY,UAAUH,GAAII;AAE5B,WAAOJ,GAAIK,aAAaL,GAAIM,aAa9B,SAAmBJ,IAASE,IAAUC,IAAAA;AACpC,YAAME,KAAUC,EAAkBH,EAAAA,GAC5BI,KAAYC,KAAKC,MAAMT,KAAU,GAAA,GACjCU,KAASF,KAAKC,MAAMP,KAAW,GAAA,IAAU;AAC/C,aAmCF,SAAyBS,IAAQC,IAAKC,IAAAA;AAEpC,cAAMC,KAAQD,KAAO,GACfE,KAjZ0B,SAiZYC,WAAWF,EAAAA,GACjDG,KA1YuB,SA0YYD,WAAWF,EAAAA;AAGpD,YAAII,KAASH,KAAYJ,KAAS,GAC9BQ,KAASF,KAAYL,IACrBQ,KAAAA;AAEAF,QAAAA,KA3YI,OA4YNA,KAASA,KA5YH,KA4YgB/B,IAAI,GAC1BiC,KAAAA;AAAW,SAGTF,OAAW9B,KAAM2B,KAAY3B,KAAK8B,KAAS9B,MAAQ8B,KAAS9B,KAAK2B,KAAY3B,MAAMgC,OACrFF;AAAAA,SAGEA,OAAW7B,KAAM0B,KAAY1B,KAAK6B,KAAS7B,MAAQ6B,KAAS7B,KAAK0B,KAAY1B,MAAM+B,QACrFF,MAEIA,OAAW9B,KACb8B;AAIAA,QAAAA,KA5ZI,OA6ZNA,KAASA,KA7ZH,KA6ZgB/B,IAAI;AAGxBgC,QAAAA,KAjaI,MAkaNA,KAASA,KAlaH,KAkagBhC,IAAI,GAC1BiC,KAAAA,QAGAA,KAAAA;AAAW,SAGPD,OAAW/B,KAAQ6B,KAAY7B,KAAO+B,KAAS/B,MAAW+B,KAAS/B,KAAO6B,KAAY7B,MAAOgC,OACjGD;AAAAA,SAGIA,OAAW9B,KAAQ4B,KAAY5B,KAAO8B,KAAS9B,MAAW8B,KAAS9B,KAAO4B,KAAY5B,MAAO+B,QACjGD,MAEIA,OAAW/B,KACb+B;AAIAA,QAAAA,KArbI,OAsbNA,KAASA,KAtbH,KAsbgBhC,IAAI;AAI5B,eADkBkC,OAAOC,aAAaJ,EAAAA,IAAUG,OAAOC,aAAaH,EAAAA;MAAAA,EA3F7CZ,IAAWG,IAAQL,EAAAA;IAAAA,EAjBSP,GAAIE,SAASF,GAAII,UAAUJ,GAAIK,UAAAA,IAAcJ,GAASwB,OAAOxB,GAASyB,SAAS,GAAGhC,EAAAA,IAAYS,GAAUsB,OAAOtB,GAAUuB,SAAS,GAAGhC,EAAAA;EAAAA,EAhM1L,SAAiBD,IAAAA;AACf,UAAMkC,KAAMlC,GAAGM,KACT6B,KAAOnC,GAAGK,KACV+B,KA7HgB,SA8HhBC,KAASC,EAASJ,EAAAA,GAClBK,KAAUD,EAASH,EAAAA;AACzB,QAAIK;AAEJA,IAAAA,KAAavB,KAAKC,OAAOiB,KAAO,OAAO,CAAA,IAAK,GAG/B,QAATA,OACFK,KAAa;AAIXN,IAAAA,MAAO,MAAMA,KAAM,MAAMC,MAAQ,KAAKA,KAAO,OAC/CK,KAAa;AAIXN,IAAAA,MAAO,MAAMA,KAAM,OACjBC,MAAQ,KAAKA,KAAO,IACtBK,KAAa,KAENL,MAAQ,KAAKA,KAAO,KAC3BK,KAAa,KAENL,MAAQ,MAAMA,KAAO,KAC5BK,KAAa,KAENL,MAAQ,MAAMA,KAAO,OAC5BK,KAAa;AAKjB,UAEMC,KAAgBH,EA/ID,KA6IDE,KAAa,KAAsB,MAvI7BE,CAAAA,GA6IpBC,KAAIP,KAAInB,KAAK2B,KAAK,IAjLN,YAiLwB3B,KAAK4B,IAAIR,EAAAA,IAAUpB,KAAK4B,IAAIR,EAAAA,CAAAA,GAChES,KAAI7B,KAAK8B,IAAIV,EAAAA,IAAUpB,KAAK8B,IAAIV,EAAAA,GAChCW,KAJkB,uBAII/B,KAAKgC,IAAIZ,EAAAA,IAAUpB,KAAKgC,IAAIZ,EAAAA,GAClDzC,IAAIqB,KAAKgC,IAAIZ,EAAAA,KAAWE,KAAUE,KAElCS,IAAId,MAAK,qBAAiHC,KAAS,uBAAmHpB,KAAK4B,IAAI,IAAIR,EAAAA,IAAU,uBAA+FpB,KAAK4B,IAAI,IAAIR,EAAAA,IAAU,uBAAwDpB,KAAK4B,IAAI,IAAIR,EAAAA,IAExcc,IAlLa,SAkLgBR,MAAK/C,KAAK,IAAIkD,KAAIE,MAAKpD,IAAIA,IAAIA,IAAI,KAAK,IAAI,KAAKkD,KAAIA,KAAIA,KAAI,KAAKE,KAAI,uBAAwBpD,IAAIA,IAAIA,IAAIA,IAAIA,IAAI,OAtKhI;AAwKrB,QAAIwD,IApLe,UAoLgBF,IAAIP,KAAI1B,KAAK8B,IAAIV,EAAAA,KAAWzC,IAAIA,IAAI,KAAK,IAAIkD,KAAI,IAAIE,KAAI,IAAIA,KAAIA,MAAKpD,IAAIA,IAAIA,IAAIA,IAAI,MAAM,KAAK,KAAKkD,KAAIA,KAAIA,KAAI,MAAME,KAAI,sBAAyBpD,IAAIA,IAAIA,IAAIA,IAAIA,IAAIA,IAAI;AAC5MsC,IAAAA,KAAM,MACRkB,KApKqB;AAuKvB,WAAO,EACLzC,UAAUM,KAAKoC,MAAMD,CAAAA,GACrB3C,SAASQ,KAAKoC,MAAMF,CAAAA,GACpBvC,YAAY4B,IACZ3B,YAAYyC,EAAoBpB,EAAAA,EAAAA;EAAAA,EA7HZ,EAAE5B,KAAAA,IAAKD,KAAAA,GAAAA,CAAAA,GAAQJ,EAAAA;AAAAA;AAYhC,SAASsD,EAAQC,IAAAA;AACtB,QAAMC,KAAOC,EAAQC,EAAOH,GAAKI,YAAAA,CAAAA,CAAAA;AACjC,SAAwB,YAAA,OAAbH,GAAKnD,OAAwC,YAAA,OAAbmD,GAAKpD,MACvC,CAACoD,GAAKpD,KAAKoD,GAAKnD,KAAKmD,GAAKpD,KAAKoD,GAAKnD,GAAAA,IAEtC,CAACmD,GAAKI,MAAMJ,GAAKK,QAAQL,GAAKM,OAAON,GAAKO,GAAAA;AAAAA;AAG5C,SAASC,EAAQT,IAAAA;AACtB,MAAa,OAATA,GACF,OAAM,IAAIpD,UAAU,iCAAA;AAEtB,QAAMqD,KAAOC,EAAQC,EAAOH,GAAKI,YAAAA,CAAAA,CAAAA;AACjC,SAAwB,YAAA,OAAbH,GAAKnD,OAAwC,YAAA,OAAbmD,GAAKpD,MACvC,CAACoD,GAAKpD,KAAKoD,GAAKnD,GAAAA,IAElB,EAAEmD,GAAKI,OAAOJ,GAAKM,SAAS,IAAIN,GAAKO,MAAMP,GAAKK,UAAU,CAAA;AAAA;AAUnE,SAASxB,EAAS4B,IAAAA;AAChB,SAAQA,MAAOjD,KAAKkD,KAAK;AAAA;AAU3B,SAASC,EAASC,IAAAA;AAChB,SAAeA,KAAMpD,KAAKkD,KAAlB;AAAA;AA8FV,SAAST,EAAQnD,IAAAA;AAEf,QAAM6C,KAAc7C,GAAII,UAClBwC,KAAa5C,GAAIE,SAAAA,EACjBI,YAAEA,IAAUD,YAAEA,GAAAA,IAAeL;AAEnC,MAAIK,KAAa,KAAKA,KAAa,GACjC,QAAO;AAGT,QAAMwB,KApNgB,SAqNhBkC,MAAM,IAAIrD,KAAK2B,KAAK,UAAA,MAAqB,IAAI3B,KAAK2B,KAAK,UAAA,IAGvD2B,KAAIpB,KAlNW;AAmNrB,MAAIqB,KAAIpB;AAMJvC,EAAAA,KAAa,QACf2D,MApNqB;AAwNvB,QAAMC,KAlNe,KAkND7D,KAAa,KAAsB,MA5M7B8B,GAiNpBgC,KADIF,KA9OS,SAAA,qBAiPbG,IAAUD,MAAM,IAAIJ,KAAK,IAAI,KAAKA,KAAKA,KAAKA,KAAK,MAAMrD,KAAK4B,IAAI,IAAI6B,EAAAA,KAAO,KAAKJ,KAAKA,KAAK,KAAK,KAAKA,KAAKA,KAAKA,KAAKA,KAAK,MAAMrD,KAAK4B,IAAI,IAAI6B,EAAAA,IAAO,MAAMJ,KAAKA,KAAKA,KAAK,KAAMrD,KAAK4B,IAAI,IAAI6B,EAAAA,GAG1LE,IAAKxC,KAAInB,KAAK2B,KAAK,IA1PP,YA0PyB3B,KAAK4B,IAAI8B,CAAAA,IAAW1D,KAAK4B,IAAI8B,CAAAA,CAAAA,GAClEE,IAAK5D,KAAK8B,IAAI4B,CAAAA,IAAW1D,KAAK8B,IAAI4B,CAAAA,GAClCG,IAVkB,uBAUK7D,KAAKgC,IAAI0B,CAAAA,IAAW1D,KAAKgC,IAAI0B,CAAAA,GACpDI,IAAAA,aAAK3C,KAAwBnB,KAAK+D,IAAI,IA7P1B,YA6P4C/D,KAAK4B,IAAI8B,CAAAA,IAAW1D,KAAK4B,IAAI8B,CAAAA,GAAU,GAAA,GAC/FM,IAAIV,MAxPS,SAwPJK;AAEf,MAAItE,IAAMqE,IAAWC,IAAK3D,KAAK8B,IAAI4B,CAAAA,IAAWI,KAAOE,IAAIA,IAAI,KAAK,IAAI,IAAIJ,IAAK,KAAKC,IAAK,IAAIA,IAAKA,IAAK,uBAAuBG,IAAIA,IAAIA,IAAIA,IAAI,MAAM,KAAK,KAAKJ,IAAK,MAAMC,IAAK,KAAKD,IAAKA,IAAK,qBAAwB,IAAIC,IAAKA,KAAMG,IAAIA,IAAIA,IAAIA,IAAIA,IAAIA,IAAI;AAC5P3E,MAAM8D,EAAS9D,CAAAA;AAEf,MAGI4E,GAHA7E,KAAO4E,KAAK,IAAI,IAAIJ,IAAKC,KAAMG,IAAIA,IAAIA,IAAI,KAAK,IAAI,IAAIH,IAAK,KAAKD,IAAK,IAAIC,IAAKA,IAAK,sBAAsB,KAAKD,IAAKA,KAAMI,IAAIA,IAAIA,IAAIA,IAAIA,IAAI,OAAOhE,KAAKgC,IAAI0B,CAAAA;AAInK,MAHAtE,IAAMoE,KAAaL,EAAS/D,CAAAA,GAGA,YAAA,OAAjBE,GAAIN,UAAuB;AACpC,UAAMkF,KAAWzB,EAAQ,EACvB/C,UAAUJ,GAAII,WAAWJ,GAAIN,UAC7BQ,SAASF,GAAIE,UAAUF,GAAIN,UAC3BY,YAAYN,GAAIM,YAChBD,YAAYL,GAAIK,WAAAA,CAAAA;AAElBsE,QAAS,EACPlB,KAAKmB,GAAS7E,KACdyD,OAAOoB,GAAS9E,KAChByD,QAAQxD,GACRuD,MAAMxD,EAAAA;EAAAA,MAIR6E,KAAS,EACP5E,KAAAA,GACAD,KAAAA,EAAAA;AAGJ,SAAO6E;AAAAA;AAWF,SAAS5B,EAAoB8B,IAAAA;AAClC,MAAIA,MAAY,MAAMA,MAAY,GAEhC,QAAO;AACF,MAAIA,KAAW,MAAMA,MAAAA,KAAiB;AAE3C,UACMC,KAAa,GACbC,KAAAA;AAEN,WAJoB,uBAGNrE,KAAKC,OAAOkE,KAAWE,MAAeD,EAAAA,CAAAA;EAAAA;AAE/C,SAAID,KAAW,MAAMA,KAAAA,MAGnB,MAAA;AAHF;AAgDT,SAASrE,EAAkBwE,IAAAA;AACzB,MAAIzE,KAAUyE,KA9XM;AAmYpB,SAJgB,MAAZzE,OACFA,KAhYkB,IAmYbA;AAAAA;AAuFT,SAAS6C,EAAO6B,IAAAA;AAEd,MAAIA,MAAoC,MAAtBA,GAAWvD,OAC3B,OAAM,IAAI7B,UAAU,kCAAA;AAItBoF,EAAAA,KAAaA,GAAWC,QAAQ,MAAM,EAAA;AAEtC,QAAA,EAAMxD,QAAEA,GAAAA,IAAWuD;AAEnB,MAEIE,IAFAC,KAAO,MACPC,KAAK,IAELL,KAAI;AAGR,SAAA,CAAQ,QAAUM,KAAKH,KAAWF,GAAWM,OAAOP,EAAAA,CAAAA,KAAK;AACvD,QAAIA,MAAK,EACP,OAAM,IAAIQ,MAAM,oCAAkCP,EAAAA;AAEpDI,IAAAA,MAAMF,IACNH;EAAAA;AAGF,QAAM3E,KAAaoF,SAASJ,IAAI,EAAA;AAEhC,MAAU,MAANL,MAAWA,KAAI,IAAItD,GAGrB,OAAM,IAAI8D,MAAM,mCAAiCP,EAAAA;AAGnD,QAAM3E,KAAa2E,GAAWM,OAAOP,IAAAA;AAGrC,MAAI1E,MAAc,OAAsB,QAAfA,MAAqC,QAAfA,MAAsBA,MAAc,OAAsB,QAAfA,MAAqC,QAAfA,GAC9G,OAAM,IAAIkF,MAAM,yBAAyBlF,EAAAA,iBAA2B2E,EAAAA,EAAAA;AAGtEG,EAAAA,KAAOH,GAAWS,UAAUV,IAAGA,MAAK,CAAA;AAEpC,QAAMW,IAAMnF,EAAkBH,EAAAA,GAExBuF,IAwDR,SAA4BC,IAAGF,IAAAA;AAG7B,QAAIG,KAzjB4B,SAyjBO5E,WAAWyE,KAAM,CAAA,GACpDI,KAAe,KACfC,KAAAA;AAEJ,WAAOF,OAAWD,GAAE3E,WAAW,CAAA,KAAI;AAQjC,UAPA4E,MACIA,OAAWxG,KACbwG,MAEEA,OAAWvG,KACbuG,MAEEA,KAvjBE,IAujBU;AACd,YAAIE,GACF,OAAM,IAAIR,MAAM,oBAAkBK,EAAAA;AAEpCC,QAAAA,KAASzG,GACT2G,KAAAA;MAAe;AAEjBD,MAAAA,MAAgB;IAAA;AAGlB,WAAOA;EAAAA,EAjF6BX,GAAKG,OAAO,CAAA,GAAII,CAAAA;AACpD,MAAIM,IAmGN,SAA6BC,IAAGP,IAAAA;AAE9B,QAAIO,KAAI,IACN,OAAM,IAAIrG,UAAU,sCAAoCqG,EAAAA;AAK1D,QAAIC,KAlmByB,SAkmBOjF,WAAWyE,KAAM,CAAA,GACjDS,KAAgB,GAChBJ,KAAAA;AAEJ,WAAOG,OAAWD,GAAEhF,WAAW,CAAA,KAAI;AAUjC,UATAiF,MACIA,OAAW7G,KACb6G,MAEEA,OAAW5G,KACb4G,MAIEA,KA3mBE,IA2mBU;AACd,YAAIH,GACF,OAAM,IAAIR,MAAM,oBAAkBU,EAAAA;AAEpCC,QAAAA,KAAS9G,GACT2G,KAAAA;MAAe;AAEjBI,MAAAA,MAAiB;IAAA;AAGnB,WAAOA;EAAAA,EAnI6BhB,GAAKG,OAAO,CAAA,GAAII,CAAAA;AAMpD,SAAOM,IAAYI,EAAe/F,EAAAA,IAChC2F,MAAa;AAIf,QAAMK,IAAY5E,KAASsD;AAE3B,MAAIsB,IAAY,KAAM,EACpB,OAAM,IAAId,MAAM,oKAGFP,EAAAA;AAGhB,QAAMsB,IAAMD,IAAY;AAExB,MAEIE,GAAeC,GAAkBC,GAFjCC,IAAa,GACbC,IAAc;AAEdL,MAAM,MACRC,IAAgB,MAAS9F,KAAK+D,IAAI,IAAI8B,CAAAA,GACtCE,IAAmBxB,GAAWS,UAAUV,IAAGA,KAAIuB,CAAAA,GAC/CI,IAAaE,WAAWJ,CAAAA,IAAoBD,GAC5CE,IAAoBzB,GAAWS,UAAUV,KAAIuB,CAAAA,GAC7CK,IAAcC,WAAWH,CAAAA,IAAqBF;AAMhD,SAAO,EACLtG,SAJcyG,IAAaf,GAK3BxF,UAJewG,IAAcX,GAK7B3F,YAAAA,IACAD,YAAAA,IACAX,UAAU8G,EAAAA;AAAAA;AAuGd,SAASH,EAAe/F,IAAAA;AACtB,MAAIF;AACJ,UAAQE,IAAAA;IACR,KAAK;AACHF,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF,KAAK;AACHA,MAAAA,KAAW;AACX;IACF;AACEA,MAAAA,KAAAA;EAAY;AAEd,MAAIA,MAAY,EACd,QAAOA;AAGP,QAAM,IAAIP,UAAU,0BAAwBS,EAAAA;AAAAA;", "names": ["A", "I", "O", "forward", "ll", "accuracy", "Array", "isArray", "TypeError", "lon", "lat", "utm", "seasting", "easting", "snorthing", "northing", "zoneNumber", "zoneLetter", "setParm", "get100kSetForZone", "setColumn", "Math", "floor", "setRow", "column", "row", "parm", "index", "colOrigin", "charCodeAt", "<PERSON><PERSON><PERSON><PERSON>", "colInt", "rowInt", "rollover", "String", "fromCharCode", "substr", "length", "Lat", "<PERSON>", "a", "LatRad", "degToRad", "LongRad", "ZoneNumber", "LongOriginRad", "UTM_ZONE_WIDTH", "N", "sqrt", "sin", "T", "tan", "C", "cos", "M", "UTMEasting", "UTMNorthing", "trunc", "getLetterDesignator", "inverse", "mgrs", "bbox", "UTMtoLL", "decode", "toUpperCase", "left", "bottom", "right", "top", "toPoint", "deg", "PI", "radToDeg", "rad", "e1", "x", "y", "<PERSON><PERSON><PERSON><PERSON>", "mu", "phi1Rad", "N1", "T1", "C1", "R1", "pow", "D", "result", "topRight", "latitude", "bandHeight", "minLatitude", "i", "mgrsString", "replace", "testChar", "hun<PERSON>", "sb", "test", "char<PERSON>t", "Error", "parseInt", "substring", "set", "east100k", "e", "curCol", "eastingValue", "rewind<PERSON><PERSON><PERSON>", "north100k", "n", "curRow", "northingValue", "getMinNorthing", "remainder", "sep", "accuracyBonus", "sepEastingString", "sepNorthingString", "sepEasting", "sepNorthing", "parseFloat"]}