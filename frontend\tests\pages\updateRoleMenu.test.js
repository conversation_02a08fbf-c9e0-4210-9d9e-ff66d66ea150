import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import UpdateRoleMenu from "../../src/pages/Dashboard/User/UpdateRoleMenu";
import axiosInstance from "../../src/axios";
import { useUser } from "../../src/hooks/UserHook";

jest.mock("../../src/axios");
jest.mock('../../src/hooks/UserHook');

jest.mock("../../src/components/ConfirmModal", () => ({ title, message, initialState, onClose, onConfirm }) => (
    <div role="dialog">
        <div>{title}</div>
        <div>{message}</div>
        <button onClick={onConfirm} data-testid="confirm">
            Confirm
        </button>
        <button onClick={onClose} data-testid="cancel">
            Cancel
        </button>
    </div>
));


describe("UpdateRoleMenu", () => {
    const mockUser = { _id: "user1", name: "User One", role_id: 1 };
    const mockRoles = [
        { role_id: 2, role_name: "User", hierarchy_number: 5 },
        { role_id: 1, role_name: "Admin", hierarchy_number: 1 },
    ];

    const mockOnSuccess = jest.fn();
    const mockSetUpdatingRole = jest.fn();
    const mockSetUpdateRoleAnchorEl = jest.fn();

    beforeEach(() => {
        axiosInstance.patch.mockResolvedValue({});
        useUser.mockReturnValue({
            user: {
                name: 'Test User',
                hasPermissions: jest.fn(),
                role: {
                    hierarchy_number: 2
                }
            },
        });
    });

    it("renders roles menu correctly", () => {
        render(
            <UpdateRoleMenu
                updateRoleAnchorEl={document.createElement("div")}
                setUpdateRoleAnchorEl={mockSetUpdateRoleAnchorEl}
                roles={mockRoles}
                selectedUser={mockUser}
                setUpdatingRole={mockSetUpdatingRole}
                onSuccess={mockOnSuccess}
            />
        );

        expect(screen.getByText("User")).toBeInTheDocument();
        expect(screen.getByText("Admin")).toBeInTheDocument();
    });

    it("closes the menu when clicking on a role", async () => {
        const { container } = render(
            <UpdateRoleMenu
                updateRoleAnchorEl={document.createElement("div")}
                setUpdateRoleAnchorEl={mockSetUpdateRoleAnchorEl}
                roles={mockRoles}
                selectedUser={mockUser}
                setUpdatingRole={mockSetUpdatingRole}
                onSuccess={mockOnSuccess}
            />
        );

        fireEvent.click(screen.getByText("Admin"));
        fireEvent.click(screen.getByTestId('confirm'));

        fireEvent.click(screen.getByRole("menuitem", { name: /Admin/i }));
        fireEvent.click(screen.getByTestId('cancel'));

        fireEvent.click(screen.getByRole("menuitem", { name: /User/i }));
        fireEvent.click(screen.getByTestId('cancel'));

        await waitFor(() => expect(mockSetUpdateRoleAnchorEl).toHaveBeenCalled());
    });
});
