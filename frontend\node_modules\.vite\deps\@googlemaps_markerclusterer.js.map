{"version": 3, "sources": ["../../kdbush/index.js", "../../supercluster/index.js", "../../@googlemaps/markerclusterer/node_modules/tslib/tslib.es6.js", "../../@googlemaps/markerclusterer/src/marker-utils.ts", "../../@googlemaps/markerclusterer/src/cluster.ts", "../../@googlemaps/markerclusterer/src/algorithms/utils.ts", "../../@googlemaps/markerclusterer/src/algorithms/core.ts", "../../@googlemaps/markerclusterer/src/algorithms/grid.ts", "../../@googlemaps/markerclusterer/src/algorithms/noop.ts", "../../@googlemaps/markerclusterer/src/algorithms/supercluster.ts", "../../@googlemaps/markerclusterer/src/algorithms/superviewport.ts", "../../@googlemaps/markerclusterer/src/renderer.ts", "../../@googlemaps/markerclusterer/src/overlay-view-safe.ts", "../../@googlemaps/markerclusterer/src/markerclusterer.ts"], "sourcesContent": ["\nconst ARRAY_TYPES = [\n    Int8Array, Uint8Array, Uint8ClampedArray, Int16Array, Uint16Array,\n    Int32Array, Uint32Array, Float32Array, Float64Array\n];\n\n/** @typedef {Int8ArrayConstructor | Uint8ArrayConstructor | Uint8ClampedArrayConstructor | Int16ArrayConstructor | Uint16ArrayConstructor | Int32ArrayConstructor | Uint32ArrayConstructor | Float32ArrayConstructor | Float64ArrayConstructor} TypedArrayConstructor */\n\nconst VERSION = 1; // serialized format version\nconst HEADER_SIZE = 8;\n\nexport default class KDBush {\n\n    /**\n     * Creates an index from raw `ArrayBuffer` data.\n     * @param {ArrayBuffer} data\n     */\n    static from(data) {\n        if (!(data instanceof ArrayBuffer)) {\n            throw new Error('Data must be an instance of ArrayBuffer.');\n        }\n        const [magic, versionAndType] = new Uint8Array(data, 0, 2);\n        if (magic !== 0xdb) {\n            throw new Error('Data does not appear to be in a KDBush format.');\n        }\n        const version = versionAndType >> 4;\n        if (version !== VERSION) {\n            throw new Error(`Got v${version} data when expected v${VERSION}.`);\n        }\n        const ArrayType = ARRAY_TYPES[versionAndType & 0x0f];\n        if (!ArrayType) {\n            throw new Error('Unrecognized array type.');\n        }\n        const [nodeSize] = new Uint16Array(data, 2, 1);\n        const [numItems] = new Uint32Array(data, 4, 1);\n\n        return new KDBush(numItems, nodeSize, ArrayType, data);\n    }\n\n    /**\n     * Creates an index that will hold a given number of items.\n     * @param {number} numItems\n     * @param {number} [nodeSize=64] Size of the KD-tree node (64 by default).\n     * @param {TypedArrayConstructor} [ArrayType=Float64Array] The array type used for coordinates storage (`Float64Array` by default).\n     * @param {ArrayBuffer} [data] (For internal use only)\n     */\n    constructor(numItems, nodeSize = 64, ArrayType = Float64Array, data) {\n        if (isNaN(numItems) || numItems < 0) throw new Error(`Unpexpected numItems value: ${numItems}.`);\n\n        this.numItems = +numItems;\n        this.nodeSize = Math.min(Math.max(+nodeSize, 2), 65535);\n        this.ArrayType = ArrayType;\n        this.IndexArrayType = numItems < 65536 ? Uint16Array : Uint32Array;\n\n        const arrayTypeIndex = ARRAY_TYPES.indexOf(this.ArrayType);\n        const coordsByteSize = numItems * 2 * this.ArrayType.BYTES_PER_ELEMENT;\n        const idsByteSize = numItems * this.IndexArrayType.BYTES_PER_ELEMENT;\n        const padCoords = (8 - idsByteSize % 8) % 8;\n\n        if (arrayTypeIndex < 0) {\n            throw new Error(`Unexpected typed array class: ${ArrayType}.`);\n        }\n\n        if (data && (data instanceof ArrayBuffer)) { // reconstruct an index from a buffer\n            this.data = data;\n            this.ids = new this.IndexArrayType(this.data, HEADER_SIZE, numItems);\n            this.coords = new this.ArrayType(this.data, HEADER_SIZE + idsByteSize + padCoords, numItems * 2);\n            this._pos = numItems * 2;\n            this._finished = true;\n        } else { // initialize a new index\n            this.data = new ArrayBuffer(HEADER_SIZE + coordsByteSize + idsByteSize + padCoords);\n            this.ids = new this.IndexArrayType(this.data, HEADER_SIZE, numItems);\n            this.coords = new this.ArrayType(this.data, HEADER_SIZE + idsByteSize + padCoords, numItems * 2);\n            this._pos = 0;\n            this._finished = false;\n\n            // set header\n            new Uint8Array(this.data, 0, 2).set([0xdb, (VERSION << 4) + arrayTypeIndex]);\n            new Uint16Array(this.data, 2, 1)[0] = nodeSize;\n            new Uint32Array(this.data, 4, 1)[0] = numItems;\n        }\n    }\n\n    /**\n     * Add a point to the index.\n     * @param {number} x\n     * @param {number} y\n     * @returns {number} An incremental index associated with the added item (starting from `0`).\n     */\n    add(x, y) {\n        const index = this._pos >> 1;\n        this.ids[index] = index;\n        this.coords[this._pos++] = x;\n        this.coords[this._pos++] = y;\n        return index;\n    }\n\n    /**\n     * Perform indexing of the added points.\n     */\n    finish() {\n        const numAdded = this._pos >> 1;\n        if (numAdded !== this.numItems) {\n            throw new Error(`Added ${numAdded} items when expected ${this.numItems}.`);\n        }\n        // kd-sort both arrays for efficient search\n        sort(this.ids, this.coords, this.nodeSize, 0, this.numItems - 1, 0);\n\n        this._finished = true;\n        return this;\n    }\n\n    /**\n     * Search the index for items within a given bounding box.\n     * @param {number} minX\n     * @param {number} minY\n     * @param {number} maxX\n     * @param {number} maxY\n     * @returns {number[]} An array of indices correponding to the found items.\n     */\n    range(minX, minY, maxX, maxY) {\n        if (!this._finished) throw new Error('Data not yet indexed - call index.finish().');\n\n        const {ids, coords, nodeSize} = this;\n        const stack = [0, ids.length - 1, 0];\n        const result = [];\n\n        // recursively search for items in range in the kd-sorted arrays\n        while (stack.length) {\n            const axis = stack.pop() || 0;\n            const right = stack.pop() || 0;\n            const left = stack.pop() || 0;\n\n            // if we reached \"tree node\", search linearly\n            if (right - left <= nodeSize) {\n                for (let i = left; i <= right; i++) {\n                    const x = coords[2 * i];\n                    const y = coords[2 * i + 1];\n                    if (x >= minX && x <= maxX && y >= minY && y <= maxY) result.push(ids[i]);\n                }\n                continue;\n            }\n\n            // otherwise find the middle index\n            const m = (left + right) >> 1;\n\n            // include the middle item if it's in range\n            const x = coords[2 * m];\n            const y = coords[2 * m + 1];\n            if (x >= minX && x <= maxX && y >= minY && y <= maxY) result.push(ids[m]);\n\n            // queue search in halves that intersect the query\n            if (axis === 0 ? minX <= x : minY <= y) {\n                stack.push(left);\n                stack.push(m - 1);\n                stack.push(1 - axis);\n            }\n            if (axis === 0 ? maxX >= x : maxY >= y) {\n                stack.push(m + 1);\n                stack.push(right);\n                stack.push(1 - axis);\n            }\n        }\n\n        return result;\n    }\n\n    /**\n     * Search the index for items within a given radius.\n     * @param {number} qx\n     * @param {number} qy\n     * @param {number} r Query radius.\n     * @returns {number[]} An array of indices correponding to the found items.\n     */\n    within(qx, qy, r) {\n        if (!this._finished) throw new Error('Data not yet indexed - call index.finish().');\n\n        const {ids, coords, nodeSize} = this;\n        const stack = [0, ids.length - 1, 0];\n        const result = [];\n        const r2 = r * r;\n\n        // recursively search for items within radius in the kd-sorted arrays\n        while (stack.length) {\n            const axis = stack.pop() || 0;\n            const right = stack.pop() || 0;\n            const left = stack.pop() || 0;\n\n            // if we reached \"tree node\", search linearly\n            if (right - left <= nodeSize) {\n                for (let i = left; i <= right; i++) {\n                    if (sqDist(coords[2 * i], coords[2 * i + 1], qx, qy) <= r2) result.push(ids[i]);\n                }\n                continue;\n            }\n\n            // otherwise find the middle index\n            const m = (left + right) >> 1;\n\n            // include the middle item if it's in range\n            const x = coords[2 * m];\n            const y = coords[2 * m + 1];\n            if (sqDist(x, y, qx, qy) <= r2) result.push(ids[m]);\n\n            // queue search in halves that intersect the query\n            if (axis === 0 ? qx - r <= x : qy - r <= y) {\n                stack.push(left);\n                stack.push(m - 1);\n                stack.push(1 - axis);\n            }\n            if (axis === 0 ? qx + r >= x : qy + r >= y) {\n                stack.push(m + 1);\n                stack.push(right);\n                stack.push(1 - axis);\n            }\n        }\n\n        return result;\n    }\n}\n\n/**\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} nodeSize\n * @param {number} left\n * @param {number} right\n * @param {number} axis\n */\nfunction sort(ids, coords, nodeSize, left, right, axis) {\n    if (right - left <= nodeSize) return;\n\n    const m = (left + right) >> 1; // middle index\n\n    // sort ids and coords around the middle index so that the halves lie\n    // either left/right or top/bottom correspondingly (taking turns)\n    select(ids, coords, m, left, right, axis);\n\n    // recursively kd-sort first half and second half on the opposite axis\n    sort(ids, coords, nodeSize, left, m - 1, 1 - axis);\n    sort(ids, coords, nodeSize, m + 1, right, 1 - axis);\n}\n\n/**\n * Custom Floyd-Rivest selection algorithm: sort ids and coords so that\n * [left..k-1] items are smaller than k-th item (on either x or y axis)\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} k\n * @param {number} left\n * @param {number} right\n * @param {number} axis\n */\nfunction select(ids, coords, k, left, right, axis) {\n\n    while (right > left) {\n        if (right - left > 600) {\n            const n = right - left + 1;\n            const m = k - left + 1;\n            const z = Math.log(n);\n            const s = 0.5 * Math.exp(2 * z / 3);\n            const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n            const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n            const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n            select(ids, coords, k, newLeft, newRight, axis);\n        }\n\n        const t = coords[2 * k + axis];\n        let i = left;\n        let j = right;\n\n        swapItem(ids, coords, left, k);\n        if (coords[2 * right + axis] > t) swapItem(ids, coords, left, right);\n\n        while (i < j) {\n            swapItem(ids, coords, i, j);\n            i++;\n            j--;\n            while (coords[2 * i + axis] < t) i++;\n            while (coords[2 * j + axis] > t) j--;\n        }\n\n        if (coords[2 * left + axis] === t) swapItem(ids, coords, left, j);\n        else {\n            j++;\n            swapItem(ids, coords, j, right);\n        }\n\n        if (j <= k) left = j + 1;\n        if (k <= j) right = j - 1;\n    }\n}\n\n/**\n * @param {Uint16Array | Uint32Array} ids\n * @param {InstanceType<TypedArrayConstructor>} coords\n * @param {number} i\n * @param {number} j\n */\nfunction swapItem(ids, coords, i, j) {\n    swap(ids, i, j);\n    swap(coords, 2 * i, 2 * j);\n    swap(coords, 2 * i + 1, 2 * j + 1);\n}\n\n/**\n * @param {InstanceType<TypedArrayConstructor>} arr\n * @param {number} i\n * @param {number} j\n */\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\n/**\n * @param {number} ax\n * @param {number} ay\n * @param {number} bx\n * @param {number} by\n */\nfunction sqDist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\n", "\nimport KDBush from 'kdbush';\n\nconst defaultOptions = {\n    minZoom: 0,   // min zoom to generate clusters on\n    maxZoom: 16,  // max zoom level to cluster the points on\n    minPoints: 2, // minimum points to form a cluster\n    radius: 40,   // cluster radius in pixels\n    extent: 512,  // tile extent (radius is calculated relative to it)\n    nodeSize: 64, // size of the KD-tree leaf node, affects performance\n    log: false,   // whether to log timing info\n\n    // whether to generate numeric ids for input features (in vector tiles)\n    generateId: false,\n\n    // a reduce function for calculating custom cluster properties\n    reduce: null, // (accumulated, props) => { accumulated.sum += props.sum; }\n\n    // properties to use for individual points when running the reducer\n    map: props => props // props => ({sum: props.my_value})\n};\n\nconst fround = Math.fround || (tmp => ((x) => { tmp[0] = +x; return tmp[0]; }))(new Float32Array(1));\n\nconst OFFSET_ZOOM = 2;\nconst OFFSET_ID = 3;\nconst OFFSET_PARENT = 4;\nconst OFFSET_NUM = 5;\nconst OFFSET_PROP = 6;\n\nexport default class Supercluster {\n    constructor(options) {\n        this.options = Object.assign(Object.create(defaultOptions), options);\n        this.trees = new Array(this.options.maxZoom + 1);\n        this.stride = this.options.reduce ? 7 : 6;\n        this.clusterProps = [];\n    }\n\n    load(points) {\n        const {log, minZoom, maxZoom} = this.options;\n\n        if (log) console.time('total time');\n\n        const timerId = `prepare ${  points.length  } points`;\n        if (log) console.time(timerId);\n\n        this.points = points;\n\n        // generate a cluster object for each point and index input points into a KD-tree\n        const data = [];\n\n        for (let i = 0; i < points.length; i++) {\n            const p = points[i];\n            if (!p.geometry) continue;\n\n            const [lng, lat] = p.geometry.coordinates;\n            const x = fround(lngX(lng));\n            const y = fround(latY(lat));\n            // store internal point/cluster data in flat numeric arrays for performance\n            data.push(\n                x, y, // projected point coordinates\n                Infinity, // the last zoom the point was processed at\n                i, // index of the source feature in the original input array\n                -1, // parent cluster id\n                1 // number of points in a cluster\n            );\n            if (this.options.reduce) data.push(0); // noop\n        }\n        let tree = this.trees[maxZoom + 1] = this._createTree(data);\n\n        if (log) console.timeEnd(timerId);\n\n        // cluster points on max zoom, then cluster the results on previous zoom, etc.;\n        // results in a cluster hierarchy across zoom levels\n        for (let z = maxZoom; z >= minZoom; z--) {\n            const now = +Date.now();\n\n            // create a new set of clusters for the zoom and index them with a KD-tree\n            tree = this.trees[z] = this._createTree(this._cluster(tree, z));\n\n            if (log) console.log('z%d: %d clusters in %dms', z, tree.numItems, +Date.now() - now);\n        }\n\n        if (log) console.timeEnd('total time');\n\n        return this;\n    }\n\n    getClusters(bbox, zoom) {\n        let minLng = ((bbox[0] + 180) % 360 + 360) % 360 - 180;\n        const minLat = Math.max(-90, Math.min(90, bbox[1]));\n        let maxLng = bbox[2] === 180 ? 180 : ((bbox[2] + 180) % 360 + 360) % 360 - 180;\n        const maxLat = Math.max(-90, Math.min(90, bbox[3]));\n\n        if (bbox[2] - bbox[0] >= 360) {\n            minLng = -180;\n            maxLng = 180;\n        } else if (minLng > maxLng) {\n            const easternHem = this.getClusters([minLng, minLat, 180, maxLat], zoom);\n            const westernHem = this.getClusters([-180, minLat, maxLng, maxLat], zoom);\n            return easternHem.concat(westernHem);\n        }\n\n        const tree = this.trees[this._limitZoom(zoom)];\n        const ids = tree.range(lngX(minLng), latY(maxLat), lngX(maxLng), latY(minLat));\n        const data = tree.data;\n        const clusters = [];\n        for (const id of ids) {\n            const k = this.stride * id;\n            clusters.push(data[k + OFFSET_NUM] > 1 ? getClusterJSON(data, k, this.clusterProps) : this.points[data[k + OFFSET_ID]]);\n        }\n        return clusters;\n    }\n\n    getChildren(clusterId) {\n        const originId = this._getOriginId(clusterId);\n        const originZoom = this._getOriginZoom(clusterId);\n        const errorMsg = 'No cluster with the specified id.';\n\n        const tree = this.trees[originZoom];\n        if (!tree) throw new Error(errorMsg);\n\n        const data = tree.data;\n        if (originId * this.stride >= data.length) throw new Error(errorMsg);\n\n        const r = this.options.radius / (this.options.extent * Math.pow(2, originZoom - 1));\n        const x = data[originId * this.stride];\n        const y = data[originId * this.stride + 1];\n        const ids = tree.within(x, y, r);\n        const children = [];\n        for (const id of ids) {\n            const k = id * this.stride;\n            if (data[k + OFFSET_PARENT] === clusterId) {\n                children.push(data[k + OFFSET_NUM] > 1 ? getClusterJSON(data, k, this.clusterProps) : this.points[data[k + OFFSET_ID]]);\n            }\n        }\n\n        if (children.length === 0) throw new Error(errorMsg);\n\n        return children;\n    }\n\n    getLeaves(clusterId, limit, offset) {\n        limit = limit || 10;\n        offset = offset || 0;\n\n        const leaves = [];\n        this._appendLeaves(leaves, clusterId, limit, offset, 0);\n\n        return leaves;\n    }\n\n    getTile(z, x, y) {\n        const tree = this.trees[this._limitZoom(z)];\n        const z2 = Math.pow(2, z);\n        const {extent, radius} = this.options;\n        const p = radius / extent;\n        const top = (y - p) / z2;\n        const bottom = (y + 1 + p) / z2;\n\n        const tile = {\n            features: []\n        };\n\n        this._addTileFeatures(\n            tree.range((x - p) / z2, top, (x + 1 + p) / z2, bottom),\n            tree.data, x, y, z2, tile);\n\n        if (x === 0) {\n            this._addTileFeatures(\n                tree.range(1 - p / z2, top, 1, bottom),\n                tree.data, z2, y, z2, tile);\n        }\n        if (x === z2 - 1) {\n            this._addTileFeatures(\n                tree.range(0, top, p / z2, bottom),\n                tree.data, -1, y, z2, tile);\n        }\n\n        return tile.features.length ? tile : null;\n    }\n\n    getClusterExpansionZoom(clusterId) {\n        let expansionZoom = this._getOriginZoom(clusterId) - 1;\n        while (expansionZoom <= this.options.maxZoom) {\n            const children = this.getChildren(clusterId);\n            expansionZoom++;\n            if (children.length !== 1) break;\n            clusterId = children[0].properties.cluster_id;\n        }\n        return expansionZoom;\n    }\n\n    _appendLeaves(result, clusterId, limit, offset, skipped) {\n        const children = this.getChildren(clusterId);\n\n        for (const child of children) {\n            const props = child.properties;\n\n            if (props && props.cluster) {\n                if (skipped + props.point_count <= offset) {\n                    // skip the whole cluster\n                    skipped += props.point_count;\n                } else {\n                    // enter the cluster\n                    skipped = this._appendLeaves(result, props.cluster_id, limit, offset, skipped);\n                    // exit the cluster\n                }\n            } else if (skipped < offset) {\n                // skip a single point\n                skipped++;\n            } else {\n                // add a single point\n                result.push(child);\n            }\n            if (result.length === limit) break;\n        }\n\n        return skipped;\n    }\n\n    _createTree(data) {\n        const tree = new KDBush(data.length / this.stride | 0, this.options.nodeSize, Float32Array);\n        for (let i = 0; i < data.length; i += this.stride) tree.add(data[i], data[i + 1]);\n        tree.finish();\n        tree.data = data;\n        return tree;\n    }\n\n    _addTileFeatures(ids, data, x, y, z2, tile) {\n        for (const i of ids) {\n            const k = i * this.stride;\n            const isCluster = data[k + OFFSET_NUM] > 1;\n\n            let tags, px, py;\n            if (isCluster) {\n                tags = getClusterProperties(data, k, this.clusterProps);\n                px = data[k];\n                py = data[k + 1];\n            } else {\n                const p = this.points[data[k + OFFSET_ID]];\n                tags = p.properties;\n                const [lng, lat] = p.geometry.coordinates;\n                px = lngX(lng);\n                py = latY(lat);\n            }\n\n            const f = {\n                type: 1,\n                geometry: [[\n                    Math.round(this.options.extent * (px * z2 - x)),\n                    Math.round(this.options.extent * (py * z2 - y))\n                ]],\n                tags\n            };\n\n            // assign id\n            let id;\n            if (isCluster || this.options.generateId) {\n                // optionally generate id for points\n                id = data[k + OFFSET_ID];\n            } else {\n                // keep id if already assigned\n                id = this.points[data[k + OFFSET_ID]].id;\n            }\n\n            if (id !== undefined) f.id = id;\n\n            tile.features.push(f);\n        }\n    }\n\n    _limitZoom(z) {\n        return Math.max(this.options.minZoom, Math.min(Math.floor(+z), this.options.maxZoom + 1));\n    }\n\n    _cluster(tree, zoom) {\n        const {radius, extent, reduce, minPoints} = this.options;\n        const r = radius / (extent * Math.pow(2, zoom));\n        const data = tree.data;\n        const nextData = [];\n        const stride = this.stride;\n\n        // loop through each point\n        for (let i = 0; i < data.length; i += stride) {\n            // if we've already visited the point at this zoom level, skip it\n            if (data[i + OFFSET_ZOOM] <= zoom) continue;\n            data[i + OFFSET_ZOOM] = zoom;\n\n            // find all nearby points\n            const x = data[i];\n            const y = data[i + 1];\n            const neighborIds = tree.within(data[i], data[i + 1], r);\n\n            const numPointsOrigin = data[i + OFFSET_NUM];\n            let numPoints = numPointsOrigin;\n\n            // count the number of points in a potential cluster\n            for (const neighborId of neighborIds) {\n                const k = neighborId * stride;\n                // filter out neighbors that are already processed\n                if (data[k + OFFSET_ZOOM] > zoom) numPoints += data[k + OFFSET_NUM];\n            }\n\n            // if there were neighbors to merge, and there are enough points to form a cluster\n            if (numPoints > numPointsOrigin && numPoints >= minPoints) {\n                let wx = x * numPointsOrigin;\n                let wy = y * numPointsOrigin;\n\n                let clusterProperties;\n                let clusterPropIndex = -1;\n\n                // encode both zoom and point index on which the cluster originated -- offset by total length of features\n                const id = ((i / stride | 0) << 5) + (zoom + 1) + this.points.length;\n\n                for (const neighborId of neighborIds) {\n                    const k = neighborId * stride;\n\n                    if (data[k + OFFSET_ZOOM] <= zoom) continue;\n                    data[k + OFFSET_ZOOM] = zoom; // save the zoom (so it doesn't get processed twice)\n\n                    const numPoints2 = data[k + OFFSET_NUM];\n                    wx += data[k] * numPoints2; // accumulate coordinates for calculating weighted center\n                    wy += data[k + 1] * numPoints2;\n\n                    data[k + OFFSET_PARENT] = id;\n\n                    if (reduce) {\n                        if (!clusterProperties) {\n                            clusterProperties = this._map(data, i, true);\n                            clusterPropIndex = this.clusterProps.length;\n                            this.clusterProps.push(clusterProperties);\n                        }\n                        reduce(clusterProperties, this._map(data, k));\n                    }\n                }\n\n                data[i + OFFSET_PARENT] = id;\n                nextData.push(wx / numPoints, wy / numPoints, Infinity, id, -1, numPoints);\n                if (reduce) nextData.push(clusterPropIndex);\n\n            } else { // left points as unclustered\n                for (let j = 0; j < stride; j++) nextData.push(data[i + j]);\n\n                if (numPoints > 1) {\n                    for (const neighborId of neighborIds) {\n                        const k = neighborId * stride;\n                        if (data[k + OFFSET_ZOOM] <= zoom) continue;\n                        data[k + OFFSET_ZOOM] = zoom;\n                        for (let j = 0; j < stride; j++) nextData.push(data[k + j]);\n                    }\n                }\n            }\n        }\n\n        return nextData;\n    }\n\n    // get index of the point from which the cluster originated\n    _getOriginId(clusterId) {\n        return (clusterId - this.points.length) >> 5;\n    }\n\n    // get zoom of the point from which the cluster originated\n    _getOriginZoom(clusterId) {\n        return (clusterId - this.points.length) % 32;\n    }\n\n    _map(data, i, clone) {\n        if (data[i + OFFSET_NUM] > 1) {\n            const props = this.clusterProps[data[i + OFFSET_PROP]];\n            return clone ? Object.assign({}, props) : props;\n        }\n        const original = this.points[data[i + OFFSET_ID]].properties;\n        const result = this.options.map(original);\n        return clone && result === original ? Object.assign({}, result) : result;\n    }\n}\n\nfunction getClusterJSON(data, i, clusterProps) {\n    return {\n        type: 'Feature',\n        id: data[i + OFFSET_ID],\n        properties: getClusterProperties(data, i, clusterProps),\n        geometry: {\n            type: 'Point',\n            coordinates: [xLng(data[i]), yLat(data[i + 1])]\n        }\n    };\n}\n\nfunction getClusterProperties(data, i, clusterProps) {\n    const count = data[i + OFFSET_NUM];\n    const abbrev =\n        count >= 10000 ? `${Math.round(count / 1000)  }k` :\n        count >= 1000 ? `${Math.round(count / 100) / 10  }k` : count;\n    const propIndex = data[i + OFFSET_PROP];\n    const properties = propIndex === -1 ? {} : Object.assign({}, clusterProps[propIndex]);\n    return Object.assign(properties, {\n        cluster: true,\n        cluster_id: data[i + OFFSET_ID],\n        point_count: count,\n        point_count_abbreviated: abbrev\n    });\n}\n\n// longitude/latitude to spherical mercator in [0..1] range\nfunction lngX(lng) {\n    return lng / 360 + 0.5;\n}\nfunction latY(lat) {\n    const sin = Math.sin(lat * Math.PI / 180);\n    const y = (0.5 - 0.25 * Math.log((1 + sin) / (1 - sin)) / Math.PI);\n    return y < 0 ? 0 : y > 1 ? 1 : y;\n}\n\n// spherical mercator to longitude/latitude\nfunction xLng(x) {\n    return (x - 0.5) * 360;\n}\nfunction yLat(y) {\n    const y2 = (180 - y * 360) * Math.PI / 180;\n    return 360 * Math.atan(Math.exp(y2)) / Math.PI - 90;\n}\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/**\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Supports markers of either either \"legacy\" or \"advanced\" types.\n */\nexport type Marker =\n  | google.maps.Marker\n  | google.maps.marker.AdvancedMarkerElement;\n\n/**\n * util class that creates a common set of convenience functions to wrap\n * shared behavior of Advanced Markers and Markers.\n */\nexport class MarkerUtils {\n  public static isAdvancedMarkerAvailable(map: google.maps.Map): boolean {\n    return (\n      google.maps.marker &&\n      map.getMapCapabilities().isAdvancedMarkersAvailable === true\n    );\n  }\n\n  public static isAdvancedMarker(\n    marker: Marker\n  ): marker is google.maps.marker.AdvancedMarkerElement {\n    return (\n      google.maps.marker &&\n      marker instanceof google.maps.marker.AdvancedMarkerElement\n    );\n  }\n\n  public static setMap(marker: Marker, map: google.maps.Map | null) {\n    if (this.isAdvancedMarker(marker)) {\n      marker.map = map;\n    } else {\n      marker.setMap(map);\n    }\n  }\n\n  public static getPosition(marker: Marker): google.maps.LatLng {\n    // SuperClusterAlgorithm.calculate expects a LatLng instance so we fake it for Adv Markers\n    if (this.isAdvancedMarker(marker)) {\n      if (marker.position) {\n        if (marker.position instanceof google.maps.LatLng) {\n          return marker.position;\n        }\n        // since we can't cast to LatLngLiteral for reasons =(\n        if (marker.position.lat && marker.position.lng) {\n          return new google.maps.LatLng(\n            marker.position.lat,\n            marker.position.lng\n          );\n        }\n      }\n      return new google.maps.LatLng(null);\n    }\n    return marker.getPosition();\n  }\n\n  public static getVisible(marker: Marker) {\n    if (this.isAdvancedMarker(marker)) {\n      /**\n       * Always return true for Advanced Markers because the clusterer\n       * uses getVisible as a way to count legacy markers not as an actual\n       * indicator of visibility for some reason. Even when markers are hidden\n       * Marker.getVisible returns `true` and this is used to set the marker count\n       * on the cluster. See the behavior of Cluster.count\n       */\n      return true;\n    }\n    return marker.getVisible();\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MarkerUtils, Marker } from \"./marker-utils\";\n\nexport interface ClusterOptions {\n  position?: google.maps.LatLng | google.maps.LatLngLiteral;\n  markers?: Marker[];\n}\n\nexport class Cluster {\n  public marker?: Marker;\n  public readonly markers?: Marker[];\n  protected _position: google.maps.LatLng;\n\n  constructor({ markers, position }: ClusterOptions) {\n    this.markers = markers;\n\n    if (position) {\n      if (position instanceof google.maps.LatLng) {\n        this._position = position;\n      } else {\n        this._position = new google.maps.LatLng(position);\n      }\n    }\n  }\n\n  public get bounds(): google.maps.LatLngBounds | undefined {\n    if (this.markers.length === 0 && !this._position) {\n      return;\n    }\n\n    const bounds = new google.maps.LatLngBounds(this._position, this._position);\n    for (const marker of this.markers) {\n      bounds.extend(MarkerUtils.getPosition(marker));\n    }\n    return bounds;\n  }\n\n  public get position(): google.maps.LatLng {\n    return this._position || this.bounds.getCenter();\n  }\n\n  /**\n   * Get the count of **visible** markers.\n   */\n  public get count(): number {\n    return this.markers.filter((m: Marker) => MarkerUtils.getVisible(m)).length;\n  }\n\n  /**\n   * Add a marker to the cluster.\n   */\n  public push(marker: Marker): void {\n    this.markers.push(marker);\n  }\n\n  /**\n   * Cleanup references and remove marker from map.\n   */\n  public delete(): void {\n    if (this.marker) {\n      MarkerUtils.setMap(this.marker, null);\n      this.marker = undefined;\n    }\n    this.markers.length = 0;\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MarkerUtils, Marker } from \"../marker-utils\";\n\n/**\n * Returns the markers visible in a padded map viewport\n *\n * @param map\n * @param mapCanvasProjection\n * @param markers The list of marker to filter\n * @param viewportPaddingPixels The padding in pixel\n * @returns The list of markers in the padded viewport\n */\nexport const filterMarkersToPaddedViewport = (\n  map: google.maps.Map,\n  mapCanvasProjection: google.maps.MapCanvasProjection,\n  markers: Marker[],\n  viewportPaddingPixels: number\n): Marker[] => {\n  const extendedMapBounds = extendBoundsToPaddedViewport(\n    map.getBounds(),\n    mapCanvasProjection,\n    viewportPaddingPixels\n  );\n  return markers.filter((marker) =>\n    extendedMapBounds.contains(MarkerUtils.getPosition(marker))\n  );\n};\n\n/**\n * Extends a bounds by a number of pixels in each direction\n */\nexport const extendBoundsToPaddedViewport = (\n  bounds: google.maps.LatLngBounds,\n  projection: google.maps.MapCanvasProjection,\n  numPixels: number\n): google.maps.LatLngBounds => {\n  const { northEast, southWest } = latLngBoundsToPixelBounds(\n    bounds,\n    projection\n  );\n  const extendedPixelBounds = extendPixelBounds(\n    { northEast, southWest },\n    numPixels\n  );\n  return pixelBoundsToLatLngBounds(extendedPixelBounds, projection);\n};\n\n/**\n * Gets the extended bounds as a bbox [westLng, southLat, eastLng, northLat]\n */\nexport const getPaddedViewport = (\n  bounds: google.maps.LatLngBounds,\n  projection: google.maps.MapCanvasProjection,\n  pixels: number\n): [number, number, number, number] => {\n  const extended = extendBoundsToPaddedViewport(bounds, projection, pixels);\n  const ne = extended.getNorthEast();\n  const sw = extended.getSouthWest();\n\n  return [sw.lng(), sw.lat(), ne.lng(), ne.lat()];\n};\n\n/**\n * Returns the distance between 2 positions.\n *\n * @hidden\n */\nexport const distanceBetweenPoints = (\n  p1: google.maps.LatLngLiteral,\n  p2: google.maps.LatLngLiteral\n): number => {\n  const R = 6371; // Radius of the Earth in km\n  const dLat = ((p2.lat - p1.lat) * Math.PI) / 180;\n  const dLon = ((p2.lng - p1.lng) * Math.PI) / 180;\n  const sinDLat = Math.sin(dLat / 2);\n  const sinDLon = Math.sin(dLon / 2);\n  const a =\n    sinDLat * sinDLat +\n    Math.cos((p1.lat * Math.PI) / 180) *\n      Math.cos((p2.lat * Math.PI) / 180) *\n      sinDLon *\n      sinDLon;\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  return R * c;\n};\n\ntype PixelBounds = {\n  northEast: google.maps.Point;\n  southWest: google.maps.Point;\n};\n\n/**\n * Converts a LatLng bound to pixels.\n *\n * @hidden\n */\nconst latLngBoundsToPixelBounds = (\n  bounds: google.maps.LatLngBounds,\n  projection: google.maps.MapCanvasProjection\n): PixelBounds => {\n  return {\n    northEast: projection.fromLatLngToDivPixel(bounds.getNorthEast()),\n    southWest: projection.fromLatLngToDivPixel(bounds.getSouthWest()),\n  };\n};\n\n/**\n * Extends a pixel bounds by numPixels in all directions.\n *\n * @hidden\n */\nexport const extendPixelBounds = (\n  { northEast, southWest }: PixelBounds,\n  numPixels: number\n): PixelBounds => {\n  northEast.x += numPixels;\n  northEast.y -= numPixels;\n\n  southWest.x -= numPixels;\n  southWest.y += numPixels;\n\n  return { northEast, southWest };\n};\n\n/**\n * @hidden\n */\nexport const pixelBoundsToLatLngBounds = (\n  { northEast, southWest }: PixelBounds,\n  projection: google.maps.MapCanvasProjection\n): google.maps.LatLngBounds => {\n  const sw = projection.fromDivPixelToLatLng(southWest);\n  const ne = projection.fromDivPixelToLatLng(northEast);\n  return new google.maps.LatLngBounds(sw, ne);\n};\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Cluster } from \"../cluster\";\nimport { filterMarkersToPaddedViewport } from \"./utils\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\n\nexport interface AlgorithmInput {\n  /**\n   * The map containing the markers and clusters.\n   */\n  map: google.maps.Map;\n  /**\n   * An array of markers to be clustered.\n   *\n   * There are some specific edge cases to be aware of including the following:\n   * * Markers that are not visible.\n   */\n  markers: Marker[];\n  /**\n   * The `mapCanvasProjection` enables easy conversion from lat/lng to pixel.\n   *\n   * @see [MapCanvasProjection](https://developers.google.com/maps/documentation/javascript/reference/overlay-view#MapCanvasProjection)\n   */\n  mapCanvasProjection: google.maps.MapCanvasProjection;\n}\n\nexport interface AlgorithmOutput {\n  /**\n   * The clusters returned based upon the {@link AlgorithmInput}.\n   */\n  clusters: Cluster[];\n  /**\n   * A boolean flag indicating that the clusters have not changed.\n   */\n  changed?: boolean;\n}\n\nexport interface Algorithm {\n  /**\n   * Calculates an array of {@link Cluster}.\n   */\n  calculate: ({ markers, map }: AlgorithmInput) => AlgorithmOutput;\n}\n\nexport interface AlgorithmOptions {\n  // Markers are not clustered at maxZoom and above.\n  maxZoom?: number;\n}\n\n/**\n * @hidden\n */\nexport abstract class AbstractAlgorithm implements Algorithm {\n  protected maxZoom: number;\n\n  constructor({ maxZoom = 16 }: AlgorithmOptions) {\n    this.maxZoom = maxZoom;\n  }\n  /**\n   * Helper function to bypass clustering based upon some map state such as\n   * zoom, number of markers, etc.\n   *\n   * ```typescript\n   *  cluster({markers, map}: AlgorithmInput): Cluster[] {\n   *    if (shouldBypassClustering(map)) {\n   *      return this.noop({markers})\n   *    }\n   * }\n   * ```\n   */\n  protected noop<T extends Pick<AlgorithmInput, \"markers\">>({\n    markers,\n  }: T): Cluster[] {\n    return noop(markers);\n  }\n  /**\n   * Calculates an array of {@link Cluster}. Calculate is separate from\n   * {@link cluster} as it does preprocessing on the markers such as filtering\n   * based upon the viewport as in {@link AbstractViewportAlgorithm}. Caching\n   * and other optimizations can also be done here.\n   */\n  public abstract calculate({ markers, map }: AlgorithmInput): AlgorithmOutput;\n\n  /**\n   * Clusters the markers and called from {@link calculate}.\n   */\n  protected abstract cluster({ markers, map }: AlgorithmInput): Cluster[];\n}\n\n/**\n * @hidden\n */\nexport interface ViewportAlgorithmOptions extends AlgorithmOptions {\n  /**\n   * The number of pixels to extend beyond the viewport bounds when filtering\n   * markers prior to clustering.\n   */\n  viewportPadding?: number;\n}\n\n/**\n * Abstract viewport algorithm proves a class to filter markers by a padded\n * viewport. This is a common optimization.\n *\n * @hidden\n */\nexport abstract class AbstractViewportAlgorithm extends AbstractAlgorithm {\n  protected viewportPadding = 60;\n\n  constructor({ viewportPadding = 60, ...options }: ViewportAlgorithmOptions) {\n    super(options);\n    this.viewportPadding = viewportPadding;\n  }\n  public calculate({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): AlgorithmOutput {\n    if (map.getZoom() >= this.maxZoom) {\n      return {\n        clusters: this.noop({\n          markers,\n        }),\n        changed: false,\n      };\n    }\n\n    return {\n      clusters: this.cluster({\n        markers: filterMarkersToPaddedViewport(\n          map,\n          mapCanvasProjection,\n          markers,\n          this.viewportPadding\n        ),\n        map,\n        mapCanvasProjection,\n      }),\n    };\n  }\n  protected abstract cluster({ markers, map }: AlgorithmInput): Cluster[];\n}\n\n/**\n * @hidden\n */\nexport const noop = (markers: Marker[]): Cluster[] => {\n  const clusters = markers.map(\n    (marker) =>\n      new Cluster({\n        position: MarkerUtils.getPosition(marker),\n        markers: [marker],\n      })\n  );\n  return clusters;\n};\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AbstractViewportAlgorithm,\n  AlgorithmInput,\n  AlgorithmOutput,\n  ViewportAlgorithmOptions,\n} from \"./core\";\nimport {\n  distanceBetweenPoints,\n  extendBoundsToPaddedViewport,\n  filterMarkersToPaddedViewport,\n} from \"./utils\";\n\nimport { Cluster } from \"../cluster\";\nimport equal from \"fast-deep-equal\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\n\nexport interface GridOptions extends ViewportAlgorithmOptions {\n  gridSize?: number;\n  /**\n   * Max distance between cluster center and point in meters.\n   * @default 10000\n   */\n  maxDistance?: number;\n}\n\n/**\n * The default Grid algorithm historically used in Google Maps marker\n * clustering.\n *\n * The Grid algorithm does not implement caching and markers may flash as the\n * viewport changes. Instead use {@link SuperClusterAlgorithm}.\n */\nexport class GridAlgorithm extends AbstractViewportAlgorithm {\n  protected gridSize: number;\n  protected maxDistance: number;\n  protected clusters: Cluster[] = [];\n  protected state = { zoom: -1 };\n\n  constructor({ maxDistance = 40000, gridSize = 40, ...options }: GridOptions) {\n    super(options);\n\n    this.maxDistance = maxDistance;\n    this.gridSize = gridSize;\n  }\n\n  public calculate({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): AlgorithmOutput {\n    const state = { zoom: map.getZoom() };\n    let changed = false;\n    if (this.state.zoom >= this.maxZoom && state.zoom >= this.maxZoom) {\n      // still at or beyond maxZoom, no change\n    } else {\n      changed = !equal(this.state, state);\n    }\n    this.state = state;\n    if (map.getZoom() >= this.maxZoom) {\n      return {\n        clusters: this.noop({\n          markers,\n        }),\n        changed,\n      };\n    }\n\n    return {\n      clusters: this.cluster({\n        markers: filterMarkersToPaddedViewport(\n          map,\n          mapCanvasProjection,\n          markers,\n          this.viewportPadding\n        ),\n        map,\n        mapCanvasProjection,\n      }),\n    };\n  }\n\n  protected cluster({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): Cluster[] {\n    this.clusters = [];\n    markers.forEach((marker) => {\n      this.addToClosestCluster(marker, map, mapCanvasProjection);\n    });\n\n    return this.clusters;\n  }\n\n  protected addToClosestCluster(\n    marker: Marker,\n    map: google.maps.Map,\n    projection: google.maps.MapCanvasProjection\n  ): void {\n    let maxDistance = this.maxDistance; // Some large number\n    let cluster: Cluster = null;\n\n    for (let i = 0; i < this.clusters.length; i++) {\n      const candidate = this.clusters[i];\n      const distance = distanceBetweenPoints(\n        candidate.bounds.getCenter().toJSON(),\n        MarkerUtils.getPosition(marker).toJSON()\n      );\n\n      if (distance < maxDistance) {\n        maxDistance = distance;\n        cluster = candidate;\n      }\n    }\n\n    if (\n      cluster &&\n      extendBoundsToPaddedViewport(\n        cluster.bounds,\n        projection,\n        this.gridSize\n      ).contains(MarkerUtils.getPosition(marker))\n    ) {\n      cluster.push(marker);\n    } else {\n      const cluster = new Cluster({ markers: [marker] });\n      this.clusters.push(cluster);\n    }\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AbstractAlgorithm,\n  AlgorithmInput,\n  AlgorithmOptions,\n  AlgorithmOutput,\n} from \"./core\";\n\nimport { Cluster } from \"../cluster\";\n\n/**\n * Noop algorithm does not generate any clusters or filter markers by the an extended viewport.\n */\nexport class NoopAlgorithm extends AbstractAlgorithm {\n  constructor({ ...options }: AlgorithmOptions) {\n    super(options);\n  }\n  public calculate({\n    markers,\n    map,\n    mapCanvasProjection,\n  }: AlgorithmInput): AlgorithmOutput {\n    return {\n      clusters: this.cluster({ markers, map, mapCanvasProjection }),\n      changed: false,\n    };\n  }\n\n  protected cluster(input: AlgorithmInput): Cluster[] {\n    return this.noop(input);\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AbstractAlgorithm, AlgorithmInput, AlgorithmOutput } from \"./core\";\nimport SuperCluster, { ClusterFeature } from \"supercluster\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\nimport { Cluster } from \"../cluster\";\nimport equal from \"fast-deep-equal\";\n\nexport type SuperClusterOptions = SuperCluster.Options<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  { [name: string]: any },\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  { [name: string]: any }\n>;\n\n/**\n * A very fast JavaScript algorithm for geospatial point clustering using KD trees.\n *\n * @see https://www.npmjs.com/package/supercluster for more information on options.\n */\nexport class SuperClusterAlgorithm extends AbstractAlgorithm {\n  protected superCluster: SuperCluster;\n  protected markers: Marker[];\n  protected clusters: Cluster[];\n  protected state = { zoom: -1 };\n\n  constructor({ maxZoom, radius = 60, ...options }: SuperClusterOptions) {\n    super({ maxZoom });\n\n    this.superCluster = new SuperCluster({\n      maxZoom: this.maxZoom,\n      radius,\n      ...options,\n    });\n  }\n\n  public calculate(input: AlgorithmInput): AlgorithmOutput {\n    let changed = false;\n    const state = { zoom: input.map.getZoom() };\n\n    if (!equal(input.markers, this.markers)) {\n      changed = true;\n      // TODO use proxy to avoid copy?\n      this.markers = [...input.markers];\n\n      const points = this.markers.map((marker) => {\n        const position = MarkerUtils.getPosition(marker);\n        const coordinates = [position.lng(), position.lat()];\n        return {\n          type: \"Feature\" as const,\n          geometry: {\n            type: \"Point\" as const,\n            coordinates,\n          },\n          properties: { marker },\n        };\n      });\n      this.superCluster.load(points);\n    }\n\n    if (!changed) {\n      if (this.state.zoom <= this.maxZoom || state.zoom <= this.maxZoom) {\n        changed = !equal(this.state, state);\n      }\n    }\n\n    this.state = state;\n\n    if (changed) {\n      this.clusters = this.cluster(input);\n    }\n\n    return { clusters: this.clusters, changed };\n  }\n\n  public cluster({ map }: AlgorithmInput): Cluster[] {\n    return this.superCluster\n      .getClusters([-180, -90, 180, 90], Math.round(map.getZoom()))\n      .map((feature: ClusterFeature<{ marker: Marker }>) =>\n        this.transformCluster(feature)\n      );\n  }\n\n  protected transformCluster({\n    geometry: {\n      coordinates: [lng, lat],\n    },\n    properties,\n  }: ClusterFeature<{ marker: Marker }>): Cluster {\n    if (properties.cluster) {\n      return new Cluster({\n        markers: this.superCluster\n          .getLeaves(properties.cluster_id, Infinity)\n          .map((leaf) => leaf.properties.marker),\n        position: { lat, lng },\n      });\n    }\n\n    const marker = properties.marker;\n\n    return new Cluster({\n      markers: [marker],\n      position: MarkerUtils.getPosition(marker),\n    });\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AbstractViewportAlgorithm,\n  AlgorithmInput,\n  AlgorithmOutput,\n  ViewportAlgorithmOptions,\n} from \"./core\";\nimport { SuperClusterOptions } from \"./supercluster\";\nimport SuperCluster, { ClusterFeature } from \"supercluster\";\nimport { MarkerUtils, Marker } from \"../marker-utils\";\nimport { Cluster } from \"../cluster\";\nimport { getPaddedViewport } from \"./utils\";\nimport equal from \"fast-deep-equal\";\n\nexport interface SuperClusterViewportOptions\n  extends SuperClusterOptions,\n    ViewportAlgorithmOptions {}\n\nexport interface SuperClusterViewportState {\n  /* The current zoom level */\n  zoom: number;\n\n  /* The current viewport as a bbox [westLng, southLat, eastLng, northLat] */\n  view: [number, number, number, number];\n}\n\n/**\n * A very fast JavaScript algorithm for geospatial point clustering using KD trees.\n *\n * @see https://www.npmjs.com/package/supercluster for more information on options.\n */\nexport class SuperClusterViewportAlgorithm extends AbstractViewportAlgorithm {\n  protected superCluster: SuperCluster;\n  protected markers: Marker[];\n  protected clusters: Cluster[];\n  protected state: SuperClusterViewportState;\n\n  constructor({\n    maxZoom,\n    radius = 60,\n    viewportPadding = 60,\n    ...options\n  }: SuperClusterViewportOptions) {\n    super({ maxZoom, viewportPadding });\n\n    this.superCluster = new SuperCluster({\n      maxZoom: this.maxZoom,\n      radius,\n      ...options,\n    });\n\n    this.state = { zoom: -1, view: [0, 0, 0, 0] };\n  }\n\n  public calculate(input: AlgorithmInput): AlgorithmOutput {\n    const state: SuperClusterViewportState = {\n      zoom: Math.round(input.map.getZoom()),\n      view: getPaddedViewport(\n        input.map.getBounds(),\n        input.mapCanvasProjection,\n        this.viewportPadding\n      ),\n    };\n\n    let changed = !equal(this.state, state);\n    if (!equal(input.markers, this.markers)) {\n      changed = true;\n      // TODO use proxy to avoid copy?\n      this.markers = [...input.markers];\n\n      const points = this.markers.map((marker) => {\n        const position = MarkerUtils.getPosition(marker);\n        const coordinates = [position.lng(), position.lat()];\n        return {\n          type: \"Feature\" as const,\n          geometry: {\n            type: \"Point\" as const,\n            coordinates,\n          },\n          properties: { marker },\n        };\n      });\n      this.superCluster.load(points);\n    }\n\n    if (changed) {\n      this.clusters = this.cluster(input);\n      this.state = state;\n    }\n\n    return { clusters: this.clusters, changed };\n  }\n\n  public cluster({ map, mapCanvasProjection }: AlgorithmInput): Cluster[] {\n    /* recalculate new state because we can't use the cached version. */\n    const state: SuperClusterViewportState = {\n      zoom: Math.round(map.getZoom()),\n      view: getPaddedViewport(\n        map.getBounds(),\n        mapCanvasProjection,\n        this.viewportPadding\n      ),\n    };\n\n    return this.superCluster\n      .getClusters(state.view, state.zoom)\n      .map((feature: ClusterFeature<{ marker: Marker }>) =>\n        this.transformCluster(feature)\n      );\n  }\n\n  protected transformCluster({\n    geometry: {\n      coordinates: [lng, lat],\n    },\n    properties,\n  }: ClusterFeature<{ marker: Marker }>): Cluster {\n    if (properties.cluster) {\n      return new Cluster({\n        markers: this.superCluster\n          .getLeaves(properties.cluster_id, Infinity)\n          .map((leaf) => leaf.properties.marker),\n        position: { lat, lng },\n      });\n    }\n\n    const marker = properties.marker;\n\n    return new Cluster({\n      markers: [marker],\n      position: MarkerUtils.getPosition(marker),\n    });\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Cluster } from \"./cluster\";\nimport { Marker, MarkerUtils } from \"./marker-utils\";\n\n/**\n * Provides statistics on all clusters in the current render cycle for use in {@link Renderer.render}.\n */\nexport class ClusterStats {\n  public readonly markers: { sum: number };\n  public readonly clusters: {\n    count: number;\n    markers: {\n      mean: number;\n      sum: number;\n      min: number;\n      max: number;\n    };\n  };\n\n  constructor(markers: Marker[], clusters: Cluster[]) {\n    this.markers = { sum: markers.length };\n    const clusterMarkerCounts = clusters.map((a) => a.count);\n    const clusterMarkerSum = clusterMarkerCounts.reduce((a, b) => a + b, 0);\n\n    this.clusters = {\n      count: clusters.length,\n      markers: {\n        mean: clusterMarkerSum / clusters.length,\n        sum: clusterMarkerSum,\n        min: Math.min(...clusterMarkerCounts),\n        max: Math.max(...clusterMarkerCounts),\n      },\n    };\n  }\n}\n\nexport interface Renderer {\n  /**\n   * Turn a {@link Cluster} into a `Marker`.\n   *\n   * Below is a simple example to create a marker with the number of markers in the cluster as a label.\n   *\n   * ```typescript\n   * return new google.maps.Marker({\n   *   position,\n   *   label: String(markers.length),\n   * });\n   * ```\n   */\n  render(cluster: Cluster, stats: ClusterStats, map: google.maps.Map): Marker;\n}\n\nexport class DefaultRenderer implements Renderer {\n  /**\n   * The default render function for the library used by {@link MarkerClusterer}.\n   *\n   * Currently set to use the following:\n   *\n   * ```typescript\n   * // change color if this cluster has more markers than the mean cluster\n   * const color =\n   *   count > Math.max(10, stats.clusters.markers.mean)\n   *     ? \"#ff0000\"\n   *     : \"#0000ff\";\n   *\n   * // create svg url with fill color\n   * const svg = window.btoa(`\n   * <svg fill=\"${color}\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\">\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".6\" r=\"70\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\n   *   <circle cx=\"120\" cy=\"120\" opacity=\".1\" r=\"130\" />\n   * </svg>`);\n   *\n   * // create marker using svg icon\n   * return new google.maps.Marker({\n   *   position,\n   *   icon: {\n   *     url: `data:image/svg+xml;base64,${svg}`,\n   *     scaledSize: new google.maps.Size(45, 45),\n   *   },\n   *   label: {\n   *     text: String(count),\n   *     color: \"rgba(255,255,255,0.9)\",\n   *     fontSize: \"12px\",\n   *   },\n   *   // adjust zIndex to be above other markers\n   *   zIndex: 1000 + count,\n   * });\n   * ```\n   */\n  public render(\n    { count, position }: Cluster,\n    stats: ClusterStats,\n    map: google.maps.Map\n  ): Marker {\n    // change color if this cluster has more markers than the mean cluster\n    const color =\n      count > Math.max(10, stats.clusters.markers.mean) ? \"#ff0000\" : \"#0000ff\";\n\n    // create svg literal with fill color\n    const svg = `<svg fill=\"${color}\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\" width=\"50\" height=\"50\">\n<circle cx=\"120\" cy=\"120\" opacity=\".6\" r=\"70\" />\n<circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\n<circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\n<text x=\"50%\" y=\"50%\" style=\"fill:#fff\" text-anchor=\"middle\" font-size=\"50\" dominant-baseline=\"middle\" font-family=\"roboto,arial,sans-serif\">${count}</text>\n</svg>`;\n\n    const title = `Cluster of ${count} markers`,\n      // adjust zIndex to be above other markers\n      zIndex: number = Number(google.maps.Marker.MAX_ZINDEX) + count;\n\n    if (MarkerUtils.isAdvancedMarkerAvailable(map)) {\n      // create cluster SVG element\n      const parser = new DOMParser();\n      const svgEl = parser.parseFromString(\n        svg,\n        \"image/svg+xml\"\n      ).documentElement;\n      svgEl.setAttribute(\"transform\", \"translate(0 25)\");\n\n      const clusterOptions: google.maps.marker.AdvancedMarkerElementOptions = {\n        map,\n        position,\n        zIndex,\n        title,\n        content: svgEl,\n      };\n      return new google.maps.marker.AdvancedMarkerElement(clusterOptions);\n    }\n\n    const clusterOptions: google.maps.MarkerOptions = {\n      position,\n      zIndex,\n      title,\n      icon: {\n        url: `data:image/svg+xml;base64,${btoa(svg)}`,\n        anchor: new google.maps.Point(25, 25),\n      },\n    };\n    return new google.maps.Marker(clusterOptions);\n  }\n}\n", "/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface OverlayViewSafe extends google.maps.OverlayView {}\n\n/**\n * Extends an object's prototype by another's.\n *\n * @param type1 The Type to be extended.\n * @param type2 The Type to extend with.\n * @ignore\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction extend(type1: any, type2: any): void {\n  /* istanbul ignore next */\n  // eslint-disable-next-line prefer-const\n  for (let property in type2.prototype) {\n    type1.prototype[property] = type2.prototype[property];\n  }\n}\n\n/**\n * @ignore\n */\nexport class OverlayViewSafe {\n  constructor() {\n    // MarkerClusterer implements google.maps.OverlayView interface. We use the\n    // extend function to extend MarkerClusterer with google.maps.OverlayView\n    // because it might not always be available when the code is defined so we\n    // look for it at the last possible moment. If it doesn't exist now then\n    // there is no point going ahead :)\n    extend(OverlayViewSafe, google.maps.OverlayView);\n  }\n}\n", "/**\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Algorithm,\n  AlgorithmOptions,\n  SuperClusterAlgorithm,\n} from \"./algorithms\";\nimport { ClusterStats, DefaultRenderer, Renderer } from \"./renderer\";\nimport { Cluster } from \"./cluster\";\nimport { OverlayViewSafe } from \"./overlay-view-safe\";\nimport { MarkerUtils, Marker } from \"./marker-utils\";\n\nexport type onClusterClickHandler = (\n  event: google.maps.MapMouseEvent,\n  cluster: Cluster,\n  map: google.maps.Map\n) => void;\nexport interface MarkerClustererOptions {\n  markers?: Marker[];\n  /**\n   * An algorithm to cluster markers. Default is {@link SuperClusterAlgorithm}. Must\n   * provide a `calculate` method accepting {@link AlgorithmInput} and returning\n   * an array of {@link Cluster}.\n   */\n  algorithm?: Algorithm;\n  algorithmOptions?: AlgorithmOptions;\n  map?: google.maps.Map | null;\n  /**\n   * An object that converts a {@link Cluster} into a `google.maps.Marker`.\n   * Default is {@link DefaultRenderer}.\n   */\n  renderer?: Renderer;\n  onClusterClick?: onClusterClickHandler;\n}\n\nexport enum MarkerClustererEvents {\n  CLUSTERING_BEGIN = \"clusteringbegin\",\n  CLUSTERING_END = \"clusteringend\",\n  CLUSTER_CLICK = \"click\",\n}\n\nexport const defaultOnClusterClickHandler: onClusterClickHandler = (\n  _: google.maps.MapMouseEvent,\n  cluster: Cluster,\n  map: google.maps.Map\n): void => {\n  map.fitBounds(cluster.bounds);\n};\n/**\n * MarkerClusterer creates and manages per-zoom-level clusters for large amounts\n * of markers. See {@link MarkerClustererOptions} for more details.\n *\n */\nexport class MarkerClusterer extends OverlayViewSafe {\n  /** @see {@link MarkerClustererOptions.onClusterClick} */\n  public onClusterClick: onClusterClickHandler;\n  /** @see {@link MarkerClustererOptions.algorithm} */\n  protected algorithm: Algorithm;\n  protected clusters: Cluster[];\n  protected markers: Marker[];\n  /** @see {@link MarkerClustererOptions.renderer} */\n  protected renderer: Renderer;\n  /** @see {@link MarkerClustererOptions.map} */\n  protected map: google.maps.Map | null;\n  protected idleListener: google.maps.MapsEventListener;\n\n  constructor({\n    map,\n    markers = [],\n    algorithmOptions = {},\n    algorithm = new SuperClusterAlgorithm(algorithmOptions),\n    renderer = new DefaultRenderer(),\n    onClusterClick = defaultOnClusterClickHandler,\n  }: MarkerClustererOptions) {\n    super();\n    this.markers = [...markers];\n    this.clusters = [];\n\n    this.algorithm = algorithm;\n    this.renderer = renderer;\n\n    this.onClusterClick = onClusterClick;\n\n    if (map) {\n      this.setMap(map);\n    }\n  }\n\n  public addMarker(marker: Marker, noDraw?: boolean): void {\n    if (this.markers.includes(marker)) {\n      return;\n    }\n\n    this.markers.push(marker);\n    if (!noDraw) {\n      this.render();\n    }\n  }\n\n  public addMarkers(markers: Marker[], noDraw?: boolean): void {\n    markers.forEach((marker) => {\n      this.addMarker(marker, true);\n    });\n\n    if (!noDraw) {\n      this.render();\n    }\n  }\n\n  public removeMarker(marker: Marker, noDraw?: boolean): boolean {\n    const index = this.markers.indexOf(marker);\n\n    if (index === -1) {\n      // Marker is not in our list of markers, so do nothing:\n      return false;\n    }\n\n    MarkerUtils.setMap(marker, null);\n    this.markers.splice(index, 1); // Remove the marker from the list of managed markers\n\n    if (!noDraw) {\n      this.render();\n    }\n\n    return true;\n  }\n\n  public removeMarkers(markers: Marker[], noDraw?: boolean): boolean {\n    let removed = false;\n\n    markers.forEach((marker) => {\n      removed = this.removeMarker(marker, true) || removed;\n    });\n\n    if (removed && !noDraw) {\n      this.render();\n    }\n\n    return removed;\n  }\n\n  public clearMarkers(noDraw?: boolean): void {\n    this.markers.length = 0;\n\n    if (!noDraw) {\n      this.render();\n    }\n  }\n\n  /**\n   * Recalculates and draws all the marker clusters.\n   */\n  public render(): void {\n    const map = this.getMap();\n    if (map instanceof google.maps.Map && map.getProjection()) {\n      google.maps.event.trigger(\n        this,\n        MarkerClustererEvents.CLUSTERING_BEGIN,\n        this\n      );\n      const { clusters, changed } = this.algorithm.calculate({\n        markers: this.markers,\n        map,\n        mapCanvasProjection: this.getProjection(),\n      });\n\n      // Allow algorithms to return flag on whether the clusters/markers have changed.\n      if (changed || changed == undefined) {\n        // Accumulate the markers of the clusters composed of a single marker.\n        // Those clusters directly use the marker.\n        // Clusters with more than one markers use a group marker generated by a renderer.\n        const singleMarker = new Set<Marker>();\n        for (const cluster of clusters) {\n          if (cluster.markers.length == 1) {\n            singleMarker.add(cluster.markers[0]);\n          }\n        }\n\n        const groupMarkers: Marker[] = [];\n        // Iterate the clusters that are currently rendered.\n        for (const cluster of this.clusters) {\n          if (cluster.marker == null) {\n            continue;\n          }\n          if (cluster.markers.length == 1) {\n            if (!singleMarker.has(cluster.marker)) {\n              // The marker:\n              // - was previously rendered because it is from a cluster with 1 marker,\n              // - should no more be rendered as it is not in singleMarker.\n              MarkerUtils.setMap(cluster.marker, null);\n            }\n          } else {\n            // Delay the removal of old group markers to avoid flickering.\n            groupMarkers.push(cluster.marker);\n          }\n        }\n\n        this.clusters = clusters;\n        this.renderClusters();\n\n        // Delayed removal of the markers of the former groups.\n        requestAnimationFrame(() =>\n          groupMarkers.forEach((marker) => MarkerUtils.setMap(marker, null))\n        );\n      }\n      google.maps.event.trigger(\n        this,\n        MarkerClustererEvents.CLUSTERING_END,\n        this\n      );\n    }\n  }\n\n  public onAdd(): void {\n    this.idleListener = this.getMap().addListener(\n      \"idle\",\n      this.render.bind(this)\n    );\n    this.render();\n  }\n\n  public onRemove(): void {\n    google.maps.event.removeListener(this.idleListener);\n    this.reset();\n  }\n\n  protected reset(): void {\n    this.markers.forEach((marker) => MarkerUtils.setMap(marker, null));\n    this.clusters.forEach((cluster) => cluster.delete());\n    this.clusters = [];\n  }\n\n  protected renderClusters(): void {\n    // Generate stats to pass to renderers.\n    const stats = new ClusterStats(this.markers, this.clusters);\n    const map = this.getMap() as google.maps.Map;\n\n    this.clusters.forEach((cluster) => {\n      if (cluster.markers.length === 1) {\n        cluster.marker = cluster.markers[0];\n      } else {\n        // Generate the marker to represent the group.\n        cluster.marker = this.renderer.render(cluster, stats, map);\n        // Make sure all individual markers are removed from the map.\n        cluster.markers.forEach((marker) => MarkerUtils.setMap(marker, null));\n        if (this.onClusterClick) {\n          cluster.marker.addListener(\n            \"click\",\n            /* istanbul ignore next */\n            (event: google.maps.MapMouseEvent) => {\n              google.maps.event.trigger(\n                this,\n                MarkerClustererEvents.CLUSTER_CLICK,\n                cluster\n              );\n              this.onClusterClick(event, cluster, map);\n            }\n          );\n        }\n      }\n      MarkerUtils.setMap(cluster.marker, map);\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;;;AACA,IAAM,cAAc;AAAA,EAChB;AAAA,EAAW;AAAA,EAAY;AAAA,EAAmB;AAAA,EAAY;AAAA,EACtD;AAAA,EAAY;AAAA,EAAa;AAAA,EAAc;AAC3C;AAIA,IAAM,UAAU;AAChB,IAAM,cAAc;AAEpB,IAAqB,SAArB,MAAqB,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,OAAO,KAAK,MAAM;AACd,QAAI,EAAE,gBAAgB,cAAc;AAChC,YAAM,IAAI,MAAM,0CAA0C;AAAA,IAC9D;AACA,UAAM,CAAC,OAAO,cAAc,IAAI,IAAI,WAAW,MAAM,GAAG,CAAC;AACzD,QAAI,UAAU,KAAM;AAChB,YAAM,IAAI,MAAM,gDAAgD;AAAA,IACpE;AACA,UAAM,UAAU,kBAAkB;AAClC,QAAI,YAAY,SAAS;AACrB,YAAM,IAAI,MAAM,QAAQ,OAAO,wBAAwB,OAAO,GAAG;AAAA,IACrE;AACA,UAAM,YAAY,YAAY,iBAAiB,EAAI;AACnD,QAAI,CAAC,WAAW;AACZ,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC9C;AACA,UAAM,CAAC,QAAQ,IAAI,IAAI,YAAY,MAAM,GAAG,CAAC;AAC7C,UAAM,CAAC,QAAQ,IAAI,IAAI,YAAY,MAAM,GAAG,CAAC;AAE7C,WAAO,IAAI,QAAO,UAAU,UAAU,WAAW,IAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,UAAU,WAAW,IAAI,YAAY,cAAc,MAAM;AACjE,QAAI,MAAM,QAAQ,KAAK,WAAW,EAAG,OAAM,IAAI,MAAM,+BAA+B,QAAQ,GAAG;AAE/F,SAAK,WAAW,CAAC;AACjB,SAAK,WAAW,KAAK,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK;AACtD,SAAK,YAAY;AACjB,SAAK,iBAAiB,WAAW,QAAQ,cAAc;AAEvD,UAAM,iBAAiB,YAAY,QAAQ,KAAK,SAAS;AACzD,UAAM,iBAAiB,WAAW,IAAI,KAAK,UAAU;AACrD,UAAM,cAAc,WAAW,KAAK,eAAe;AACnD,UAAM,aAAa,IAAI,cAAc,KAAK;AAE1C,QAAI,iBAAiB,GAAG;AACpB,YAAM,IAAI,MAAM,iCAAiC,SAAS,GAAG;AAAA,IACjE;AAEA,QAAI,QAAS,gBAAgB,aAAc;AACvC,WAAK,OAAO;AACZ,WAAK,MAAM,IAAI,KAAK,eAAe,KAAK,MAAM,aAAa,QAAQ;AACnE,WAAK,SAAS,IAAI,KAAK,UAAU,KAAK,MAAM,cAAc,cAAc,WAAW,WAAW,CAAC;AAC/F,WAAK,OAAO,WAAW;AACvB,WAAK,YAAY;AAAA,IACrB,OAAO;AACH,WAAK,OAAO,IAAI,YAAY,cAAc,iBAAiB,cAAc,SAAS;AAClF,WAAK,MAAM,IAAI,KAAK,eAAe,KAAK,MAAM,aAAa,QAAQ;AACnE,WAAK,SAAS,IAAI,KAAK,UAAU,KAAK,MAAM,cAAc,cAAc,WAAW,WAAW,CAAC;AAC/F,WAAK,OAAO;AACZ,WAAK,YAAY;AAGjB,UAAI,WAAW,KAAK,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,MAAO,WAAW,KAAK,cAAc,CAAC;AAC3E,UAAI,YAAY,KAAK,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI;AACtC,UAAI,YAAY,KAAK,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI;AAAA,IAC1C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,GAAG,GAAG;AACN,UAAM,QAAQ,KAAK,QAAQ;AAC3B,SAAK,IAAI,KAAK,IAAI;AAClB,SAAK,OAAO,KAAK,MAAM,IAAI;AAC3B,SAAK,OAAO,KAAK,MAAM,IAAI;AAC3B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACL,UAAM,WAAW,KAAK,QAAQ;AAC9B,QAAI,aAAa,KAAK,UAAU;AAC5B,YAAM,IAAI,MAAM,SAAS,QAAQ,wBAAwB,KAAK,QAAQ,GAAG;AAAA,IAC7E;AAEA,SAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,UAAU,GAAG,KAAK,WAAW,GAAG,CAAC;AAElE,SAAK,YAAY;AACjB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,MAAM,MAAM,MAAM,MAAM;AAC1B,QAAI,CAAC,KAAK,UAAW,OAAM,IAAI,MAAM,6CAA6C;AAElF,UAAM,EAAC,KAAK,QAAQ,SAAQ,IAAI;AAChC,UAAM,QAAQ,CAAC,GAAG,IAAI,SAAS,GAAG,CAAC;AACnC,UAAM,SAAS,CAAC;AAGhB,WAAO,MAAM,QAAQ;AACjB,YAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,YAAM,QAAQ,MAAM,IAAI,KAAK;AAC7B,YAAM,OAAO,MAAM,IAAI,KAAK;AAG5B,UAAI,QAAQ,QAAQ,UAAU;AAC1B,iBAAS,IAAI,MAAM,KAAK,OAAO,KAAK;AAChC,gBAAMA,KAAI,OAAO,IAAI,CAAC;AACtB,gBAAMC,KAAI,OAAO,IAAI,IAAI,CAAC;AAC1B,cAAID,MAAK,QAAQA,MAAK,QAAQC,MAAK,QAAQA,MAAK,KAAM,QAAO,KAAK,IAAI,CAAC,CAAC;AAAA,QAC5E;AACA;AAAA,MACJ;AAGA,YAAM,IAAK,OAAO,SAAU;AAG5B,YAAM,IAAI,OAAO,IAAI,CAAC;AACtB,YAAM,IAAI,OAAO,IAAI,IAAI,CAAC;AAC1B,UAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,KAAM,QAAO,KAAK,IAAI,CAAC,CAAC;AAGxE,UAAI,SAAS,IAAI,QAAQ,IAAI,QAAQ,GAAG;AACpC,cAAM,KAAK,IAAI;AACf,cAAM,KAAK,IAAI,CAAC;AAChB,cAAM,KAAK,IAAI,IAAI;AAAA,MACvB;AACA,UAAI,SAAS,IAAI,QAAQ,IAAI,QAAQ,GAAG;AACpC,cAAM,KAAK,IAAI,CAAC;AAChB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,IAAI,IAAI;AAAA,MACvB;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,IAAI,IAAI,GAAG;AACd,QAAI,CAAC,KAAK,UAAW,OAAM,IAAI,MAAM,6CAA6C;AAElF,UAAM,EAAC,KAAK,QAAQ,SAAQ,IAAI;AAChC,UAAM,QAAQ,CAAC,GAAG,IAAI,SAAS,GAAG,CAAC;AACnC,UAAM,SAAS,CAAC;AAChB,UAAM,KAAK,IAAI;AAGf,WAAO,MAAM,QAAQ;AACjB,YAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,YAAM,QAAQ,MAAM,IAAI,KAAK;AAC7B,YAAM,OAAO,MAAM,IAAI,KAAK;AAG5B,UAAI,QAAQ,QAAQ,UAAU;AAC1B,iBAAS,IAAI,MAAM,KAAK,OAAO,KAAK;AAChC,cAAI,OAAO,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,KAAK,GAAI,QAAO,KAAK,IAAI,CAAC,CAAC;AAAA,QAClF;AACA;AAAA,MACJ;AAGA,YAAM,IAAK,OAAO,SAAU;AAG5B,YAAM,IAAI,OAAO,IAAI,CAAC;AACtB,YAAM,IAAI,OAAO,IAAI,IAAI,CAAC;AAC1B,UAAI,OAAO,GAAG,GAAG,IAAI,EAAE,KAAK,GAAI,QAAO,KAAK,IAAI,CAAC,CAAC;AAGlD,UAAI,SAAS,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG;AACxC,cAAM,KAAK,IAAI;AACf,cAAM,KAAK,IAAI,CAAC;AAChB,cAAM,KAAK,IAAI,IAAI;AAAA,MACvB;AACA,UAAI,SAAS,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG;AACxC,cAAM,KAAK,IAAI,CAAC;AAChB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,IAAI,IAAI;AAAA,MACvB;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AACJ;AAUA,SAAS,KAAK,KAAK,QAAQ,UAAU,MAAM,OAAO,MAAM;AACpD,MAAI,QAAQ,QAAQ,SAAU;AAE9B,QAAM,IAAK,OAAO,SAAU;AAI5B,SAAO,KAAK,QAAQ,GAAG,MAAM,OAAO,IAAI;AAGxC,OAAK,KAAK,QAAQ,UAAU,MAAM,IAAI,GAAG,IAAI,IAAI;AACjD,OAAK,KAAK,QAAQ,UAAU,IAAI,GAAG,OAAO,IAAI,IAAI;AACtD;AAYA,SAAS,OAAO,KAAK,QAAQ,GAAG,MAAM,OAAO,MAAM;AAE/C,SAAO,QAAQ,MAAM;AACjB,QAAI,QAAQ,OAAO,KAAK;AACpB,YAAM,IAAI,QAAQ,OAAO;AACzB,YAAM,IAAI,IAAI,OAAO;AACrB,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC;AAClC,YAAM,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACxE,YAAM,UAAU,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AAC7D,YAAM,WAAW,KAAK,IAAI,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AACrE,aAAO,KAAK,QAAQ,GAAG,SAAS,UAAU,IAAI;AAAA,IAClD;AAEA,UAAM,IAAI,OAAO,IAAI,IAAI,IAAI;AAC7B,QAAI,IAAI;AACR,QAAI,IAAI;AAER,aAAS,KAAK,QAAQ,MAAM,CAAC;AAC7B,QAAI,OAAO,IAAI,QAAQ,IAAI,IAAI,EAAG,UAAS,KAAK,QAAQ,MAAM,KAAK;AAEnE,WAAO,IAAI,GAAG;AACV,eAAS,KAAK,QAAQ,GAAG,CAAC;AAC1B;AACA;AACA,aAAO,OAAO,IAAI,IAAI,IAAI,IAAI,EAAG;AACjC,aAAO,OAAO,IAAI,IAAI,IAAI,IAAI,EAAG;AAAA,IACrC;AAEA,QAAI,OAAO,IAAI,OAAO,IAAI,MAAM,EAAG,UAAS,KAAK,QAAQ,MAAM,CAAC;AAAA,SAC3D;AACD;AACA,eAAS,KAAK,QAAQ,GAAG,KAAK;AAAA,IAClC;AAEA,QAAI,KAAK,EAAG,QAAO,IAAI;AACvB,QAAI,KAAK,EAAG,SAAQ,IAAI;AAAA,EAC5B;AACJ;AAQA,SAAS,SAAS,KAAK,QAAQ,GAAG,GAAG;AACjC,OAAK,KAAK,GAAG,CAAC;AACd,OAAK,QAAQ,IAAI,GAAG,IAAI,CAAC;AACzB,OAAK,QAAQ,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC;AACrC;AAOA,SAAS,KAAK,KAAK,GAAG,GAAG;AACrB,QAAM,MAAM,IAAI,CAAC;AACjB,MAAI,CAAC,IAAI,IAAI,CAAC;AACd,MAAI,CAAC,IAAI;AACb;AAQA,SAAS,OAAO,IAAI,IAAI,IAAI,IAAI;AAC5B,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,SAAO,KAAK,KAAK,KAAK;AAC1B;;;ACnUA,IAAM,iBAAiB;AAAA,EACnB,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,WAAW;AAAA;AAAA,EACX,QAAQ;AAAA;AAAA,EACR,QAAQ;AAAA;AAAA,EACR,UAAU;AAAA;AAAA,EACV,KAAK;AAAA;AAAA;AAAA,EAGL,YAAY;AAAA;AAAA,EAGZ,QAAQ;AAAA;AAAA;AAAA,EAGR,KAAK,WAAS;AAAA;AAClB;AAEA,IAAM,SAAS,KAAK,UAAW,0BAAQ,CAAC,MAAM;AAAE,MAAI,CAAC,IAAI,CAAC;AAAG,SAAO,IAAI,CAAC;AAAG,GAAI,IAAI,aAAa,CAAC,CAAC;AAEnG,IAAM,cAAc;AACpB,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,aAAa;AACnB,IAAM,cAAc;AAEpB,IAAqB,eAArB,MAAkC;AAAA,EAC9B,YAAY,SAAS;AACjB,SAAK,UAAU,OAAO,OAAO,OAAO,OAAO,cAAc,GAAG,OAAO;AACnE,SAAK,QAAQ,IAAI,MAAM,KAAK,QAAQ,UAAU,CAAC;AAC/C,SAAK,SAAS,KAAK,QAAQ,SAAS,IAAI;AACxC,SAAK,eAAe,CAAC;AAAA,EACzB;AAAA,EAEA,KAAK,QAAQ;AACT,UAAM,EAAC,KAAK,SAAS,QAAO,IAAI,KAAK;AAErC,QAAI,IAAK,SAAQ,KAAK,YAAY;AAElC,UAAM,UAAU,WAAa,OAAO,MAAQ;AAC5C,QAAI,IAAK,SAAQ,KAAK,OAAO;AAE7B,SAAK,SAAS;AAGd,UAAM,OAAO,CAAC;AAEd,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAM,IAAI,OAAO,CAAC;AAClB,UAAI,CAAC,EAAE,SAAU;AAEjB,YAAM,CAAC,KAAK,GAAG,IAAI,EAAE,SAAS;AAC9B,YAAM,IAAI,OAAO,KAAK,GAAG,CAAC;AAC1B,YAAM,IAAI,OAAO,KAAK,GAAG,CAAC;AAE1B,WAAK;AAAA,QACD;AAAA,QAAG;AAAA;AAAA,QACH;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACJ;AACA,UAAI,KAAK,QAAQ,OAAQ,MAAK,KAAK,CAAC;AAAA,IACxC;AACA,QAAI,OAAO,KAAK,MAAM,UAAU,CAAC,IAAI,KAAK,YAAY,IAAI;AAE1D,QAAI,IAAK,SAAQ,QAAQ,OAAO;AAIhC,aAAS,IAAI,SAAS,KAAK,SAAS,KAAK;AACrC,YAAM,MAAM,CAAC,KAAK,IAAI;AAGtB,aAAO,KAAK,MAAM,CAAC,IAAI,KAAK,YAAY,KAAK,SAAS,MAAM,CAAC,CAAC;AAE9D,UAAI,IAAK,SAAQ,IAAI,4BAA4B,GAAG,KAAK,UAAU,CAAC,KAAK,IAAI,IAAI,GAAG;AAAA,IACxF;AAEA,QAAI,IAAK,SAAQ,QAAQ,YAAY;AAErC,WAAO;AAAA,EACX;AAAA,EAEA,YAAY,MAAM,MAAM;AACpB,QAAI,WAAW,KAAK,CAAC,IAAI,OAAO,MAAM,OAAO,MAAM;AACnD,UAAM,SAAS,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC;AAClD,QAAI,SAAS,KAAK,CAAC,MAAM,MAAM,QAAQ,KAAK,CAAC,IAAI,OAAO,MAAM,OAAO,MAAM;AAC3E,UAAM,SAAS,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC;AAElD,QAAI,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK;AAC1B,eAAS;AACT,eAAS;AAAA,IACb,WAAW,SAAS,QAAQ;AACxB,YAAM,aAAa,KAAK,YAAY,CAAC,QAAQ,QAAQ,KAAK,MAAM,GAAG,IAAI;AACvE,YAAM,aAAa,KAAK,YAAY,CAAC,MAAM,QAAQ,QAAQ,MAAM,GAAG,IAAI;AACxE,aAAO,WAAW,OAAO,UAAU;AAAA,IACvC;AAEA,UAAM,OAAO,KAAK,MAAM,KAAK,WAAW,IAAI,CAAC;AAC7C,UAAM,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC;AAC7E,UAAM,OAAO,KAAK;AAClB,UAAM,WAAW,CAAC;AAClB,eAAW,MAAM,KAAK;AAClB,YAAM,IAAI,KAAK,SAAS;AACxB,eAAS,KAAK,KAAK,IAAI,UAAU,IAAI,IAAI,eAAe,MAAM,GAAG,KAAK,YAAY,IAAI,KAAK,OAAO,KAAK,IAAI,SAAS,CAAC,CAAC;AAAA,IAC1H;AACA,WAAO;AAAA,EACX;AAAA,EAEA,YAAY,WAAW;AACnB,UAAM,WAAW,KAAK,aAAa,SAAS;AAC5C,UAAM,aAAa,KAAK,eAAe,SAAS;AAChD,UAAM,WAAW;AAEjB,UAAM,OAAO,KAAK,MAAM,UAAU;AAClC,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,QAAQ;AAEnC,UAAM,OAAO,KAAK;AAClB,QAAI,WAAW,KAAK,UAAU,KAAK,OAAQ,OAAM,IAAI,MAAM,QAAQ;AAEnE,UAAM,IAAI,KAAK,QAAQ,UAAU,KAAK,QAAQ,SAAS,KAAK,IAAI,GAAG,aAAa,CAAC;AACjF,UAAM,IAAI,KAAK,WAAW,KAAK,MAAM;AACrC,UAAM,IAAI,KAAK,WAAW,KAAK,SAAS,CAAC;AACzC,UAAM,MAAM,KAAK,OAAO,GAAG,GAAG,CAAC;AAC/B,UAAM,WAAW,CAAC;AAClB,eAAW,MAAM,KAAK;AAClB,YAAM,IAAI,KAAK,KAAK;AACpB,UAAI,KAAK,IAAI,aAAa,MAAM,WAAW;AACvC,iBAAS,KAAK,KAAK,IAAI,UAAU,IAAI,IAAI,eAAe,MAAM,GAAG,KAAK,YAAY,IAAI,KAAK,OAAO,KAAK,IAAI,SAAS,CAAC,CAAC;AAAA,MAC1H;AAAA,IACJ;AAEA,QAAI,SAAS,WAAW,EAAG,OAAM,IAAI,MAAM,QAAQ;AAEnD,WAAO;AAAA,EACX;AAAA,EAEA,UAAU,WAAW,OAAO,QAAQ;AAChC,YAAQ,SAAS;AACjB,aAAS,UAAU;AAEnB,UAAM,SAAS,CAAC;AAChB,SAAK,cAAc,QAAQ,WAAW,OAAO,QAAQ,CAAC;AAEtD,WAAO;AAAA,EACX;AAAA,EAEA,QAAQ,GAAG,GAAG,GAAG;AACb,UAAM,OAAO,KAAK,MAAM,KAAK,WAAW,CAAC,CAAC;AAC1C,UAAM,KAAK,KAAK,IAAI,GAAG,CAAC;AACxB,UAAM,EAAC,QAAQ,OAAM,IAAI,KAAK;AAC9B,UAAM,IAAI,SAAS;AACnB,UAAM,OAAO,IAAI,KAAK;AACtB,UAAM,UAAU,IAAI,IAAI,KAAK;AAE7B,UAAM,OAAO;AAAA,MACT,UAAU,CAAC;AAAA,IACf;AAEA,SAAK;AAAA,MACD,KAAK,OAAO,IAAI,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK,IAAI,MAAM;AAAA,MACtD,KAAK;AAAA,MAAM;AAAA,MAAG;AAAA,MAAG;AAAA,MAAI;AAAA,IAAI;AAE7B,QAAI,MAAM,GAAG;AACT,WAAK;AAAA,QACD,KAAK,MAAM,IAAI,IAAI,IAAI,KAAK,GAAG,MAAM;AAAA,QACrC,KAAK;AAAA,QAAM;AAAA,QAAI;AAAA,QAAG;AAAA,QAAI;AAAA,MAAI;AAAA,IAClC;AACA,QAAI,MAAM,KAAK,GAAG;AACd,WAAK;AAAA,QACD,KAAK,MAAM,GAAG,KAAK,IAAI,IAAI,MAAM;AAAA,QACjC,KAAK;AAAA,QAAM;AAAA,QAAI;AAAA,QAAG;AAAA,QAAI;AAAA,MAAI;AAAA,IAClC;AAEA,WAAO,KAAK,SAAS,SAAS,OAAO;AAAA,EACzC;AAAA,EAEA,wBAAwB,WAAW;AAC/B,QAAI,gBAAgB,KAAK,eAAe,SAAS,IAAI;AACrD,WAAO,iBAAiB,KAAK,QAAQ,SAAS;AAC1C,YAAM,WAAW,KAAK,YAAY,SAAS;AAC3C;AACA,UAAI,SAAS,WAAW,EAAG;AAC3B,kBAAY,SAAS,CAAC,EAAE,WAAW;AAAA,IACvC;AACA,WAAO;AAAA,EACX;AAAA,EAEA,cAAc,QAAQ,WAAW,OAAO,QAAQ,SAAS;AACrD,UAAM,WAAW,KAAK,YAAY,SAAS;AAE3C,eAAW,SAAS,UAAU;AAC1B,YAAM,QAAQ,MAAM;AAEpB,UAAI,SAAS,MAAM,SAAS;AACxB,YAAI,UAAU,MAAM,eAAe,QAAQ;AAEvC,qBAAW,MAAM;AAAA,QACrB,OAAO;AAEH,oBAAU,KAAK,cAAc,QAAQ,MAAM,YAAY,OAAO,QAAQ,OAAO;AAAA,QAEjF;AAAA,MACJ,WAAW,UAAU,QAAQ;AAEzB;AAAA,MACJ,OAAO;AAEH,eAAO,KAAK,KAAK;AAAA,MACrB;AACA,UAAI,OAAO,WAAW,MAAO;AAAA,IACjC;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,YAAY,MAAM;AACd,UAAM,OAAO,IAAI,OAAO,KAAK,SAAS,KAAK,SAAS,GAAG,KAAK,QAAQ,UAAU,YAAY;AAC1F,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,KAAK,OAAQ,MAAK,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;AAChF,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAAA,EAEA,iBAAiB,KAAK,MAAM,GAAG,GAAG,IAAI,MAAM;AACxC,eAAW,KAAK,KAAK;AACjB,YAAM,IAAI,IAAI,KAAK;AACnB,YAAM,YAAY,KAAK,IAAI,UAAU,IAAI;AAEzC,UAAI,MAAM,IAAI;AACd,UAAI,WAAW;AACX,eAAO,qBAAqB,MAAM,GAAG,KAAK,YAAY;AACtD,aAAK,KAAK,CAAC;AACX,aAAK,KAAK,IAAI,CAAC;AAAA,MACnB,OAAO;AACH,cAAM,IAAI,KAAK,OAAO,KAAK,IAAI,SAAS,CAAC;AACzC,eAAO,EAAE;AACT,cAAM,CAAC,KAAK,GAAG,IAAI,EAAE,SAAS;AAC9B,aAAK,KAAK,GAAG;AACb,aAAK,KAAK,GAAG;AAAA,MACjB;AAEA,YAAM,IAAI;AAAA,QACN,MAAM;AAAA,QACN,UAAU,CAAC;AAAA,UACP,KAAK,MAAM,KAAK,QAAQ,UAAU,KAAK,KAAK,EAAE;AAAA,UAC9C,KAAK,MAAM,KAAK,QAAQ,UAAU,KAAK,KAAK,EAAE;AAAA,QAClD,CAAC;AAAA,QACD;AAAA,MACJ;AAGA,UAAI;AACJ,UAAI,aAAa,KAAK,QAAQ,YAAY;AAEtC,aAAK,KAAK,IAAI,SAAS;AAAA,MAC3B,OAAO;AAEH,aAAK,KAAK,OAAO,KAAK,IAAI,SAAS,CAAC,EAAE;AAAA,MAC1C;AAEA,UAAI,OAAO,OAAW,GAAE,KAAK;AAE7B,WAAK,SAAS,KAAK,CAAC;AAAA,IACxB;AAAA,EACJ;AAAA,EAEA,WAAW,GAAG;AACV,WAAO,KAAK,IAAI,KAAK,QAAQ,SAAS,KAAK,IAAI,KAAK,MAAM,CAAC,CAAC,GAAG,KAAK,QAAQ,UAAU,CAAC,CAAC;AAAA,EAC5F;AAAA,EAEA,SAAS,MAAM,MAAM;AACjB,UAAM,EAAC,QAAQ,QAAQ,QAAQ,UAAS,IAAI,KAAK;AACjD,UAAM,IAAI,UAAU,SAAS,KAAK,IAAI,GAAG,IAAI;AAC7C,UAAM,OAAO,KAAK;AAClB,UAAM,WAAW,CAAC;AAClB,UAAM,SAAS,KAAK;AAGpB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,QAAQ;AAE1C,UAAI,KAAK,IAAI,WAAW,KAAK,KAAM;AACnC,WAAK,IAAI,WAAW,IAAI;AAGxB,YAAM,IAAI,KAAK,CAAC;AAChB,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,cAAc,KAAK,OAAO,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC;AAEvD,YAAM,kBAAkB,KAAK,IAAI,UAAU;AAC3C,UAAI,YAAY;AAGhB,iBAAW,cAAc,aAAa;AAClC,cAAM,IAAI,aAAa;AAEvB,YAAI,KAAK,IAAI,WAAW,IAAI,KAAM,cAAa,KAAK,IAAI,UAAU;AAAA,MACtE;AAGA,UAAI,YAAY,mBAAmB,aAAa,WAAW;AACvD,YAAI,KAAK,IAAI;AACb,YAAI,KAAK,IAAI;AAEb,YAAI;AACJ,YAAI,mBAAmB;AAGvB,cAAM,OAAO,IAAI,SAAS,MAAM,MAAM,OAAO,KAAK,KAAK,OAAO;AAE9D,mBAAW,cAAc,aAAa;AAClC,gBAAM,IAAI,aAAa;AAEvB,cAAI,KAAK,IAAI,WAAW,KAAK,KAAM;AACnC,eAAK,IAAI,WAAW,IAAI;AAExB,gBAAM,aAAa,KAAK,IAAI,UAAU;AACtC,gBAAM,KAAK,CAAC,IAAI;AAChB,gBAAM,KAAK,IAAI,CAAC,IAAI;AAEpB,eAAK,IAAI,aAAa,IAAI;AAE1B,cAAI,QAAQ;AACR,gBAAI,CAAC,mBAAmB;AACpB,kCAAoB,KAAK,KAAK,MAAM,GAAG,IAAI;AAC3C,iCAAmB,KAAK,aAAa;AACrC,mBAAK,aAAa,KAAK,iBAAiB;AAAA,YAC5C;AACA,mBAAO,mBAAmB,KAAK,KAAK,MAAM,CAAC,CAAC;AAAA,UAChD;AAAA,QACJ;AAEA,aAAK,IAAI,aAAa,IAAI;AAC1B,iBAAS,KAAK,KAAK,WAAW,KAAK,WAAW,UAAU,IAAI,IAAI,SAAS;AACzE,YAAI,OAAQ,UAAS,KAAK,gBAAgB;AAAA,MAE9C,OAAO;AACH,iBAAS,IAAI,GAAG,IAAI,QAAQ,IAAK,UAAS,KAAK,KAAK,IAAI,CAAC,CAAC;AAE1D,YAAI,YAAY,GAAG;AACf,qBAAW,cAAc,aAAa;AAClC,kBAAM,IAAI,aAAa;AACvB,gBAAI,KAAK,IAAI,WAAW,KAAK,KAAM;AACnC,iBAAK,IAAI,WAAW,IAAI;AACxB,qBAAS,IAAI,GAAG,IAAI,QAAQ,IAAK,UAAS,KAAK,KAAK,IAAI,CAAC,CAAC;AAAA,UAC9D;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAAA;AAAA,EAGA,aAAa,WAAW;AACpB,WAAQ,YAAY,KAAK,OAAO,UAAW;AAAA,EAC/C;AAAA;AAAA,EAGA,eAAe,WAAW;AACtB,YAAQ,YAAY,KAAK,OAAO,UAAU;AAAA,EAC9C;AAAA,EAEA,KAAK,MAAM,GAAG,OAAO;AACjB,QAAI,KAAK,IAAI,UAAU,IAAI,GAAG;AAC1B,YAAM,QAAQ,KAAK,aAAa,KAAK,IAAI,WAAW,CAAC;AACrD,aAAO,QAAQ,OAAO,OAAO,CAAC,GAAG,KAAK,IAAI;AAAA,IAC9C;AACA,UAAM,WAAW,KAAK,OAAO,KAAK,IAAI,SAAS,CAAC,EAAE;AAClD,UAAM,SAAS,KAAK,QAAQ,IAAI,QAAQ;AACxC,WAAO,SAAS,WAAW,WAAW,OAAO,OAAO,CAAC,GAAG,MAAM,IAAI;AAAA,EACtE;AACJ;AAEA,SAAS,eAAe,MAAM,GAAG,cAAc;AAC3C,SAAO;AAAA,IACH,MAAM;AAAA,IACN,IAAI,KAAK,IAAI,SAAS;AAAA,IACtB,YAAY,qBAAqB,MAAM,GAAG,YAAY;AAAA,IACtD,UAAU;AAAA,MACN,MAAM;AAAA,MACN,aAAa,CAAC,KAAK,KAAK,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC;AAAA,IAClD;AAAA,EACJ;AACJ;AAEA,SAAS,qBAAqB,MAAM,GAAG,cAAc;AACjD,QAAM,QAAQ,KAAK,IAAI,UAAU;AACjC,QAAM,SACF,SAAS,MAAQ,GAAG,KAAK,MAAM,QAAQ,GAAI,CAAG,MAC9C,SAAS,MAAO,GAAG,KAAK,MAAM,QAAQ,GAAG,IAAI,EAAI,MAAM;AAC3D,QAAM,YAAY,KAAK,IAAI,WAAW;AACtC,QAAM,aAAa,cAAc,KAAK,CAAC,IAAI,OAAO,OAAO,CAAC,GAAG,aAAa,SAAS,CAAC;AACpF,SAAO,OAAO,OAAO,YAAY;AAAA,IAC7B,SAAS;AAAA,IACT,YAAY,KAAK,IAAI,SAAS;AAAA,IAC9B,aAAa;AAAA,IACb,yBAAyB;AAAA,EAC7B,CAAC;AACL;AAGA,SAAS,KAAK,KAAK;AACf,SAAO,MAAM,MAAM;AACvB;AACA,SAAS,KAAK,KAAK;AACf,QAAM,MAAM,KAAK,IAAI,MAAM,KAAK,KAAK,GAAG;AACxC,QAAM,IAAK,MAAM,OAAO,KAAK,KAAK,IAAI,QAAQ,IAAI,IAAI,IAAI,KAAK;AAC/D,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACnC;AAGA,SAAS,KAAK,GAAG;AACb,UAAQ,IAAI,OAAO;AACvB;AACA,SAAS,KAAK,GAAG;AACb,QAAM,MAAM,MAAM,IAAI,OAAO,KAAK,KAAK;AACvC,SAAO,MAAM,KAAK,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK;AACrD;;;AC/XO,SAAS,OAAO,GAAG,GAAG;AACzB,MAAI,IAAI,CAAA;AACR,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAChC;AACI,SAAO;AACX;ICvBa,oBAAW;EACf,OAAO,0BAA0B,KAAoB;AAC1D,WACE,OAAO,KAAK,UACZ,IAAI,mBAAkB,EAAG,+BAA+B;;EAIrD,OAAO,iBACZ,QAAc;AAEd,WACE,OAAO,KAAK,UACZ,kBAAkB,OAAO,KAAK,OAAO;;EAIlC,OAAO,OAAO,QAAgB,KAA2B;AAC9D,QAAI,KAAK,iBAAiB,MAAM,GAAG;AACjC,aAAO,MAAM;IACd,OAAM;AACL,aAAO,OAAO,GAAG;IAClB;;EAGI,OAAO,YAAY,QAAc;AAEtC,QAAI,KAAK,iBAAiB,MAAM,GAAG;AACjC,UAAI,OAAO,UAAU;AACnB,YAAI,OAAO,oBAAoB,OAAO,KAAK,QAAQ;AACjD,iBAAO,OAAO;QACf;AAED,YAAI,OAAO,SAAS,OAAO,OAAO,SAAS,KAAK;AAC9C,iBAAO,IAAI,OAAO,KAAK,OACrB,OAAO,SAAS,KAChB,OAAO,SAAS,GAAG;QAEtB;MACF;AACD,aAAO,IAAI,OAAO,KAAK,OAAO,IAAI;IACnC;AACD,WAAO,OAAO,YAAW;;EAGpB,OAAO,WAAW,QAAc;AACrC,QAAI,KAAK,iBAAiB,MAAM,GAAG;AAQjC,aAAO;IACR;AACD,WAAO,OAAO,WAAU;;AAE3B;IC9DY,gBAAO;EAKlB,YAAY,EAAE,SAAS,SAAQ,GAAkB;AAC/C,SAAK,UAAU;AAEf,QAAI,UAAU;AACZ,UAAI,oBAAoB,OAAO,KAAK,QAAQ;AAC1C,aAAK,YAAY;MAClB,OAAM;AACL,aAAK,YAAY,IAAI,OAAO,KAAK,OAAO,QAAQ;MACjD;IACF;;EAGH,IAAW,SAAM;AACf,QAAI,KAAK,QAAQ,WAAW,KAAK,CAAC,KAAK,WAAW;AAChD;IACD;AAED,UAAM,SAAS,IAAI,OAAO,KAAK,aAAa,KAAK,WAAW,KAAK,SAAS;AAC1E,eAAW,UAAU,KAAK,SAAS;AACjC,aAAO,OAAO,YAAY,YAAY,MAAM,CAAC;IAC9C;AACD,WAAO;;EAGT,IAAW,WAAQ;AACjB,WAAO,KAAK,aAAa,KAAK,OAAO,UAAS;;;;;EAMhD,IAAW,QAAK;AACd,WAAO,KAAK,QAAQ,OAAO,CAAC,MAAc,YAAY,WAAW,CAAC,CAAC,EAAE;;;;;EAMhE,KAAK,QAAc;AACxB,SAAK,QAAQ,KAAK,MAAM;;;;;EAMnB,SAAM;AACX,QAAI,KAAK,QAAQ;AACf,kBAAY,OAAO,KAAK,QAAQ,IAAI;AACpC,WAAK,SAAS;IACf;AACD,SAAK,QAAQ,SAAS;;AAEzB;ACrDM,IAAM,gCAAgC,CAC3C,KACA,qBACA,SACA,0BACY;AACZ,QAAM,oBAAoB,6BACxB,IAAI,UAAS,GACb,qBACA,qBAAqB;AAEvB,SAAO,QAAQ,OAAO,CAAC,WACrB,kBAAkB,SAAS,YAAY,YAAY,MAAM,CAAC,CAAC;AAE/D;AAKa,IAAA,+BAA+B,CAC1C,QACA,YACA,cAC4B;AAC5B,QAAM,EAAE,WAAW,UAAS,IAAK,0BAC/B,QACA,UAAU;AAEZ,QAAM,sBAAsB,kBAC1B,EAAE,WAAW,UAAS,GACtB,SAAS;AAEX,SAAO,0BAA0B,qBAAqB,UAAU;AAClE;AAKa,IAAA,oBAAoB,CAC/B,QACA,YACA,WACoC;AACpC,QAAM,WAAW,6BAA6B,QAAQ,YAAY,MAAM;AACxE,QAAM,KAAK,SAAS,aAAY;AAChC,QAAM,KAAK,SAAS,aAAY;AAEhC,SAAO,CAAC,GAAG,IAAG,GAAI,GAAG,IAAG,GAAI,GAAG,IAAG,GAAI,GAAG,IAAG,CAAE;AAChD;IAOa,wBAAwB,CACnC,IACA,OACU;AACV,QAAM,IAAI;AACV,QAAM,QAAS,GAAG,MAAM,GAAG,OAAO,KAAK,KAAM;AAC7C,QAAM,QAAS,GAAG,MAAM,GAAG,OAAO,KAAK,KAAM;AAC7C,QAAM,UAAU,KAAK,IAAI,OAAO,CAAC;AACjC,QAAM,UAAU,KAAK,IAAI,OAAO,CAAC;AACjC,QAAM,IACJ,UAAU,UACV,KAAK,IAAK,GAAG,MAAM,KAAK,KAAM,GAAG,IAC/B,KAAK,IAAK,GAAG,MAAM,KAAK,KAAM,GAAG,IACjC,UACA;AACJ,QAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AACvD,SAAO,IAAI;AACb;AAYA,IAAM,4BAA4B,CAChC,QACA,eACe;AACf,SAAO;IACL,WAAW,WAAW,qBAAqB,OAAO,aAAY,CAAE;IAChE,WAAW,WAAW,qBAAqB,OAAO,aAAY,CAAE;;AAEpE;AAOO,IAAM,oBAAoB,CAC/B,EAAE,WAAW,UAAS,GACtB,cACe;AACf,YAAU,KAAK;AACf,YAAU,KAAK;AAEf,YAAU,KAAK;AACf,YAAU,KAAK;AAEf,SAAO,EAAE,WAAW,UAAS;AAC/B;AAKO,IAAM,4BAA4B,CACvC,EAAE,WAAW,UAAS,GACtB,eAC4B;AAC5B,QAAM,KAAK,WAAW,qBAAqB,SAAS;AACpD,QAAM,KAAK,WAAW,qBAAqB,SAAS;AACpD,SAAO,IAAI,OAAO,KAAK,aAAa,IAAI,EAAE;AAC5C;ICnFsB,0BAAiB;EAGrC,YAAY,EAAE,UAAU,GAAE,GAAoB;AAC5C,SAAK,UAAU;;;;;;;;;;;;;;EAcP,KAAgD,EACxD,QAAO,GACL;AACF,WAAO,KAAK,OAAO;;AActB;AAmBK,IAAgB,4BAAhB,cAAkD,kBAAiB;EAGvE,YAAY,IAA8D;QAA9D,EAAE,kBAAkB,GAAE,IAAA,IAAK,UAAO,OAAA,IAAlC,CAAA,iBAAA,CAAoC;AAC9C,UAAM,OAAO;AAHL,SAAe,kBAAG;AAI1B,SAAK,kBAAkB;;EAElB,UAAU,EACf,SACA,KACA,oBAAmB,GACJ;AACf,QAAI,IAAI,QAAO,KAAM,KAAK,SAAS;AACjC,aAAO;QACL,UAAU,KAAK,KAAK;UAClB;SACD;QACD,SAAS;;IAEZ;AAED,WAAO;MACL,UAAU,KAAK,QAAQ;QACrB,SAAS,8BACP,KACA,qBACA,SACA,KAAK,eAAe;QAEtB;QACA;OACD;;;AAIN;AAKY,IAAA,OAAO,CAAC,YAAgC;AACnD,QAAM,WAAW,QAAQ,IACvB,CAAC,WACC,IAAI,QAAQ;IACV,UAAU,YAAY,YAAY,MAAM;IACxC,SAAS,CAAC,MAAM;EACjB,CAAA,CAAC;AAEN,SAAO;AACT;ACzHM,IAAO,gBAAP,cAA6B,0BAAyB;EAM1D,YAAY,IAA+D;AAA/D,QAAA,EAAE,cAAc,KAAO,WAAW,GAAE,IAAA,IAAK,UAAzC,OAAA,IAAA,CAAA,eAAA,UAAA,CAAkD;AAC5D,UAAM,OAAO;AAJL,SAAQ,WAAc,CAAA;AACtB,SAAA,QAAQ,EAAE,MAAM,GAAE;AAK1B,SAAK,cAAc;AACnB,SAAK,WAAW;;EAGX,UAAU,EACf,SACA,KACA,oBAAmB,GACJ;AACf,UAAM,QAAQ,EAAE,MAAM,IAAI,QAAO,EAAE;AACnC,QAAI,UAAU;AACd,QAAI,KAAK,MAAM,QAAQ,KAAK,WAAW,MAAM,QAAQ,KAAK,QAAS;SAE5D;AACL,gBAAU,KAAC,uBAAAC,SAAM,KAAK,OAAO,KAAK;IACnC;AACD,SAAK,QAAQ;AACb,QAAI,IAAI,QAAO,KAAM,KAAK,SAAS;AACjC,aAAO;QACL,UAAU,KAAK,KAAK;UAClB;SACD;QACD;;IAEH;AAED,WAAO;MACL,UAAU,KAAK,QAAQ;QACrB,SAAS,8BACP,KACA,qBACA,SACA,KAAK,eAAe;QAEtB;QACA;OACD;;;EAIK,QAAQ,EAChB,SACA,KACA,oBAAmB,GACJ;AACf,SAAK,WAAW,CAAA;AAChB,YAAQ,QAAQ,CAAC,WAAU;AACzB,WAAK,oBAAoB,QAAQ,KAAK,mBAAmB;IAC3D,CAAC;AAED,WAAO,KAAK;;EAGJ,oBACR,QACA,KACA,YAA2C;AAE3C,QAAI,cAAc,KAAK;AACvB,QAAI,UAAmB;AAEvB,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,YAAY,KAAK,SAAS,CAAC;AACjC,YAAM,WAAW,sBACf,UAAU,OAAO,UAAS,EAAG,OAAM,GACnC,YAAY,YAAY,MAAM,EAAE,OAAM,CAAE;AAG1C,UAAI,WAAW,aAAa;AAC1B,sBAAc;AACd,kBAAU;MACX;IACF;AAED,QACE,WACA,6BACE,QAAQ,QACR,YACA,KAAK,QAAQ,EACb,SAAS,YAAY,YAAY,MAAM,CAAC,GAC1C;AACA,cAAQ,KAAK,MAAM;IACpB,OAAM;AACL,YAAMC,WAAU,IAAI,QAAQ,EAAE,SAAS,CAAC,MAAM,EAAC,CAAE;AACjD,WAAK,SAAS,KAAKA,QAAO;IAC3B;;AAEJ;ACrHK,IAAO,gBAAP,cAA6B,kBAAiB;EAClD,YAAY,IAAgC;QAA3B,UAAO,OAAA,IAAZ,CAAA,CAAc;AACxB,UAAM,OAAO;;EAER,UAAU,EACf,SACA,KACA,oBAAmB,GACJ;AACf,WAAO;MACL,UAAU,KAAK,QAAQ,EAAE,SAAS,KAAK,oBAAmB,CAAE;MAC5D,SAAS;;;EAIH,QAAQ,OAAqB;AACrC,WAAO,KAAK,KAAK,KAAK;;AAEzB;ACZK,IAAO,wBAAP,cAAqC,kBAAiB;EAM1D,YAAY,IAAyD;QAAzD,EAAE,SAAS,SAAS,GAAE,IAAA,IAAK,UAA3B,OAAA,IAAA,CAAA,WAAA,QAAA,CAAoC;AAC9C,UAAM,EAAE,QAAO,CAAE;AAHT,SAAA,QAAQ,EAAE,MAAM,GAAE;AAK1B,SAAK,eAAe,IAAI,aAAY,OAAA,OAAA,EAClC,SAAS,KAAK,SACd,OAAM,GACH,OAAO,CAAA;;EAIP,UAAU,OAAqB;AACpC,QAAI,UAAU;AACd,UAAM,QAAQ,EAAE,MAAM,MAAM,IAAI,QAAO,EAAE;AAEzC,QAAI,KAAC,uBAAAD,SAAM,MAAM,SAAS,KAAK,OAAO,GAAG;AACvC,gBAAU;AAEV,WAAK,UAAU,CAAC,GAAG,MAAM,OAAO;AAEhC,YAAM,SAAS,KAAK,QAAQ,IAAI,CAAC,WAAU;AACzC,cAAM,WAAW,YAAY,YAAY,MAAM;AAC/C,cAAM,cAAc,CAAC,SAAS,IAAG,GAAI,SAAS,IAAG,CAAE;AACnD,eAAO;UACL,MAAM;UACN,UAAU;YACR,MAAM;YACN;UACD;UACD,YAAY,EAAE,OAAM;;MAExB,CAAC;AACD,WAAK,aAAa,KAAK,MAAM;IAC9B;AAED,QAAI,CAAC,SAAS;AACZ,UAAI,KAAK,MAAM,QAAQ,KAAK,WAAW,MAAM,QAAQ,KAAK,SAAS;AACjE,kBAAU,KAAC,uBAAAA,SAAM,KAAK,OAAO,KAAK;MACnC;IACF;AAED,SAAK,QAAQ;AAEb,QAAI,SAAS;AACX,WAAK,WAAW,KAAK,QAAQ,KAAK;IACnC;AAED,WAAO,EAAE,UAAU,KAAK,UAAU,QAAO;;EAGpC,QAAQ,EAAE,IAAG,GAAkB;AACpC,WAAO,KAAK,aACT,YAAY,CAAC,MAAM,KAAK,KAAK,EAAE,GAAG,KAAK,MAAM,IAAI,QAAO,CAAE,CAAC,EAC3D,IAAI,CAAC,YACJ,KAAK,iBAAiB,OAAO,CAAC;;EAI1B,iBAAiB,EACzB,UAAU,EACR,aAAa,CAAC,KAAK,GAAG,EAAC,GAEzB,WAAU,GACyB;AACnC,QAAI,WAAW,SAAS;AACtB,aAAO,IAAI,QAAQ;QACjB,SAAS,KAAK,aACX,UAAU,WAAW,YAAY,QAAQ,EACzC,IAAI,CAAC,SAAS,KAAK,WAAW,MAAM;QACvC,UAAU,EAAE,KAAK,IAAG;MACrB,CAAA;IACF;AAED,UAAM,SAAS,WAAW;AAE1B,WAAO,IAAI,QAAQ;MACjB,SAAS,CAAC,MAAM;MAChB,UAAU,YAAY,YAAY,MAAM;IACzC,CAAA;;AAEJ;ACzEK,IAAO,gCAAP,cAA6C,0BAAyB;EAM1E,YAAY,IAKkB;AALlB,QAAA,EACV,SACA,SAAS,IACT,kBAAkB,GAAE,IAEQ,IADzB,UAAO,OAAA,IAJA,CAAA,WAAA,UAAA,iBAAA,CAKX;AACC,UAAM,EAAE,SAAS,gBAAe,CAAE;AAElC,SAAK,eAAe,IAAI,aAAY,OAAA,OAAA,EAClC,SAAS,KAAK,SACd,OAAM,GACH,OAAO,CAAA;AAGZ,SAAK,QAAQ,EAAE,MAAM,IAAI,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,EAAC;;EAGtC,UAAU,OAAqB;AACpC,UAAM,QAAmC;MACvC,MAAM,KAAK,MAAM,MAAM,IAAI,QAAO,CAAE;MACpC,MAAM,kBACJ,MAAM,IAAI,UAAS,GACnB,MAAM,qBACN,KAAK,eAAe;;AAIxB,QAAI,UAAU,KAAC,uBAAAA,SAAM,KAAK,OAAO,KAAK;AACtC,QAAI,KAAC,uBAAAA,SAAM,MAAM,SAAS,KAAK,OAAO,GAAG;AACvC,gBAAU;AAEV,WAAK,UAAU,CAAC,GAAG,MAAM,OAAO;AAEhC,YAAM,SAAS,KAAK,QAAQ,IAAI,CAAC,WAAU;AACzC,cAAM,WAAW,YAAY,YAAY,MAAM;AAC/C,cAAM,cAAc,CAAC,SAAS,IAAG,GAAI,SAAS,IAAG,CAAE;AACnD,eAAO;UACL,MAAM;UACN,UAAU;YACR,MAAM;YACN;UACD;UACD,YAAY,EAAE,OAAM;;MAExB,CAAC;AACD,WAAK,aAAa,KAAK,MAAM;IAC9B;AAED,QAAI,SAAS;AACX,WAAK,WAAW,KAAK,QAAQ,KAAK;AAClC,WAAK,QAAQ;IACd;AAED,WAAO,EAAE,UAAU,KAAK,UAAU,QAAO;;EAGpC,QAAQ,EAAE,KAAK,oBAAmB,GAAkB;AAEzD,UAAM,QAAmC;MACvC,MAAM,KAAK,MAAM,IAAI,QAAO,CAAE;MAC9B,MAAM,kBACJ,IAAI,UAAS,GACb,qBACA,KAAK,eAAe;;AAIxB,WAAO,KAAK,aACT,YAAY,MAAM,MAAM,MAAM,IAAI,EAClC,IAAI,CAAC,YACJ,KAAK,iBAAiB,OAAO,CAAC;;EAI1B,iBAAiB,EACzB,UAAU,EACR,aAAa,CAAC,KAAK,GAAG,EAAC,GAEzB,WAAU,GACyB;AACnC,QAAI,WAAW,SAAS;AACtB,aAAO,IAAI,QAAQ;QACjB,SAAS,KAAK,aACX,UAAU,WAAW,YAAY,QAAQ,EACzC,IAAI,CAAC,SAAS,KAAK,WAAW,MAAM;QACvC,UAAU,EAAE,KAAK,IAAG;MACrB,CAAA;IACF;AAED,UAAM,SAAS,WAAW;AAE1B,WAAO,IAAI,QAAQ;MACjB,SAAS,CAAC,MAAM;MAChB,UAAU,YAAY,YAAY,MAAM;IACzC,CAAA;;AAEJ;IC9HY,qBAAY;EAYvB,YAAY,SAAmB,UAAmB;AAChD,SAAK,UAAU,EAAE,KAAK,QAAQ,OAAM;AACpC,UAAM,sBAAsB,SAAS,IAAI,CAAC,MAAM,EAAE,KAAK;AACvD,UAAM,mBAAmB,oBAAoB,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC;AAEtE,SAAK,WAAW;MACd,OAAO,SAAS;MAChB,SAAS;QACP,MAAM,mBAAmB,SAAS;QAClC,KAAK;QACL,KAAK,KAAK,IAAI,GAAG,mBAAmB;QACpC,KAAK,KAAK,IAAI,GAAG,mBAAmB;MACrC;;;AAGN;IAkBY,wBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAuCnB,OACL,EAAE,OAAO,SAAQ,GACjB,OACA,KAAoB;AAGpB,UAAM,QACJ,QAAQ,KAAK,IAAI,IAAI,MAAM,SAAS,QAAQ,IAAI,IAAI,YAAY;AAGlE,UAAM,MAAM,cAAc,KAAK;;;;+IAI4G,KAAK;;AAGhJ,UAAM,QAAQ,cAAc,KAAK,YAE/B,SAAiB,OAAO,OAAO,KAAK,OAAO,UAAU,IAAI;AAE3D,QAAI,YAAY,0BAA0B,GAAG,GAAG;AAE9C,YAAM,SAAS,IAAI,UAAS;AAC5B,YAAM,QAAQ,OAAO,gBACnB,KACA,eAAe,EACf;AACF,YAAM,aAAa,aAAa,iBAAiB;AAEjD,YAAME,kBAAkE;QACtE;QACA;QACA;QACA;QACA,SAAS;;AAEX,aAAO,IAAI,OAAO,KAAK,OAAO,sBAAsBA,eAAc;IACnE;AAED,UAAM,iBAA4C;MAChD;MACA;MACA;MACA,MAAM;QACJ,KAAK,6BAA6B,KAAK,GAAG,CAAC;QAC3C,QAAQ,IAAI,OAAO,KAAK,MAAM,IAAI,EAAE;MACrC;;AAEH,WAAO,IAAI,OAAO,KAAK,OAAO,cAAc;;AAE/C;AClID,SAAS,OAAO,OAAY,OAAU;AAGpC,WAAS,YAAY,MAAM,WAAW;AACpC,UAAM,UAAU,QAAQ,IAAI,MAAM,UAAU,QAAQ;EACrD;AACH;IAKa,wBAAA,iBAAe;EAC1B,cAAA;AAME,WAAO,kBAAiB,OAAO,KAAK,WAAW;;AAElD;ICEW;CAAZ,SAAYC,wBAAqB;AAC/B,EAAAA,uBAAA,kBAAA,IAAA;AACA,EAAAA,uBAAA,gBAAA,IAAA;AACA,EAAAA,uBAAA,eAAA,IAAA;AACF,GAJY,0BAAA,wBAIX,CAAA,EAAA;AAEY,IAAA,+BAAsD,CACjE,GACA,SACA,QACQ;AACR,MAAI,UAAU,QAAQ,MAAM;AAC9B;AAMM,IAAO,kBAAP,cAA+B,gBAAe;EAalD,YAAY,EACV,KACA,UAAU,CAAA,GACV,mBAAmB,CAAA,GACnB,YAAY,IAAI,sBAAsB,gBAAgB,GACtD,WAAW,IAAI,gBAAe,GAC9B,iBAAiB,6BAA4B,GACtB;AACvB,UAAK;AACL,SAAK,UAAU,CAAC,GAAG,OAAO;AAC1B,SAAK,WAAW,CAAA;AAEhB,SAAK,YAAY;AACjB,SAAK,WAAW;AAEhB,SAAK,iBAAiB;AAEtB,QAAI,KAAK;AACP,WAAK,OAAO,GAAG;IAChB;;EAGI,UAAU,QAAgB,QAAgB;AAC/C,QAAI,KAAK,QAAQ,SAAS,MAAM,GAAG;AACjC;IACD;AAED,SAAK,QAAQ,KAAK,MAAM;AACxB,QAAI,CAAC,QAAQ;AACX,WAAK,OAAM;IACZ;;EAGI,WAAW,SAAmB,QAAgB;AACnD,YAAQ,QAAQ,CAAC,WAAU;AACzB,WAAK,UAAU,QAAQ,IAAI;IAC7B,CAAC;AAED,QAAI,CAAC,QAAQ;AACX,WAAK,OAAM;IACZ;;EAGI,aAAa,QAAgB,QAAgB;AAClD,UAAM,QAAQ,KAAK,QAAQ,QAAQ,MAAM;AAEzC,QAAI,UAAU,IAAI;AAEhB,aAAO;IACR;AAED,gBAAY,OAAO,QAAQ,IAAI;AAC/B,SAAK,QAAQ,OAAO,OAAO,CAAC;AAE5B,QAAI,CAAC,QAAQ;AACX,WAAK,OAAM;IACZ;AAED,WAAO;;EAGF,cAAc,SAAmB,QAAgB;AACtD,QAAI,UAAU;AAEd,YAAQ,QAAQ,CAAC,WAAU;AACzB,gBAAU,KAAK,aAAa,QAAQ,IAAI,KAAK;IAC/C,CAAC;AAED,QAAI,WAAW,CAAC,QAAQ;AACtB,WAAK,OAAM;IACZ;AAED,WAAO;;EAGF,aAAa,QAAgB;AAClC,SAAK,QAAQ,SAAS;AAEtB,QAAI,CAAC,QAAQ;AACX,WAAK,OAAM;IACZ;;;;;EAMI,SAAM;AACX,UAAM,MAAM,KAAK,OAAM;AACvB,QAAI,eAAe,OAAO,KAAK,OAAO,IAAI,cAAa,GAAI;AACzD,aAAO,KAAK,MAAM,QAChB,MACA,sBAAsB,kBACtB,IAAI;AAEN,YAAM,EAAE,UAAU,QAAO,IAAK,KAAK,UAAU,UAAU;QACrD,SAAS,KAAK;QACd;QACA,qBAAqB,KAAK,cAAa;MACxC,CAAA;AAGD,UAAI,WAAW,WAAW,QAAW;AAInC,cAAM,eAAe,oBAAI,IAAG;AAC5B,mBAAW,WAAW,UAAU;AAC9B,cAAI,QAAQ,QAAQ,UAAU,GAAG;AAC/B,yBAAa,IAAI,QAAQ,QAAQ,CAAC,CAAC;UACpC;QACF;AAED,cAAM,eAAyB,CAAA;AAE/B,mBAAW,WAAW,KAAK,UAAU;AACnC,cAAI,QAAQ,UAAU,MAAM;AAC1B;UACD;AACD,cAAI,QAAQ,QAAQ,UAAU,GAAG;AAC/B,gBAAI,CAAC,aAAa,IAAI,QAAQ,MAAM,GAAG;AAIrC,0BAAY,OAAO,QAAQ,QAAQ,IAAI;YACxC;UACF,OAAM;AAEL,yBAAa,KAAK,QAAQ,MAAM;UACjC;QACF;AAED,aAAK,WAAW;AAChB,aAAK,eAAc;AAGnB,8BAAsB,MACpB,aAAa,QAAQ,CAAC,WAAW,YAAY,OAAO,QAAQ,IAAI,CAAC,CAAC;MAErE;AACD,aAAO,KAAK,MAAM,QAChB,MACA,sBAAsB,gBACtB,IAAI;IAEP;;EAGI,QAAK;AACV,SAAK,eAAe,KAAK,OAAM,EAAG,YAChC,QACA,KAAK,OAAO,KAAK,IAAI,CAAC;AAExB,SAAK,OAAM;;EAGN,WAAQ;AACb,WAAO,KAAK,MAAM,eAAe,KAAK,YAAY;AAClD,SAAK,MAAK;;EAGF,QAAK;AACb,SAAK,QAAQ,QAAQ,CAAC,WAAW,YAAY,OAAO,QAAQ,IAAI,CAAC;AACjE,SAAK,SAAS,QAAQ,CAAC,YAAY,QAAQ,OAAM,CAAE;AACnD,SAAK,WAAW,CAAA;;EAGR,iBAAc;AAEtB,UAAM,QAAQ,IAAI,aAAa,KAAK,SAAS,KAAK,QAAQ;AAC1D,UAAM,MAAM,KAAK,OAAM;AAEvB,SAAK,SAAS,QAAQ,CAAC,YAAW;AAChC,UAAI,QAAQ,QAAQ,WAAW,GAAG;AAChC,gBAAQ,SAAS,QAAQ,QAAQ,CAAC;MACnC,OAAM;AAEL,gBAAQ,SAAS,KAAK,SAAS,OAAO,SAAS,OAAO,GAAG;AAEzD,gBAAQ,QAAQ,QAAQ,CAAC,WAAW,YAAY,OAAO,QAAQ,IAAI,CAAC;AACpE,YAAI,KAAK,gBAAgB;AACvB,kBAAQ,OAAO;YACb;;YAEA,CAAC,UAAoC;AACnC,qBAAO,KAAK,MAAM,QAChB,MACA,sBAAsB,eACtB,OAAO;AAET,mBAAK,eAAe,OAAO,SAAS,GAAG;YACzC;UAAC;QAEJ;MACF;AACD,kBAAY,OAAO,QAAQ,QAAQ,GAAG;IACxC,CAAC;;AAEJ;", "names": ["x", "y", "equal", "cluster", "clusterOptions", "MarkerClustererEvents"]}