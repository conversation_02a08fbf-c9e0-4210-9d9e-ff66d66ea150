import { useContext } from "react";
import { AppContext } from "../contexts/AppContext";

/**
 *
 * @typedef {object} ScreenSizeType
 * @property {boolean} xs
 * @property {boolean} sm
 * @property {boolean} md
 * @property {boolean} lg
 */

/**
 * Hook to use the app context.
 *
 * @typedef {object} obj
 * @property {boolean} isMobile - Check if screen is mobile
 * @property {ScreenSizeType} screenSize - Check current screen size
 * @property {Object} google - The Google Maps object (available after the API is loaded).
 * @property {number} deviceHeight - The height of the device's window.
 * @property {Object} selectedVessel - The currently selected vessel (can be null or undefined).
 * @property {string} devMode - Developer Mode
 * @property {Function} setDevMode - Change Developer Mode
 * @property {string} timezone - The timezone of the selected stream
 * @property {Function} setTimezone - Change timezone
 * @return {obj} An object containing app related functions.
 */
export const useApp = () => {
    const context = useContext(AppContext);
    if (context === undefined) {
        throw new Error("useApp must be used within AppProvider");
    }
    return context;
};
