import axios from 'axios';
import environment from '../environment';

// Create a global event emitter or use a method to trigger navigation
let onSessionExpiry = () => {};
let onError = () => {};
let onSuccess = () => {};

const axiosInstance = axios.create({
    baseURL: environment.VITE_API_URL + '/api',
    withCredentials: true,
});

axiosInstance.interceptors.request.use(
    (config) => {
        if (!config.headers['Authorization']) {
            const token = localStorage.getItem('jwt_token');
            if (token) {
                config.headers['Authorization'] = `Bearer ${token}`;
            }
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

axiosInstance.interceptors.response.use(
    (response) => {
        const { config } = response
        if (config.meta?.showSnackbar) {
            onSuccess && onSuccess(response?.data?.message || `Success`, { variant: 'success' })
        }
        return response
    },
    async (error) => {
        const { config, response } = error

        // If RateLimit-Reset header is present and the status code is 429 (Too Many Requests)
        if (response?.status === 429 && response?.headers.get('RateLimit-Reset')) {
            console.warn('rate-limit detected')
            const retryAfter = parseInt(response.headers.get('RateLimit-Reset'), 10); // in seconds

            // Delay the retry according to the RateLimit-Reset header value
            await new Promise(resolve => setTimeout(resolve, retryAfter * 1000)); // Convert seconds to milliseconds

            // Retry the original request
            return axiosInstance(config);
        }

        if (config.meta?.showSnackbar !== false) {
            let errorMessage = 'Unexpected error occurred';

            if (error.code === "ERR_NETWORK") {
                errorMessage = 'Network Error. Please check your internet and try again';
            } else if (response) {
                // Handle blob responses (when responseType is 'blob')
                if (response.data instanceof Blob && response.data.type === 'application/json') {
                    try {
                        const text = await response.data.text();
                        const jsonData = JSON.parse(text);
                        errorMessage = jsonData.message || `Unexpected error occurred (Code ${response.status})`;
                    } catch (e) {
                        errorMessage = `Unexpected error occurred (Code ${response.status})`;
                    }
                } else if (response.data?.message) {
                    errorMessage = response.data.message;
                } else {
                    errorMessage = `Unexpected error occurred (Code ${response.status})`;
                }
            }

            onError && onError(errorMessage, { variant: 'error' });
        }

        if (response && response.status === 401) {
            onSessionExpiry && onSessionExpiry()
        }

        return Promise.reject(error);
    }
);

// Method to register the unauthorized callback
export const registerSessionExpiryCallback = (callback) => {
    onSessionExpiry = callback;
};

export const registerErrorCallback = (callback) => {
    onError = callback;
};

export const registerSuccessCallback = (callback) => {
    onSuccess = callback;
};

export default axiosInstance;