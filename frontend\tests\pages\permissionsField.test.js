import { render, screen, fireEvent } from '@testing-library/react';
import PermissionsField from '../../src/pages/Dashboard/User/Roles/PermissionsField';
import { useUser } from '../../src/hooks/UserHook';
import { Autocomplete, TextField } from '@mui/material';

jest.mock('../../src/hooks/UserHook', () => ({
    useUser: jest.fn(),
}));

describe('PermissionsField Component', () => {
    let setRolesMock, setAllowResetMock, setAllowSaveMock, setUpdatedRolesMock, setRowHeightMock;

    beforeEach(() => {
        setRolesMock = jest.fn();
        setAllowResetMock = jest.fn();
        setAllowSaveMock = jest.fn();
        setUpdatedRolesMock = jest.fn();
        setRowHeightMock = jest.fn();

        useUser.mockReturnValue({
            user: {
                role_id: '1',
                hierarchy_number: 1,
                role: {
                    role_id: '1',
                    hierarchy_number: 1,
                },
            },
        });
    });

    it('should render correctly with the given permissions and role', () => {
        const permissions = [{ _id: 'p1', permission_name: 'Read', assignable: true }];
        const role = { _id: 'r1', role_name: 'Admin', denied_permissions: [] };

        render(
            <PermissionsField
                role={role}
                permissions={permissions}
                setRoles={setRolesMock}
                setAllowReset={setAllowResetMock}
                setAllowSave={setAllowSaveMock}
                setUpdatedRoles={setUpdatedRolesMock}
                setRowHeight={setRowHeightMock}
            />
        );

        expect(screen.getByRole('combobox')).toBeInTheDocument();
        expect(screen.getByText('Read')).toBeInTheDocument();
    });

    it('should disable Autocomplete if role is not editable or the user cannot update the role', () => {
        const permissions = [{ _id: 'p1', permission_name: 'Read', assignable: true }];
        const role = { _id: 'r1', role_name: 'Admin', denied_permissions: [], editable: false };

        render(
            <PermissionsField
                role={role}
                permissions={permissions}
                setRoles={setRolesMock}
                setAllowReset={setAllowResetMock}
                setAllowSave={setAllowSaveMock}
                setUpdatedRoles={setUpdatedRolesMock}
                setRowHeight={setRowHeightMock}
            />
        );

        const autocomplete = screen.getByRole('combobox');
        expect(autocomplete).toBeDisabled();
    });

    it('should update row height when permissions change', () => {
        const permissions = [{ _id: 'p1', permission_name: 'Read', assignable: true }];
        const role = { _id: 'r1', role_name: 'Admin', denied_permissions: [] };

        render(
            <PermissionsField
                role={role}
                permissions={permissions}
                setRoles={setRolesMock}
                setAllowReset={setAllowResetMock}
                setAllowSave={setAllowSaveMock}
                setUpdatedRoles={setUpdatedRolesMock}
                setRowHeight={setRowHeightMock}
            />
        );

        expect(setRowHeightMock).toHaveBeenCalledWith(role._id, expect.any(Number));
    });

    it('should not allow updates if role hierarchy_number is not equal to user role_number', () => {
        const permissions = [
            { _id: 'p1', permission_name: 'Read', assignable: true },
        ];
        const role = { _id: 'r1', role_name: 'Admin', denied_permissions: [], hierarchy_number: 2 };

        render(
            <PermissionsField
                role={role}
                permissions={permissions}
                setRoles={setRolesMock}
                setAllowReset={setAllowResetMock}
                setAllowSave={setAllowSaveMock}
                setUpdatedRoles={setUpdatedRolesMock}
                setRowHeight={setRowHeightMock}
            />
        );

        const autocomplete = screen.getByRole('combobox');
        expect(autocomplete).toBeDisabled();
    });

    it('should not allow updates if role hierarchy_number is equal to user role_number', () => {
        const permissions = [
            { _id: 'p1', permission_name: 'Read', assignable: true },
        ];
        const role = { _id: 'r1', role_name: 'Admin', denied_permissions: [], hierarchy_number: 2, role_id: '1', editable: true };

        render(
            <PermissionsField
                role={role}
                permissions={permissions}
                setRoles={setRolesMock}
                setAllowReset={setAllowResetMock}
                setAllowSave={setAllowSaveMock}
                setUpdatedRoles={setUpdatedRolesMock}
                setRowHeight={setRowHeightMock}
            />
        );

        const autocomplete = screen.getByRole('combobox');
        expect(autocomplete).toBeDisabled();
    });

    it('should allow updates if role hierarchy_number is greater than or equal to user role_number and editable to true', () => {
        const permissions = [
            { _id: 'p1', permission_name: 'Read', assignable: true },
        ];
        const role = { _id: 'r1', role_name: 'Admin', denied_permissions: [], hierarchy_number: 2, role_id: '2', editable: true };

        render(
            <PermissionsField
                role={role}
                permissions={permissions}
                setRoles={setRolesMock}
                setAllowReset={setAllowResetMock}
                setAllowSave={setAllowSaveMock}
                setUpdatedRoles={setUpdatedRolesMock}
                setRowHeight={setRowHeightMock}
            />
        );

        const autocomplete = screen.getByRole('combobox');
        expect(autocomplete).not.toBeDisabled();
    });

    it('should not allow updates if role hierarchy_number is equal to user role_number and set editable to true', () => {
        const permissions = [
            { _id: 'p1', permission_name: 'Read', assignable: true },
        ];
        const role = { _id: 'r1', role_name: 'Admin', denied_permissions: [], hierarchy_number: 1, role_id: '2', editable: true };

        render(
            <PermissionsField
                role={role}
                permissions={permissions}
                setRoles={setRolesMock}
                setAllowReset={setAllowResetMock}
                setAllowSave={setAllowSaveMock}
                setUpdatedRoles={setUpdatedRolesMock}
                setRowHeight={setRowHeightMock}
            />
        );

        const autocomplete = screen.getByRole('combobox');
        expect(autocomplete).toBeDisabled();
    });
});
