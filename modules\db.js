const mongoose = require("mongoose");

mongoose.set("strictQuery", false);

const db = {
    qm: mongoose.createConnection(process.env.MONGO_URI, { dbName: "quartermaster" }),
    qmai: mongoose.createConnection(process.env.MONGO_URI, { dbName: "artifact_processor" }),
    qmShared: mongoose.createConnection(process.env.MONGO_URI, { dbName: "quartermaster-shared" }),
    locations: mongoose.createConnection(process.env.MONGO_URI, { dbName: "locations" }),
    ais: mongoose.createConnection(process.env.MONGO_URI, { dbName: "ais" }),
    audio: mongoose.createConnection(process.env.MONGO_URI, { dbName: "audio_processor" }),
};

db.qm.on("open", () => console.log("DB connected to Quartermaster"));
db.qmai.on("open", () => console.log("DB connected to QMAI"));
db.qmShared.on("open", () => console.log("DB connected to Quartermaster-Shared"));
db.locations.on("open", () => console.log("DB connected to Locations"));
db.ais.on("open", () => console.log("DB connected to AIS"));
db.audio.on("open", () => console.log("DB connected to QMAudio"));

db.qm.on("error", (err) => console.error(err));
db.qmai.on("error", (err) => console.error(err));
db.qmShared.on("error", (err) => console.error(err));
db.locations.on("error", (err) => console.error(err));
db.ais.on("error", (err) => console.error(err));
db.audio.on("error", (err) => console.error(err));

module.exports = db;
