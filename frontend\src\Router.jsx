import { Navigate, Route, RouterProvider, createBrowserRouter, createRoutesFromElements } from "react-router-dom";
import { CssBaseline, Grid, ThemeProvider } from "@mui/material";
import theme from "./theme";
import { UserProvider } from "./providers/UserProvider.jsx";
import { lazy, Suspense } from "react";
import { AppProvider } from "./providers/AppProvider.jsx";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { SnackbarProvider } from "notistack";
import { useEffect, useRef, useState } from "react";
import { registerErrorCallback, registerSuccessCallback } from "./axios.js";
import { useToaster } from "./hooks/ToasterHook.jsx";
import { version } from "../package.json";
import idb from "./indexedDB.js";
import ErrorBoundary from "./components/ErrorBoundary";
import { VesselInfoProvider } from "./providers/VesselInfoProvider.jsx";
import { GroupRegionProvider } from "./providers/GroupRegionProvider.jsx";
import SplashScreen from "./components/SplashScreen.jsx";

// Lazy load components
const Login = lazy(() => import("./pages/Login/Login"));
const VideoStream = lazy(() => import("./pages/Dashboard/Stream/VideoStream"));
const DashboardLayout = lazy(() => import("./layouts/DashboardLayout"));
const ForgotPassword = lazy(() => import("./pages/ForgotPassword/ForgotPassword.jsx"));
const ResetPassword = lazy(() => import("./pages/ResetPassword/ResetPassword.jsx"));
const HomeLayout = lazy(() => import("./layouts/HomeLayout.jsx"));
const FullMap = lazy(() => import("./pages/Dashboard/Map/FullMap.jsx"));
const UserManagement = lazy(() => import("./pages/Dashboard/User/UserManagement.jsx"));
const LogManagement = lazy(() => import("./pages/Dashboard/LogsPage/LogManagement.jsx"));
const ApiKeyManagement = lazy(() => import("./pages/Dashboard/ApiKeys/ApiKeyManagement.jsx"));
const StatisticsManagement = lazy(() => import("./pages/Dashboard/Statistics/StatisticsManagement.jsx"));
const OTPInput = lazy(() => import("./pages/OTPInput/OTPInput.jsx"));
const Settings = lazy(() => import("./pages/Settings/Settings.jsx"));
const Signup = lazy(() => import("./pages/Login/Signup.jsx"));
const EventsManagement = lazy(() => import("./pages/Dashboard/Events/EventManagement.jsx"));
const NotificationManagement = lazy(() => import("./pages/Dashboard/Notification/NotificationManagement.jsx"));
const Subscription = lazy(() => import("./pages/Subscription/Subscription.jsx"));
// const RegionGroupManagement = lazy(() => import("./pages/Dashboard/VesselManagement/RegionGroup/RegionGroupManagement.js"));
const VesselManagement = lazy(() => import("./pages/Dashboard/VesselManagement/VesselManagement.jsx"));

const withErrorBoundary = (Component) => {
    return (
        <ErrorBoundary>
            <Component />
        </ErrorBoundary>
    );
};

export const DashboardRouteToggler = ({ path }) => {
    return (
        <Grid container display={"block"} width={"100%"} height={"100%"}>
            <Grid display={path === "stream" ? "block" : "none"} width={"100%"} height={"100%"}>
                {withErrorBoundary(VideoStream)}
            </Grid>
            <Grid display={path === "map" ? "block" : "none"} width={"100%"} height={"100%"}>
                {withErrorBoundary(FullMap)}
            </Grid>
            <Grid display={path === "users" ? "block" : "none"} width={"100%"} height={"100%"}>
                {withErrorBoundary(UserManagement)}
            </Grid>
            <Grid display={path === "logs" ? "block" : "none"} width={"100%"} height={"100%"}>
                {withErrorBoundary(LogManagement)}
            </Grid>
            {/* <Grid display={path === "region-groups" ? "block" : "none"} width={"100%"} height={"100%"}>
                {withErrorBoundary(RegionGroupManagement)}
            </Grid> */}
            <Grid display={path === "api-keys" ? "block" : "none"} width={"100%"} height={"100%"}>
                {withErrorBoundary(ApiKeyManagement)}
            </Grid>
            <Grid display={path === "statistics" ? "block" : "none"} width={"100%"} height={"100%"}>
                {withErrorBoundary(StatisticsManagement)}
            </Grid>
            <Grid display={path === "settings" ? "block" : "none"} width={"100%"} height={"100%"}>
                {withErrorBoundary(Settings)}
            </Grid>
            <Grid display={path === "events" ? "block" : "none"} width={"100%"} height={"100%"}>
                {withErrorBoundary(EventsManagement)}
            </Grid>
            <Grid display={path === "notification" ? "block" : "none"} width={"100%"} height={"100%"}>
                {withErrorBoundary(NotificationManagement)}
            </Grid>
            <Grid item display={path === "vessel-management" ? "block" : "none"} width={"100%"} height={"100%"}>
                {withErrorBoundary(VesselManagement)}
            </Grid>
        </Grid>
    );
};

const router = createBrowserRouter(
    createRoutesFromElements(
        <Route>
            <Route path="/" element={<HomeLayout />}>
                <Route index element={<Login />} />
                <Route path="/login" element={<Login />} />
                <Route path="/signup" element={<Signup />} />
                <Route path="/otp" element={<OTPInput />} />
                <Route path="/forgot-password" element={<ForgotPassword />} />
                <Route path="/reset-password/:token" element={<ResetPassword />} />
            </Route>
            <Route path="/subscription" element={<Subscription />} />
            <Route path="/dashboard" element={<DashboardLayout />} key={"test"}>
                <Route index element={<Navigate to={"stream"} />} />
                <Route path="stream" element={<DashboardRouteToggler path={"stream"} />} />
                <Route path="map" element={<DashboardRouteToggler path={"map"} />} />
                <Route path="users" element={<DashboardRouteToggler path={"users"} />} />
                <Route path="logs" element={<DashboardRouteToggler path={"logs"} />} />
                {/* <Route path="region-groups" element={<DashboardRouteToggler path={"region-groups"} />} /> */}
                <Route path="api-keys" element={<DashboardRouteToggler path={"api-keys"} />} />
                <Route path="statistics" element={<DashboardRouteToggler path={"statistics"} />} />
                <Route path="settings" element={<DashboardRouteToggler path={"settings"} />} />
                <Route path="events/:id?" element={<DashboardRouteToggler path={"events"} />} />
                <Route path="notification" element={<DashboardRouteToggler path={"notification"} />} />
                <Route path="vessel-management" element={<DashboardRouteToggler path={"vessel-management"} />} />
            </Route>
            <Route path="*" element={<Navigate to={"/"} />} />
        </Route>,
    ),
);

const RouterWrapper = () => {
    const toaster = useToaster();

    useEffect(() => {
        registerErrorCallback((message, options) => toaster(message, options));
        registerSuccessCallback((message, options) => toaster(message, options));
    }, []);

    return (
        <Suspense fallback={<SplashScreen />}>
            <RouterProvider router={router} />
        </Suspense>
    );
};

export const initializeApp = async () => {
    try {
        console.log("initializeApp");
        const savedVersion = localStorage.getItem("QMA_Version");
        if (!savedVersion || savedVersion !== version) {
            localStorage.setItem("QMA_Version", version);
            await idb.clearIndexedDB();
        }
        await idb.initDB();
    } catch (err) {
        console.error("Error checking version:", err);
    }
    return true;
};

export default function Router() {
    const [ready, setReady] = useState(false);
    const [showDelayMessage, setShowDelayMessage] = useState(false);

    useEffect(() => {
        initializeApp()
            .catch(console.error)
            .finally(() => setReady(true));
    }, []);

    const timeout = useRef();
    useEffect(() => {
        clearTimeout(timeout.current);
        if (ready) return;
        timeout.current = setTimeout(() => {
            setShowDelayMessage(true);
        }, 3000);
    }, [ready]);

    if (!ready && showDelayMessage)
        return (
            <div style={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100vh" }}>
                <p style={{ fontWeight: "bold", fontSize: 22 }}>If page does not load, please try closing any other tabs for the website</p>
            </div>
        );

    if (!ready) return null;

    return (
        <ThemeProvider theme={theme}>
            <CssBaseline />
            <AppProvider>
                <UserProvider>
                    <VesselInfoProvider>
                        <GroupRegionProvider>
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <SnackbarProvider maxSnack={3} disableWindowBlurListener autoHideDuration={3000}>
                                    <RouterWrapper />
                                </SnackbarProvider>
                            </LocalizationProvider>
                        </GroupRegionProvider>
                    </VesselInfoProvider>
                </UserProvider>
            </AppProvider>
        </ThemeProvider>
    );
}
