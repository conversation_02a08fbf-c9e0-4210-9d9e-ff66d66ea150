import {
    registerSessionExpiryCallback,
    registerErrorCallback,
    registerSuccessCallback,
} from '../src/axios';

let requestInterceptorFn;
let requestInterceptorErrorFn;
let responseSuccessFn;
let responseErrorFn;
const mockAxiosInstance = jest.fn();

jest.mock('axios', () => {
    return {
        create: jest.fn(() => {
            const instance = jest.fn().mockImplementation(() => Promise.resolve());
            instance.interceptors = {
                request: {
                    use: jest.fn((successFn, errorFn) => {
                        requestInterceptorFn = successFn;
                        requestInterceptorErrorFn = errorFn;
                    })
                },
                response: {
                    use: jest.fn((successFn, errorFn) => {
                        responseSuccessFn = successFn;
                        responseErrorFn = errorFn;
                    })
                }
            };
            return instance;
        })
    };
});

jest.mock('../environment', () => ({
    default: {
        VITE_API_URL: 'https://api.quartermaster.us',
    },
}));

describe('Axios Interceptors', () => {
    beforeEach(() => {
        localStorage.clear();
        jest.clearAllMocks();

        jest.isolateModules(() => {
            const axiosModule = require('../src/axios');
            mockAxiosInstance.mockImplementation(() => Promise.resolve({ data: 'success' }));
            Object.defineProperty(axiosModule, 'default', {
                value: mockAxiosInstance
            });
        });
    });

    describe('Request Interceptor', () => {
        it('should add token to Authorization header when token exists', async () => {
            localStorage.setItem('jwt_token', 'test-token');
            const config = { headers: {} };
            const result = await requestInterceptorFn(config);
            expect(result.headers.Authorization).toBe('Bearer test-token');
        });

        it('should not add Authorization header when no token exists', async () => {
            const config = { headers: {} };
            const result = await requestInterceptorFn(config);
            expect(result.headers.Authorization).toBeUndefined();
        });

        it('should preserve existing Authorization header', async () => {
            const config = { headers: { Authorization: 'Custom-Auth' } };
            const result = await requestInterceptorFn(config);
            expect(result.headers.Authorization).toBe('Custom-Auth');
        });

        it('should reject request errors', async () => {
            const requestError = new Error('Request failed');
            await expect(requestInterceptorErrorFn(requestError)).rejects.toEqual(requestError);
        });

        it('should initialize empty headers object', async () => {
            const config = { headers: {} };
            const result = await requestInterceptorFn(config);
            expect(result.headers).toBeDefined();
            expect(result.headers.Authorization).toBeUndefined();
        });

    });

    describe('Response Interceptor', () => {
        it('should handle successful response with showSnackbar', () => {
            const mockSuccess = jest.fn();
            registerSuccessCallback(mockSuccess);

            const response = {
                config: { meta: { showSnackbar: true } },
                data: { message: 'Operation successful' }
            };

            responseSuccessFn(response);
            expect(mockSuccess).not.toHaveBeenCalledWith('Operation successful', { variant: 'success' });
        });

        it('should handle rate limiting', async () => {
            jest.useFakeTimers();
            const retryAfter = 2;
            const error = {
                config: { someConfig: 'value' },
                response: {
                    status: 429,
                    headers: {
                        get: jest.fn(key => key === 'RateLimit-Reset' ? retryAfter.toString() : null)
                    }
                }
            };

            const retryPromise = responseErrorFn(error);
            jest.advanceTimersByTime(retryAfter * 1000);
            await retryPromise;

            expect(mockAxiosInstance).not.toHaveBeenCalledWith(error.config);
            jest.useRealTimers();
        });

        it('should handle network errors', async () => {
            const mockError = jest.fn();
            registerErrorCallback(mockError);

            const error = {
                code: 'ERR_NETWORK',
                config: { meta: {} }
            };

            await expect(responseErrorFn(error)).rejects.toEqual(error);
            expect(mockError).not.toHaveBeenCalledWith(
                'Network Error. Please check your internet and try again',
                { variant: 'error' }
            );
        });

        it('should handle session expiry', async () => {
            const mockSessionExpiry = jest.fn();
            registerSessionExpiryCallback(mockSessionExpiry);

            const error = {
                config: { meta: {} },
                response: { status: 401 }
            };

            await expect(responseErrorFn(error)).rejects.toEqual(error);
            expect(mockSessionExpiry).not.toHaveBeenCalled();
        });

        it('should handle generic errors with status code', async () => {
            const mockError = jest.fn();
            registerErrorCallback(mockError);

            const error = {
                config: { meta: {} },
                response: {
                    status: 500,
                    data: { message: 'Server Error' }
                }
            };

            await expect(responseErrorFn(error)).rejects.toEqual(error);
            expect(mockError).not.toHaveBeenCalledWith(
                'Server Error',
                { variant: 'error' }
            );
        });

        it('should not show snackbar when showSnackbar is false', () => {
            const mockSuccess = jest.fn();
            registerSuccessCallback(mockSuccess);

            const response = {
                config: { meta: { showSnackbar: false } },
                data: { message: 'Silent success' }
            };

            responseSuccessFn(response);
            expect(mockSuccess).not.toHaveBeenCalled();
        });

        it('should show default success message when no message provided', () => {
            const mockSuccess = jest.fn();
            registerSuccessCallback(mockSuccess);

            const response = {
                config: { meta: { showSnackbar: true } },
                data: {}
            };

            responseSuccessFn(response);
            expect(mockSuccess).not.toHaveBeenCalledWith('Success', { variant: 'success' });
        });

        it('should not show error snackbar when showSnackbar is false', async () => {
            const mockError = jest.fn();
            registerErrorCallback(mockError);

            const error = {
                config: { meta: { showSnackbar: false } },
                response: {
                    status: 500,
                    data: { message: 'Silent error' }
                }
            };

            await expect(responseErrorFn(error)).rejects.toEqual(error);
            expect(mockError).not.toHaveBeenCalled();
        });
    });
});
