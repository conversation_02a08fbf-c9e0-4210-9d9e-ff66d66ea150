const express = require("express");
const router = express.Router();
const userRoutes = require("./User.route");
const artifactRoutes = require("./Artifact.route");
const notificationSummaryRoutes = require("./NotificationSummary.route");
const kinesisRoutes = require("./Kinesis.route");
const vesselLocationRoutes = require("./VesselLocation.route");

router.use("/users", userRoutes);
router.use("/artifacts", artifactRoutes);
router.use("/summaryReports", notificationSummaryRoutes);
router.use("/kinesis", kinesisRoutes);
router.use("/vesselLocations", vesselLocationRoutes);

module.exports = router;
