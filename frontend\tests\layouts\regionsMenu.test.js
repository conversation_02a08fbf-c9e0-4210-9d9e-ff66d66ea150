import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import RegionsMenu from '../../src/layouts/RegionsMenu';
import { useApp } from '../../src/hooks/AppHook';
import axiosInstance from '../../src/axios';
import { ThemeProvider } from '@mui/material';
import theme from '../../src/theme';

jest.mock('../../src/hooks/AppHook');
jest.mock('../../src/axios');

const mockRegions = [
    { value: 'region1', name: 'Region 1' },
    { value: 'region2', name: 'Region 2' },
    { value: 'region3', name: 'Region 3' }
];

describe('Regions Menu', () => {
    const mockSetRegion = jest.fn();
    const mockSetRegionsAnchorEl = jest.fn();
    const mockAnchorEl = document.createElement('div');

    beforeEach(() => {
        jest.clearAllMocks();
        useApp.mockReturnValue({
            region: 'region1',
            setRegion: mockSetRegion,
            isMobile: false
        });
        axiosInstance.get.mockResolvedValue({ data: mockRegions });
    });

    const renderRegionsMenu = (props = {}) => {
        const defaultProps = {
            regionsAnchorEl: mockAnchorEl,
            setRegionsAnchorEl: mockSetRegionsAnchorEl,
            menuOpen: true,
            ...props
        };

        return render(
            <ThemeProvider theme={theme}>
                <RegionsMenu {...defaultProps} />
            </ThemeProvider>
        );
    };

    it('should fetch and display regions on mount', async () => {
        renderRegionsMenu();

        expect(screen.getByRole('progressbar')).toBeInTheDocument();

        await waitFor(() => {
            mockRegions.forEach(region => {
                expect(screen.getByText(region.name)).toBeInTheDocument();
            });
        });
    });

    it('should handle region selection', async () => {
        renderRegionsMenu();

        await waitFor(() => {
            expect(screen.getByText('Region 2')).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText('Region 2'));

        expect(mockSetRegion).toHaveBeenCalledWith('region2');
        expect(mockSetRegionsAnchorEl).toHaveBeenCalledWith(null);
    });

    it('should disable current region', async () => {
        useApp.mockReturnValue({
            region: 'region1',
            setRegion: mockSetRegion,
            isMobile: false
        });

        renderRegionsMenu();

        await waitFor(() => {
            const currentRegion = screen.getByText('Region 1');
            expect(currentRegion.closest('[role="menuitem"]')).toHaveAttribute('aria-disabled', 'true');
        });
    });

    it('should handle menu close', async () => {
        renderRegionsMenu();

        await waitFor(() => {
            expect(screen.getByRole('menu')).toBeInTheDocument();
        });

        fireEvent.keyDown(screen.getByRole('menu'), { key: 'Escape' });
        expect(mockSetRegionsAnchorEl).toHaveBeenCalledWith(null);
    });

    it('should handle fetch error', async () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
        axiosInstance.get.mockRejectedValue(new Error('Fetch failed'));

        renderRegionsMenu();

        await waitFor(() => {
            expect(consoleSpy).toHaveBeenCalled();
        });

        consoleSpy.mockRestore();
    });

    it('should adjust menu position for mobile', async () => {
        useApp.mockReturnValue({
            region: 'region1',
            setRegion: mockSetRegion,
            isMobile: true
        });

        renderRegionsMenu();

        await waitFor(() => {
            const menu = screen.getByRole('menu');
            expect(menu).toHaveClass('MuiList-root');
        });
    });

    it('should handle empty regions array', async () => {
        axiosInstance.get.mockResolvedValue({ data: [] });

        renderRegionsMenu();

        expect(screen.getByRole('progressbar')).toBeInTheDocument();

        await waitFor(() => {
            expect(screen.queryByRole('menuitem')).not.toBeInTheDocument();
        });
    });

    it('should handle menu position based on menuOpen prop', async () => {
        renderRegionsMenu({ menuOpen: false });

        await waitFor(() => {
            const menu = screen.getByRole('menu');
            expect(menu).toHaveClass('MuiList-root');
        });
    });

    it('should handle menu position based on menuOpen prop', async () => {
        renderRegionsMenu({ menuOpen: false });

        await waitFor(() => {
            const menu = screen.getByRole('menu');
            expect(menu).toHaveClass('MuiList-root');
        });
    });
});
