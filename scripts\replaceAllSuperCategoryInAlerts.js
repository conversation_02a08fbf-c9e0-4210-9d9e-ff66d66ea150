require("dotenv").config();
const db = require("../modules/db");
const NotificationAlert = require("../models/NotificationAlert");

const dryRun = process.argv.includes("--dry-run");

function clearLine() {
    process.stdout.write("\r\x1b[K");
}

function writeProgress(message) {
    clearLine();
    process.stdout.write(message);
}

function writeLine(message) {
    clearLine();
    process.stdout.write(message + "\n");
}

async function replaceAllSuperCategoryInAlerts() {
    try {
        writeLine("🔍 Fetching unique super_categories from analysis_results...");
        const superCategories = await db.qmai.collection("analysis_results").distinct("super_category", { super_category: { $ne: null } });
        if (!superCategories.length) {
            writeLine("✅ No super_categories found in analysis_results.");
            process.exit(0);
        }
        writeLine(`Found ${superCategories.length} unique super_categories. Updating NotificationAlerts...`);
        if (dryRun) {
            writeLine("🟡 DRY RUN MODE - No changes will be made");
        }
        const alerts = await NotificationAlert.find({ super_category: "all" });
        if (!alerts.length) {
            writeLine("✅ No NotificationAlerts with 'all' in super_category.");
            process.exit(0);
        }
        let updated = 0;
        for (let i = 0; i < alerts.length; i++) {
            const alert = alerts[i];
            const progress = `[${i + 1}/${alerts.length}]`;
            writeProgress(`Processing alert ${alert._id} ${progress}`);
            let newSuperCategories = Array.isArray(alert.super_category)
                ? alert.super_category.flatMap((cat) => (cat === "all" ? superCategories : [cat]))
                : superCategories;
            newSuperCategories = [...new Set(newSuperCategories)];
            if (dryRun) {
                writeLine(`\n[DRY RUN] Would update alert ${alert._id}: ${JSON.stringify(newSuperCategories)}`);
            } else {
                await NotificationAlert.updateOne({ _id: alert._id }, { $set: { super_category: newSuperCategories } });
            }
            updated++;
        }
        writeLine(`\n✅ Done. ${dryRun ? "Would update" : "Updated"} ${updated} NotificationAlerts.`);
    } catch (error) {
        writeLine(`❌ Error: ${error.message}`);
        process.exit(1);
    } finally {
        process.exit(0);
    }
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qm.once("open", resolve);
        db.qm.on("error", reject);
    }),
    new Promise((resolve, reject) => {
        db.qmai.once("open", resolve);
        db.qmai.on("error", reject);
    }),
])
    .then(() => {
        replaceAllSuperCategoryInAlerts();
    })
    .catch((err) => {
        writeLine(`❌ Database connection failed: ${err.message}`);
        process.exit(1);
    });
