const express = require("express");
const router = express.Router();
const { body } = require("express-validator");
const mongoose = require("mongoose");
const assignEndpointId = require("../middlewares/assignEndpointId");
const isAuthenticated = require("../middlewares/auth");
const { validateData } = require("../middlewares/validator");
const { validateError, canAccessVessel } = require("../utils/functions");
const { endpointIds } = require("../utils/endpointIds");
const limitPromise = require("../modules/pLimit");
const db = require("../modules/db");
const vesselService = require("../services/Vessel.service");
const { default: rateLimit } = require("express-rate-limit");
const compression = require("compression");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 40,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});

router.use("/", apiLimiter);
router.use(compression());

router.post(
    "/:vesselId",
    assignEndpointId.bind(this, endpointIds.FETCH_AUDIOS),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                // cannot be verified in test cases
                // param("vesselId").isMongoId().withMessage("Invalid vessel ID"),
                body("startTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestamp")
                    .isInt()
                    .customSanitizer((v) => Number(v))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { vesselId } = req.params;
            const { startTimestamp, endTimestamp } = req.body;
            console.log(`/audio ${vesselId}`, startTimestamp, endTimestamp);

            if (endTimestamp && !startTimestamp) {
                return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
            }

            const vessel = await vesselService.findById({ id: vesselId });
            if (!vessel) return res.status(404).json({ message: "Vessel does not exist" });

            if (!canAccessVessel(req, vessel)) {
                return res.status(403).json({ message: `Cannot access artifacts for '${vesselId}'` });
            }

            const query = { is_archived: { $ne: true } };
            query.onboard_vessel_id = mongoose.Types.ObjectId(vesselId);

            if (startTimestamp) {
                const endTime = endTimestamp || Date.now();
                query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTime) };
            }
            query.host_location = { $ne: null };

            const audios = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/audio ${vesselId} querying DB`);

                const cursor = db.audio.collection("audio_files").find(query, {
                    projection: {
                        _id: 1,
                        frequency: 1,
                        timestamp: 1,
                        aws_region: 1,
                        bucket_name: 1,
                        host_location: 1,
                        onboard_vessel_id: 1,
                        audio_path: 1,
                    },
                });

                if (isSwagger) {
                    cursor.limit(20);
                }

                return await cursor.toArray();
            });
            console.log(`/audio ${vesselId} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/audio ${vesselId} received ${audios?.length} audios`);

            if (isClosed) return res.end();

            res.json(audios);

            console.log(`/audio ${vesselId} time taken to respond ${new Date().getTime() - ts}`);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

module.exports = router;
