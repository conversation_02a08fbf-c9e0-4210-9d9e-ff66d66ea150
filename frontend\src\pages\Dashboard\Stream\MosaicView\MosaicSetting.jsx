import { Box, Grid, Switch, Typography } from "@mui/material";
import React from "react";
import theme from "../../../../theme";
import MosaicReorder from "./MosaicReorder";
import { useApp } from "../../../../hooks/AppHook";
// added import

const MosaicSetting = ({ scrubbarToggle, lockSlider, streams, changeMosaicListOrder, changeMosaicLayout, mosaicLayout }) => {
    const { isMobile } = useApp();
    const handleChange = () => {
        scrubbarToggle();
    };
    const handleMosaicReorderSave = (newOrder) => {
        changeMosaicListOrder(newOrder);
    };

    const handleMosaicLayoutChange = (newLayout) => {
        changeMosaicLayout(newLayout);
    };

    return (
        <Box
            sx={{
                minWidth: { xs: "0", md: "350px" },
                width: "100%",
                position: "relative",
            }}
        >
            <Grid container>
                <Grid
                    container
                    display={"flex"}
                    alignItems={"center"}
                    justifyContent={"space-between"}
                    sx={{ borderBottom: "2px solid black", paddingY: isMobile ? 0 : 1, paddingX: isMobile ? 0 : 2 }}
                >
                    <Grid>
                        <Typography sx={{ color: theme.palette.custom.text }}>Use single slider for all streams</Typography>
                    </Grid>
                    <Grid>
                        <Switch
                            checked={lockSlider}
                            onChange={handleChange}
                            inputProps={{ "aria-label": "controlled" }}
                            sx={{
                                "& .MuiSwitch-track": {
                                    backgroundColor: "#FFFFFF",
                                },
                                "& .Mui-checked+.MuiSwitch-track": {
                                    backgroundColor: theme.palette.custom.mainBlue + " !important",
                                    opacity: "1 !important",
                                },
                                "& .MuiSwitch-thumb": {
                                    backgroundColor: "#FFFFFF",
                                },
                            }}
                        />
                    </Grid>
                </Grid>
                <Grid container sx={{ borderBottom: "2px solid black", width: "100%" }} display={isMobile ? "none" : "flex"}>
                    <Grid sx={{ paddingY: 1, paddingX: 2 }}>
                        <Typography>Select Mosaic Layout View</Typography>
                    </Grid>
                    <Grid container spacing={1} sx={{ padding: 1, justifyContent: "center" }}>
                        {[4, 6, 8].map((layout) => (
                            <Grid key={layout}>
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        width: 40,
                                        height: 40,
                                        borderRadius: 1,
                                        backgroundColor: mosaicLayout === layout ? theme.palette.primary.main : theme.palette.grey[300],
                                        color: mosaicLayout === layout ? theme.palette.common.white : theme.palette.text.primary,
                                        cursor: "pointer",
                                        transition: "background-color 0.3s",
                                    }}
                                    onClick={() => handleMosaicLayoutChange(layout)}
                                >
                                    <Typography variant="button">{layout}</Typography>
                                </Box>
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
                <Grid container display={isMobile ? "none" : "flex"}>
                    <Grid sx={{ borderBottom: "2px solid black", width: "100%", paddingY: 1, paddingX: 2 }}>
                        <Typography>Select Sensor Order Preferences</Typography>
                    </Grid>
                    <Grid sx={{ margin: "auto" }}>
                        <MosaicReorder
                            streams={streams}
                            onSave={handleMosaicReorderSave} // use the new handler here
                            onCancel={() => {}}
                        />
                    </Grid>
                </Grid>
            </Grid>
        </Box>
    );
};

export default MosaicSetting;
