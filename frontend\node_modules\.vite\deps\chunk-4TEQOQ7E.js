import {
  Timeout
} from "./chunk-PI4AJCOC.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/utils/esm/ponyfillGlobal/ponyfillGlobal.js
var ponyfillGlobal_default = typeof window != "undefined" && window.Math == Math ? window : typeof self != "undefined" && self.Math == Math ? self : Function("return this")();

// node_modules/@mui/utils/esm/useIsFocusVisible/useIsFocusVisible.js
var React = __toESM(require_react(), 1);
var hadFocusVisibleRecentlyTimeout = new Timeout();

// node_modules/@mui/utils/esm/getReactNodeRef/getReactNodeRef.js
var React2 = __toESM(require_react(), 1);
/*! Bundled license information:

@mui/utils/esm/index.js:
  (**
   * @mui/utils v7.1.0
   *
   * @license MIT
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-4TEQOQ7E.js.map
