
const hasPermission = (permissions, req, res, next) => {
    if (req.user) {
        if (permissions.every(p_id => req.user.permissions.some(p => p.permission_id === p_id))) {
            return next();
        } else {
            return res.status(403).json({ message: 'You cannot access this resource' });
        }
    } else if (req.api_key_id) {
        // already authorized in auth middleware
        return next();
    } else {
        return res.status(403).json({ message: 'Login to access this resource' });
    }
}

module.exports = hasPermission