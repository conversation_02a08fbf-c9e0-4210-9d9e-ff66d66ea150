import { Button, CircularProgress, Grid, MenuItem, Modal, TextField } from "@mui/material";
import { Field, Form, Formik } from "formik";
import ModalContainer from "../../../../components/ModalContainer";
import { createRegionGroupSchema } from "../../../../validation-schemas";
// import MultiSelect from "../../../../components/MultiSelect";
import { timezonesList } from "../../../../utils";
import regionGroupController from "../../../../controllers/RegionGroup.controller";
import useVesselInfo from "../../../../hooks/VesselInfoHook";
import useGroupRegions from "../../../../hooks/GroupRegionHook";

const UpdateRegionGroupModal = ({ regionGroup, showUpdateModal, setShowUpdateModal, onSuccess, vessels }) => {
    // const theme = useTheme();
    const { fetchVesselsInfo } = useVesselInfo();
    const { fetchRegions } = useGroupRegions();
    const handleClose = () => {
        setShowUpdateModal(false);
    };

    const handleSubmit = async (values, { setSubmitting }) => {
        try {
            setSubmitting(true);
            const res = await regionGroupController.update({ id: regionGroup._id, ...values });
            onSuccess && onSuccess(res.regionGroup);
            setSubmitting(false);
            setShowUpdateModal(false);
            fetchVesselsInfo();
            fetchRegions();
        } catch (err) {
            console.error("An error occurred while creating a region group:", err);
        }
    };

    if (!vessels) return null;
    if (!regionGroup?._id) return null;
    // const vesselAlreadyInRegionGroup = vessels.filter((v) => v.region_group_id).map((v) => v.vessel_id);
    // const currentGroupVesselIds = regionGroup.vessel_ids || [];

    return (
        <Modal open={showUpdateModal} onClose={() => {}}>
            <ModalContainer title={"Update Region Group"} onClose={handleClose}>
                <Formik
                    initialValues={{
                        name: regionGroup.name,
                        timezone: regionGroup.timezone,
                        // vessel_ids: regionGroup.vessel_ids || [],
                    }}
                    validationSchema={createRegionGroupSchema}
                    onSubmit={handleSubmit}
                >
                    {({ errors, touched, isSubmitting, values }) => (
                        <Form>
                            <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, lg: 500 }}>
                                <Grid>
                                    <Field
                                        as={TextField}
                                        required={!values.name}
                                        name={"name"}
                                        value={values.name}
                                        label="Region Name"
                                        variant="filled"
                                        error={touched.name && Boolean(errors.name)}
                                        helperText={touched.name && errors.name}
                                        size="small"
                                        fullWidth
                                        sx={{
                                            backgroundColor: "transparent",
                                            "& .MuiFilledInput-root": {
                                                backgroundColor: "transparent",
                                                borderBottom: "none",
                                                border: (theme) => `1px solid ${theme.palette.custom.borderColor}`,
                                                borderRadius: "8px",
                                            },
                                            "& .MuiInputBase-input": {
                                                padding: 1.5,
                                            },
                                            "& .MuiFilledInput-root::after,.MuiFilledInput-root::before": {
                                                border: "none !important",
                                            },
                                            "& .MuiInputBase-root": {
                                                backgroundColor: "transparent",
                                                borderBottom: "none",
                                                border: (theme) => `1px solid ${theme.palette.custom.borderColor}`,
                                                borderRadius: "8px",
                                            },
                                            "& .MuiInputLabel-shrink": {
                                                display: "none",
                                            },
                                        }}
                                    />
                                </Grid>
                                <Grid>
                                    <Field
                                        as={TextField}
                                        required
                                        select
                                        name={"timezone"}
                                        value={values.timezone}
                                        label="Timezone"
                                        variant="filled"
                                        size="small"
                                        error={touched.timezone && Boolean(errors.timezone)}
                                        helperText={touched.timezone && errors.timezone}
                                        fullWidth
                                        sx={{
                                            "& .MuiInputBase-input": {
                                                padding: 1.5,
                                            },
                                        }}
                                    >
                                        {timezonesList.map((timezone) => (
                                            <MenuItem key={timezone.offset} value={timezone.offset}>
                                                {timezone.representative} ({timezone.offset})
                                            </MenuItem>
                                        ))}
                                    </Field>
                                </Grid>

                                {/* <Grid>
                                    <MultiSelect
                                        loading={managedVessels.length === 0}
                                        options={managedVessels}
                                        value={values.vessel_ids}
                                        getOptionDisabled={(o, c) => {
                                            // Always allow if currently selected
                                            if (c) return false;
                                            // Disable if vessel is in another group and not in this group
                                            return vesselAlreadyInRegionGroup.includes(o.vessel_id) && !currentGroupVesselIds.includes(o.vessel_id);
                                        }}
                                        multiple
                                        disableCloseOnSelect
                                        label={values.vessel_ids.length === 0 ? "Select Vessels *" : `${values.vessel_ids.length} selected *`}
                                        getOptionLabel={(o) => o.name || `Vessel ${o.vessel_id}`}
                                        isOptionEqualToValue={(o, v) => v.includes(o.vessel_id)}
                                        renderTags={() => null}
                                        onChange={(e, newValue) => {
                                            setValues((v) => ({
                                                ...v,
                                                vessel_ids: newValue.map((o) => (typeof o === "string" ? o : o.vessel_id)),
                                            }));
                                        }}
                                        backgroundColor={theme.palette.custom.darkBlue}
                                        sx={{
                                            ".MuiInputBase-input": {
                                                height: 24,
                                            },
                                        }}
                                        borderRadius={8}
                                        InputLabelStyle={{ fontSize: 16, opacity: 0.5 }}
                                        TextFieldProps={{
                                            error: touched.vessel_ids && Boolean(errors.vessel_ids),
                                            helperText: touched.vessel_ids && errors.vessel_ids,
                                            onBlur: () => {
                                                if (!touched.vessel_ids) {
                                                    setFieldTouched("vessel_ids", true, true);
                                                }
                                                if (values.vessel_ids.length === 0) {
                                                    setErrors({ ...errors, vessel_ids: "At least one vessel is required" });
                                                }
                                            },
                                        }}
                                    />
                                </Grid> */}

                                <Grid justifyContent={"center"} display={"flex"}>
                                    <Button
                                        type="submit"
                                        variant="contained"
                                        disabled={!values.name || !values.timezone || isSubmitting}
                                        sx={{
                                            backgroundColor: (theme) => theme.palette.custom.mainBlue,
                                            color: "#FFFFFF",
                                            padding: "10px 24px",
                                        }}
                                    >
                                        {isSubmitting ? (
                                            <>
                                                <CircularProgress size={18} />
                                                <span style={{ marginLeft: 10 }}>Updating</span>
                                            </>
                                        ) : (
                                            "Submit"
                                        )}
                                    </Button>
                                </Grid>
                            </Grid>
                        </Form>
                    )}
                </Formik>
            </ModalContainer>
        </Modal>
    );
};

export default UpdateRegionGroupModal;
