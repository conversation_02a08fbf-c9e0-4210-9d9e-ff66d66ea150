require("dotenv").config();
const sharp = require("sharp");
const awsS3 = require("../modules/awsS3");
const db = require("../modules/db");
const Vessel = require("../models/Vessel");

function clearLine() {
    process.stdout.write("\r\x1b[K");
}

function writeProgress(message) {
    clearLine();
    process.stdout.write(message);
}

function writeLine(message) {
    clearLine();
    process.stdout.write(message + "\n");
}

async function convertAllVesselThumbnails() {
    try {
        writeLine("🔍 Connecting to databases...");
        writeLine("🔍 Searching for vessels with thumbnails...");
        const vessels = await Vessel.find({ thumbnail_s3_key: { $ne: null } });
        if (!vessels.length) {
            writeLine("✅ No vessels with thumbnails found.");
            process.exit(0);
        }
        writeLine(`Found ${vessels.length} vessels with thumbnails. Starting conversion...`);
        let converted = 0,
            skipped = 0,
            failed = 0;
        for (let i = 0; i < vessels.length; i++) {
            const vessel = vessels[i];
            const key = vessel.thumbnail_s3_key;
            const vesselInfo = `Vessel: ${vessel.name} (ID: ${vessel._id}, Unit: ${vessel.unit_id})`;
            writeProgress(`Processing ${i + 1}/${vessels.length}: ${vesselInfo}`);
            try {
                if (vessel.thumbnail_compressed_s3_key && vessel.thumbnail_compressed_s3_key.endsWith(".webp")) {
                    writeLine(`[SKIP] ${vesselInfo} already has .webp thumbnail.`);
                    skipped++;
                    continue;
                }
                // Download from S3
                const s3Obj = await awsS3.getS3Object(awsS3.s3Config.buckets.assets.name, awsS3.s3Config.buckets.assets.region, key, true);
                const origBuffer = s3Obj.Body;
                // Convert to webp
                const webpBuffer = await sharp(origBuffer).resize({ width: 512 }).webp({ quality: 90 }).toBuffer();
                // Generate new key
                const baseName = key.split("/").pop().split(".")[0];
                const dir = key.split("/").slice(0, -1).join("/");
                const newKey = `${dir ? dir + "/" : ""}${baseName}.webp`;
                // Upload to S3
                await awsS3.uploadFileToS3(
                    { buffer: webpBuffer, originalname: `${baseName}.webp`, mimetype: "image/webp" },
                    awsS3.s3Config.buckets.assets,
                    newKey,
                    { ACL: "private", ContentType: "image/webp" },
                );
                // Update vessel
                vessel.thumbnail_compressed_s3_key = newKey;
                await vessel.save();
                writeLine(`[OK] ${vesselInfo} thumbnail converted to .webp`);
                converted++;
            } catch (err) {
                writeLine(`[FAIL] ${vesselInfo}: ${err.message}`);
                failed++;
            }
        }
        writeLine(`\n✅ Done. Converted: ${converted}, Skipped: ${skipped}, Failed: ${failed}`);
    } catch (err) {
        writeLine(`❌ Fatal error: ${err.message}`);
        process.exit(1);
    } finally {
        process.exit(0);
    }
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qm.once("open", resolve);
        db.qm.on("error", reject);
    }),
    new Promise((resolve, reject) => {
        db.qmShared.once("open", resolve);
        db.qmShared.on("error", reject);
    }),
])
    .then(() => {
        convertAllVesselThumbnails();
    })
    .catch((err) => {
        writeLine(`❌ Database connection failed: ${err.message}`);
        process.exit(1);
    });
