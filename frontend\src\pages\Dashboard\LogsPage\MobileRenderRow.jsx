import React from "react";
import { Grid, Typography } from "@mui/material";
import dayjs from "dayjs";
import theme from "../../../theme";
import { userValues } from "../../../utils.js";
import { useUser } from "../../../hooks/UserHook.jsx";

const MobileRenderRow = ({ index, style, data }) => {
    const { logs } = data;
    const log = logs[index + 1];
    const { user } = useUser();

    if (!log) return null;

    return (
        <div style={style}>
            <Grid
                container
                key={log._id}
                sx={{
                    display: "flex",
                    width: "100%",
                    paddingY: 2,
                    borderBottom: logs.length - 1 === index + 1 ? "none" : `1px solid ${theme.palette.custom.borderColor}`,
                    rowGap: "10px",
                    flexWrap: "wrap",
                }}
            >
                {[
                    {
                        label: "Last Connected",
                        value: dayjs(log.connect_timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true })),
                    },
                    {
                        label: "Disconnected",
                        value:
                            log.disconnect_timestamp &&
                            dayjs(log.disconnect_timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true })),
                    },
                    { label: "Device", value: log.device },
                    { label: "Browser", value: log.browser },
                ].map((field, idx) => (
                    <Grid
                        key={idx}
                        sx={{
                            color: "#FFFFFF",
                            padding: 0,
                            border: "none",
                        }}
                        size={{
                            xs: 12,
                            sm: 4,
                        }}
                    >
                        <Typography
                            fontSize="14px"
                            fontWeight="400"
                            sx={{
                                color: theme.palette.custom.mainBlue,
                                flex: 1,
                                padding: 0,
                                border: "none",
                            }}
                        >
                            {field.label}
                        </Typography>
                        <Typography variant="h6" fontSize="16px !important" color="#fff">
                            {field.value ? field.value : "--"}
                        </Typography>
                    </Grid>
                ))}
            </Grid>
        </div>
    );
};

export default MobileRenderRow;
