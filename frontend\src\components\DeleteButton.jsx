import { IconButton, Tooltip } from "@mui/material";
import { Delete } from "@mui/icons-material";
import theme from "../theme";

export default function DeleteButton({ onClick, disabled = false, buttonStyle = {}, iconStyle = {} }) {
    return (
        <Tooltip enterDelay={300} title="Delete" placement="bottom">
            <span>
                <IconButton
                    onClick={onClick}
                    disabled={disabled}
                    sx={{
                        background: "#E600000D",
                        border: `1px solid ${theme.palette.custom.borderColor}`,
                        borderRadius: "5px",
                        padding: "8px",
                        ...buttonStyle,
                    }}
                >
                    <Delete color="error" sx={{ fontSize: "18px", ...iconStyle }} />
                </IconButton>
            </span>
        </Tooltip>
    );
}
