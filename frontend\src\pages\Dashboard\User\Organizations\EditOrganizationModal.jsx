import { useEffect, useState } from "react";
import axiosInstance from "../../../../axios";
import { Button, Grid, Modal, TextField } from "@mui/material";
import ModalContainer from "../../../../components/ModalContainer";

const EditOrganizationModal = ({ showEditModal, setShowEditModal, organizationToEdit, setOrganizationToEdit, onSuccess }) => {
    const [organizationName, setOrganizationName] = useState("");
    const [domain, setDomain] = useState("");

    useEffect(() => {
        if (organizationToEdit) {
            setOrganizationName(organizationToEdit.name || "");
            setDomain(organizationToEdit.domain || "");
        }
    }, [organizationToEdit]);

    const handleClose = () => {
        setShowEditModal(false);
        setOrganizationName("");
        setDomain("");
        setOrganizationToEdit(null);
    };

    const onEdit = () => {
        axiosInstance
            .patch(`/organizations/${organizationToEdit._id}`, { name: organizationName, domain }, { meta: { showSnackbar: true } })
            .then(() => {
                onSuccess && onSuccess();
                handleClose();
            })
            .catch(console.error);
    };

    return (
        <Modal open={Boolean(showEditModal)} onClose={handleClose}>
            <ModalContainer title={"Edit Organization"} onClose={handleClose}>
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: 500 }}>
                    <Grid>
                        <TextField
                            value={organizationName}
                            onChange={(e) => setOrganizationName(e.target.value)}
                            label="Organization Name"
                            variant="filled"
                            fullWidth
                        />
                    </Grid>
                    <Grid>
                        <TextField value={domain} onChange={(e) => setDomain(e.target.value)} label="Domain" variant="filled" fullWidth />
                    </Grid>
                    <Grid justifyContent={"center"} display={"flex"}>
                        <Button disabled={!organizationName || !domain} variant="contained" onClick={onEdit}>
                            Save Changes
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default EditOrganizationModal;
