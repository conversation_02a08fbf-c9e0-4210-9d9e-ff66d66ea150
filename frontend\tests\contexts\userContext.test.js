import { render, screen } from '@testing-library/react';
import { useContext } from 'react';
import { UserContext } from '../../src/contexts/UserContext';

const SomeComponent = () => {
    const { user } = useContext(UserContext);
    return <div>{user.name}</div>;
};

describe('UserContext', () => {
    it('should render Mobile when screenSize.xs is true', () => {
        const mockContextValue = {
            user: { name: 'john' },
        };

        render(
            <UserContext.Provider value={mockContextValue}>
                <SomeComponent />
            </UserContext.Provider>
        );

        expect(screen.getByText('john')).toBeInTheDocument();
    });
});
