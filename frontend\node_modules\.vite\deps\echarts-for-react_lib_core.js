import {
  require_fast_deep_equal
} from "./chunk-SK6RFYXP.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS
} from "./chunk-EWTE5DHJ.js";

// node_modules/tslib/tslib.es6.mjs
var tslib_es6_exports = {};
__export(tslib_es6_exports, {
  __addDisposableResource: () => __addDisposableResource,
  __assign: () => __assign,
  __asyncDelegator: () => __asyncDelegator,
  __asyncGenerator: () => __asyncGenerator,
  __asyncValues: () => __asyncValues,
  __await: () => __await,
  __awaiter: () => __awaiter,
  __classPrivateFieldGet: () => __classPrivateFieldGet,
  __classPrivateFieldIn: () => __classPrivateFieldIn,
  __classPrivateFieldSet: () => __classPrivateFieldSet,
  __createBinding: () => __createBinding,
  __decorate: () => __decorate,
  __disposeResources: () => __disposeResources,
  __esDecorate: () => __esDecorate,
  __exportStar: () => __exportStar,
  __extends: () => __extends,
  __generator: () => __generator,
  __importDefault: () => __importDefault,
  __importStar: () => __importStar,
  __makeTemplateObject: () => __makeTemplateObject,
  __metadata: () => __metadata,
  __param: () => __param,
  __propKey: () => __propKey,
  __read: () => __read,
  __rest: () => __rest,
  __rewriteRelativeImportExtension: () => __rewriteRelativeImportExtension,
  __runInitializers: () => __runInitializers,
  __setFunctionName: () => __setFunctionName,
  __spread: () => __spread,
  __spreadArray: () => __spreadArray,
  __spreadArrays: () => __spreadArrays,
  __values: () => __values,
  default: () => tslib_es6_default
});
function __extends(d, b) {
  if (typeof b !== "function" && b !== null)
    throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
  extendStatics(d, b);
  function __() {
    this.constructor = d;
  }
  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}
function __rest(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
    t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
      if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
        t[p[i]] = s[p[i]];
    }
  return t;
}
function __decorate(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function __param(paramIndex, decorator) {
  return function(target, key) {
    decorator(target, key, paramIndex);
  };
}
function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
}
function __runInitializers(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
}
function __propKey(x) {
  return typeof x === "symbol" ? x : "".concat(x);
}
function __setFunctionName(f, name, prefix) {
  if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
  return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
}
function __metadata(metadataKey, metadataValue) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(metadataKey, metadataValue);
}
function __awaiter(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
}
function __generator(thisArg, body) {
  var _ = { label: 0, sent: function() {
    if (t[0] & 1) throw t[1];
    return t[1];
  }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() {
    return this;
  }), g;
  function verb(n) {
    return function(v) {
      return step([n, v]);
    };
  }
  function step(op) {
    if (f) throw new TypeError("Generator is already executing.");
    while (g && (g = 0, op[0] && (_ = 0)), _) try {
      if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
      if (y = 0, t) op = [op[0] & 2, t.value];
      switch (op[0]) {
        case 0:
        case 1:
          t = op;
          break;
        case 4:
          _.label++;
          return { value: op[1], done: false };
        case 5:
          _.label++;
          y = op[1];
          op = [0];
          continue;
        case 7:
          op = _.ops.pop();
          _.trys.pop();
          continue;
        default:
          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
            _ = 0;
            continue;
          }
          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
            _.label = op[1];
            break;
          }
          if (op[0] === 6 && _.label < t[1]) {
            _.label = t[1];
            t = op;
            break;
          }
          if (t && _.label < t[2]) {
            _.label = t[2];
            _.ops.push(op);
            break;
          }
          if (t[2]) _.ops.pop();
          _.trys.pop();
          continue;
      }
      op = body.call(thisArg, _);
    } catch (e) {
      op = [6, e];
      y = 0;
    } finally {
      f = t = 0;
    }
    if (op[0] & 5) throw op[1];
    return { value: op[0] ? op[1] : void 0, done: true };
  }
}
function __exportStar(m, o) {
  for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);
}
function __values(o) {
  var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
  if (m) return m.call(o);
  if (o && typeof o.length === "number") return {
    next: function() {
      if (o && i >= o.length) o = void 0;
      return { value: o && o[i++], done: !o };
    }
  };
  throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
}
function __read(o, n) {
  var m = typeof Symbol === "function" && o[Symbol.iterator];
  if (!m) return o;
  var i = m.call(o), r, ar = [], e;
  try {
    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
  } catch (error) {
    e = { error };
  } finally {
    try {
      if (r && !r.done && (m = i["return"])) m.call(i);
    } finally {
      if (e) throw e.error;
    }
  }
  return ar;
}
function __spread() {
  for (var ar = [], i = 0; i < arguments.length; i++)
    ar = ar.concat(__read(arguments[i]));
  return ar;
}
function __spreadArrays() {
  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
  for (var r = Array(s), k = 0, i = 0; i < il; i++)
    for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
      r[k] = a[j];
  return r;
}
function __spreadArray(to, from, pack) {
  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
    if (ar || !(i in from)) {
      if (!ar) ar = Array.prototype.slice.call(from, 0, i);
      ar[i] = from[i];
    }
  }
  return to.concat(ar || Array.prototype.slice.call(from));
}
function __await(v) {
  return this instanceof __await ? (this.v = v, this) : new __await(v);
}
function __asyncGenerator(thisArg, _arguments, generator) {
  if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
  var g = generator.apply(thisArg, _arguments || []), i, q = [];
  return i = Object.create((typeof AsyncIterator === "function" ? AsyncIterator : Object).prototype), verb("next"), verb("throw"), verb("return", awaitReturn), i[Symbol.asyncIterator] = function() {
    return this;
  }, i;
  function awaitReturn(f) {
    return function(v) {
      return Promise.resolve(v).then(f, reject);
    };
  }
  function verb(n, f) {
    if (g[n]) {
      i[n] = function(v) {
        return new Promise(function(a, b) {
          q.push([n, v, a, b]) > 1 || resume(n, v);
        });
      };
      if (f) i[n] = f(i[n]);
    }
  }
  function resume(n, v) {
    try {
      step(g[n](v));
    } catch (e) {
      settle(q[0][3], e);
    }
  }
  function step(r) {
    r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
  }
  function fulfill(value) {
    resume("next", value);
  }
  function reject(value) {
    resume("throw", value);
  }
  function settle(f, v) {
    if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
  }
}
function __asyncDelegator(o) {
  var i, p;
  return i = {}, verb("next"), verb("throw", function(e) {
    throw e;
  }), verb("return"), i[Symbol.iterator] = function() {
    return this;
  }, i;
  function verb(n, f) {
    i[n] = o[n] ? function(v) {
      return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v;
    } : f;
  }
}
function __asyncValues(o) {
  if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
  var m = o[Symbol.asyncIterator], i;
  return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
    return this;
  }, i);
  function verb(n) {
    i[n] = o[n] && function(v) {
      return new Promise(function(resolve, reject) {
        v = o[n](v), settle(resolve, reject, v.done, v.value);
      });
    };
  }
  function settle(resolve, reject, d, v) {
    Promise.resolve(v).then(function(v2) {
      resolve({ value: v2, done: d });
    }, reject);
  }
}
function __makeTemplateObject(cooked, raw) {
  if (Object.defineProperty) {
    Object.defineProperty(cooked, "raw", { value: raw });
  } else {
    cooked.raw = raw;
  }
  return cooked;
}
function __importStar(mod) {
  if (mod && mod.__esModule) return mod;
  var result = {};
  if (mod != null) {
    for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
  }
  __setModuleDefault(result, mod);
  return result;
}
function __importDefault(mod) {
  return mod && mod.__esModule ? mod : { default: mod };
}
function __classPrivateFieldGet(receiver, state, kind, f) {
  if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
  return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
}
function __classPrivateFieldSet(receiver, state, value, kind, f) {
  if (kind === "m") throw new TypeError("Private method is not writable");
  if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
  if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
  return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
}
function __classPrivateFieldIn(state, receiver) {
  if (receiver === null || typeof receiver !== "object" && typeof receiver !== "function") throw new TypeError("Cannot use 'in' operator on non-object");
  return typeof state === "function" ? receiver === state : state.has(receiver);
}
function __addDisposableResource(env, value, async) {
  if (value !== null && value !== void 0) {
    if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
    var dispose, inner;
    if (async) {
      if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
      dispose = value[Symbol.asyncDispose];
    }
    if (dispose === void 0) {
      if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
      dispose = value[Symbol.dispose];
      if (async) inner = dispose;
    }
    if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
    if (inner) dispose = function() {
      try {
        inner.call(this);
      } catch (e) {
        return Promise.reject(e);
      }
    };
    env.stack.push({ value, dispose, async });
  } else if (async) {
    env.stack.push({ async: true });
  }
  return value;
}
function __disposeResources(env) {
  function fail(e) {
    env.error = env.hasError ? new _SuppressedError(e, env.error, "An error was suppressed during disposal.") : e;
    env.hasError = true;
  }
  var r, s = 0;
  function next() {
    while (r = env.stack.pop()) {
      try {
        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);
        if (r.dispose) {
          var result = r.dispose.call(r.value);
          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) {
            fail(e);
            return next();
          });
        } else s |= 1;
      } catch (e) {
        fail(e);
      }
    }
    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();
    if (env.hasError) throw env.error;
  }
  return next();
}
function __rewriteRelativeImportExtension(path, preserveJsx) {
  if (typeof path === "string" && /^\.\.?\//.test(path)) {
    return path.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i, function(m, tsx, d, ext, cm) {
      return tsx ? preserveJsx ? ".jsx" : ".js" : d && (!ext || !cm) ? m : d + ext + "." + cm.toLowerCase() + "js";
    });
  }
  return path;
}
var extendStatics, __assign, __createBinding, __setModuleDefault, ownKeys, _SuppressedError, tslib_es6_default;
var init_tslib_es6 = __esm({
  "node_modules/tslib/tslib.es6.mjs"() {
    extendStatics = function(d, b) {
      extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
        d2.__proto__ = b2;
      } || function(d2, b2) {
        for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
      };
      return extendStatics(d, b);
    };
    __assign = function() {
      __assign = Object.assign || function __assign2(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    __createBinding = Object.create ? function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      var desc = Object.getOwnPropertyDescriptor(m, k);
      if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = { enumerable: true, get: function() {
          return m[k];
        } };
      }
      Object.defineProperty(o, k2, desc);
    } : function(o, m, k, k2) {
      if (k2 === void 0) k2 = k;
      o[k2] = m[k];
    };
    __setModuleDefault = Object.create ? function(o, v) {
      Object.defineProperty(o, "default", { enumerable: true, value: v });
    } : function(o, v) {
      o["default"] = v;
    };
    ownKeys = function(o) {
      ownKeys = Object.getOwnPropertyNames || function(o2) {
        var ar = [];
        for (var k in o2) if (Object.prototype.hasOwnProperty.call(o2, k)) ar[ar.length] = k;
        return ar;
      };
      return ownKeys(o);
    };
    _SuppressedError = typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
      var e = new Error(message);
      return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
    };
    tslib_es6_default = {
      __extends,
      __assign,
      __rest,
      __decorate,
      __param,
      __esDecorate,
      __runInitializers,
      __propKey,
      __setFunctionName,
      __metadata,
      __awaiter,
      __generator,
      __createBinding,
      __exportStar,
      __values,
      __read,
      __spread,
      __spreadArrays,
      __spreadArray,
      __await,
      __asyncGenerator,
      __asyncDelegator,
      __asyncValues,
      __makeTemplateObject,
      __importStar,
      __importDefault,
      __classPrivateFieldGet,
      __classPrivateFieldSet,
      __classPrivateFieldIn,
      __addDisposableResource,
      __disposeResources,
      __rewriteRelativeImportExtension
    };
  }
});

// node_modules/size-sensor/lib/id.js
var require_id = __commonJS({
  "node_modules/size-sensor/lib/id.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports["default"] = void 0;
    var id = 1;
    var _default = function _default2() {
      return "".concat(id++);
    };
    exports["default"] = _default;
  }
});

// node_modules/size-sensor/lib/debounce.js
var require_debounce = __commonJS({
  "node_modules/size-sensor/lib/debounce.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports["default"] = void 0;
    var _default = function _default2(fn) {
      var delay = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 60;
      var timer = null;
      return function() {
        var _this = this;
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        clearTimeout(timer);
        timer = setTimeout(function() {
          fn.apply(_this, args);
        }, delay);
      };
    };
    exports["default"] = _default;
  }
});

// node_modules/size-sensor/lib/constant.js
var require_constant = __commonJS({
  "node_modules/size-sensor/lib/constant.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.SizeSensorId = exports.SensorTabIndex = exports.SensorClassName = void 0;
    var SizeSensorId = "size-sensor-id";
    exports.SizeSensorId = SizeSensorId;
    var SensorClassName = "size-sensor-object";
    exports.SensorClassName = SensorClassName;
    var SensorTabIndex = "-1";
    exports.SensorTabIndex = SensorTabIndex;
  }
});

// node_modules/size-sensor/lib/sensors/object.js
var require_object = __commonJS({
  "node_modules/size-sensor/lib/sensors/object.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.createSensor = void 0;
    var _debounce = _interopRequireDefault(require_debounce());
    var _constant = require_constant();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { "default": obj };
    }
    var createSensor = function createSensor2(element, whenDestroy) {
      var sensor = void 0;
      var listeners = [];
      var newSensor = function newSensor2() {
        if (getComputedStyle(element).position === "static") {
          element.style.position = "relative";
        }
        var obj = document.createElement("object");
        obj.onload = function() {
          obj.contentDocument.defaultView.addEventListener("resize", resizeListener);
          resizeListener();
        };
        obj.style.display = "block";
        obj.style.position = "absolute";
        obj.style.top = "0";
        obj.style.left = "0";
        obj.style.height = "100%";
        obj.style.width = "100%";
        obj.style.overflow = "hidden";
        obj.style.pointerEvents = "none";
        obj.style.zIndex = "-1";
        obj.style.opacity = "0";
        obj.setAttribute("class", _constant.SensorClassName);
        obj.setAttribute("tabindex", _constant.SensorTabIndex);
        obj.type = "text/html";
        element.appendChild(obj);
        obj.data = "about:blank";
        return obj;
      };
      var resizeListener = (0, _debounce["default"])(function() {
        listeners.forEach(function(listener) {
          listener(element);
        });
      });
      var bind = function bind2(cb) {
        if (!sensor) {
          sensor = newSensor();
        }
        if (listeners.indexOf(cb) === -1) {
          listeners.push(cb);
        }
      };
      var destroy = function destroy2() {
        if (sensor && sensor.parentNode) {
          if (sensor.contentDocument) {
            sensor.contentDocument.defaultView.removeEventListener("resize", resizeListener);
          }
          sensor.parentNode.removeChild(sensor);
          element.removeAttribute(_constant.SizeSensorId);
          sensor = void 0;
          listeners = [];
          whenDestroy && whenDestroy();
        }
      };
      var unbind = function unbind2(cb) {
        var idx = listeners.indexOf(cb);
        if (idx !== -1) {
          listeners.splice(idx, 1);
        }
        if (listeners.length === 0 && sensor) {
          destroy();
        }
      };
      return {
        element,
        bind,
        destroy,
        unbind
      };
    };
    exports.createSensor = createSensor;
  }
});

// node_modules/size-sensor/lib/sensors/resizeObserver.js
var require_resizeObserver = __commonJS({
  "node_modules/size-sensor/lib/sensors/resizeObserver.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.createSensor = void 0;
    var _constant = require_constant();
    var _debounce = _interopRequireDefault(require_debounce());
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { "default": obj };
    }
    var createSensor = function createSensor2(element, whenDestroy) {
      var sensor = void 0;
      var listeners = [];
      var resizeListener = (0, _debounce["default"])(function() {
        listeners.forEach(function(listener) {
          listener(element);
        });
      });
      var newSensor = function newSensor2() {
        var s = new ResizeObserver(resizeListener);
        s.observe(element);
        resizeListener();
        return s;
      };
      var bind = function bind2(cb) {
        if (!sensor) {
          sensor = newSensor();
        }
        if (listeners.indexOf(cb) === -1) {
          listeners.push(cb);
        }
      };
      var destroy = function destroy2() {
        sensor.disconnect();
        listeners = [];
        sensor = void 0;
        element.removeAttribute(_constant.SizeSensorId);
        whenDestroy && whenDestroy();
      };
      var unbind = function unbind2(cb) {
        var idx = listeners.indexOf(cb);
        if (idx !== -1) {
          listeners.splice(idx, 1);
        }
        if (listeners.length === 0 && sensor) {
          destroy();
        }
      };
      return {
        element,
        bind,
        destroy,
        unbind
      };
    };
    exports.createSensor = createSensor;
  }
});

// node_modules/size-sensor/lib/sensors/index.js
var require_sensors = __commonJS({
  "node_modules/size-sensor/lib/sensors/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.createSensor = void 0;
    var _object = require_object();
    var _resizeObserver = require_resizeObserver();
    var createSensor = typeof ResizeObserver !== "undefined" ? _resizeObserver.createSensor : _object.createSensor;
    exports.createSensor = createSensor;
  }
});

// node_modules/size-sensor/lib/sensorPool.js
var require_sensorPool = __commonJS({
  "node_modules/size-sensor/lib/sensorPool.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.removeSensor = exports.getSensor = exports.Sensors = void 0;
    var _id = _interopRequireDefault(require_id());
    var _sensors = require_sensors();
    var _constant = require_constant();
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { "default": obj };
    }
    var Sensors = {};
    exports.Sensors = Sensors;
    function clean(sensorId) {
      if (sensorId && Sensors[sensorId]) {
        delete Sensors[sensorId];
      }
    }
    var getSensor = function getSensor2(element) {
      var sensorId = element.getAttribute(_constant.SizeSensorId);
      if (sensorId && Sensors[sensorId]) {
        return Sensors[sensorId];
      }
      var newId = (0, _id["default"])();
      element.setAttribute(_constant.SizeSensorId, newId);
      var sensor = (0, _sensors.createSensor)(element, function() {
        return clean(newId);
      });
      Sensors[newId] = sensor;
      return sensor;
    };
    exports.getSensor = getSensor;
    var removeSensor = function removeSensor2(sensor) {
      var sensorId = sensor.element.getAttribute(_constant.SizeSensorId);
      sensor.destroy();
      clean(sensorId);
    };
    exports.removeSensor = removeSensor;
  }
});

// node_modules/size-sensor/lib/index.js
var require_lib = __commonJS({
  "node_modules/size-sensor/lib/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.ver = exports.clear = exports.bind = void 0;
    var _sensorPool = require_sensorPool();
    var bind = function bind2(element, cb) {
      var sensor = (0, _sensorPool.getSensor)(element);
      sensor.bind(cb);
      return function() {
        sensor.unbind(cb);
      };
    };
    exports.bind = bind;
    var clear = function clear2(element) {
      var sensor = (0, _sensorPool.getSensor)(element);
      (0, _sensorPool.removeSensor)(sensor);
    };
    exports.clear = clear;
    var ver = "1.0.2";
    exports.ver = ver;
  }
});

// node_modules/echarts-for-react/lib/helper/pick.js
var require_pick = __commonJS({
  "node_modules/echarts-for-react/lib/helper/pick.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.pick = void 0;
    function pick(obj, keys) {
      var r = {};
      keys.forEach(function(key) {
        r[key] = obj[key];
      });
      return r;
    }
    exports.pick = pick;
  }
});

// node_modules/echarts-for-react/lib/helper/is-function.js
var require_is_function = __commonJS({
  "node_modules/echarts-for-react/lib/helper/is-function.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isFunction = void 0;
    function isFunction(v) {
      return typeof v === "function";
    }
    exports.isFunction = isFunction;
  }
});

// node_modules/echarts-for-react/lib/helper/is-string.js
var require_is_string = __commonJS({
  "node_modules/echarts-for-react/lib/helper/is-string.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isString = void 0;
    function isString(v) {
      return typeof v === "string";
    }
    exports.isString = isString;
  }
});

// node_modules/echarts-for-react/lib/helper/is-equal.js
var require_is_equal = __commonJS({
  "node_modules/echarts-for-react/lib/helper/is-equal.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.isEqual = void 0;
    var tslib_1 = (init_tslib_es6(), __toCommonJS(tslib_es6_exports));
    var fast_deep_equal_1 = (0, tslib_1.__importDefault)(require_fast_deep_equal());
    exports.isEqual = fast_deep_equal_1.default;
  }
});

// node_modules/echarts-for-react/lib/core.js
var require_core = __commonJS({
  "node_modules/echarts-for-react/lib/core.js"(exports) {
    Object.defineProperty(exports, "__esModule", { value: true });
    var tslib_1 = (init_tslib_es6(), __toCommonJS(tslib_es6_exports));
    var react_1 = (0, tslib_1.__importStar)(require_react());
    var size_sensor_1 = require_lib();
    var pick_1 = require_pick();
    var is_function_1 = require_is_function();
    var is_string_1 = require_is_string();
    var is_equal_1 = require_is_equal();
    var EChartsReactCore = (
      /** @class */
      function(_super) {
        (0, tslib_1.__extends)(EChartsReactCore2, _super);
        function EChartsReactCore2(props) {
          var _this = _super.call(this, props) || this;
          _this.echarts = props.echarts;
          _this.ele = null;
          _this.isInitialResize = true;
          return _this;
        }
        EChartsReactCore2.prototype.componentDidMount = function() {
          this.renderNewEcharts();
        };
        EChartsReactCore2.prototype.componentDidUpdate = function(prevProps) {
          var shouldSetOption = this.props.shouldSetOption;
          if ((0, is_function_1.isFunction)(shouldSetOption) && !shouldSetOption(prevProps, this.props)) {
            return;
          }
          if (!(0, is_equal_1.isEqual)(prevProps.theme, this.props.theme) || !(0, is_equal_1.isEqual)(prevProps.opts, this.props.opts) || !(0, is_equal_1.isEqual)(prevProps.onEvents, this.props.onEvents)) {
            this.dispose();
            this.renderNewEcharts();
            return;
          }
          var pickKeys = ["option", "notMerge", "lazyUpdate", "showLoading", "loadingOption"];
          if (!(0, is_equal_1.isEqual)((0, pick_1.pick)(this.props, pickKeys), (0, pick_1.pick)(prevProps, pickKeys))) {
            this.updateEChartsOption();
          }
          if (!(0, is_equal_1.isEqual)(prevProps.style, this.props.style) || !(0, is_equal_1.isEqual)(prevProps.className, this.props.className)) {
            this.resize();
          }
        };
        EChartsReactCore2.prototype.componentWillUnmount = function() {
          this.dispose();
        };
        EChartsReactCore2.prototype.getEchartsInstance = function() {
          return this.echarts.getInstanceByDom(this.ele) || this.echarts.init(this.ele, this.props.theme, this.props.opts);
        };
        EChartsReactCore2.prototype.dispose = function() {
          if (this.ele) {
            try {
              (0, size_sensor_1.clear)(this.ele);
            } catch (e) {
              console.warn(e);
            }
            this.echarts.dispose(this.ele);
          }
        };
        EChartsReactCore2.prototype.renderNewEcharts = function() {
          var _this = this;
          var _a = this.props, onEvents = _a.onEvents, onChartReady = _a.onChartReady;
          var echartsInstance = this.updateEChartsOption();
          this.bindEvents(echartsInstance, onEvents || {});
          if ((0, is_function_1.isFunction)(onChartReady))
            onChartReady(echartsInstance);
          if (this.ele) {
            (0, size_sensor_1.bind)(this.ele, function() {
              _this.resize();
            });
          }
        };
        EChartsReactCore2.prototype.bindEvents = function(instance, events) {
          function _bindEvent(eventName2, func) {
            if ((0, is_string_1.isString)(eventName2) && (0, is_function_1.isFunction)(func)) {
              instance.on(eventName2, function(param) {
                func(param, instance);
              });
            }
          }
          for (var eventName in events) {
            if (Object.prototype.hasOwnProperty.call(events, eventName)) {
              _bindEvent(eventName, events[eventName]);
            }
          }
        };
        EChartsReactCore2.prototype.updateEChartsOption = function() {
          var _a = this.props, option = _a.option, _b = _a.notMerge, notMerge = _b === void 0 ? false : _b, _c = _a.lazyUpdate, lazyUpdate = _c === void 0 ? false : _c, showLoading = _a.showLoading, _d = _a.loadingOption, loadingOption = _d === void 0 ? null : _d;
          var echartInstance = this.getEchartsInstance();
          echartInstance.setOption(option, notMerge, lazyUpdate);
          if (showLoading)
            echartInstance.showLoading(loadingOption);
          else
            echartInstance.hideLoading();
          return echartInstance;
        };
        EChartsReactCore2.prototype.resize = function() {
          var echartsInstance = this.getEchartsInstance();
          if (!this.isInitialResize) {
            try {
              echartsInstance.resize();
            } catch (e) {
              console.warn(e);
            }
          }
          this.isInitialResize = false;
        };
        EChartsReactCore2.prototype.render = function() {
          var _this = this;
          var _a = this.props, style = _a.style, _b = _a.className, className = _b === void 0 ? "" : _b;
          var newStyle = (0, tslib_1.__assign)({ height: 300 }, style);
          return react_1.default.createElement("div", { ref: function(e) {
            _this.ele = e;
          }, style: newStyle, className: "echarts-for-react " + className });
        };
        return EChartsReactCore2;
      }(react_1.PureComponent)
    );
    exports.default = EChartsReactCore;
  }
});
export default require_core();
//# sourceMappingURL=echarts-for-react_lib_core.js.map
