const mongoose = require("mongoose");
const db = require("../modules/db");
const ioEmitter = require("../modules/ioEmitter");

const artifactFlaggedSchema = new mongoose.Schema({
    artifactId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        index: true,
    },
    flaggedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    flaggedAt: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
});

artifactFlaggedSchema.index({ artifactId: 1, flaggedBy: 1 });
artifactFlaggedSchema.index({ flaggedAt: -1 });

artifactFlaggedSchema.post("save", emitChangedEvent);
artifactFlaggedSchema.post("findOneAndDelete", emitChangedEvent);
artifactFlaggedSchema.post("deleteMany", emitChangedEvent);

function emitChangedEvent(flag) {
    if (flag?.deletedCount > 0) return ioEmitter.emit("notifyAll", { name: `artifacts_flagged/changed` });
    ioEmitter.emit("notifyAll", { name: `artifacts_flagged/changed`, data: flag.toObject() });
}

const ArtifactFlagged = db.qm.model("ArtifactFlagged", artifactFlaggedSchema, "artifacts_flagged");

module.exports = ArtifactFlagged;
