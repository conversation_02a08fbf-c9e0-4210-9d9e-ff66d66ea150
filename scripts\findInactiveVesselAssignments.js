require("dotenv").config();
const User = require("../models/User");
const ApiKey = require("../models/ApiKey");
const InviteToken = require("../models/InviteToken");
const Vessel = require("../models/Vessel");
const RegionGroup = require("../models/RegionGroup");
const NotificationAlert = require("../models/NotificationAlert");
const NotificationSummary = require("../models/NotificationSummary");

function clearLine() {
    process.stdout.write("\r\x1b[K");
}

function writeProgress(message) {
    clearLine();
    process.stdout.write(message);
}

function writeLine(message) {
    clearLine();
    process.stdout.write(message + "\n");
}

async function findInactiveVesselAssignments() {
    try {
        writeLine("🔍 Searching for inactive vessels...");
        const vessels = await Vessel.find({ is_active: false });
        if (!vessels.length) {
            writeLine("✅ No inactive vessels found.");
            process.exit(0);
        }
        writeLine(`Found ${vessels.length} inactive vessels. Checking assignments...`);
        let totalAssignments = 0;
        for (let i = 0; i < vessels.length; i++) {
            const vessel = vessels[i];
            const vesselId = vessel._id;
            const vesselInfo = `Vessel: ${vessel.name} (ID: ${vesselId}, Unit: ${vessel.unit_id})`;
            let found = false;
            writeProgress(`Checking assignments for ${vesselInfo}`);

            // Region Groups
            const regionGroups = await RegionGroup.find({ vessel_ids: vesselId });
            if (regionGroups.length > 0) {
                found = true;
                writeLine(`\n${vesselInfo} assigned to Region Groups:`);
                regionGroups.forEach((rg) => writeLine(`  - ${rg.name} (${rg._id})`));
                totalAssignments += regionGroups.length;
            }

            // Invite Tokens
            const inviteTokens = await InviteToken.find({ allowed_vessels: vesselId });
            if (inviteTokens.length > 0) {
                found = true;
                writeLine(`\n${vesselInfo} assigned to Invite Tokens:`);
                inviteTokens.forEach((token) => writeLine(`  - ${token._id}`));
                totalAssignments += inviteTokens.length;
            }

            // API Keys
            const apiKeys = await ApiKey.find({ allowed_vessels: vesselId });
            if (apiKeys.length > 0) {
                found = true;
                writeLine(`\n${vesselInfo} assigned to API Keys:`);
                apiKeys.forEach((apiKey) => writeLine(`  - ${apiKey._id}`));
                totalAssignments += apiKeys.length;
            }

            // Users
            const users = await User.find({ allowed_vessels: vesselId });
            if (users.length > 0) {
                found = true;
                writeLine(`\n${vesselInfo} assigned to Users:`);
                users.forEach((user) => writeLine(`  - ${user.name} (${user._id})`));
                totalAssignments += users.length;
            }

            // Notification Alerts
            const alerts = await NotificationAlert.find({ vessel_ids: vesselId });
            if (alerts.length > 0) {
                found = true;
                writeLine(`\n${vesselInfo} assigned to Notification Alerts:`);
                alerts.forEach((alert) => writeLine(`  - ${alert._id}`));
                totalAssignments += alerts.length;
            }

            // Notification Summary
            const summaries = await NotificationSummary.find({ vessel_ids: vesselId });
            if (summaries.length > 0) {
                found = true;
                writeLine(`\n${vesselInfo} assigned to Notification Summaries:`);
                summaries.forEach((summary) => writeLine(`  - ${summary._id}`));
                totalAssignments += summaries.length;
            }

            if (!found) {
                writeLine(`\n${vesselInfo} is not assigned anywhere.`);
            }
        }
        writeLine(`\n✅ Done. Total assignments found: ${totalAssignments}`);
    } catch (error) {
        writeLine(`❌ Error: ${error.message}`);
        process.exit(1);
    } finally {
        process.exit(0);
    }
}

findInactiveVesselAssignments();
