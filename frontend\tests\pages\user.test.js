import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import Users from "../../src/pages/Dashboard/User/Users";
import axiosInstance from "../../src/axios";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { Add, Delete, Edit } from "@mui/icons-material";
import { getSocket } from "../../src/socket";
import { useApp } from "../../src/hooks/AppHook";
import { useUser } from "../../src/hooks/UserHook";
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import xDataGrid from '@mui/x-data-grid';
import { isMobile } from "react-device-detect";

dayjs.extend(utc);
dayjs.extend(timezone);

jest.mock("@mui/x-data-grid", () => {
    const { DataGrid: OriginalDataGrid } = jest.requireActual("@mui/x-data-grid");
    return {
        DataGrid: (props) => (<>
            {props.slots.noRowsOverlay()}
            <OriginalDataGrid {...props} />
        </>),
    }
});
jest.mock("../../src/axios");
jest.mock("../../src/socket");
jest.mock("../../src/hooks/UserHook");
jest.mock("../../src/hooks/AppHook");
jest.mock("../../src/pages/Dashboard/User/UpdateRoleMenu", () => () => <div>Update Role Menu</div>);
jest.mock("../../src/pages/Dashboard/User/DeleteUserModal", () => () => <div>Delete User Modal</div>);
jest.mock("../../src/pages/Dashboard/User/InviteUserModal", () => () => <div>Invite User Modal</div>);
jest.mock("../../src/pages/Dashboard/User/FilterUserModal", () => () => <div>Filter User Modal</div>);

const mockTheme = createTheme();

const renderWithTheme = (component) => {
    return render(<ThemeProvider theme={mockTheme}>{component}</ThemeProvider>);
};

describe("Users", () => {
    const mockUsers = [
        { _id: "user1", name: "User One", email: "<EMAIL>", role_id: "admin", creation_timestamp: "2024-11-01T00:00:00Z", role: { role_id: "admin", role_name: "Administrator", hierarchy_number: 1 } },
        { _id: "user3", name: null, email: null, role_id: "user", creation_timestamp: "2024-11-02T00:00:00Z", role: { role_id: "user", role_name: "User", hierarchy_number: 2 } },
        { _id: "user2", name: "User Two", email: "<EMAIL>", role_id: "user", creation_timestamp: "2024-11-02T00:00:00Z", role: { role_id: "user", role_name: "User", hierarchy_number: 2 } },
    ];

    const mockRoles = [
        { role_id: "admin", role_name: "Administrator", hierarchy_number: 1 },
        { role_id: "user", role_name: "User", hierarchy_number: 2 },
    ];

    beforeEach(() => {
        useUser.mockReturnValue({ user: mockUsers[0] });
        useApp.mockReturnValue({ screenSize: { xs: false } });
        axiosInstance.get.mockImplementation((url) => {
            if (url === "/users") {
                return Promise.resolve({ data: mockUsers });
            }
            if (url === "/roles") {
                return Promise.resolve({ data: mockRoles });
            }
        });
        getSocket.mockReturnValue({
            on: jest.fn(),
            off: jest.fn(),
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("renders loading state", () => {
        useApp.mockReturnValue({ screenSize: { xs: true } });
        renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={'searchQuery'} />);
        expect(screen.getByRole("progressbar")).toBeInTheDocument();
    });

    it("renders users and roles data", async () => {
        renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={''} />);
        await waitFor(() => expect(screen.getByText("User One")).toBeInTheDocument());
        expect(screen.getByText("User Two")).toBeInTheDocument();
        expect(screen.getByText("Administrator")).toBeInTheDocument();
    });

    it("filters users based on search query", async () => {
        const { rerender } = renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={'user1'} />);
        await waitFor(() => expect(screen.getByText("User One")).toBeInTheDocument());
        rerender(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={'user1'} />);
        await waitFor(() => expect(screen.queryByText("User Two")).not.toBeInTheDocument());
    });

    it("opens and closes the add user modal", async () => {
        renderWithTheme(<Users showAddUser={true} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={''} />);
        expect(screen.queryByText("Invite User Modal")).toBeInTheDocument();
    });

    it("opens and closes the filter user modal", async () => {
        renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={true} setShowFilterModal={jest.fn()} searchQuery={''} />);
        expect(screen.getByText("Filter User Modal")).toBeInTheDocument();
    });

    it("handles role update click", async () => {
        renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={''} />);
        await waitFor(() => expect(screen.getByText("User One")).toBeInTheDocument());
        fireEvent.click(screen.getByText("Administrator"));
        expect(screen.getByText("Update Role Menu")).toBeInTheDocument();
    });

    it("handles delete user click", async () => {
        renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={''} />);
        await waitFor(() => expect(screen.getByText("User One")).toBeInTheDocument());
        fireEvent.click(screen.getAllByRole("button", { name: /delete/i })[0]);
        expect(screen.getByText("Delete User Modal")).toBeInTheDocument();
    });

    it("displays no data message when no users are found", async () => {
        axiosInstance.get.mockImplementation((url) => {
            if (url === "/users") {
                return Promise.resolve({ data: [] });
            }
            if (url === "/roles") {
                return Promise.resolve({ data: mockRoles });
            }
        });

        renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={''} />);
        await waitFor(() => expect(screen.getByText("No data available")).toBeInTheDocument());
    });

    it("displays error message when fetching users fails", async () => {
        axiosInstance.get.mockImplementation((url) => {
            if (url === "/users") {
                return Promise.reject(new Error("Failed to fetch users"));
            }
            if (url === "/roles") {
                return Promise.resolve({ data: mockRoles });
            }
        });

        renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={''} />);
        expect(axiosInstance.get).toHaveBeenCalled();
    });

    it("displays no data message when no roles are found", async () => {
        axiosInstance.get.mockImplementation((url) => {
            if (url === "/users") {
                return Promise.resolve({ data: mockUsers });
            }
            if (url === "/roles") {
                return Promise.resolve({ data: [] });
            }
        });

        renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={''} />);
        await waitFor(() => expect(screen.getByText("No data available")).toBeInTheDocument());
    });

    it("displays error message when fetching roles fails", async () => {
        axiosInstance.get.mockImplementation((url) => {
            if (url === "/users") {
                return Promise.resolve({ data: mockUsers });
            }
            if (url === "/roles") {
                return Promise.reject(new Error("Failed to fetch roles"));
            }
        });

        renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={''} />);
        expect(axiosInstance.get).toHaveBeenCalled();
    });

    it("filters users based on email search query", async () => {
        const { rerender } = renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={'<EMAIL>'} />);
        await waitFor(() => expect(screen.getByText("User One")).toBeInTheDocument());
        rerender(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={'<EMAIL>'} />);
        await waitFor(() => expect(screen.queryByText("User Two")).not.toBeInTheDocument());
    });

    it("checks role updation condition", async () => {
        useApp.mockReturnValue({ isMobile: true });
        useUser.mockReturnValue({ user: mockUsers[1] });
        renderWithTheme(<Users showAddUser={false} setShowAddUser={jest.fn()} showFilterModal={false} setShowFilterModal={jest.fn()} searchQuery={''} />);
        await waitFor(() => expect(screen.getByText("User One")).toBeInTheDocument());
        fireEvent.click(screen.getByText("Administrator"));
        expect(screen.getByText("Update Role Menu")).toBeInTheDocument();
    });
});
