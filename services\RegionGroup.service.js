const RegionGroup = require("../models/RegionGroup");
const vesselService = require("../services/Vessel.service");
const { isValidObjectId, default: mongoose } = require("mongoose");
const streamService = require("./Stream.service");
const { normalizeName } = require("../utils/functions");
class RegionGroupService {
    async find(query, projection) {
        const pipeline = [];
        if (query) {
            pipeline.push({ $match: query });
        }
        if (projection) {
            pipeline.push({ $project: projection });
        }
        pipeline.push({
            $lookup: {
                from: "users",
                localField: "created_by",
                foreignField: "_id",
                as: "created_by",
                pipeline: [
                    {
                        $project: {
                            _id: 1,
                            name: 1,
                        },
                    },
                ],
            },
        });
        pipeline.push({
            $unwind: "$created_by",
        });

        const regionGroups = await RegionGroup.aggregate(pipeline);

        const vessels = await vesselService.find({}, { _id: 1, name: 1, unit_id: 1, region_group_id: 1 });

        const updatedRegionGroups = regionGroups.map((regionGroup) => {
            // const vesselsInRegionGroup = vessels.filter((vessel) => regionGroup.vessel_ids?.includes(vessel._id.toString()));
            const vesselsInRegionGroup = vessels.filter((vessel) => regionGroup._id.toString() === vessel.region_group_id?.toString());

            return {
                ...regionGroup,
                vessels: vesselsInRegionGroup.map((vessel) => ({
                    vessel_id: vessel._id.toString(),
                    unit_id: vessel.unit_id,
                    name: vessel.name,
                })),
            };
        });

        return updatedRegionGroups;
    }

    async findById({ id }) {
        if (!isValidObjectId(id)) throw new Error("Invalid region group id");
        const regionGroup = await RegionGroup.aggregate([
            {
                $match: {
                    _id: new mongoose.Types.ObjectId(id),
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "created_by",
                    foreignField: "_id",
                    as: "created_by",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                            },
                        },
                    ],
                },
            },
            {
                $unwind: "$created_by",
            },
        ]);

        if (!regionGroup.length) return null;

        const vessels = await vesselService.find({}, { _id: 1, name: 1, unit_id: 1, region_group_id: 1 });
        const vesselsInRegionGroup = vessels.filter((vessel) => regionGroup[0]._id.toString() === vessel.region_group_id?.toString());
        // const vesselsInRegionGroup = vessels.filter((vessel) => regionGroup[0].vessel_ids.includes(vessel._id.toString()) );

        return {
            ...regionGroup[0],
            vessels: vesselsInRegionGroup.map((vessel) => ({
                vessel_id: vessel._id.toString(),
                unit_id: vessel.unit_id,
                name: vessel.name,
            })),
        };
    }

    async create({ name, timezone, created_by }) {
        const regionGroup = await RegionGroup.create({ name, timezone, created_by });
        streamService.resetCache();
        return await this.findById({ id: regionGroup._id });
    }

    async update({ id, name, timezone }) {
        if (!isValidObjectId(id)) throw new Error("Invalid region group id");

        const data = { name, timezone };
        Object.keys(data).forEach((key) => {
            if (data[key] === undefined) delete data[key];
        });

        data.name = normalizeName(data.name);
        const regionGroup = await RegionGroup.findByIdAndUpdate(id, data);
        streamService.resetCache();
        return await this.findById({ id: regionGroup._id });
    }

    async delete({ id }) {
        const vessels = await vesselService.find({ region_group_id: id }, { _id: 1 });
        if (vessels.length > 0) throw { status: 409, message: "Cannot delete region group: vessels are associated with this group." };

        const data = await RegionGroup.findByIdAndDelete(id);
        streamService.resetCache();
        return data;
    }
}

const regionGroupService = new RegionGroupService();

module.exports = regionGroupService;
