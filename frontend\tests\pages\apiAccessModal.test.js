import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import ApiAccessModal from "../../src/pages/Dashboard/ApiKeys//ApiAccessModal";
import axiosInstance from "../../src/axios";

jest.mock("../../src/axios", () => ({
    patch: jest.fn(),
}));

describe("ApiAccessModal", () => {
    let setUpdateKeyAccess, endpoints;

    beforeEach(() => {
        setUpdateKeyAccess = jest.fn();
        endpoints = [
            { endpoint_id: "1", name: "Endpoint 1", category: "Category 1", is_public: false },
            { endpoint_id: "2", name: "Endpoint 2", category: "Category 1", is_public: true },
            { endpoint_id: "3", name: "Endpoint 3", category: "Category 2", is_public: false },
        ];
    });

    it("renders the modal with category and endpoint checkboxes", () => {
        render(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: ["1"] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={endpoints}
            />
        );
        expect(screen.getByText("Update API Access #1")).toBeInTheDocument();
        expect(screen.getByLabelText("Category 1")).toBeInTheDocument();
        expect(screen.getByLabelText("Endpoint 1")).toBeInTheDocument();
        expect(screen.getByLabelText("Endpoint 2")).toBeInTheDocument();
    });

    it("toggles category checkbox and shows endpoints", () => {
        render(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: ["1"] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={endpoints}
            />
        );
        const categoryCheckbox = screen.getByLabelText("Category 1");

        fireEvent.click(categoryCheckbox);
        fireEvent.click(categoryCheckbox);

        expect(screen.getByLabelText("Endpoint 1")).toBeInTheDocument();
        expect(screen.getByLabelText("Endpoint 2")).toBeInTheDocument();
    });

    it("checks/unchecks individual endpoints when category is unchecked", async () => {
        render(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: ["1"] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={endpoints}
            />
        );
        const endpointCheckbox = screen.getByLabelText("Endpoint 1");
        fireEvent.click(endpointCheckbox);

        await waitFor(() => {
            expect(endpointCheckbox).not.toBeChecked();
        });

        fireEvent.click(endpointCheckbox);

        await waitFor(() => {
            expect(screen.getByLabelText("Endpoint 1")).toBeChecked();
        });
    });

    it("enables save button when changes are made", async () => {
        render(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: ["1"] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={endpoints}
            />
        );
        const endpointCheckbox = screen.getByLabelText("Endpoint 1");
        fireEvent.click(endpointCheckbox);

        await waitFor(() => {
            expect(screen.getByText("Save")).toBeEnabled();
        });
    });

    it("disables save button while saving", async () => {
        render(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: ["1"] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={endpoints}
            />
        );
        axiosInstance.patch.mockResolvedValueOnce({});
        const endpointCheckbox = screen.getByLabelText("Endpoint 1");
        fireEvent.click(endpointCheckbox);

        await waitFor(() => {
            expect(screen.getByText("Save")).toBeEnabled();
        });

        fireEvent.click(screen.getByText("Save"));

        expect(screen.getByText("Save").querySelector('svg')).toBeInTheDocument();
        expect(screen.getByText("Save")).toBeDisabled();
    });

    it("calls onReset and restores initial state when Reset button is clicked", async () => {
        render(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: ["1"] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={endpoints}
            />
        );
        const endpointCheckbox = screen.getByLabelText("Endpoint 1");
        fireEvent.click(endpointCheckbox);
        expect(screen.getByText("Save")).toBeEnabled();

        fireEvent.click(screen.getByText("Reset"));

        await waitFor(() => {
            expect(screen.getByLabelText("Endpoint 1")).toBeChecked();
            expect(screen.getByText("Save")).toBeDisabled();
        });
    });

    it("calls handleClose when modal is closed", async () => {
        render(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: ["1"] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={[]}
            />
        );
        fireEvent.click(screen.getByTestId("CloseIcon"));
        await waitFor(() => {
            expect(setUpdateKeyAccess).toHaveBeenCalledWith();
        });
    });

    it("disables save button if no changes are made", async () => {
        render(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: ["1"] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={endpoints}
            />
        );
        const saveButton = screen.getByText("Save");
        expect(saveButton).toBeDisabled();
        fireEvent.click(screen.getByLabelText("Endpoint 1"));
        fireEvent.click(screen.getByText("Reset"));
        expect(saveButton).toBeDisabled();
    });

    it("toggles category expansion and icon on IconButton click", () => {
        const newEndpoints = [
            { endpoint_id: "1", name: "Endpoint 1", category: "Category 1", is_public: false },
        ];
        render(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: ["1"] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={newEndpoints}
            />
        );
        const categoryCheckbox = screen.getByLabelText("Category 1");
        fireEvent.click(categoryCheckbox);

        const removeButton = screen.getByTestId("RemoveIcon");
        fireEvent.click(removeButton);

        expect(screen.getByLabelText("Endpoint 1")).toBeInTheDocument();

        const addButton = screen.getByTestId("AddIcon");
        fireEvent.click(addButton);

        expect(screen.queryByLabelText("Endpoint 1")).toBeInTheDocument();
    });

    it("does not open the modal when updatedKey is falsy", () => {
        render(
            <ApiAccessModal
                updateKeyAccess={null}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={endpoints}
            />
        );
        expect(screen.queryByText("Update API Access")).not.toBeInTheDocument();
    });

    it("updates expandedCategories when endpoints prop is updated", () => {
        const { rerender } = render(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: ["1"] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={endpoints}
            />
        );

        expect(screen.getByLabelText("Category 1")).toBeInTheDocument();

        const newEndpoints = [
            { endpoint_id: "4", name: "New Endpoint", category: "Category 3", is_public: false },
        ];
        rerender(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: ["1"] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={newEndpoints}
            />
        );

        expect(screen.getByLabelText("Category 3")).toBeInTheDocument();
        expect(screen.getByLabelText("New Endpoint")).toBeInTheDocument();
    });


    it("sorts categories correctly based on their enabled/disabled status", () => {
        const newEndpoints = [
            { endpoint_id: "1", name: "Endpoint 1", category: "Category 1", is_public: false },
            { endpoint_id: "2", name: "Endpoint 2", category: "Category 2", is_public: true },
            { endpoint_id: "3", name: "Endpoint 3", category: "Category 3", is_public: false },
            { endpoint_id: "4", name: "Endpoint 4", category: "Category 2", is_public: true },
        ];

        render(
            <ApiAccessModal
                updateKeyAccess={{ _id: "123", serial: 1, allowed_endpoints: [] }}
                setUpdateKeyAccess={setUpdateKeyAccess}
                endpoints={newEndpoints}
            />
        );

        const categories = screen.getAllByRole("checkbox", { name: /Category/i }).map((checkbox) => checkbox.labels[0].textContent);

        expect(categories).toEqual(["Category 1", "Category 3", "Category 2"]);
    });
});
