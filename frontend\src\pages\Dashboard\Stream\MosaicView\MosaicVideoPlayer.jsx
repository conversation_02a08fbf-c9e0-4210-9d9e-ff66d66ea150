import React, { useRef, useEffect, useState } from "react";
import videojs from "video.js";
import "video.js/dist/video-js.css";
import { Box, Grid, IconButton, Slider, Tooltip } from "@mui/material";
import { Pause, PlayArrow, Fullscreen } from "@mui/icons-material";

const MosaicVideoPlayer = ({ hlsUrl, streamMode, totalDuration }) => {
    console.log("HLS URL:", hlsUrl);
    console.log("Stream Mode:", streamMode);
    console.log("Total Duration:", totalDuration);

    const videoNode = useRef(null);
    const videoContainerRef = useRef(null);
    const player = useRef(null);
    const scrubBarRef = useRef();
    const [isPlayingVideo, setIsPlayingVideo] = useState(true);
    const [hoverTime, setHoverTime] = useState(null);
    const [playBack, setPlayBack] = useState({
        offset: 0,
        currentVideoPlayTime: 0,
    });
    const [isFullscreen, setIsFullScreen] = useState(false);
    const call = useRef(false);

    useEffect(() => {
        if (hlsUrl) {
            if (player.current) {
                player.current.src({ type: "application/x-mpegURL", src: hlsUrl });
            } else {
                player.current = videojs(videoNode.current, {
                    autoplay: true,
                    muted: true,
                    controls: true,
                    liveui: true,
                    techOrder: ["html5"],
                    controlBar: false,
                    Livetracker: true,
                    userActions: {
                        doubleClick: false,
                    },
                    sources: [
                        {
                            src: hlsUrl,
                            type: "application/x-mpegURL",
                        },
                    ],
                    html5: {
                        hlsjsConfig: {
                            enableWorker: true,
                            maxBufferSize: 60 * 1000 * 15, // 15 MB
                            maxBufferLength: 30, // Buffer up to 30 seconds
                            liveBackBufferLength: 15, // Allow 15 seconds of back buffer
                            liveSyncDurationCount: 3, // Sync to the latest 3 segments for live streams
                        },
                        vhs: {
                            liveLLHLS: true,
                            targetLatency: 2,
                            smoothQualityChange: true,
                        },
                    },
                });

                player.current.on("play", handlePlayEvent);
                player.current.on("pause", handlePauseEvent);
                player.current.on("fullscreenchange", () => handleFullscreenChange(player));
                player.current.on("error", () => {
                    console.error("Video.js error:", player.current.error());
                });
            }
        }
    }, [hlsUrl]);

    const handlePlayEvent = () => {
        setIsPlayingVideo(true);
    };

    const handlePauseEvent = () => {
        setIsPlayingVideo(false);
    };

    const handleFullscreenChange = (player) => {
        player;
        setIsFullScreen(videoContainerRef.current.fullscreenElement != null);
        if (videoContainerRef.current.fullscreenElement) {
            videoContainerRef.current.el().style.zIndex = -1;
        }
    };

    const handleHover = (event) => {
        const scrubBar = scrubBarRef.current;
        const hoverPosition = event.nativeEvent.offsetX;
        const scrubWidth = scrubBar.offsetWidth;

        const hoveredTime = (hoverPosition / scrubWidth) * totalDuration;
        setHoverTime(hoveredTime);
    };

    const handlePause = () => {
        if (player.current) {
            if (!player.current.paused()) {
                player.current.pause();
            } else {
                player.current.play();
            }
            setIsPlayingVideo(!isPlayingVideo);
        }
    };

    const handleScrub = (event, newValue) => {
        const video = videoNode.current;
        if (video) {
            const clickedTime = (newValue / 100) * totalDuration;

            if (clickedTime > 4500) {
                setPlayBack(() => ({
                    offset: clickedTime,
                    currentVideoPlayTime: 0,
                }));
                call.current = true;
            } else {
                setPlayBack(() => ({
                    offset: 0,
                    currentVideoPlayTime: clickedTime > 4500 ? 0 : clickedTime,
                }));
            }
            video.currentTime = clickedTime;
        }
    };

    const formatTime = (timeInSeconds) => {
        const startDate = new Date(Date.now() - totalDuration * 1000);
        const pastDate = new Date(startDate.getTime() + timeInSeconds * 1000);
        const formattedDate = `${pastDate.getFullYear()}-${(pastDate.getMonth() + 1).toString().padStart(2, "0")}-${pastDate.getDate().toString().padStart(2, "0")}`;
        return (
            <Grid sx={{ textAlign: "center" }}>
                <Grid>({formattedDate})</Grid>
            </Grid>
        );
    };

    const handleTimeUpdate = () => {
        const video = videoNode.current;
        if (video) {
            const currentTime = video.currentTime;
            if (call.current) {
                setPlayBack((prev) => ({
                    offset: prev.offset,
                    currentVideoPlayTime: 0,
                }));
                call.current = false;
            } else {
                setPlayBack((prev) => ({
                    offset: prev.offset,
                    currentVideoPlayTime: currentTime > 4500 ? 0 : currentTime,
                }));
            }
        }
    };

    useEffect(() => {
        const video = videoNode.current;

        if (video && streamMode === "ON_DEMAND") {
            video.addEventListener("timeupdate", handleTimeUpdate);
        }
        return () => {
            if (video && streamMode === "ON_DEMAND") {
                video.removeEventListener("timeupdate", handleTimeUpdate);
            }
        };
    }, [streamMode]);

    const handleFullscreen = () => {
        if (videoContainerRef.current) {
            if (!document.fullscreenElement) {
                videoContainerRef.current.requestFullscreen();
                setIsFullScreen(true);
            } else {
                document.exitFullscreen();
                setIsFullScreen(false);
            }
        }
    };

    return (
        <Grid minHeight={{ xs: 300, lg: "auto" }} ref={videoContainerRef} onDoubleClick={handleFullscreen} size="grow">
            <video
                ref={videoNode}
                id="video"
                className="video-js vjs-default-skin"
                preload="auto"
                style={{
                    width: "100%",
                    height: isFullscreen ? "100%" : "95%",
                }}
            />
            <Box
                sx={{
                    borderRadius: isFullscreen ? 0 : "10px",
                    position: "absolute",
                    width: "100%",
                    bottom: "0",
                    display: "flex", // Ensure it has display flex for positioning children correctly
                    zIndex: 100000, // Ensure it is above other video elements
                }}
            >
                <Grid
                    container
                    alignItems="center"
                    gap={1}
                    sx={{
                        position: "static",
                        bottom: 0,
                        width: "100%",
                        paddingX: 1,
                        paddingBottom: 0.5,
                        paddingTop: isFullscreen ? 0.5 : 0,
                        marginTop: -0.5,
                        visibility: isFullscreen ? "visible" : "visible",
                        height: "auto", // Make sure height is explicitly set
                    }}
                >
                    <Grid display={"flex"} justifyContent={"center"} alignItems={"center"}>
                        <IconButton onClick={handlePause} sx={{ color: "white", padding: 0 }}>
                            {isPlayingVideo ? <Pause sx={{ fontSize: 15 }} /> : <PlayArrow sx={{ fontSize: 15 }} />}
                        </IconButton>
                    </Grid>
                    {streamMode === "ON_DEMAND" ? (
                        <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} sx={{ padding: "15px 0" }} size="grow">
                            <div
                                ref={scrubBarRef}
                                style={{
                                    position: "relative",
                                    width: "100%",
                                    cursor: "pointer",
                                    display: "flex",
                                }}
                                onMouseMove={handleHover}
                            >
                                <Tooltip open={hoverTime !== null} title={formatTime(hoverTime || 0)} placement="top" arrow followCursor>
                                    <Slider
                                        value={((playBack.offset + playBack.currentVideoPlayTime) / totalDuration) * 100}
                                        valueLabelDisplay="off"
                                        onMouseLeave={() => setHoverTime(null)}
                                        onChangeCommitted={handleScrub}
                                        sx={{
                                            "&.MuiSlider-root": { height: 4, padding: 0 },
                                            "& .MuiSlider-thumb": { height: 10, width: 10 },
                                            "& .MuiSlider-thumb::after": { content: "none" },
                                        }}
                                    />
                                </Tooltip>
                            </div>
                        </Grid>
                    ) : (
                        <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} size="grow">
                            <div
                                style={{
                                    position: "relative",
                                    width: "100%",
                                }}
                            ></div>
                        </Grid>
                    )}
                    <Grid display={"flex"} justifyContent={"center"} alignItems={"center"}>
                        <IconButton onClick={handleFullscreen} sx={{ color: "white", padding: 0 }}>
                            <Fullscreen sx={{ fontSize: 15 }} />
                        </IconButton>
                    </Grid>
                </Grid>
            </Box>
        </Grid>
    );
};

export default MosaicVideoPlayer;
