const mongoose = require("mongoose");
const db = require("../modules/db");
const ioEmitter = require("../modules/ioEmitter");

const sessionLogSchema = new mongoose.Schema({
    socket_id: { type: String, required: true },
    device: { type: String, required: true },
    browser: { type: String, required: true },
    user_id: { type: mongoose.Schema.Types.ObjectId, required: true },
    disconnect_timestamp: { type: Date, default: null },
    connect_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
    environment: { type: String, required: true, default: () => process.env.NODE_ENV },
});

sessionLogSchema.post("save", emitChangedEvent);

sessionLogSchema.post("findOneAndDelete", emitChangedEvent);

function emitChangedEvent(log) {
    ioEmitter.emit("notifyAll", { name: `logs/changed`, data: log.toObject() });
}

const SessionLog = db.qm.model("SessionLog", sessionLogSchema, "logs_sessions");

module.exports = SessionLog;
module.exports.emitChangedEvent = process.env.NODE_ENV === "test" && emitChangedEvent;
