require("dotenv").config();
const Vessel = require("../models/Vessel");
const db = require("../modules/db");

const dryRun = process.argv.includes("--dry-run");

// Progress bar utilities
function createProgressBar(current, total, barLength = 40) {
    const percentage = Math.round((current / total) * 100);
    const filledLength = Math.round((current / total) * barLength);
    const bar = "█".repeat(filledLength) + "░".repeat(barLength - filledLength);
    return `[${bar}] ${percentage}% (${current}/${total})`;
}

function clearLine() {
    process.stdout.write("\r\x1b[K");
}

function writeProgress(message) {
    clearLine();
    process.stdout.write(message);
}

function writeLine(message) {
    clearLine();
    process.stdout.write(message + "\n");
}

function convertUnitsToVessels(allowedUnits, unitToVesselMap) {
    if (!Array.isArray(allowedUnits)) return [];

    const vesselIds = [];
    const unmappedUnits = [];

    allowedUnits.forEach((unitObj) => {
        if (typeof unitObj === "object" && unitObj.unit_id) {
            const vesselId = unitToVesselMap.get(unitObj.unit_id);
            if (vesselId) {
                vesselIds.push(vesselId);
            } else {
                unmappedUnits.push(unitObj.unit_id);
            }
        } else if (typeof unitObj === "string") {
            const vesselId = unitToVesselMap.get(unitObj);
            if (vesselId) {
                vesselIds.push(vesselId);
            } else {
                unmappedUnits.push(unitObj);
            }
        }
    });

    if (unmappedUnits.length > 0) {
        writeLine(`⚠️  Could not map unit_ids to vessels: ${unmappedUnits.join(", ")}`);
    }

    return vesselIds;
}

async function migrateUsers(unitToVesselMap) {
    writeLine("\n👥 Migrating users...");
    const users = await db.qm
        .collection("users")
        .find({ is_deleted: { $ne: true } })
        .toArray();
    let updated = 0;

    for (let i = 0; i < users.length; i++) {
        const user = users[i];
        const progress = createProgressBar(i + 1, users.length);
        writeProgress(`👥 Processing users... ${progress}`);

        try {
            if (user.allowed_units) {
                const allowedVessels = convertUnitsToVessels(user.allowed_units, unitToVesselMap);
                if (allowedVessels.length > 0 || (user.allowed_units && user.allowed_units.length === 0)) {
                    if (!dryRun) {
                        await db.qm
                            .collection("users")
                            .updateOne({ _id: user._id }, { $set: { allowed_vessels: allowedVessels }, $unset: { allowed_units: "" } });
                    }
                    updated++;
                }
            }
        } catch (error) {
            writeLine(`❌ Error migrating user ${user._id}: ${error.message}`);
        }
    }

    writeLine(`✅ Users migration completed: ${updated} updated`);
    return updated;
}

async function migrateApiKeys(unitToVesselMap) {
    writeLine("\n🔑 Migrating API keys...");
    const apiKeys = await db.qm
        .collection("apikeys")
        .find({ is_deleted: { $ne: true } })
        .toArray();
    let updated = 0;

    for (let i = 0; i < apiKeys.length; i++) {
        const apiKey = apiKeys[i];
        const progress = createProgressBar(i + 1, apiKeys.length);
        writeProgress(`🔑 Processing API keys... ${progress}`);

        try {
            if (apiKey.allowed_units) {
                const allowedVessels = convertUnitsToVessels(apiKey.allowed_units, unitToVesselMap);
                if (allowedVessels.length > 0 || (apiKey.allowed_units && apiKey.allowed_units.length === 0)) {
                    if (!dryRun) {
                        await db.qm
                            .collection("apikeys")
                            .updateOne({ _id: apiKey._id }, { $set: { allowed_vessels: allowedVessels }, $unset: { allowed_units: "" } });
                    }
                    updated++;
                }
            }
        } catch (error) {
            writeLine(`❌ Error migrating API key ${apiKey._id}: ${error.message}`);
        }
    }

    writeLine(`✅ API keys migration completed: ${updated} updated`);
    return updated;
}

async function migrateInviteTokens(unitToVesselMap) {
    writeLine("\n📧 Migrating invite tokens...");
    const inviteTokens = await db.qm
        .collection("invitetokens")
        .find({ is_deleted: { $ne: true } })
        .toArray();
    let updated = 0;

    for (let i = 0; i < inviteTokens.length; i++) {
        const token = inviteTokens[i];
        const progress = createProgressBar(i + 1, inviteTokens.length);
        writeProgress(`📧 Processing invite tokens... ${progress}`);

        try {
            if (token.allowed_units) {
                const allowedVessels = convertUnitsToVessels(token.allowed_units, unitToVesselMap);
                if (allowedVessels.length > 0 || (token.allowed_units && token.allowed_units.length === 0)) {
                    if (!dryRun) {
                        await db.qm
                            .collection("invitetokens")
                            .updateOne({ _id: token._id }, { $set: { allowed_vessels: allowedVessels }, $unset: { allowed_units: "" } });
                    }
                    updated++;
                }
            }
        } catch (error) {
            writeLine(`❌ Error migrating invite token ${token._id}: ${error.message}`);
        }
    }

    writeLine(`✅ Invite tokens migration completed: ${updated} updated`);
    return updated;
}

async function migrateAllowedUnitsToVessels() {
    try {
        writeLine("🚀 Starting migration from allowed_units to allowed_vessels...");
        if (dryRun) {
            writeLine("🔍 DRY RUN MODE - No changes will be made");
        }

        // Create mapping from unit_id to vessel ObjectId (only active vessels)
        writeProgress("📋 Creating unit_id to vessel_id mapping...");
        const vessels = await Vessel.find({ unit_id: { $ne: null, $exists: true }, is_active: true }, { _id: 1, unit_id: 1, name: 1 });

        const unitToVesselMap = new Map();
        vessels.forEach((vessel) => {
            unitToVesselMap.set(vessel.unit_id, vessel._id);
        });

        writeLine(`✅ Created mapping for ${unitToVesselMap.size} vessels`);

        // Run migrations for each collection
        const usersUpdated = await migrateUsers(unitToVesselMap);
        const apiKeysUpdated = await migrateApiKeys(unitToVesselMap);
        const inviteTokensUpdated = await migrateInviteTokens(unitToVesselMap);

        const totalUpdated = usersUpdated + apiKeysUpdated + inviteTokensUpdated;

        // Final summary
        writeLine("\n📊 Migration Summary:");
        writeLine("=".repeat(50));
        writeLine(`👥 Users: ${usersUpdated} updated`);
        writeLine(`🔑 API Keys: ${apiKeysUpdated} updated`);
        writeLine(`📧 Invite Tokens: ${inviteTokensUpdated} updated`);
        writeLine(`📈 Total Records: ${totalUpdated} updated`);
        writeLine("=".repeat(50));

        if (dryRun) {
            writeLine("🔍 DRY RUN COMPLETED - No actual changes were made");
        } else {
            writeLine("✅ MIGRATION COMPLETED SUCCESSFULLY!");
        }
    } catch (error) {
        writeLine(`❌ Migration failed: ${error.message}`);
        process.exit(1);
    } finally {
        process.exit(0);
    }
}

migrateAllowedUnitsToVessels();
