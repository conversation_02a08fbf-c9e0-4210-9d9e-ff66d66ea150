require("dotenv").config();
const db = require("../modules/db");
const SessionLog = require("../models/SessionLog");

const environment = "portal";

async function migrateData() {
    const session = await db.qm.startSession();
    session.startTransaction();
    try {
        console.log("Starting migration...");

        // Step 1: Add connect_timestamp to connect logs (with aggregation-style update)
        const connectLogsUpdate = await SessionLog.updateMany({ type: "connect", connect_timestamp: { $exists: false }, environment }, [
            { $set: { connect_timestamp: "$timestamp", disconnect_timestamp: null } },
        ]);
        console.log(`Updated ${connectLogsUpdate.modifiedCount} connect logs with connect_timestamp.`);

        // Step 2: Find all disconnect logs
        const disconnectLogs = await SessionLog.find({ type: "disconnect", environment }).lean();

        const socketIds = disconnectLogs.map((log) => log.socket_id);

        // Step 3: Find all connect logs with matching socket_ids
        const connectLogs = await SessionLog.find({
            socket_id: { $in: socketIds },
            type: "connect",
            environment,
        }).lean();

        const connectMap = new Map();
        for (const log of connectLogs) {
            connectMap.set(log.socket_id, log.timestamp);
        }

        // Step 4: Prepare bulkWrite updates
        const bulkUpdates = [];
        for (const disconnect of disconnectLogs) {
            const connectTimestamp = connectMap.get(disconnect.socket_id);
            if (connectTimestamp) {
                bulkUpdates.push({
                    updateMany: {
                        filter: { socket_id: disconnect.socket_id, environment },
                        update: {
                            $set: {
                                connect_timestamp: connectTimestamp,
                                disconnect_timestamp: disconnect.timestamp,
                            },
                        },
                    },
                });
            }
        }

        if (bulkUpdates.length) {
            const bulkResult = await SessionLog.bulkWrite(bulkUpdates);
            console.log(`Bulk updated ${bulkResult.modifiedCount} logs with timestamps.`);
        }

        // Step 5: Delete disconnect logs
        const deleteResult = await SessionLog.deleteMany({ type: "disconnect", environment });
        console.log(`Deleted ${deleteResult.deletedCount} disconnect logs.`);

        // Step 6: Remove 'timestamp' and 'type' fields
        const unsetFields = await SessionLog.collection.updateMany({ environment }, { $unset: { timestamp: "", type: "" } });
        console.log(`Removed 'timestamp' and 'type' from ${unsetFields.modifiedCount} documents.`);

        await session.commitTransaction();
        console.log("Migration completed successfully!");
    } catch (error) {
        await session.abortTransaction();
        console.error("Migration failed:", error);
    } finally {
        session.endSession();
        process.exit(0);
    }
}

migrateData();
