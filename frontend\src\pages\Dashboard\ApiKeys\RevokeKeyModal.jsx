import { <PERSON>dal, <PERSON>rid, Typo<PERSON>, Button } from "@mui/material";
import ModalContainer from "../../../components/ModalContainer";
import apiKeyController from "../../../controllers/ApiKey.controller";

const RevokeKeyModal = ({ revokeKey, setRevoke<PERSON>ey, setRevoking, fetchKeys }) => {
    const handleClose = () => {
        setRevokeKey();
    };

    const onSubmit = async () => {
        try {
            setRevoking(revokeKey._id);
            await apiKeyController.revoke({ id: revokeKey._id, revoke: !revokeKey.is_revoked });
            fetchKeys();
            handleClose();
        } catch (error) {
            console.error("Error revoking API key:", error);
        } finally {
            setRevoking(null);
        }
    };

    return (
        <Modal open={revokeKey ? true : false} onClose={handleClose}>
            <ModalContainer title={`${revokeKey?.is_revoked ? "Restore" : "Revoke"} Key`} headerPosition="center">
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: "auto" }}>
                    <Grid>
                        <Typography fontWeight={"500"} fontSize={"15px"}>
                            Are you sure you want to {revokeKey?.is_revoked ? "restore" : "revoke"} API key #{revokeKey?.serial}?
                        </Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" onClick={handleClose} className="btn-cancel">
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button
                                variant="contained"
                                sx={{ textTransform: "none", padding: "10px 24px" }}
                                color={revokeKey?.is_revoked ? "success" : "warning"}
                                onClick={onSubmit}
                            >
                                {revokeKey?.is_revoked ? "Restore" : "Revoke"}
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default RevokeKeyModal;
