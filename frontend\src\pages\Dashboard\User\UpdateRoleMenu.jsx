import { useState } from "react";
import axiosInstance from "../../../axios";
import { alpha, Menu, MenuItem } from "@mui/material";
import { permissions, roles as roleIds } from "../../../utils";
import ConfirmModal from "../../../components/ConfirmModal";
import { useUser } from "../../../hooks/UserHook";
import theme from "../../../theme";

const UpdateRoleMenu = ({ updateRoleAnchorEl, setUpdateRoleAnchorEl, roles, selectedUser, setUpdatingRole, onSuccess, organizations }) => {
    const { user } = useUser();
    const open = Boolean(updateRoleAnchorEl);
    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);
    const [confirmModalDetails, setConfirmModalDetails] = useState({ title: "", message: "" });

    const handleClick = async (role) => {
        if (role.role_id === roleIds.admin) {
            const confirmed = await openConfirmationDialog({
                title: "Confirm",
                message: (
                    <>
                        Are you sure you want to assign <b>Admin</b> role to <b>{selectedUser.name}</b>? This change is irreversible.
                    </>
                ),
            });

            if (!confirmed) {
                handleClose();
                return;
            }
        }
        setUpdatingRole(selectedUser._id);
        axiosInstance
            .patch(`/users/${selectedUser._id}/role`, { role_id: role.role_id }, { meta: { showSnackbar: true } })
            .then(() => onSuccess && onSuccess())
            .catch(console.error)
            .finally(() => setUpdatingRole());
        handleClose();
    };

    const openConfirmationDialog = ({ title, message }) => {
        return new Promise((resolve) => {
            const handleConfirm = () => {
                resolve(true);
                setConfirmModalOpen(false);
            };

            const handleCancel = () => {
                resolve(false);
                setConfirmModalOpen(false);
            };

            setConfirmModalDetails({ title, message, onConfirm: handleConfirm, onCancel: handleCancel });
            setConfirmModalOpen(true);
        });
    };

    const handleClose = () => {
        setUpdateRoleAnchorEl(null);
    };

    function isUpdateable(role) {
        if (role.role_id === selectedUser.role_id) return true;
        if (user.role.hierarchy_number >= role.hierarchy_number) return true;
        if (
            organizations.find((org) => org._id == selectedUser.organization_id)?.is_miscellaneous &&
            !role.denied_permissions.includes(permissions.manageUsers)
        )
            return true;
        return false;
    }

    return (
        <>
            <Menu
                anchorEl={updateRoleAnchorEl}
                open={open}
                onClose={handleClose}
                sx={{
                    "& .MuiPaper-root": {
                        minWidth: updateRoleAnchorEl ? updateRoleAnchorEl.offsetWidth : "auto",
                        backgroundColor: theme.palette.primary.main,
                    },
                }}
                anchorOrigin={{
                    vertical: 35,
                    horizontal: 0,
                }}
            >
                {roles
                    .slice()
                    .sort((a, b) => a.hierarchy_number - b.hierarchy_number)
                    .map((role) => (
                        <MenuItem
                            key={role.role_id}
                            disabled={isUpdateable(role)}
                            onClick={() => handleClick(role)}
                            sx={{
                                gap: 1,
                                "&:hover": {
                                    backgroundColor: alpha(theme.palette.custom.darkBlue, 0.3) + " !important",
                                },
                            }}
                        >
                            {role.role_name}
                        </MenuItem>
                    ))}
            </Menu>
            <ConfirmModal
                title={confirmModalDetails.title}
                message={confirmModalDetails.message}
                initialState={isConfirmModalOpen}
                onClose={confirmModalDetails.onCancel}
                onConfirm={confirmModalDetails.onConfirm}
                isDanger={true}
            />
        </>
    );
};

export default UpdateRoleMenu;
