import axiosInstance from "../axios";

class EmailsDomainsController {
    async getAllAllowedEmailDomains() {
        try {
            const res = await axiosInstance.get(`/emailsDomain`);
            return res;
        } catch (error) {
            console.log("Error Get All Allowed Emails Domains " + error);
            return error.response.data;
        }
    }
}

const emailsDomainsController = new EmailsDomainsController();

export default emailsDomainsController;
