const express = require("express");
const router = express.Router();
const assignEndpointId = require("../../middlewares/assignEndpointId");
const { endpointIds } = require("../../utils/endpointIds");
const { validateData } = require("../../middlewares/validator");
const { query } = require("express-validator");
const jwt = require("jsonwebtoken");
const { Readable } = require("node:stream");
const { getStaticMap } = require("../../utils/staticMap");

router.get(
    "/map.:ext?",
    assignEndpointId.bind(this, endpointIds.FETCH_MAP_FOR_SUMMARIES_V2),
    validateData.bind(this, [
        query("token")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        // decrypt jwt_token to get locations
        const { token } = req.query;
        const { markers } = jwt.verify(token, process.env.JWT_SECRET);

        try {
            if (!markers) {
                return res.status(500).send({ message: "Error processing request" });
            }

            const { ext } = req.params;

            const mapData = await getStaticMap(markers, ext);

            if (mapData) {
                res.setHeader("Content-Type", mapData.mimeType);
                res.setHeader("Content-Length", mapData.source.headers.get("content-length"));
                res.setHeader("access-control-allow-origin", "*");
                res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                res.setHeader("Expires", "0");
                res.setHeader("Pragma", "no-cache");

                if (mapData.source) {
                    const nodeStream = Readable.fromWeb(mapData.source.body);
                    nodeStream.pipe(res);
                } else {
                    return res.status(500).send({ message: "Error serving image" });
                }
            } else {
                res.status(500).send({ message: "Error serving image" });
            }
        } catch (error) {
            console.error("Error serving image:", error);
            res.status(500).send({ message: "Error serving image" });
        }
    },
);

module.exports = router;
