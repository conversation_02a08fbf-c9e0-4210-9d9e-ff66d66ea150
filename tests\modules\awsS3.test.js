// Mock AWS SDK
const mockS3 = {
    listObjectsV2: jest.fn(),
    headObject: jest.fn(),
};

jest.mock('aws-sdk', () => {
    return {
        S3: jest.fn(() => mockS3),
    };
});

const { checkKeyExists, bucketKeys } = require('../../modules/awsS3'); // Update with your module path

describe('checkKeyExists', () => {
    // let bucketKeys;

    // beforeEach(() => {
    //     bucketKeys = require('../../modules/awsS3').bucketKeys = {}; // Initialize bucketKeys mock
    // });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return true if key already exists in bucketKeys', async () => {
        bucketKeys['test-bucket'] = ['existing-key'];

        const result = await checkKeyExists('test-bucket', 'existing-key');
        expect(result).toBe(true);
        expect(mockS3.headObject).not.toHaveBeenCalled();
    });

    it('should return true if headObject resolves successfully and key is added to bucketKeys', async () => {
        // bucketKeys['test-bucket'] = [];

        mockS3.headObject.mockImplementationOnce(() => ({
            promise: () => Promise.resolve(),
        }));

        const result = await checkKeyExists('test-bucket-2', 'new-key');
        expect(result).toBe(true);
        expect(mockS3.headObject).toHaveBeenCalledWith({
            Bucket: 'test-bucket-2',
            Key: 'new-key',
        });
        expect(bucketKeys['test-bucket-2']).toContain('new-key');
    });

    it('should return false if headObject throws 404 error', async () => {
        bucketKeys['test-bucket'] = [];

        mockS3.headObject.mockImplementationOnce(() => ({
            promise: () => Promise.reject({ statusCode: 404 }),
        }));

        const result = await checkKeyExists('test-bucket', 'non-existent-key');
        expect(result).toBe(false);
        expect(mockS3.headObject).toHaveBeenCalledWith({
            Bucket: 'test-bucket',
            Key: 'non-existent-key',
        });
    });

    it('should throw an error for other S3 errors', async () => {
        mockS3.headObject.mockImplementationOnce(() => ({
            promise: () => Promise.reject(new Error('S3 Error')),
        }));

        await expect(checkKeyExists('test-bucket', 'key')).rejects.toThrow('S3 Error');
    });
});
