import {
    Button,
    Grid,
    Modal,
    FormControl,
    FormLabel,
    Checkbox,
    Box,
    Chip,
    ListItemText,
    Autocomplete,
    CircularProgress,
    TextField,
    alpha,
    Typography,
    FormControlLabel,
    Stack,
    Tooltip,
} from "@mui/material";
import { useState } from "react";
import ModalContainer from "../../../components/ModalContainer";
import theme from "../../../theme";
import axiosInstance from "../../../axios";
import { useUser } from "../../../hooks/UserHook";
import { validateEmailDomain } from "../../../utils";
import NotificationFlag from "../../../components/NotificationFlag";

const ALL_OPTION = "All";

const buildVesselsVal = (notification, vessels) => {
    const vesselIds = notification.vessel_ids || [];

    if (vesselIds.includes("all")) {
        return [ALL_OPTION];
    }

    return vessels.filter((v) => vesselIds.includes(v.vessel_id)).map((v) => v.name);
};
const buildSuperCategoryVal = (notification) => {
    return Array.isArray(notification.super_category)
        ? notification.super_category.includes("all")
            ? [ALL_OPTION]
            : notification.super_category
        : [notification.super_category];
};
const buildSubCategoryVal = (notification) => {
    return Array.isArray(notification.sub_category)
        ? notification.sub_category.includes("all")
            ? [ALL_OPTION]
            : notification.sub_category
        : [notification.sub_category];
};
const buildFlagsVal = (notification, allFlag) => {
    return Array.isArray(notification.country_flags)
        ? notification.country_flags.includes("all")
            ? [ALL_OPTION]
            : notification.country_flags.map((flag) => {
                  return { name: flag, code: allFlag.find((e) => e.name === flag)?.code };
              })
        : [notification.country_flags];
};
const buildNotificationPreferenceVal = (notification) => {
    return notification.type === "both" ? ["email", "app"] : [notification.type];
};

const CreateEditNotificationModal = ({
    showModal,
    setShowModal,
    editNotifcation,
    setEditNotificaiton,
    setNotificationAddLoad,
    notificationAddLoad,
    filterItems,
    vessels,
    emailDomains,
    isEditModal = false,
}) => {
    const { user } = useUser();
    const [submitDisable, setSubmitDisable] = useState(true);
    const vesselName = vessels?.filter((vessel) => vessel.name && vessel.name.toLowerCase() !== "unregistered").map((vessel) => vessel.name) || [];
    const vesselsNames = [ALL_OPTION].concat(vesselName);
    const superCategory = filterItems["superCategories"] || [];
    const superCategories = superCategory;
    // const subCategory = filterItems["categories"] || [];
    // const subCategories = [ALL_OPTION].concat(subCategory);
    const allFlag = filterItems["countryFlags"] || [];
    const flags = [ALL_OPTION].concat(allFlag);

    const [vessel, setVessel] = useState(isEditModal ? buildVesselsVal(editNotifcation, vessels) : []);
    const [superCtg, setSuperCtg] = useState(isEditModal ? buildSuperCategoryVal(editNotifcation) : []);
    const [subCtg, setSubCtg] = useState(isEditModal ? buildSubCategoryVal(editNotifcation) : []);
    const [countryFlags, setCountryFlags] = useState(isEditModal ? buildFlagsVal(editNotifcation, allFlag) : []);
    const [notificationPreference, setNotificationPreference] = useState(isEditModal ? buildNotificationPreferenceVal(editNotifcation) : []);

    const [emails, setEmails] = useState(isEditModal ? editNotifcation.receivers : []);
    const [emailInput, setEmailInput] = useState("");
    const [emailError, setEmailError] = useState("");
    const [isEmailsShown, setIsEmailsShown] = useState(notificationPreference.includes("email"));

    const handleEmailInputChange = (e) => {
        setEmailInput(e.target.value);
    };

    const handleEmailInputKeyDown = (e) => {
        setEmailError("");
        if (e.key === "Enter" || e.key === ",") {
            e.preventDefault();
            const newEmail = emailInput.trim();

            if (!newEmail) {
                setEmailError("Email cannot be empty.");
                return;
            }
            if (!user.email) {
                setEmailError(`You cannot add an email because no email is associated with your account.`);
                return;
            }
            const validationError = validateEmailDomain(newEmail, user, emailDomains);

            if (validationError) {
                setEmailError(validationError);
                return;
            }
            if (!emails.includes(newEmail)) {
                setEmails([...emails, newEmail]);
                setEmailInput("");
                !isEditModal && setEmailError("");
                isEditModal && validateForm(vessel, superCtg, subCtg, countryFlags, notificationPreference);
            } else {
                !isEditModal && setEmailError("Email already exists in the list.");
            }
        }
    };
    const handleEmailDelete = (emailToDelete) => {
        setEmails(emails.filter((email) => email !== emailToDelete));
        !isEditModal && validateForm(vessel, superCtg, subCtg, countryFlags, notificationPreference);
    };

    const validateForm = (vessel, superCtg, subCtg, countryFlags, notificationPreference) => {
        // subCtg.length > 0
        const isValid = vessel.length > 0 && superCtg.length > 0 && countryFlags.length > 0 && notificationPreference.length > 0;
        setSubmitDisable(!isValid);
    };

    const handlePreferenceChange = (e) => {
        const { name, checked } = e.target;
        const newPreference = notificationPreference.includes(name)
            ? notificationPreference.filter((v) => v !== name)
            : [...notificationPreference, name];
        setNotificationPreference(newPreference);

        if (name === "email") {
            setIsEmailsShown(checked);
        }

        validateForm(vessel, superCtg, subCtg, countryFlags, newPreference);
    };

    const handleClose = (event, reason) => {
        if (reason === "backdropClick") {
            return;
        }
        if (isEditModal) {
            setSubmitDisable(true);
            setEditNotificaiton();
            setShowModal(false);
        } else {
            setShowModal(false);
            setVessel([]);
            setSuperCtg([]);
            setSubCtg([]);
            setCountryFlags([]);
            setNotificationPreference([]);
            setEmails([]);
            setEmailInput("");
        }
    };

    const handleSubmit = async () => {
        setNotificationAddLoad(true);

        try {
            if (isEditModal) {
                const data = {
                    super_category: superCtg,
                    // sub_category: subCtg.includes(ALL_OPTION) ? ['all'] : subCtg,
                    country_flags: countryFlags.includes(ALL_OPTION) ? ["all"] : countryFlags.map((cf) => cf.name),
                    type: notificationPreference.includes("app") && notificationPreference.includes("email") ? "both" : notificationPreference[0],
                    vessel_ids: vessel.includes(ALL_OPTION)
                        ? ["all"]
                        : vessel
                              .map((v) => {
                                  const found = vessels.find((vessel) => vessel.name === v);
                                  return found ? found.vessel_id : null;
                              })
                              .filter((v) => v != null),
                    title: vessel.includes(ALL_OPTION) ? ["all"] : vessel,
                    ...(emails.length > 0 && { receivers: emails }),
                    // receivers: emails,
                };
                await axiosInstance.patch(`/notificationsAlerts/${editNotifcation._id}`, data, { meta: { showSnackbar: true } }).then(() => {
                    setNotificationAddLoad(false);
                    setSubmitDisable(true);
                    setEditNotificaiton();
                    setShowModal(false);
                });
                // .catch((Err) => {
                //     setNotificationAddLoad(false)
                // })
            } else {
                const data = {
                    super_category: superCtg,
                    // sub_category: subCtg.includes(ALL_OPTION) ? ['all'] : subCtg,
                    sub_category: ["all"],
                    country_flags: countryFlags.includes(ALL_OPTION) ? ["all"] : countryFlags.map((cf) => cf.name),
                    type: notificationPreference.includes("app") && notificationPreference.includes("email") ? "both" : notificationPreference[0],
                    vessel_ids: vessel.includes(ALL_OPTION)
                        ? ["all"]
                        : vessel
                              .map((v) => {
                                  const found = vessels.find((vess) => vess.name === v);
                                  return found ? found.vessel_id : null;
                              })
                              .filter((v) => v != null),
                    title: vessel.includes(ALL_OPTION) ? ["all"] : vessel,
                    is_enabled: 1,
                    ...(emails.length > 0 && { receivers: emails }),
                };
                await axiosInstance.post("/notificationsAlerts", data, { meta: { showSnackbar: true } }).then(() => {
                    setSubmitDisable(false);
                    setShowModal(false);
                    setVessel([]);
                    setSuperCtg([]);
                    setSubCtg([]);
                    setCountryFlags([]);
                    setNotificationPreference([]);
                    setEmails([]);
                    setEmailInput("");
                    setNotificationAddLoad(false);
                });
            }
        } catch (error) {
            console.log(`Error Notification Alert ${isEditModal ? "Update" : "Create"} ` + error);
            setNotificationAddLoad(false);
        }
    };

    const handleSelectChange = (setSelected, value, label, selected) => {
        const procAllMultiselectChoose = () => {
            let res;
            if (value.includes(ALL_OPTION)) {
                if (value.length > 1 && selected.includes(ALL_OPTION)) {
                    res = value.filter((item) => item !== ALL_OPTION);
                } else {
                    res = [ALL_OPTION];
                }
            } else {
                res = value;
            }

            return res;
        };

        let newVessel = label === "Vessels" ? (value = procAllMultiselectChoose()) : vessel;
        let newSuperCtg = label === "Category" ? (value = procAllMultiselectChoose()) : superCtg;
        let newSubCtg = label === "Sub Category" ? (value = procAllMultiselectChoose()) : subCtg;
        let newFlags = label === "Country Flag" ? (value = procAllMultiselectChoose()) : countryFlags;

        setSelected(value);
        validateForm(newVessel, newSuperCtg, newSubCtg, newFlags, notificationPreference);
    };

    const handleClear = () => {
        setVessel(isEditModal ? buildVesselsVal(editNotifcation, vessels) : []);
        setSuperCtg(isEditModal ? buildSuperCategoryVal(editNotifcation) : []);
        setSubCtg(isEditModal ? buildSubCategoryVal(editNotifcation) : []);
        setCountryFlags(isEditModal ? buildFlagsVal(editNotifcation, allFlag) : []);
        setNotificationPreference(isEditModal ? buildNotificationPreferenceVal(editNotifcation) : []);
        setEmails(isEditModal ? editNotifcation.receivers : []);
        setEmailInput("");
        setSubmitDisable(true);
    };

    const renderAutocompleteTextItem = (props, option, customKey, { selected, allPredicateLabel = ALL_OPTION }) => {
        if (!option) return null;
        // eslint-disable-next-line no-unused-vars
        const { key, ...otherProps } = props;
        return (
            <li {...otherProps} key={customKey}>
                <Checkbox checked={selected} />
                <ListItemText
                    primary={(option === ALL_OPTION ? allPredicateLabel : option).replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase())}
                />
            </li>
        );
    };

    const renderAutocompleteFlagItem = (props, option, customKey, { selected }) => {
        if (!option) return null;

        const name = (option.name || option).replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());
        return (
            <li {...props} key={customKey}>
                <Checkbox checked={selected} />
                <NotificationFlag option={option} />
                <ListItemText primary={name} />
            </li>
        );
    };

    const renderAutocomplete = (
        items,
        selected,
        setSelected,
        label,
        renderMethod = renderAutocompleteTextItem,
        multiple = true,
        allPredicateLabel = ALL_OPTION,
    ) => {
        return (
            <FormControl sx={{ width: "100%", maxHeight: "", overflowX: "auto" }} size="small">
                <FormLabel sx={{ marginBottom: 1, color: "white", fontWeight: "bold" }}>{label}</FormLabel>
                <Autocomplete
                    multiple={multiple}
                    value={selected}
                    onChange={(event, newValue) => handleSelectChange(setSelected, newValue, label, selected)}
                    options={items}
                    disableCloseOnSelect
                    getOptionLabel={(option) => {
                        if (!option) return "";

                        if (option === ALL_OPTION || option?.name === ALL_OPTION) return allPredicateLabel;
                        return (option?.name || option).replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());
                    }}
                    renderInput={(params) => <TextField {...params} label={"Select " + label} variant="outlined" />}
                    sx={{ "& .MuiFormLabel-root": { color: alpha("#FFFFFF", 0.6), fontWeight: 400 } }}
                    renderTags={(value, getTagProps) =>
                        value.map((option, index) => {
                            const { key, ...tagProps } = getTagProps({ index }); // extract key
                            return (
                                <Chip
                                    {...tagProps}
                                    sx={{
                                        color: "#FFFFFF",
                                        backgroundColor: theme.palette.primary.main,
                                        "& .MuiChip-deleteIcon": { color: theme.palette.grey[500] },
                                        "& .MuiChip-deleteIcon:hover": { color: theme.palette.common.white },
                                    }}
                                    key={option.id || key}
                                    variant="outlined"
                                    label={
                                        <Stack direction="row" spacing={1} alignItems="center">
                                            <NotificationFlag option={option} />
                                            <span>
                                                {option === ALL_OPTION || option?.name === ALL_OPTION ? allPredicateLabel : option.name || option}
                                            </span>
                                        </Stack>
                                    }
                                />
                            );
                        })
                    }
                    renderOption={(props, option, { selected, index }) => {
                        const key = `${option}-${index}`;
                        return renderMethod(props, option, key, { selected, allPredicateLabel });
                    }}
                    isOptionEqualToValue={(option, value) => (option.name || option) === (value.name || value)}
                />
            </FormControl>
        );
    };

    return (
        <Modal open={Boolean(showModal)} onClose={handleClose}>
            <ModalContainer title={isEditModal ? "Edit Alert" : "Create Alert"} onClose={handleClose} showDivider>
                <Grid maxHeight={"70vh"} overflow="auto">
                    <Grid container direction="column" gap={2} width={{ xs: 300, sm: 500 }} sx={{ flexWrap: "nowrap" }}>
                        {renderAutocomplete(vesselsNames, vessel, setVessel, "Vessels")}
                        {renderAutocomplete(superCategories, superCtg, setSuperCtg, "Category", renderAutocompleteTextItem, true)}
                        {/* {renderAutocomplete(subCategories, subCtg, setSubCtg, "Sub Category")} */}
                        {renderAutocomplete(flags, countryFlags, setCountryFlags, "Country Flag", renderAutocompleteFlagItem)}
                    </Grid>
                    <Grid container width={{ xs: 300, sm: 500 }} sx={{ marginTop: "5px", marginBottom: "5px" }}>
                        <Grid width={"100%"} sx={{ paddingTop: "8px" }}>
                            <Typography>Notification Preference</Typography>
                        </Grid>
                        <Grid container>
                            {user.email ? (
                                <Tooltip title={"Email will be send to your account email"} placement="bottom">
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={notificationPreference.includes("email")}
                                                onChange={handlePreferenceChange}
                                                name="email"
                                            />
                                        }
                                        label="Email"
                                        componentsProps={{ typography: { fontWeight: 300 } }}
                                    />
                                </Tooltip>
                            ) : (
                                ""
                            )}
                            <FormControlLabel
                                control={<Checkbox checked={notificationPreference.includes("app")} onChange={handlePreferenceChange} name="app" />}
                                label="In-App"
                                componentsProps={{ typography: { fontWeight: 300 } }}
                            />
                        </Grid>
                    </Grid>
                    {/*INFO: block showing emails*/}
                    {
                        isEmailsShown === "1" ? (
                            <Grid container direction="column" gap={2} width={{ xs: 300, sm: 500 }} sx={{ marginTop: "5px", marginBottom: "5px" }}>
                                <Grid>
                                    <Typography item>Email Addresses</Typography>
                                </Grid>
                                <Box
                                    sx={{
                                        display: "flex",
                                        flexWrap: "wrap",
                                        alignItems: "center",
                                        gap: 1,
                                        border: "1px solid rgba(255, 255, 255, 0.23)",
                                        borderRadius: "4px",
                                        padding: "8px",
                                        minHeight: "50px",
                                        maxHeight: "120px",
                                        overflow: "auto",
                                    }}
                                >
                                    {user.email && (
                                        <Chip
                                            key={user.email}
                                            label={user.email}
                                            onDelete={() => handleEmailDelete(user.email)}
                                            disabled
                                            sx={{
                                                borderRadius: "4px",
                                                color: "#FFFFFF",
                                                fontWeight: "bold",
                                                backgroundColor: "#1B1F2D",
                                                "& .MuiChip-deleteIcon": {
                                                    color: theme.palette.grey[500],
                                                },
                                            }}
                                        />
                                    )}
                                    {emails.map((email) => (
                                        <Chip
                                            key={email}
                                            label={email}
                                            onDelete={() => handleEmailDelete(email)}
                                            sx={{
                                                borderRadius: "4px",
                                                color: "#FFFFFF",
                                                backgroundColor: "#1B1F2D",
                                                "& .MuiChip-deleteIcon": {
                                                    color: theme.palette.grey[500],
                                                },
                                            }}
                                        />
                                    ))}
                                    <TextField
                                        variant="outlined"
                                        value={emailInput}
                                        onChange={handleEmailInputChange}
                                        onKeyDown={handleEmailInputKeyDown}
                                        placeholder={"Additional Email Addresses"}
                                        sx={{
                                            flexGrow: 1,
                                            minWidth: "120px",
                                            "& .MuiOutlinedInput-root": {
                                                padding: "4px",
                                                border: "none",
                                                "& fieldset": {
                                                    border: "none",
                                                },
                                            },
                                            "& .MuiInputBase-input": {
                                                padding: "6px",
                                                textAlign: "left",
                                            },
                                        }}
                                    />
                                </Box>
                                {emailError && (
                                    <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                                        {emailError}
                                    </Typography>
                                )}
                            </Grid>
                        ) : (
                            ""
                        )
                        //     (
                        //     <>
                        //         <Grid item>
                        //             <Box
                        //                 sx={{
                        //                     display: "flex",
                        //                     flexWrap: "wrap",
                        //                     alignItems: "center",
                        //                     gap: 1,
                        //                     // border: "1px solid rgba(255, 255, 255, 0.23)",
                        //                     // borderRadius: "4px",
                        //                     padding: "8px",
                        //                     minHeight: "105px",
                        //                     maxHeight: "105px",
                        //                     overflow: "auto",
                        //                 }}
                        //             >
                        //                 &nbsp;
                        //             </Box>
                        //         </Grid>
                        //     </>
                        // )
                    }
                </Grid>
                <Grid container gap={2} justifyContent="center" mt={2}>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                textTransform: "none",
                                border: "1px solid grey",
                                padding: "10px",
                            }}
                            onClick={handleClear}
                        >
                            Reset
                        </Button>
                    </Grid>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                textTransform: "none",
                                border: "1px solid grey",
                                padding: "10px",
                            }}
                            onClick={handleClose}
                        >
                            Close
                        </Button>
                    </Grid>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                display: "flex",
                                gap: 1,
                                backgroundColor: theme.palette.custom.mainBlue,
                                "&:hover": { backgroundColor: theme.palette.custom.mainBlue },
                            }}
                            variant="contained"
                            onClick={handleSubmit}
                            disabled={notificationAddLoad || submitDisable}
                        >
                            {notificationAddLoad && <CircularProgress size={18} />}
                            {isEditModal ? "Update" : "Create"}
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default CreateEditNotificationModal;
