import { render, screen, waitFor, act } from '@testing-library/react';
import { BrowserRouter as Router, useNavigate, Routes, Route } from 'react-router-dom';
import HomeLayout from '../../src/layouts/HomeLayout';

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: jest.fn(),
}));

describe('HomeLayout Component', () => {
    let mockNavigate;

    beforeEach(() => {
        mockNavigate = jest.fn();
        useNavigate.mockReturnValue(mockNavigate);
    });


    const renderWithRouter = (component) => {
        return render(
            <Router>
                <Routes>
                    <Route path="/" element={component}>
                        <Route index element={<div>Login Page</div>} />
                    </Route>
                </Routes>
            </Router>
        );
    };

    it('should render the component and logo without errors', () => {
        renderWithRouter(<HomeLayout />);

        const logoImage = screen.getAllByAltText('Quartermaster Logo')[0];
        expect(logoImage).toBeInTheDocument();
        expect(logoImage.src).toContain('quartermaster-logo-home.svg');
    });

    it('should navigate to dashboard if jwt_token exists', async () => {
        localStorage.setItem('jwt_token', 'test_token');
        renderWithRouter(<HomeLayout />);

        expect(mockNavigate).toHaveBeenCalledWith('/dashboard/stream');
    });

    it('should show child content', () => {
        renderWithRouter(<HomeLayout />);

        expect(screen.getByText('Login Page')).toBeInTheDocument();
    });
});
