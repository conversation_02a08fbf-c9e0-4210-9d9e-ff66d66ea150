import React from 'react';
import { render, screen } from '@testing-library/react';
import Line<PERSON><PERSON> from '../../../src/components/Charts/LineChart';

jest.mock('react-chartjs-2', () => ({
    Line: () => <div data-testid="line-chart" />,
}));

describe('LineChart Component', () => {
    const mockData = {
        labels: ['January', 'February', 'March'],
        datasets: [
            {
                label: 'Dataset 1',
                data: [65, 59, 80],
                borderColor: '#FF5733',
                backgroundColor: 'rgba(255, 87, 51, 0.2)',
                fill: true,
            },
        ],
    };

    const mockOptions = {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: 'Sample Line Chart',
            },
        },
    };

    it('renders without crashing', () => {
        render(<LineChart data={mockData} options={mockOptions} />);
        expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });

    it('renders with provided data and options', () => {
        render(<LineChart data={mockData} options={mockOptions} />);
        
        expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });
});
