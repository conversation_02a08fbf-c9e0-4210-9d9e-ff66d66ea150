import { render, screen } from '@testing-library/react';
import KPICard from '../../src/components/KPICard';
import { ThemeProvider } from '@mui/material';
import theme from '../../src/theme';
import { useApp } from '../../src/hooks/AppHook';

jest.mock('../../src/hooks/AppHook', () => ({
    useApp: jest.fn().mockReturnValue({ screenSize: { xs: false, sm: false, md: false, lg: false } }),
}));


describe('KPICard', () => {
    const renderWithTheme = (component) => {
        return render(<ThemeProvider theme={theme}>{component}</ThemeProvider>);
    };

    it('renders the title correctly', () => {
        useApp.mockReturnValue({ screenSize: { xs: true, sm: false, md: false, lg: false } })
        renderWithTheme(<KPICard title="Test Title" />);
        expect(screen.getByText('Test Title')).toBeInTheDocument();
    });

    it('renders the value correctly when provided', () => {
        useApp.mockReturnValue({ screenSize: { xs: false, sm: true, md: false, lg: false } })
        renderWithTheme(<KPICard title="Test Title" value={'123'} />);
        renderWithTheme(<KPICard title="Test Title" value={1} />);
        expect(screen.getByText('123')).toBeInTheDocument();
    });

    it('renders multiple values when provided', () => {
        useApp.mockReturnValue({ screenSize: { xs: true, sm: true, md: false, lg: false } })
        const values = [
            { title: 'Metric 1', value: 50 },
            { title: 'Metric 2', value: 75 },
            { title: 'Metric 3', value: 7 },
        ];
        const { rerender } = renderWithTheme(<KPICard title="Test Title" values={values} />);
        useApp.mockReturnValue({ screenSize: { xs: false, sm: false, md: false, lg: false } })
        rerender(<KPICard title="Test Title" values={values} />);
        expect(screen.getByText('Metric 1')).toBeInTheDocument();
        expect(screen.getByText(50)).toBeInTheDocument();
        expect(screen.getByText('Metric 2')).toBeInTheDocument();
        expect(screen.getByText(75)).toBeInTheDocument();
    });

    it('renders correctly when neither value nor values are provided', () => {
        renderWithTheme(<KPICard title="Test Title" />);
        expect(screen.getByText('Test Title')).toBeInTheDocument();
        expect(screen.queryByText('123')).not.toBeInTheDocument();
    });
});
