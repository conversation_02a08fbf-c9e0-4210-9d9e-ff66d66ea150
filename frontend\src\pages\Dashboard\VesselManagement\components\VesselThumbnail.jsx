import { useState, useEffect } from "react";
import { alpha, CircularProgress, Box } from "@mui/material";
import { ImageNotSupported, ZoomIn } from "@mui/icons-material";
import theme from "../../../../theme";
import ImageViewerModal from "./ImageViewerModal";
import s3Controller from "../../../../controllers/S3.controller";

const VesselThumbnail = ({ thumbnailS3Key, vesselName, size = 50, enableZoom = true }) => {
    const [imageError, setImageError] = useState(false);
    const [imageLoading, setImageLoading] = useState(true);
    const [showImageModal, setShowImageModal] = useState(false);
    const [presignedUrl, setPresignedUrl] = useState("");

    useEffect(() => {
        const loadImageUrl = async () => {
            if (!thumbnailS3Key) {
                setPresignedUrl("");
                setImageLoading(false);
                return;
            }

            try {
                setImageLoading(true);
                setImageError(false);

                const imageUrl = await s3Controller.fetchCloudfrontSignedUrl(thumbnailS3Key);

                if (imageUrl) {
                    setPresignedUrl(imageUrl);
                } else {
                    setImageError(true);
                }
            } catch (error) {
                console.error("Error loading image URL:", error);
                setImageError(true);
            } finally {
                setImageLoading(false);
            }
        };

        loadImageUrl();
    }, [thumbnailS3Key]);

    const handleImageError = () => {
        setImageError(true);
        setImageLoading(false);
    };

    const handleImageLoad = () => {
        setImageLoading(false);
    };

    const handleThumbnailClick = () => {
        if (enableZoom && !imageError && presignedUrl) {
            setShowImageModal(true);
        }
    };

    if (!thumbnailS3Key || imageError || !presignedUrl) {
        return (
            <Box
                sx={{
                    width: size,
                    bgcolor: alpha(theme.palette.custom.mainBlue, 0.1),
                    border: `2px solid ${alpha(theme.palette.custom.mainBlue, 0.3)}`,
                    borderRadius: 1,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    cursor: enableZoom ? "pointer" : "default",
                }}
                onClick={enableZoom ? handleThumbnailClick : undefined}
            >
                <ImageNotSupported sx={{ color: theme.palette.custom.mainBlue, fontSize: size * 0.4 }} />
            </Box>
        );
    }

    return (
        <>
            <Box
                position="relative"
                sx={{
                    cursor: enableZoom ? "pointer" : "default",
                    "&:hover .zoom-overlay": enableZoom ? { opacity: 1 } : { opacity: 0 },
                }}
                onClick={handleThumbnailClick}
            >
                {imageLoading && (
                    <Box
                        sx={{
                            width: size,
                            bgcolor: alpha(theme.palette.custom.mainBlue, 0.1),
                            border: `2px solid ${alpha(theme.palette.custom.mainBlue, 0.3)}`,
                            borderRadius: 1,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <CircularProgress size={size * 0.3} />
                    </Box>
                )}
                <Box
                    component="img"
                    src={presignedUrl}
                    alt={vesselName}
                    onError={handleImageError}
                    onLoad={handleImageLoad}
                    sx={{
                        width: size,
                        objectFit: "cover",
                        border: `2px solid ${alpha(theme.palette.custom.mainBlue, 0.3)}`,
                        borderRadius: 1,
                        display: imageLoading ? "none" : "block",
                        transition: "transform 0.2s ease-in-out",
                        "&:hover": enableZoom
                            ? {
                                  transform: "scale(1.05)",
                              }
                            : {},
                    }}
                />

                {enableZoom && !imageLoading && !imageError && (
                    <Box
                        className="zoom-overlay"
                        sx={{
                            position: "absolute",
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            bgcolor: alpha("#000000", 0.6),
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            borderRadius: 1,
                            opacity: 0,
                            transition: "opacity 0.2s ease-in-out",
                        }}
                    >
                        <ZoomIn sx={{ color: "#FFFFFF", fontSize: size * 0.4 }} />
                    </Box>
                )}
            </Box>

            <ImageViewerModal open={showImageModal} onClose={() => setShowImageModal(false)} imageUrl={presignedUrl} vesselName={vesselName} />
        </>
    );
};

export default VesselThumbnail;
