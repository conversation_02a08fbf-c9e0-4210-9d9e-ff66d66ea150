import React from 'react';
import { render, screen } from '@testing-library/react';
import <PERSON><PERSON><PERSON> from '../../../src/components/Charts/PieChart';

jest.mock('react-chartjs-2', () => ({
    Pie: () => <div data-testid="pie-chart" />,
}));

describe('PieChart Component', () => {
    const mockData = {
        labels: ['Label 1', 'Label 2'],
        datasets: [
            {
                label: 'Dataset 1',
                data: [10, 20],
                backgroundColor: ['#FF6384', '#36A2EB'],
            },
        ],
    };
    
    const mockOptions = {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: 'Sample Pie Chart',
            },
        },
    };

    it('renders without crashing', () => {
        render(<PieChart data={mockData} options={mockOptions} />);
        expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
    });

    it('renders with provided data and options', () => {
        render(<PieChart data={mockData} options={mockOptions} />);
        
        expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
    });
});
