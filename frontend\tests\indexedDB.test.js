import idb from '../src/indexedDB';
import { openDB } from 'idb';

jest.mock('idb', () => ({
    openDB: jest.fn()
}));

describe('IndexedDB Module', () => {
    let mockDB;
    let mockTx;
    let mockStore;
    const DB_NAME = 'quartermaster';

    jest.spyOn(console, 'log').mockImplementation(() => { });

    beforeEach(() => {
        mockStore = {
            get: jest.fn(),
            add: jest.fn(),
            delete: jest.fn(),
            getAll: jest.fn()
        };

        mockTx = {
            objectStore: jest.fn(() => mockStore),
            done: Promise.resolve()
        };

        mockDB = {
            transaction: jest.fn(() => mockTx),
            objectStoreNames: { contains: jest.fn() },
            close: jest.fn(),
            version: 1,
            createObjectStore: jest.fn()
        };

        openDB.mockResolvedValue(mockDB);
        jest.clearAllMocks();
    });

    it('should initialize the database', async () => {
        await idb.initDB();
        expect(openDB).toHaveBeenCalledWith(DB_NAME);
    });

    it('should add items to an existing store', async () => {
        mockDB.objectStoreNames.contains.mockReturnValue(true);
        mockStore.get.mockResolvedValue(null);

        const items = [{ _id: '1', data: 'test data 1' }, { _id: '2', data: 'test data 2' }];
        await idb.addItems('testStore', items);

        expect(mockDB.transaction).toHaveBeenCalledWith('testStore', 'readwrite');
        expect(mockStore.add).toHaveBeenCalledTimes(2);
        expect(mockStore.add).toHaveBeenCalledWith(items[0]);
        expect(mockStore.add).toHaveBeenCalledWith(items[1]);
    });

    it('should not add items that already exist', async () => {
        mockDB.objectStoreNames.contains.mockReturnValue(true);
        mockStore.get.mockResolvedValue({ _id: '1', data: 'existing data' });

        const items = [{ _id: '1', data: 'test data 1' }];
        await idb.addItems('testStore', items);

        expect(mockStore.add).not.toHaveBeenCalledWith(items[0]);
    });

    it('should retrieve all items from the store', async () => {
        mockDB.objectStoreNames.contains.mockReturnValue(true);
        mockStore.getAll.mockResolvedValue([{ _id: '1', data: 'data' }]);

        await idb.initDB('testStore');
        const items = await idb.getItems('testStore');

        expect(mockDB.transaction).toHaveBeenCalled();
        expect(items).toEqual([{ _id: '1', data: 'data' }]);
    });

    it('should retrieve all filtered items from the store', async () => {
        mockDB.objectStoreNames.contains.mockReturnValue(true);
        mockStore.getAll.mockResolvedValue([{ _id: '1', data: 'data' }]);
        const filter = jest.fn();
        await idb.initDB('testStore');
        await idb.getItems('testStore', filter);

        expect(mockDB.transaction).toHaveBeenCalled();
        expect(filter).toHaveBeenCalled();
    });

    it('should return an empty array if the store does not exist', async () => {
        mockDB.objectStoreNames.contains.mockReturnValue(false);
        await idb.initDB('testStore');
        const items = await idb.getItems('nonexistentStore');

        expect(items).toEqual([]);
    });

    it('should delete an item from the store', async () => {
        await idb.initDB('testStore');
        await idb.deleteItem('testStore', '1');

        expect(mockDB.transaction).toHaveBeenCalledWith('testStore', 'readwrite');
        expect(mockStore.delete).toHaveBeenCalledWith('1');
    });

    it('should create a new object store if it does not exist', async () => {
        openDB.mockImplementationOnce((_name, _version, { upgrade }) => {
            upgrade(mockDB);
            return Promise.resolve(mockDB);
        });

        mockDB.objectStoreNames.contains.mockReturnValue(false);
        const storeName = 'newStore';

        await idb.addItems(storeName, [{ _id: '1', data: 'data' }]);

        expect(mockDB.createObjectStore).toHaveBeenCalledWith(storeName, { keyPath: '_id' });
    });

    it('should not create a new object store if it does exist', async () => {
        openDB.mockImplementationOnce((_name, _version, { upgrade }) => {
            upgrade(mockDB);
            return Promise.resolve(mockDB);
        });

        mockDB.objectStoreNames.contains.mockReturnValue(true);
        const storeName = 'newStore';

        await idb.addItems(storeName, [{ _id: '1', data: 'data' }]);

        expect(mockDB.createObjectStore).not.toHaveBeenCalled();
    });

    it('should clear all items from a store', async () => {
        global.indexedDB = {
            databases: jest.fn().mockResolvedValue([{ name: 'DB_1' }, { name: 'DB_2' }]),
            deleteDatabase: jest.fn(),
        };

        mockDB.objectStoreNames.contains.mockReturnValue(true);
        const storeName = 'testStore';
        const clearStore = jest.fn();
        mockStore.clear = clearStore;

        await idb.clearIndexedDB(storeName);

        expect(global.indexedDB.databases).toHaveBeenCalled();
        expect(global.indexedDB.deleteDatabase).toHaveBeenCalledTimes(2);
        expect(global.indexedDB.deleteDatabase).toHaveBeenCalledWith('DB_1');
        expect(global.indexedDB.deleteDatabase).toHaveBeenCalledWith('DB_2');
    });

    it('should not call deleteDatabase if there is no database in the store', async () => {
        global.indexedDB = {
            databases: jest.fn().mockResolvedValue(null),
            deleteDatabase: jest.fn(),
        };

        mockDB.objectStoreNames.contains.mockReturnValue(true);
        const storeName = 'testStore';
        const clearStore = jest.fn();
        mockStore.clear = clearStore;

        await idb.clearIndexedDB(storeName);

        expect(global.indexedDB.deleteDatabase).not.toHaveBeenCalled();
    });


    it('should update an existing item in the store', async () => {
        mockDB.objectStoreNames.contains.mockReturnValue(true);
        const store = 'testStore';
        const id = '1';
        const updates = { data: 'updated data' };

        mockStore.get.mockResolvedValue({ _id: id, data: 'old data' });
        mockStore.put = jest.fn().mockResolvedValue(undefined);

        const result = await idb.updateItem(store, id, updates);

        expect(mockStore.put).toHaveBeenCalledWith({ _id: id, data: 'updated data' });
        expect(result).toEqual({ _id: id, data: 'updated data' });
    });

    it('should return empty array if objectStoreNames does not contain the store', async () => {
        mockDB.objectStoreNames.contains.mockReturnValue(false);
        const store = 'testStore';
        const id = '1';
        const updates = { data: 'updated data' };

        const result = await idb.updateItem(store, id, updates);
        expect(result).toEqual([]);
    });

    it('should return empty array if there is no existing item in store', async () => {
        mockDB.objectStoreNames.contains.mockReturnValue(false);
        await idb.initDB('testStore');
        const store = 'testStore';
        const id = '1';
        const updates = { data: 'updated data' };
        const result = await idb.updateItem(store, id, updates);
        expect(result).toEqual([]);
    });
});
