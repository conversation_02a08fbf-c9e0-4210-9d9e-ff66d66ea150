const express = require("express");
const Role = require("../models/Role");
const hasPermission = require("../middlewares/hasPermission");
const { permissions } = require("../utils/permissions");
const { validateData } = require("../middlewares/validator");
const { body, param, check } = require("express-validator");
const { validateError, isIntStrict } = require("../utils/functions");
const { default: mongoose } = require("mongoose");
const Permission = require("../models/Permission");
const { isValidObjectId } = require("mongoose");
const User = require("../models/User");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const ioEmitter = require("../modules/ioEmitter");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_ROLES), isAuthenticated, async (req, res) => {
    try {
        const roles = await Role.aggregate([
            {
                $lookup: {
                    from: "users",
                    let: { created_by: "$created_by" },
                    pipeline: [{ $match: { $expr: { $and: [{ $eq: ["$_id", "$$created_by"] }, { $ne: ["$is_deleted", true] }] } } }],
                    as: "user",
                },
            },
            {
                $addFields: {
                    user: { $arrayElemAt: ["$user", 0] },
                },
            },
            {
                $project: {
                    role_id: 1,
                    role_name: 1,
                    denied_permissions: 1,
                    deletable: 1,
                    editable: 1,
                    hierarchy_number: 1,
                    "user.name": 1,
                    "user.username": 1,
                    creation_timestamp: 1,
                },
            },
        ]);
        res.json(roles);
    } catch (err) {
        validateError(err, res);
    }
});

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_ROLE),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRoles]),
    validateData.bind(this, [
        body("role_name")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        const { role_name } = req.body;
        try {
            await Role.create({ role_name, role_id: -1, hierarchy_number: -1, created_by: req.user._id });
            res.json({ message: `Role has been created` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/:id",
    assignEndpointId.bind(this, endpointIds.UPDATE_ROLE),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRoles]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("role_name")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        body("denied_permissions")
            .isArray()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        check("denied_permissions.*")
            .if(body("denied_permissions").exists())
            .custom(isIntStrict)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const { denied_permissions } = req.body;

            const role = await Role.findById(req.params.id);
            if (!role) return res.status(404).json({ message: `Role does not exist` });
            if (!role.editable) return res.status(400).json({ message: `Role cannot be edited` });
            if (req.user.role.hierarchy_number >= role.hierarchy_number)
                return res.status(403).json({ message: "You cannot update this role as it exceeds your hierarchy level" });
            if (denied_permissions) {
                const permissions = await Permission.find();
                if (denied_permissions.some((p_id) => !permissions.find((p) => p.permission_id === p_id)))
                    return res.status(400).json({ message: `Invalid permissions provided` });
                if (
                    role.denied_permissions.some(
                        (p_id) => !permissions.find((p) => p.permission_id === p_id).assignable && !denied_permissions.includes(p_id),
                    ) ||
                    denied_permissions.some(
                        (p_id) => !permissions.find((p) => p.permission_id === p_id).assignable && !role.denied_permissions.includes(p_id),
                    )
                )
                    return res.status(403).json({ message: `Cannot edit a forbidden permission` });
            }

            ["role_name", "denied_permissions"].forEach((key) => {
                if (req.body[key] !== undefined) role[key] = req.body[key];
            });

            await role.save();

            return res.json({ message: `Role '${role.role_name}' has been edited` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/permissionUpdate",
    assignEndpointId.bind(this, endpointIds.UPDATE_ROLE),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRoles]),
    validateData.bind(this, [
        body("roles_permissions").isArray({ min: 1 }).withMessage("Roles Permissions must be a non-empty array"),
        body("roles_permissions.*._id")
            .notEmpty()
            .withMessage("Roles Permission Id is required")
            .bail()
            .custom(isValidObjectId)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const { roles_permissions } = req.body;
            const userHierarchyNumber = req.user.role.hierarchy_number;

            const roleIds = roles_permissions.map((role) => role._id);
            const dbRoles = await Role.find({ _id: { $in: roleIds } });
            const allowedRoles = dbRoles.filter((role) => role.hierarchy_number > userHierarchyNumber);

            if (allowedRoles.length === 0) {
                return res.status(403).json({ message: "You are not authorized to update any role as it exceeds your hierarchy level" });
            }

            const editableRoles = allowedRoles.filter((role) => (role.editable ? role : false));

            if (editableRoles.length === 0) {
                return res.status(403).json({ message: "No roles are permitted to be edited" });
            }
            const convertedIds = editableRoles.map((role) => ({
                ...role._doc, // Extract the _doc object which contains the actual data
                _id: String(role._doc._id), // Convert the _id in the _doc object to a string
            }));
            const updatedConvertedIds = convertedIds
                .map((role) => {
                    const matchingRole = roles_permissions.find((r) => r._id === role._id);
                    if (matchingRole) {
                        return {
                            ...role,
                            denied_permissions: matchingRole.denied_permissions,
                        };
                    }
                    return;
                })
                .filter(Boolean);
            const permissions = await Permission.find();
            const validPermissionIds = permissions.map((p) => p.permission_id);
            const nonAssignableIds = permissions.filter((p) => !p.assignable).map((p) => p.permission_id);
            const rolesWithValidPermissions = updatedConvertedIds.filter((role) =>
                role.denied_permissions.every((p_id) => validPermissionIds.includes(p_id)),
            );
            if (rolesWithValidPermissions.length === 0) {
                return res.status(403).json({ message: "The role(s) have invalid permission IDs" });
            }
            const rolesWithAllNonAssignablePermissions = rolesWithValidPermissions.filter((role) =>
                nonAssignableIds.every((p_id) => role.denied_permissions.includes(p_id)),
            );
            if (rolesWithAllNonAssignablePermissions.length === 0) {
                return res.status(403).json({ message: "The role does not contain non-assignable permission IDs" });
            }

            const validRoles = rolesWithAllNonAssignablePermissions;

            const bulkOps = validRoles.map((role) => ({
                updateOne: {
                    filter: { _id: role._id },
                    update: { $set: { denied_permissions: role.denied_permissions } },
                },
            }));
            const result = await Role.bulkWrite(bulkOps);
            validRoles.forEach((role) => {
                const updatedRole = dbRoles.find((r) => String(r._id) === String(role._id));
                // Only emit if the hierarchy number was actually updated
                if (updatedRole) {
                    ioEmitter.emit("notifyAll", {
                        name: "roles/changed",
                        data: role, // Emit only the updated role
                    });
                }
            });
            res.status(200).json({
                message: "Role Permissions Updated",
                result,
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.delete(
    "/:id",
    assignEndpointId.bind(this, endpointIds.DELETE_ROLE),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRoles]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const role = await Role.findById(req.params.id);
            if (!role) return res.status(404).json({ message: `Role does not exist` });

            if (!role.deletable) return res.status(400).json({ message: `Role cannot be deleted` });
            if (req.user.role.hierarchy_number >= role.hierarchy_number)
                return res.status(400).json({ message: "You cannot remove this role as it exceeds your hierarchy level" });
            if (await User.findOne({ role_id: role.role_id }))
                return res.status(400).json({ message: `Role cannot be deleted because it is assigned to a user` });
            await Role.findOneAndDelete({ _id: role._id });

            return res.json({ message: `Role has been deleted` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

//this is for update the  heirarchy of the roles
router.patch(
    "/reorder",
    assignEndpointId.bind(this, endpointIds.REORDER_ROLE),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageRoles]),
    validateData.bind(this, [
        body("roles").isArray({ min: 1 }).withMessage("Roles must be a non-empty array"),
        body("roles.*._id")
            .notEmpty()
            .withMessage("Role ID is required")
            .bail()
            .custom(isValidObjectId)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("roles.*.hierarchy_number")
            .notEmpty()
            .withMessage("Hierarchy number is required")
            .bail()
            .isInt({ min: 1 })
            .withMessage("Hierarchy number must be an integer greater than 0"),
    ]),
    async (req, res) => {
        const roles = req.body.roles;
        const userHierarchyNumber = req.user.role.hierarchy_number;

        try {
            const roleIds = roles.map((role) => role._id);
            const dbRoles = await Role.find({ _id: { $in: roleIds } });

            const allowedRoles = dbRoles.filter((role) => role.hierarchy_number > userHierarchyNumber);

            if (allowedRoles.length === 0) {
                return res.status(403).json({ message: "You are not authorized to update any roles" });
            }

            const allowedRoleIds = allowedRoles.map((role) => String(role._id));
            const validRoles = roles.filter((role) => allowedRoleIds.includes(String(role._id)));

            if (validRoles.length === 0) {
                return res.status(403).json({ message: "You are not authorized to update these roles" });
            }

            const bulkOps = validRoles.map((role) => ({
                updateOne: {
                    filter: { _id: role._id },
                    update: { $set: { hierarchy_number: role.hierarchy_number } },
                },
            }));

            const result = await Role.bulkWrite(bulkOps);

            validRoles.forEach((role) => {
                const updatedRole = dbRoles.find((r) => String(r._id) === String(role._id));
                // Only emit if the hierarchy number was actually updated
                if (updatedRole && updatedRole.hierarchy_number !== role.hierarchy_number) {
                    ioEmitter.emit("notifyAll", {
                        name: "roles/changed",
                        data: role, // Emit only the updated role
                    });
                }
            });

            res.status(200).json({
                message: "Hierarchy numbers updated successfully for allowed roles",
                result,
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
