const express = require("express");
const hasPermission = require("../middlewares/hasPermission");
const { permissions } = require("../utils/permissions");
const { validateData } = require("../middlewares/validator");
const { body, param, check } = require("express-validator");
const { validateError, isIntStrict } = require("../utils/functions");
const { default: mongoose } = require("mongoose");
const { isValidObjectId } = require("mongoose");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const apiKeyService = require("../services/ApiKey.service");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", isAuthenticated, hasPermission.bind(this, [permissions.manageApiKeys]), async (req, res) => {
    try {
        const apiKeys = await apiKeyService.fetchAll();
        res.json(apiKeys);
    } catch (err) {
        validateError(err, res);
    }
});

router.post(
    "/",
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageApiKeys]),
    validateData.bind(this, [
        body("description")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("email")
            .optional()
            .isEmail()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const { description, email } = req.body;
            await apiKeyService.create({ description, email, created_by: req.user._id });
            res.json({ message: `Api Key has been created` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:id/allowedEndpoints",
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageApiKeys]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("allowed_endpoints")
            .isArray()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        check("allowed_endpoints.*")
            .custom(isIntStrict)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const { allowed_endpoints } = req.body;

            const result = await apiKeyService.updateAllowedEndpoints({ id: req.params.id, allowed_endpoints });
            if (!result) return res.status(404).json({ message: `Api Key does not exist` });

            return res.json({ message: `Api Key has been updated` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:id/revoke",
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageApiKeys]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("revoke")
            .isBoolean()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const { revoke } = req.body;
            const result = await apiKeyService.updateRevocationStatus({ id: req.params.id, is_revoked: revoke });
            if (!result) return res.status(404).json({ message: `Api Key does not exist` });

            return res.json({ message: `Api Key has been updated` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.delete(
    "/:id",
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageApiKeys]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const result = await apiKeyService.delete({ id: req.params.id });
            if (!result) return res.status(404).json({ message: `Api Key does not exist` });

            return res.json({ message: `Api Key has been deleted` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:id/allowedVessels",
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageApiKeys]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("allowed_vessels")
            .isArray()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("allowed_vessels.*")
            .custom(isValidObjectId)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const { id } = req.params;
            const { allowed_vessels } = req.body;

            const apiKey = await apiKeyService.updateAllowedVessels({ id, allowed_vessels });

            if (!apiKey) return res.status(404).json({ message: "API key not found" });

            res.json(apiKey);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:id/details",
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageApiKeys]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("email")
            .optional()
            .isEmail()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("description")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const { email, description } = req.body;
            const result = await apiKeyService.update({ id: req.params.id, email, description });
            if (!result) return res.status(404).json({ message: `Api Key does not exist` });

            return res.json({ message: `Api Key details have been updated` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
