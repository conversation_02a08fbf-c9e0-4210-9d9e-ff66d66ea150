import { useContext } from 'react';
import { UserContext } from '../contexts/UserContext';

/**
 * Hook to use the user context.
 *
 * @typedef {object} obj
 * @property {object} user - The user object.
 * @property {boolean} userFetched - Check whether user has been fetched.
 * @property {function} fetchUser - Function to fetch user using jwt_token.
 * @property {boolean} sessionExpired - Check whether session has been expired.
 * @property {function} setSessionExpired - Function to set value for session expiry.
 * @property {function} login - Function to log in with credentials.
 * @property {function} logout - Function to log out.
 * @return {obj} An object containing user related functions.
 */
export const useUser = () => {
    const context = useContext(UserContext);
    if (context === undefined) {
        throw new Error('useUser must be used within a UserProvider');
    }
    return context;
};