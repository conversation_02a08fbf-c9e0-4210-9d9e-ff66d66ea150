import { render, screen, waitFor } from '@testing-library/react';
import Router, { initializeApp, DashboardRouteToggler } from '../src/Router';
import { version } from '../../package.json';
import idb from '../src/indexedDB';
import { useToaster } from '../src/hooks/ToasterHook';
import { registerErrorCallback, registerSuccessCallback } from '../src/axios';

global.setImmediate = (callback) => setTimeout(callback, 0);

jest.mock('../src/indexedDB', () => ({
    clearIndexedDB: jest.fn(),
    initDB: jest.fn()
}));

jest.mock('../src/hooks/ToasterHook', () => ({
    useToaster: jest.fn()
}));

jest.mock('../src/axios', () => ({
    registerErrorCallback: jest.fn(),
    registerSuccessCallback: jest.fn()
}));

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    RouterProvider: ({ router }) => <div data-testid="router-provider">Router Provider</div>,
    createBrowserRouter: jest.fn(() => ({})),
    createRoutesFromElements: jest.fn(children => children)
}));

jest.mock('../src/pages/Dashboard/Stream/VideoStream', () => () => <div data-testid="video-stream">VideoStream</div>);
jest.mock('../src/pages/Dashboard/Map/FullMap', () => () => <div data-testid="full-map">FullMap</div>);
jest.mock('../src/pages/Dashboard/User/UserManagement', () => () => <div data-testid="user-management">UserManagement</div>);
jest.mock('../src/pages/Dashboard/LogsPage/LogManagement', () => () => <div data-testid="log-management">LogManagement</div>);
jest.mock('../src/pages/Dashboard/ApiKeys/ApiKeyManagement', () => () => <div data-testid="api-key-management">ApiKeyManagement</div>);
jest.mock('../src/pages/Dashboard/Statistics/StatisticsManagement', () => () => <div data-testid="statistics-management">StatisticsManagement</div>);
jest.mock('../src/pages/Dashboard/Events/EventManagement', () => () => <div data-testid="events-management">EventsManagement</div>);
jest.mock('../src/pages/Settings/Settings', () => () => <div data-testid="settings">Settings</div>);

describe('Router Component', () => {
    const mockToaster = jest.fn();

    const paths = [
        { path: 'stream', testId: 'video-stream' },
        { path: 'map', testId: 'full-map' },
        { path: 'users', testId: 'user-management' },
        { path: 'logs', testId: 'log-management' },
        { path: 'api-keys', testId: 'api-key-management' },
        { path: 'statistics', testId: 'statistics-management' },
        { path: 'settings', testId: 'settings' },
        { path: 'events', testId: 'events-management' }
    ];

    beforeEach(() => {
        jest.clearAllMocks();
        useToaster.mockReturnValue(mockToaster);
        localStorage.clear();
        console.error = jest.fn();
        idb.clearIndexedDB.mockClear();
        idb.initDB.mockClear();
    });

    it('should clear IndexedDB when version is missing', async () => {
        localStorage.clear();
        await initializeApp();

        expect(localStorage.getItem('QMA_Version')).toBe(version);
        expect(idb.clearIndexedDB).toHaveBeenCalled();
        expect(idb.initDB).toHaveBeenCalled();
    });

    it('should clear IndexedDB when version is different', async () => {
        localStorage.setItem('QMA_Version', '1.0.0');
        await initializeApp();

        expect(localStorage.getItem('QMA_Version')).toBe(version);
        expect(idb.clearIndexedDB).toHaveBeenCalled();
        expect(idb.initDB).toHaveBeenCalled();
    });

    it('should not clear IndexedDB when version matches', async () => {
        localStorage.setItem('QMA_Version', version);
        await initializeApp();

        expect(localStorage.getItem('QMA_Version')).toBe(version);
        expect(idb.clearIndexedDB).not.toHaveBeenCalled();
        expect(idb.initDB).toHaveBeenCalled();
    });

    it('should handle error during initDB', async () => {
        localStorage.clear();
        const error = new Error('Init DB failed');
        idb.initDB.mockRejectedValueOnce(error);

        await initializeApp();

        expect(console.error).toHaveBeenCalledWith('Error checking version:', error);
    });

    it('should return true even after catching errors', async () => {
        localStorage.clear();
        idb.clearIndexedDB.mockRejectedValueOnce(new Error('Test error'));

        const result = await initializeApp();
        expect(result).toBe(true);
    });

    it('should not render until initialization is complete', () => {
        render(<Router />);
        expect(screen.queryByTestId('router-provider')).not.toBeInTheDocument();
    });

    it('should render after initialization', async () => {
        render(<Router />);

        await waitFor(() => {
            expect(screen.getByTestId('router-provider')).toBeInTheDocument();
        });
    });

    it.each(paths)('should show $path component when path is $path', ({ path, testId }) => {
        render(<DashboardRouteToggler path={path} />);
        const targetComponent = screen.getByTestId(testId);
        expect(targetComponent).toHaveStyle({ display: 'block' });
    });

    it('should call toaster when callbacks are triggered', async () => {
        render(<Router />);

        await waitFor(() => {
            const errorCallback = registerErrorCallback.mock.calls[0][0];
            const successCallback = registerSuccessCallback.mock.calls[0][0];

            errorCallback('Error message', { variant: 'error' });
            successCallback('Success message', { variant: 'success' });

            expect(mockToaster).toHaveBeenCalledWith('Error message', { variant: 'error' });
            expect(mockToaster).toHaveBeenCalledWith('Success message', { variant: 'success' });
        });
    });
});
