const ThingsboardDevices = require("../models/ThingsboardDevices");
const microserviceSocket = require("../microservice_socket");

class ThingsBoardService {
    async getAllDevices() {
        return ThingsboardDevices.find({}, { deviceId: 1, deviceName: 1, accessToken: 1, dashboardId: 1 });
    }

    async getDeviceByUnitId(unitId) {
        return ThingsboardDevices.findOne({ deviceName: unitId }, { deviceId: 1, deviceName: 1, accessToken: 1, dashboardId: 1 });
    }

    async resetDashboards() {
        microserviceSocket.emit("thingsboard/reset-dashboard");
    }
}

module.exports = new ThingsBoardService();
