import { Modal, Grid, FormControlLabel, Checkbox, IconButton, Button, CircularProgress } from "@mui/material";
import ModalContainer from "../../../components/ModalContainer";
import { Remove, Add } from "@mui/icons-material";
import { useEffect, useState } from "react";
import apiKeyController from "../../../controllers/ApiKey.controller";

const ApiAccessModal = ({ updateKeyAccess, setUpdateKeyAccess, endpoints, fetchKeys }) => {
    const [allowSave, setAllowSave] = useState(false);
    const [allowReset, setAllowReset] = useState(false);
    const [updatedKey, setUpdatedKey] = useState(updateKeyAccess);
    const [saving, setSaving] = useState(false);
    const [expandedCategories, setExpandedCategories] = useState([]);

    useEffect(() => {
        setUpdatedKey(updateKeyAccess);
    }, [updateKeyAccess]);

    useEffect(() => {
        if (endpoints.length > 0) {
            const accessibleEndpoints = endpoints.filter((e) => e.is_accessible);
            setExpandedCategories([...new Set(accessibleEndpoints.map((e) => e.category))]);
        }
    }, [endpoints]);

    const handleClose = () => {
        setUpdateKeyAccess();
        setAllowSave(false);
        setAllowReset(false);
    };

    const onReset = () => {
        setUpdatedKey(updateKeyAccess);
        setAllowSave(false);
        setAllowReset(false);
    };

    const onSave = async () => {
        try {
            setSaving(true);
            await apiKeyController.updateAllowedEndpoints({
                id: updatedKey._id,
                allowed_endpoints: updatedKey.allowed_endpoints,
            });
            setAllowSave(false);
            setAllowReset(false);
            fetchKeys();
            handleClose();
        } catch (error) {
            console.error("Error updating API key access:", error);
        } finally {
            fetchKeys();
            setSaving(false);
        }
    };

    const onChangeEndpoint = (e, endpoint_id) => {
        setAllowSave(true);
        setAllowReset(true);
        setUpdatedKey((v) =>
            e.target.checked
                ? { ...v, allowed_endpoints: v.allowed_endpoints.concat(endpoint_id) }
                : { ...v, allowed_endpoints: v.allowed_endpoints.filter((e_id) => e_id !== endpoint_id) },
        );
    };

    const onChangeCategory = (e, category) => {
        setAllowSave(true);
        setAllowReset(true);
        const endpoint_ids = endpoints.filter((e) => e.category === category).map((e) => e.endpoint_id);
        setUpdatedKey((v) =>
            e.target.checked
                ? { ...v, allowed_endpoints: [...new Set(v.allowed_endpoints.concat(endpoint_ids))] }
                : { ...v, allowed_endpoints: v.allowed_endpoints.filter((e_id) => !endpoint_ids.includes(e_id)) },
        );
    };

    const isCategoryChecked = (category) => {
        const categoryEndpoints = endpoints.filter((e) => e.category === category);
        return categoryEndpoints.every((e) => e.is_public || updatedKey?.allowed_endpoints.includes(e.endpoint_id));
    };

    const isCategoryIndeterminate = (category) => {
        const categoryEndpoints = endpoints.filter((e) => e.category === category);
        const selectedEndpoints = categoryEndpoints.filter((e) => e.is_public || updatedKey?.allowed_endpoints.includes(e.endpoint_id));
        return selectedEndpoints.length > 0 && selectedEndpoints.length < categoryEndpoints.length;
    };

    const isCategoryDisabled = (category) => {
        const categoryEndpoints = endpoints.filter((e) => e.category === category);
        return categoryEndpoints.every((e) => e.is_public);
    };

    const sortedCategories = [...new Set(endpoints.filter((e) => e.is_accessible).map((e) => e.category))].sort((a, b) => {
        const isADisabled = isCategoryDisabled(a);
        const isBDisabled = isCategoryDisabled(b);
        if (isADisabled && !isBDisabled) return 1;
        if (!isADisabled && isBDisabled) return -1;
        return 0;
    });

    return (
        <Modal open={updatedKey ? true : false} onClose={handleClose}>
            <ModalContainer title={`Update API Access #${updatedKey?.serial}`} onClose={handleClose}>
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: 500 }}>
                    <Grid
                        container
                        overflow={"auto"}
                        maxHeight={"50vh"}
                        padding={2}
                        border={(theme) => `1px solid ${theme.palette.custom.borderColor}`}
                        borderRadius={"5px"}
                    >
                        {sortedCategories.map((category, index) => {
                            const categoryEndpoints = endpoints.filter((e) => e.category === category && e.is_accessible);
                            if (categoryEndpoints.length === 0) return null;
                            return (
                                <Grid container key={index} flexDirection={"column"} width={"100%"}>
                                    <Grid container justifyContent={"space-between"}>
                                        <Grid>
                                            <FormControlLabel
                                                label={category}
                                                sx={{
                                                    "& .MuiTypography-root": {
                                                        fontSize: "16px",
                                                        fontWeight: "400",
                                                    },
                                                }}
                                                control={
                                                    <Checkbox
                                                        size="small"
                                                        indeterminate={isCategoryIndeterminate(category)}
                                                        checked={isCategoryChecked(category)}
                                                        onChange={(e) => onChangeCategory(e, category)}
                                                        disabled={isCategoryDisabled(category)}
                                                    />
                                                }
                                            />
                                        </Grid>
                                        <Grid>
                                            <IconButton
                                                onClick={() =>
                                                    setExpandedCategories((v) =>
                                                        v.includes(category) ? v.filter((c) => c !== category) : v.concat(category),
                                                    )
                                                }
                                            >
                                                {expandedCategories.includes(category) ? <Remove /> : <Add />}
                                            </IconButton>
                                        </Grid>
                                    </Grid>
                                    <Grid
                                        container
                                        flexDirection={"column"}
                                        display={expandedCategories.includes(category) ? "flex" : "none"}
                                        sx={{ background: (theme) => theme.palette.custom.borderColor }}
                                        padding={"10px 20px"}
                                        borderRadius={"10px"}
                                    >
                                        {categoryEndpoints.map((endpoint, index) => (
                                            <Grid key={index}>
                                                <FormControlLabel
                                                    label={endpoint.name}
                                                    sx={{
                                                        "& .MuiTypography-root": {
                                                            fontSize: "16px",
                                                            fontWeight: "400",
                                                        },
                                                    }}
                                                    control={
                                                        <Checkbox
                                                            disabled={endpoint.is_public}
                                                            size="small"
                                                            checked={
                                                                endpoint.is_public || updatedKey?.allowed_endpoints.includes(endpoint.endpoint_id)
                                                            }
                                                            onChange={(e) => onChangeEndpoint(e, endpoint.endpoint_id)}
                                                        />
                                                    }
                                                />
                                            </Grid>
                                        ))}
                                    </Grid>
                                </Grid>
                            );
                        })}
                    </Grid>
                    <Grid container gap={1} justifyContent={"flex-end"}>
                        <Grid>
                            <Button disabled={!allowReset} onClick={onReset} variant="outlined">
                                Reset
                            </Button>
                        </Grid>
                        <Grid>
                            <Button
                                disabled={!allowSave || saving}
                                startIcon={saving && <CircularProgress size={18} />}
                                variant="contained"
                                onClick={onSave}
                            >
                                Save
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default ApiAccessModal;
