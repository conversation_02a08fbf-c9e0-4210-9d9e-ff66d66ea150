import { alpha, FormControl, Grid, MenuItem, Pagination, Select, Typography } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { useEffect, useState } from "react";
import axiosInstance from "../../../../axios";
import dayjs from "dayjs";
import { userValues } from "../../../../utils";
import { SentimentVeryDissatisfied } from "@mui/icons-material";
import { useApp } from "../../../../hooks/AppHook";
import { useToaster } from "../../../../hooks/ToasterHook";
import theme from "../../../../theme";
import AddOrganizationModal from "./AddOrganizationModal";
import DeleteConfirmationModal from "./DeleteConfirmationModal";
import EditOrganizationModal from "./EditOrganizationModal";
import DeleteButton from "../../../../components/DeleteButton";
import EditButton from "../../../../components/EditButton";
import { useUser } from "../../../../hooks/UserHook.jsx";

export default function Organizations({ showAddOrganization, setShowAddOrganization }) {
    const { isMobile, timezone } = useApp();
    const { user } = useUser();
    const toaster = useToaster();
    const [filteredOrganizations, setFilteredOrganizations] = useState([]);
    const [organizations, setOrganizations] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    // eslint-disable-next-line no-unused-vars
    const [adding, setAdding] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [organizationToDelete, setOrganizationToDelete] = useState(null);
    const [showEditModal, setShowEditModal] = useState(false);
    const [organizationToEdit, setOrganizationToEdit] = useState(null);

    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    const fetchOrganizations = async () => {
        setIsLoading(true);
        try {
            const { data } = await axiosInstance.get("/organizations");
            if (Array.isArray(data) && data.length > 0) {
                setOrganizations(data);
            } else {
                setOrganizations([]);
                toaster("No data found for organizations", { variant: "warning" });
            }
        } catch (err) {
            setOrganizations([]);
            toaster("Something went wrong", { variant: "error" });
            console.error("An error occurred while fetching organizations on the Organizations Page", err);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchOrganizations();
    }, []);

    useEffect(() => {
        setFilteredOrganizations(organizations);
    }, [organizations]);

    const handleDelete = async () => {
        if (!organizationToDelete) return;
        try {
            await axiosInstance.delete(`/organizations/${organizationToDelete._id}`, { meta: { showSnackbar: true } });
            toaster("Organization deleted successfully", { variant: "success" });
            fetchOrganizations();
        } catch (err) {
            toaster("Failed to delete organization", { variant: "error" });
            console.error("Error deleting organization:", err);
        } finally {
            setOrganizationToDelete(null);
        }
    };

    const handleEditClick = (organization) => {
        setOrganizationToEdit(organization);
        setShowEditModal(true);
    };

    // Custom Footer Component
    const CustomFooter = ({ page, rowsPerPage, totalRows, onPageChange, onRowsPerPageChange }) => {
        const startIndex = (page - 1) * rowsPerPage + 1;
        const endIndex = Math.min(page * rowsPerPage, totalRows);

        return (
            <>
                <Grid
                    container
                    justifyContent={{ sm: "space-between", xs: "center" }}
                    alignItems={"center"}
                    padding={"10px"}
                    backgroundColor={alpha(theme.palette.custom.offline, 0.08)}
                    gap={2}
                    sx={{
                        borderRadius: "5px",
                    }}
                >
                    <Grid padding={"10px 20px"} size="auto">
                        <Typography fontSize={{ xs: "12px", lg: "14px" }} fontWeight={600}>
                            {`${startIndex} - ${endIndex} of ${totalRows}`}
                        </Typography>
                    </Grid>
                    <Grid size="auto">
                        <Pagination
                            count={Math.ceil(totalRows / rowsPerPage)}
                            page={page}
                            onChange={onPageChange}
                            shape="rounded"
                            siblingCount={isMobile ? 0 : 1}
                            boundaryCount={1}
                            sx={{
                                "& .MuiButtonBase-root, .MuiPaginationItem-root": {
                                    color: "#FFFFFF",
                                    minHeight: "30px",
                                    fontSize: isMobile ? "9px" : "14px",
                                    borderRadius: "8px",
                                    minWidth: "32px",
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    backgroundColor: alpha(theme.palette.custom.offline, 0.2),
                                },
                                "& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected": {
                                    color: "#FFFFFF",
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                        />
                    </Grid>
                    <Grid justifyContent="flex-end" display={"flex"} size="auto">
                        <FormControl variant="outlined">
                            <Select
                                value={rowsPerPage}
                                onChange={onRowsPerPageChange}
                                sx={{
                                    "& .MuiOutlinedInput-notchedOutline": {
                                        border: "none",
                                    },
                                    "& .MuiSelect-select": {
                                        padding: "10px",
                                        fontSize: isMobile ? "12px" : "16px",
                                        backgroundColor: theme.palette.custom.mainBlue,
                                        borderRadius: "5px",
                                        color: "#FFFFFF",
                                        minWidth: isMobile ? 0 : "80px",
                                    },
                                }}
                            >
                                {[5, 10, 20].map((size) => (
                                    <MenuItem key={size} value={size}>
                                        {isMobile ? size : `${size} / Page`}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                </Grid>
            </>
        );
    };

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handlePageSizeChange = (event) => {
        setRowsPerPage(event.target.value);
    };

    /**
     * @type {Array<import("@mui/x-data-grid").GridColDef>}
     */
    const columns = [
        { field: "name", headerName: "Name", minWidth: 200 },
        { field: "domain", headerName: "Domain", minWidth: 200 },
        { field: "user", headerName: "Created By", minWidth: 200, valueGetter: (v) => v?.name },
        { field: "user_count", headerName: "Total Users", minWidth: 150 },
        {
            field: "creation_timestamp",
            headerName: "Created",
            minWidth: 200,
            valueGetter: (v) =>
                dayjs(v)
                    .tz(timezone)
                    .format(userValues.dateTimeFormat(user, { exclude_seconds: true, exclude_hours: true, exclude_minutes: true })),
        },
        { field: "user", headerName: "Created By", valueGetter: (v) => v?.name },
        {
            field: "actions",
            headerName: "Actions",
            minWidth: 100,
            renderCell: (params) => (
                <Grid container justifyContent={"start"} gap={1}>
                    <Grid>
                        <EditButton onClick={() => handleEditClick(params.row)} />
                    </Grid>
                    <Grid>
                        <DeleteButton
                            onClick={() => {
                                setOrganizationToDelete(params.row);
                                setShowDeleteModal(true);
                            }}
                        />
                    </Grid>
                </Grid>
            ),
        },
    ];

    const columnsWithouFilters = [
        ...columns.map((col) => ({
            ...col,
            flex: 1,
            filterable: false,
            sortable: true,
            resizable: false,
            disableColumnMenu: true,
            disableReorder: true,
            disableExport: true,
        })),
    ];

    return (
        <Grid container flexDirection={"column"} height={"100%"}>
            <Grid overflow={"auto"} size="grow">
                <DataGrid
                    loading={isLoading}
                    disableRowSelectionOnClick
                    rows={filteredOrganizations.slice((page - 1) * rowsPerPage, page * rowsPerPage)}
                    columns={columnsWithouFilters}
                    getRowId={(row) => row._id}
                    slots={{
                        footer: () => (
                            <CustomFooter
                                page={page}
                                rowsPerPage={rowsPerPage}
                                totalRows={filteredOrganizations.length}
                                onPageChange={handlePageChange}
                                onRowsPerPageChange={handlePageSizeChange}
                            />
                        ),
                        noRowsOverlay: () => (
                            <Grid display={"flex"} flexDirection={"column"} alignItems={"center"} justifyContent={"center"} height={"100%"}>
                                <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
                                <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                                    No data available
                                </Typography>
                            </Grid>
                        ),
                    }}
                    getRowHeight={() => 50}
                />
                <AddOrganizationModal
                    showAddOrganization={showAddOrganization}
                    setShowAddOrganization={setShowAddOrganization}
                    setAdding={setAdding}
                    onSuccess={fetchOrganizations}
                />
                <DeleteConfirmationModal showDeleteModal={showDeleteModal} setShowDeleteModal={setShowDeleteModal} onDelete={handleDelete} />
                <EditOrganizationModal
                    showEditModal={showEditModal}
                    setShowEditModal={setShowEditModal}
                    organizationToEdit={organizationToEdit}
                    setOrganizationToEdit={setOrganizationToEdit}
                    onSuccess={fetchOrganizations}
                />
            </Grid>
        </Grid>
    );
}
