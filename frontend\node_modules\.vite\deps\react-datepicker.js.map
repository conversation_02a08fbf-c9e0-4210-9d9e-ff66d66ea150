{"version": 3, "sources": ["../../@floating-ui/react/dist/floating-ui.react.mjs", "../../@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../@floating-ui/react/dist/floating-ui.react.utils.mjs", "../../@floating-ui/utils/dist/floating-ui.utils.mjs", "../../tabbable/src/index.js", "../../@floating-ui/core/dist/floating-ui.core.mjs", "../../@floating-ui/dom/dist/floating-ui.dom.mjs", "../../@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "../../react-datepicker/node_modules/tslib/tslib.es6.js", "../../react-datepicker/src/calendar_container.tsx", "../../react-datepicker/src/click_outside_wrapper.tsx", "../../react-datepicker/src/date_utils.ts", "../../react-datepicker/src/input_time.tsx", "../../react-datepicker/src/day.tsx", "../../react-datepicker/src/week_number.tsx", "../../react-datepicker/src/week.tsx", "../../react-datepicker/src/month.tsx", "../../react-datepicker/src/month_dropdown_options.tsx", "../../react-datepicker/src/month_dropdown.tsx", "../../react-datepicker/src/month_year_dropdown_options.tsx", "../../react-datepicker/src/month_year_dropdown.tsx", "../../react-datepicker/src/time.tsx", "../../react-datepicker/src/year.tsx", "../../react-datepicker/src/year_dropdown_options.tsx", "../../react-datepicker/src/year_dropdown.tsx", "../../react-datepicker/src/calendar.tsx", "../../react-datepicker/src/calendar_icon.tsx", "../../react-datepicker/src/portal.tsx", "../../react-datepicker/src/tab_loop.tsx", "../../react-datepicker/src/with_floating.tsx", "../../react-datepicker/src/popper_component.tsx", "../../react-datepicker/src/index.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useModernLayoutEffect, useEffectEvent, getMinListIndex, getMaxListIndex, createGridCellMap, isListIndexDisabled, getGridNavigatedIndex, getGridCellIndexOfCorner, getGridCellIndices, findNonDisabledListIndex, isIndexOutOfListBounds, useLatestRef, getDocument as getDocument$1, isMouseLikePointerType, contains as contains$1, isSafari, enableFocusInside, isOutsideEvent, getPreviousTabbable, getNextTabbable, disableFocusInside, isTypeableCombobox, getFloatingFocusElement, getTabbableOptions, getNodeAncestors, activeElement, getNodeChildren as getNodeChildren$1, stopEvent, getTarget as getTarget$1, isVirtualClick, isVirtualPointerEvent, getPlatform, isTypeableElement, isReactEvent, isRootElement, isEventTargetWithin, matchesFocusVisible, isMac, getDeepestNode, getUserAgent } from '@floating-ui/react/utils';\nimport { jsx, jsxs, Fragment } from 'react/jsx-runtime';\nimport { getComputedStyle, isElement, isShadowRoot, getNodeName, isHTMLElement, getWindow, isLastTraversableNode, getParentNode, isWebKit } from '@floating-ui/utils/dom';\nimport { tabbable, isTabbable, focusable } from 'tabbable';\nimport * as ReactDOM from 'react-dom';\nimport { getOverflowAncestors, useFloating as useFloating$1, offset, detectOverflow } from '@floating-ui/react-dom';\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, getOverflowAncestors, hide, inline, limitShift, offset, platform, shift, size } from '@floating-ui/react-dom';\nimport { evaluate, max, round, min } from '@floating-ui/utils';\n\n/**\n * Merges an array of refs into a single memoized callback ref or `null`.\n * @see https://floating-ui.com/docs/react-utils#usemergerefs\n */\nfunction useMergeRefs(refs) {\n  const cleanupRef = React.useRef(undefined);\n  const refEffect = React.useCallback(instance => {\n    const cleanups = refs.map(ref => {\n      if (ref == null) {\n        return;\n      }\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return typeof refCleanup === 'function' ? refCleanup : () => {\n          refCallback(null);\n        };\n      }\n      ref.current = instance;\n      return () => {\n        ref.current = null;\n      };\n    });\n    return () => {\n      cleanups.forEach(refCleanup => refCleanup == null ? void 0 : refCleanup());\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (value != null) {\n        cleanupRef.current = refEffect(value);\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}\n\nfunction sortByDocumentPosition(a, b) {\n  const position = a.compareDocumentPosition(b);\n  if (position & Node.DOCUMENT_POSITION_FOLLOWING || position & Node.DOCUMENT_POSITION_CONTAINED_BY) {\n    return -1;\n  }\n  if (position & Node.DOCUMENT_POSITION_PRECEDING || position & Node.DOCUMENT_POSITION_CONTAINS) {\n    return 1;\n  }\n  return 0;\n}\nconst FloatingListContext = /*#__PURE__*/React.createContext({\n  register: () => {},\n  unregister: () => {},\n  map: /*#__PURE__*/new Map(),\n  elementsRef: {\n    current: []\n  }\n});\n/**\n * Provides context for a list of items within the floating element.\n * @see https://floating-ui.com/docs/FloatingList\n */\nfunction FloatingList(props) {\n  const {\n    children,\n    elementsRef,\n    labelsRef\n  } = props;\n  const [nodes, setNodes] = React.useState(() => new Set());\n  const register = React.useCallback(node => {\n    setNodes(prevSet => new Set(prevSet).add(node));\n  }, []);\n  const unregister = React.useCallback(node => {\n    setNodes(prevSet => {\n      const set = new Set(prevSet);\n      set.delete(node);\n      return set;\n    });\n  }, []);\n  const map = React.useMemo(() => {\n    const newMap = new Map();\n    const sortedNodes = Array.from(nodes.keys()).sort(sortByDocumentPosition);\n    sortedNodes.forEach((node, index) => {\n      newMap.set(node, index);\n    });\n    return newMap;\n  }, [nodes]);\n  return /*#__PURE__*/jsx(FloatingListContext.Provider, {\n    value: React.useMemo(() => ({\n      register,\n      unregister,\n      map,\n      elementsRef,\n      labelsRef\n    }), [register, unregister, map, elementsRef, labelsRef]),\n    children: children\n  });\n}\n/**\n * Used to register a list item and its index (DOM position) in the\n * `FloatingList`.\n * @see https://floating-ui.com/docs/FloatingList#uselistitem\n */\nfunction useListItem(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    label\n  } = props;\n  const {\n    register,\n    unregister,\n    map,\n    elementsRef,\n    labelsRef\n  } = React.useContext(FloatingListContext);\n  const [index, setIndex] = React.useState(null);\n  const componentRef = React.useRef(null);\n  const ref = React.useCallback(node => {\n    componentRef.current = node;\n    if (index !== null) {\n      elementsRef.current[index] = node;\n      if (labelsRef) {\n        var _node$textContent;\n        const isLabelDefined = label !== undefined;\n        labelsRef.current[index] = isLabelDefined ? label : (_node$textContent = node == null ? void 0 : node.textContent) != null ? _node$textContent : null;\n      }\n    }\n  }, [index, elementsRef, labelsRef, label]);\n  useModernLayoutEffect(() => {\n    const node = componentRef.current;\n    if (node) {\n      register(node);\n      return () => {\n        unregister(node);\n      };\n    }\n  }, [register, unregister]);\n  useModernLayoutEffect(() => {\n    const index = componentRef.current ? map.get(componentRef.current) : null;\n    if (index != null) {\n      setIndex(index);\n    }\n  }, [map]);\n  return React.useMemo(() => ({\n    ref,\n    index: index == null ? -1 : index\n  }), [index, ref]);\n}\n\nconst FOCUSABLE_ATTRIBUTE = 'data-floating-ui-focusable';\nconst ACTIVE_KEY = 'active';\nconst SELECTED_KEY = 'selected';\nconst ARROW_LEFT = 'ArrowLeft';\nconst ARROW_RIGHT = 'ArrowRight';\nconst ARROW_UP = 'ArrowUp';\nconst ARROW_DOWN = 'ArrowDown';\n\nfunction renderJsx(render, computedProps) {\n  if (typeof render === 'function') {\n    return render(computedProps);\n  }\n  if (render) {\n    return /*#__PURE__*/React.cloneElement(render, computedProps);\n  }\n  return /*#__PURE__*/jsx(\"div\", {\n    ...computedProps\n  });\n}\nconst CompositeContext = /*#__PURE__*/React.createContext({\n  activeIndex: 0,\n  onNavigate: () => {}\n});\nconst horizontalKeys = [ARROW_LEFT, ARROW_RIGHT];\nconst verticalKeys = [ARROW_UP, ARROW_DOWN];\nconst allKeys = [...horizontalKeys, ...verticalKeys];\n\n/**\n * Creates a single tab stop whose items are navigated by arrow keys, which\n * provides list navigation outside of floating element contexts.\n *\n * This is useful to enable navigation of a list of items that aren’t part of a\n * floating element. A menubar is an example of a composite, with each reference\n * element being an item.\n * @see https://floating-ui.com/docs/Composite\n */\nconst Composite = /*#__PURE__*/React.forwardRef(function Composite(props, forwardedRef) {\n  const {\n    render,\n    orientation = 'both',\n    loop = true,\n    rtl = false,\n    cols = 1,\n    disabledIndices,\n    activeIndex: externalActiveIndex,\n    onNavigate: externalSetActiveIndex,\n    itemSizes,\n    dense = false,\n    ...domProps\n  } = props;\n  const [internalActiveIndex, internalSetActiveIndex] = React.useState(0);\n  const activeIndex = externalActiveIndex != null ? externalActiveIndex : internalActiveIndex;\n  const onNavigate = useEffectEvent(externalSetActiveIndex != null ? externalSetActiveIndex : internalSetActiveIndex);\n  const elementsRef = React.useRef([]);\n  const renderElementProps = render && typeof render !== 'function' ? render.props : {};\n  const contextValue = React.useMemo(() => ({\n    activeIndex,\n    onNavigate\n  }), [activeIndex, onNavigate]);\n  const isGrid = cols > 1;\n  function handleKeyDown(event) {\n    if (!allKeys.includes(event.key)) return;\n    let nextIndex = activeIndex;\n    const minIndex = getMinListIndex(elementsRef, disabledIndices);\n    const maxIndex = getMaxListIndex(elementsRef, disabledIndices);\n    const horizontalEndKey = rtl ? ARROW_LEFT : ARROW_RIGHT;\n    const horizontalStartKey = rtl ? ARROW_RIGHT : ARROW_LEFT;\n    if (isGrid) {\n      const sizes = itemSizes || Array.from({\n        length: elementsRef.current.length\n      }, () => ({\n        width: 1,\n        height: 1\n      }));\n      // To calculate movements on the grid, we use hypothetical cell indices\n      // as if every item was 1x1, then convert back to real indices.\n      const cellMap = createGridCellMap(sizes, cols, dense);\n      const minGridIndex = cellMap.findIndex(index => index != null && !isListIndexDisabled(elementsRef, index, disabledIndices));\n      // last enabled index\n      const maxGridIndex = cellMap.reduce((foundIndex, index, cellIndex) => index != null && !isListIndexDisabled(elementsRef, index, disabledIndices) ? cellIndex : foundIndex, -1);\n      const maybeNextIndex = cellMap[getGridNavigatedIndex({\n        current: cellMap.map(itemIndex => itemIndex ? elementsRef.current[itemIndex] : null)\n      }, {\n        event,\n        orientation,\n        loop,\n        rtl,\n        cols,\n        // treat undefined (empty grid spaces) as disabled indices so we\n        // don't end up in them\n        disabledIndices: getGridCellIndices([...(disabledIndices || elementsRef.current.map((_, index) => isListIndexDisabled(elementsRef, index) ? index : undefined)), undefined], cellMap),\n        minIndex: minGridIndex,\n        maxIndex: maxGridIndex,\n        prevIndex: getGridCellIndexOfCorner(activeIndex > maxIndex ? minIndex : activeIndex, sizes, cellMap, cols,\n        // use a corner matching the edge closest to the direction we're\n        // moving in so we don't end up in the same item. Prefer\n        // top/left over bottom/right.\n        event.key === ARROW_DOWN ? 'bl' : event.key === horizontalEndKey ? 'tr' : 'tl')\n      })];\n      if (maybeNextIndex != null) {\n        nextIndex = maybeNextIndex;\n      }\n    }\n    const toEndKeys = {\n      horizontal: [horizontalEndKey],\n      vertical: [ARROW_DOWN],\n      both: [horizontalEndKey, ARROW_DOWN]\n    }[orientation];\n    const toStartKeys = {\n      horizontal: [horizontalStartKey],\n      vertical: [ARROW_UP],\n      both: [horizontalStartKey, ARROW_UP]\n    }[orientation];\n    const preventedKeys = isGrid ? allKeys : {\n      horizontal: horizontalKeys,\n      vertical: verticalKeys,\n      both: allKeys\n    }[orientation];\n    if (nextIndex === activeIndex && [...toEndKeys, ...toStartKeys].includes(event.key)) {\n      if (loop && nextIndex === maxIndex && toEndKeys.includes(event.key)) {\n        nextIndex = minIndex;\n      } else if (loop && nextIndex === minIndex && toStartKeys.includes(event.key)) {\n        nextIndex = maxIndex;\n      } else {\n        nextIndex = findNonDisabledListIndex(elementsRef, {\n          startingIndex: nextIndex,\n          decrement: toStartKeys.includes(event.key),\n          disabledIndices\n        });\n      }\n    }\n    if (nextIndex !== activeIndex && !isIndexOutOfListBounds(elementsRef, nextIndex)) {\n      var _elementsRef$current$;\n      event.stopPropagation();\n      if (preventedKeys.includes(event.key)) {\n        event.preventDefault();\n      }\n      onNavigate(nextIndex);\n      (_elementsRef$current$ = elementsRef.current[nextIndex]) == null || _elementsRef$current$.focus();\n    }\n  }\n  const computedProps = {\n    ...domProps,\n    ...renderElementProps,\n    ref: forwardedRef,\n    'aria-orientation': orientation === 'both' ? undefined : orientation,\n    onKeyDown(e) {\n      domProps.onKeyDown == null || domProps.onKeyDown(e);\n      renderElementProps.onKeyDown == null || renderElementProps.onKeyDown(e);\n      handleKeyDown(e);\n    }\n  };\n  return /*#__PURE__*/jsx(CompositeContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/jsx(FloatingList, {\n      elementsRef: elementsRef,\n      children: renderJsx(render, computedProps)\n    })\n  });\n});\n/**\n * @see https://floating-ui.com/docs/Composite\n */\nconst CompositeItem = /*#__PURE__*/React.forwardRef(function CompositeItem(props, forwardedRef) {\n  const {\n    render,\n    ...domProps\n  } = props;\n  const renderElementProps = render && typeof render !== 'function' ? render.props : {};\n  const {\n    activeIndex,\n    onNavigate\n  } = React.useContext(CompositeContext);\n  const {\n    ref,\n    index\n  } = useListItem();\n  const mergedRef = useMergeRefs([ref, forwardedRef, renderElementProps.ref]);\n  const isActive = activeIndex === index;\n  const computedProps = {\n    ...domProps,\n    ...renderElementProps,\n    ref: mergedRef,\n    tabIndex: isActive ? 0 : -1,\n    'data-active': isActive ? '' : undefined,\n    onFocus(e) {\n      domProps.onFocus == null || domProps.onFocus(e);\n      renderElementProps.onFocus == null || renderElementProps.onFocus(e);\n      onNavigate(index);\n    }\n  };\n  return renderJsx(render, computedProps);\n});\n\n// https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379\nconst SafeReact = {\n  ...React\n};\n\nlet serverHandoffComplete = false;\nlet count = 0;\nconst genId = () => // Ensure the id is unique with multiple independent versions of Floating UI\n// on <React 18\n\"floating-ui-\" + Math.random().toString(36).slice(2, 6) + count++;\nfunction useFloatingId() {\n  const [id, setId] = React.useState(() => serverHandoffComplete ? genId() : undefined);\n  useModernLayoutEffect(() => {\n    if (id == null) {\n      setId(genId());\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  React.useEffect(() => {\n    serverHandoffComplete = true;\n  }, []);\n  return id;\n}\nconst useReactId = SafeReact.useId;\n\n/**\n * Uses React 18's built-in `useId()` when available, or falls back to a\n * slightly less performant (requiring a double render) implementation for\n * earlier React versions.\n * @see https://floating-ui.com/docs/react-utils#useid\n */\nconst useId = useReactId || useFloatingId;\n\nlet devMessageSet;\nif (process.env.NODE_ENV !== \"production\") {\n  devMessageSet = /*#__PURE__*/new Set();\n}\nfunction warn() {\n  var _devMessageSet;\n  for (var _len = arguments.length, messages = new Array(_len), _key = 0; _key < _len; _key++) {\n    messages[_key] = arguments[_key];\n  }\n  const message = \"Floating UI: \" + messages.join(' ');\n  if (!((_devMessageSet = devMessageSet) != null && _devMessageSet.has(message))) {\n    var _devMessageSet2;\n    (_devMessageSet2 = devMessageSet) == null || _devMessageSet2.add(message);\n    console.warn(message);\n  }\n}\nfunction error() {\n  var _devMessageSet3;\n  for (var _len2 = arguments.length, messages = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    messages[_key2] = arguments[_key2];\n  }\n  const message = \"Floating UI: \" + messages.join(' ');\n  if (!((_devMessageSet3 = devMessageSet) != null && _devMessageSet3.has(message))) {\n    var _devMessageSet4;\n    (_devMessageSet4 = devMessageSet) == null || _devMessageSet4.add(message);\n    console.error(message);\n  }\n}\n\n/**\n * Renders a pointing arrow triangle.\n * @see https://floating-ui.com/docs/FloatingArrow\n */\nconst FloatingArrow = /*#__PURE__*/React.forwardRef(function FloatingArrow(props, ref) {\n  const {\n    context: {\n      placement,\n      elements: {\n        floating\n      },\n      middlewareData: {\n        arrow,\n        shift\n      }\n    },\n    width = 14,\n    height = 7,\n    tipRadius = 0,\n    strokeWidth = 0,\n    staticOffset,\n    stroke,\n    d,\n    style: {\n      transform,\n      ...restStyle\n    } = {},\n    ...rest\n  } = props;\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!ref) {\n      warn('The `ref` prop is required for `FloatingArrow`.');\n    }\n  }\n  const clipPathId = useId();\n  const [isRTL, setIsRTL] = React.useState(false);\n\n  // https://github.com/floating-ui/floating-ui/issues/2932\n  useModernLayoutEffect(() => {\n    if (!floating) return;\n    const isRTL = getComputedStyle(floating).direction === 'rtl';\n    if (isRTL) {\n      setIsRTL(true);\n    }\n  }, [floating]);\n  if (!floating) {\n    return null;\n  }\n  const [side, alignment] = placement.split('-');\n  const isVerticalSide = side === 'top' || side === 'bottom';\n  let computedStaticOffset = staticOffset;\n  if (isVerticalSide && shift != null && shift.x || !isVerticalSide && shift != null && shift.y) {\n    computedStaticOffset = null;\n  }\n\n  // Strokes must be double the border width, this ensures the stroke's width\n  // works as you'd expect.\n  const computedStrokeWidth = strokeWidth * 2;\n  const halfStrokeWidth = computedStrokeWidth / 2;\n  const svgX = width / 2 * (tipRadius / -8 + 1);\n  const svgY = height / 2 * tipRadius / 4;\n  const isCustomShape = !!d;\n  const yOffsetProp = computedStaticOffset && alignment === 'end' ? 'bottom' : 'top';\n  let xOffsetProp = computedStaticOffset && alignment === 'end' ? 'right' : 'left';\n  if (computedStaticOffset && isRTL) {\n    xOffsetProp = alignment === 'end' ? 'left' : 'right';\n  }\n  const arrowX = (arrow == null ? void 0 : arrow.x) != null ? computedStaticOffset || arrow.x : '';\n  const arrowY = (arrow == null ? void 0 : arrow.y) != null ? computedStaticOffset || arrow.y : '';\n  const dValue = d || 'M0,0' + (\" H\" + width) + (\" L\" + (width - svgX) + \",\" + (height - svgY)) + (\" Q\" + width / 2 + \",\" + height + \" \" + svgX + \",\" + (height - svgY)) + ' Z';\n  const rotation = {\n    top: isCustomShape ? 'rotate(180deg)' : '',\n    left: isCustomShape ? 'rotate(90deg)' : 'rotate(-90deg)',\n    bottom: isCustomShape ? '' : 'rotate(180deg)',\n    right: isCustomShape ? 'rotate(-90deg)' : 'rotate(90deg)'\n  }[side];\n  return /*#__PURE__*/jsxs(\"svg\", {\n    ...rest,\n    \"aria-hidden\": true,\n    ref: ref,\n    width: isCustomShape ? width : width + computedStrokeWidth,\n    height: width,\n    viewBox: \"0 0 \" + width + \" \" + (height > width ? height : width),\n    style: {\n      position: 'absolute',\n      pointerEvents: 'none',\n      [xOffsetProp]: arrowX,\n      [yOffsetProp]: arrowY,\n      [side]: isVerticalSide || isCustomShape ? '100%' : \"calc(100% - \" + computedStrokeWidth / 2 + \"px)\",\n      transform: [rotation, transform].filter(t => !!t).join(' '),\n      ...restStyle\n    },\n    children: [computedStrokeWidth > 0 && /*#__PURE__*/jsx(\"path\", {\n      clipPath: \"url(#\" + clipPathId + \")\",\n      fill: \"none\",\n      stroke: stroke\n      // Account for the stroke on the fill path rendered below.\n      ,\n      strokeWidth: computedStrokeWidth + (d ? 0 : 1),\n      d: dValue\n    }), /*#__PURE__*/jsx(\"path\", {\n      stroke: computedStrokeWidth && !d ? rest.fill : 'none',\n      d: dValue\n    }), /*#__PURE__*/jsx(\"clipPath\", {\n      id: clipPathId,\n      children: /*#__PURE__*/jsx(\"rect\", {\n        x: -halfStrokeWidth,\n        y: halfStrokeWidth * (isCustomShape ? -1 : 1),\n        width: width + computedStrokeWidth,\n        height: width\n      })\n    })]\n  });\n});\n\nfunction createEventEmitter() {\n  const map = new Map();\n  return {\n    emit(event, data) {\n      var _map$get;\n      (_map$get = map.get(event)) == null || _map$get.forEach(listener => listener(data));\n    },\n    on(event, listener) {\n      if (!map.has(event)) {\n        map.set(event, new Set());\n      }\n      map.get(event).add(listener);\n    },\n    off(event, listener) {\n      var _map$get2;\n      (_map$get2 = map.get(event)) == null || _map$get2.delete(listener);\n    }\n  };\n}\n\nconst FloatingNodeContext = /*#__PURE__*/React.createContext(null);\nconst FloatingTreeContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Returns the parent node id for nested floating elements, if available.\n * Returns `null` for top-level floating elements.\n */\nconst useFloatingParentNodeId = () => {\n  var _React$useContext;\n  return ((_React$useContext = React.useContext(FloatingNodeContext)) == null ? void 0 : _React$useContext.id) || null;\n};\n\n/**\n * Returns the nearest floating tree context, if available.\n */\nconst useFloatingTree = () => React.useContext(FloatingTreeContext);\n\n/**\n * Registers a node into the `FloatingTree`, returning its id.\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction useFloatingNodeId(customParentId) {\n  const id = useId();\n  const tree = useFloatingTree();\n  const reactParentId = useFloatingParentNodeId();\n  const parentId = customParentId || reactParentId;\n  useModernLayoutEffect(() => {\n    if (!id) return;\n    const node = {\n      id,\n      parentId\n    };\n    tree == null || tree.addNode(node);\n    return () => {\n      tree == null || tree.removeNode(node);\n    };\n  }, [tree, id, parentId]);\n  return id;\n}\n/**\n * Provides parent node context for nested floating elements.\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction FloatingNode(props) {\n  const {\n    children,\n    id\n  } = props;\n  const parentId = useFloatingParentNodeId();\n  return /*#__PURE__*/jsx(FloatingNodeContext.Provider, {\n    value: React.useMemo(() => ({\n      id,\n      parentId\n    }), [id, parentId]),\n    children: children\n  });\n}\n/**\n * Provides context for nested floating elements when they are not children of\n * each other on the DOM.\n * This is not necessary in all cases, except when there must be explicit communication between parent and child floating elements. It is necessary for:\n * - The `bubbles` option in the `useDismiss()` Hook\n * - Nested virtual list navigation\n * - Nested floating elements that each open on hover\n * - Custom communication between parent and child floating elements\n * @see https://floating-ui.com/docs/FloatingTree\n */\nfunction FloatingTree(props) {\n  const {\n    children\n  } = props;\n  const nodesRef = React.useRef([]);\n  const addNode = React.useCallback(node => {\n    nodesRef.current = [...nodesRef.current, node];\n  }, []);\n  const removeNode = React.useCallback(node => {\n    nodesRef.current = nodesRef.current.filter(n => n !== node);\n  }, []);\n  const [events] = React.useState(() => createEventEmitter());\n  return /*#__PURE__*/jsx(FloatingTreeContext.Provider, {\n    value: React.useMemo(() => ({\n      nodesRef,\n      addNode,\n      removeNode,\n      events\n    }), [addNode, removeNode, events]),\n    children: children\n  });\n}\n\nfunction createAttribute(name) {\n  return \"data-floating-ui-\" + name;\n}\n\nfunction clearTimeoutIfSet(timeoutRef) {\n  if (timeoutRef.current !== -1) {\n    clearTimeout(timeoutRef.current);\n    timeoutRef.current = -1;\n  }\n}\n\nconst safePolygonIdentifier = /*#__PURE__*/createAttribute('safe-polygon');\nfunction getDelay(value, prop, pointerType) {\n  if (pointerType && !isMouseLikePointerType(pointerType)) {\n    return 0;\n  }\n  if (typeof value === 'number') {\n    return value;\n  }\n  if (typeof value === 'function') {\n    const result = value();\n    if (typeof result === 'number') {\n      return result;\n    }\n    return result == null ? void 0 : result[prop];\n  }\n  return value == null ? void 0 : value[prop];\n}\nfunction getRestMs(value) {\n  if (typeof value === 'function') {\n    return value();\n  }\n  return value;\n}\n/**\n * Opens the floating element while hovering over the reference element, like\n * CSS `:hover`.\n * @see https://floating-ui.com/docs/useHover\n */\nfunction useHover(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    events,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    delay = 0,\n    handleClose = null,\n    mouseOnly = false,\n    restMs = 0,\n    move = true\n  } = props;\n  const tree = useFloatingTree();\n  const parentId = useFloatingParentNodeId();\n  const handleCloseRef = useLatestRef(handleClose);\n  const delayRef = useLatestRef(delay);\n  const openRef = useLatestRef(open);\n  const restMsRef = useLatestRef(restMs);\n  const pointerTypeRef = React.useRef();\n  const timeoutRef = React.useRef(-1);\n  const handlerRef = React.useRef();\n  const restTimeoutRef = React.useRef(-1);\n  const blockMouseMoveRef = React.useRef(true);\n  const performedPointerEventsMutationRef = React.useRef(false);\n  const unbindMouseMoveRef = React.useRef(() => {});\n  const restTimeoutPendingRef = React.useRef(false);\n  const isHoverOpen = React.useCallback(() => {\n    var _dataRef$current$open;\n    const type = (_dataRef$current$open = dataRef.current.openEvent) == null ? void 0 : _dataRef$current$open.type;\n    return (type == null ? void 0 : type.includes('mouse')) && type !== 'mousedown';\n  }, [dataRef]);\n\n  // When closing before opening, clear the delay timeouts to cancel it\n  // from showing.\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onOpenChange(_ref) {\n      let {\n        open\n      } = _ref;\n      if (!open) {\n        clearTimeoutIfSet(timeoutRef);\n        clearTimeoutIfSet(restTimeoutRef);\n        blockMouseMoveRef.current = true;\n        restTimeoutPendingRef.current = false;\n      }\n    }\n    events.on('openchange', onOpenChange);\n    return () => {\n      events.off('openchange', onOpenChange);\n    };\n  }, [enabled, events]);\n  React.useEffect(() => {\n    if (!enabled) return;\n    if (!handleCloseRef.current) return;\n    if (!open) return;\n    function onLeave(event) {\n      if (isHoverOpen()) {\n        onOpenChange(false, event, 'hover');\n      }\n    }\n    const html = getDocument$1(elements.floating).documentElement;\n    html.addEventListener('mouseleave', onLeave);\n    return () => {\n      html.removeEventListener('mouseleave', onLeave);\n    };\n  }, [elements.floating, open, onOpenChange, enabled, handleCloseRef, isHoverOpen]);\n  const closeWithDelay = React.useCallback(function (event, runElseBranch, reason) {\n    if (runElseBranch === void 0) {\n      runElseBranch = true;\n    }\n    if (reason === void 0) {\n      reason = 'hover';\n    }\n    const closeDelay = getDelay(delayRef.current, 'close', pointerTypeRef.current);\n    if (closeDelay && !handlerRef.current) {\n      clearTimeoutIfSet(timeoutRef);\n      timeoutRef.current = window.setTimeout(() => onOpenChange(false, event, reason), closeDelay);\n    } else if (runElseBranch) {\n      clearTimeoutIfSet(timeoutRef);\n      onOpenChange(false, event, reason);\n    }\n  }, [delayRef, onOpenChange]);\n  const cleanupMouseMoveHandler = useEffectEvent(() => {\n    unbindMouseMoveRef.current();\n    handlerRef.current = undefined;\n  });\n  const clearPointerEvents = useEffectEvent(() => {\n    if (performedPointerEventsMutationRef.current) {\n      const body = getDocument$1(elements.floating).body;\n      body.style.pointerEvents = '';\n      body.removeAttribute(safePolygonIdentifier);\n      performedPointerEventsMutationRef.current = false;\n    }\n  });\n  const isClickLikeOpenEvent = useEffectEvent(() => {\n    return dataRef.current.openEvent ? ['click', 'mousedown'].includes(dataRef.current.openEvent.type) : false;\n  });\n\n  // Registering the mouse events on the reference directly to bypass React's\n  // delegation system. If the cursor was on a disabled element and then entered\n  // the reference (no gap), `mouseenter` doesn't fire in the delegation system.\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onReferenceMouseEnter(event) {\n      clearTimeoutIfSet(timeoutRef);\n      blockMouseMoveRef.current = false;\n      if (mouseOnly && !isMouseLikePointerType(pointerTypeRef.current) || getRestMs(restMsRef.current) > 0 && !getDelay(delayRef.current, 'open')) {\n        return;\n      }\n      const openDelay = getDelay(delayRef.current, 'open', pointerTypeRef.current);\n      if (openDelay) {\n        timeoutRef.current = window.setTimeout(() => {\n          if (!openRef.current) {\n            onOpenChange(true, event, 'hover');\n          }\n        }, openDelay);\n      } else if (!open) {\n        onOpenChange(true, event, 'hover');\n      }\n    }\n    function onReferenceMouseLeave(event) {\n      if (isClickLikeOpenEvent()) {\n        clearPointerEvents();\n        return;\n      }\n      unbindMouseMoveRef.current();\n      const doc = getDocument$1(elements.floating);\n      clearTimeoutIfSet(restTimeoutRef);\n      restTimeoutPendingRef.current = false;\n      if (handleCloseRef.current && dataRef.current.floatingContext) {\n        // Prevent clearing `onScrollMouseLeave` timeout.\n        if (!open) {\n          clearTimeoutIfSet(timeoutRef);\n        }\n        handlerRef.current = handleCloseRef.current({\n          ...dataRef.current.floatingContext,\n          tree,\n          x: event.clientX,\n          y: event.clientY,\n          onClose() {\n            clearPointerEvents();\n            cleanupMouseMoveHandler();\n            if (!isClickLikeOpenEvent()) {\n              closeWithDelay(event, true, 'safe-polygon');\n            }\n          }\n        });\n        const handler = handlerRef.current;\n        doc.addEventListener('mousemove', handler);\n        unbindMouseMoveRef.current = () => {\n          doc.removeEventListener('mousemove', handler);\n        };\n        return;\n      }\n\n      // Allow interactivity without `safePolygon` on touch devices. With a\n      // pointer, a short close delay is an alternative, so it should work\n      // consistently.\n      const shouldClose = pointerTypeRef.current === 'touch' ? !contains$1(elements.floating, event.relatedTarget) : true;\n      if (shouldClose) {\n        closeWithDelay(event);\n      }\n    }\n\n    // Ensure the floating element closes after scrolling even if the pointer\n    // did not move.\n    // https://github.com/floating-ui/floating-ui/discussions/1692\n    function onScrollMouseLeave(event) {\n      if (isClickLikeOpenEvent()) return;\n      if (!dataRef.current.floatingContext) return;\n      handleCloseRef.current == null || handleCloseRef.current({\n        ...dataRef.current.floatingContext,\n        tree,\n        x: event.clientX,\n        y: event.clientY,\n        onClose() {\n          clearPointerEvents();\n          cleanupMouseMoveHandler();\n          if (!isClickLikeOpenEvent()) {\n            closeWithDelay(event);\n          }\n        }\n      })(event);\n    }\n    function onFloatingMouseEnter() {\n      clearTimeoutIfSet(timeoutRef);\n    }\n    function onFloatingMouseLeave(event) {\n      if (!isClickLikeOpenEvent()) {\n        closeWithDelay(event, false);\n      }\n    }\n    if (isElement(elements.domReference)) {\n      const reference = elements.domReference;\n      const floating = elements.floating;\n      if (open) {\n        reference.addEventListener('mouseleave', onScrollMouseLeave);\n      }\n      if (move) {\n        reference.addEventListener('mousemove', onReferenceMouseEnter, {\n          once: true\n        });\n      }\n      reference.addEventListener('mouseenter', onReferenceMouseEnter);\n      reference.addEventListener('mouseleave', onReferenceMouseLeave);\n      if (floating) {\n        floating.addEventListener('mouseleave', onScrollMouseLeave);\n        floating.addEventListener('mouseenter', onFloatingMouseEnter);\n        floating.addEventListener('mouseleave', onFloatingMouseLeave);\n      }\n      return () => {\n        if (open) {\n          reference.removeEventListener('mouseleave', onScrollMouseLeave);\n        }\n        if (move) {\n          reference.removeEventListener('mousemove', onReferenceMouseEnter);\n        }\n        reference.removeEventListener('mouseenter', onReferenceMouseEnter);\n        reference.removeEventListener('mouseleave', onReferenceMouseLeave);\n        if (floating) {\n          floating.removeEventListener('mouseleave', onScrollMouseLeave);\n          floating.removeEventListener('mouseenter', onFloatingMouseEnter);\n          floating.removeEventListener('mouseleave', onFloatingMouseLeave);\n        }\n      };\n    }\n  }, [elements, enabled, context, mouseOnly, move, closeWithDelay, cleanupMouseMoveHandler, clearPointerEvents, onOpenChange, open, openRef, tree, delayRef, handleCloseRef, dataRef, isClickLikeOpenEvent, restMsRef]);\n\n  // Block pointer-events of every element other than the reference and floating\n  // while the floating element is open and has a `handleClose` handler. Also\n  // handles nested floating elements.\n  // https://github.com/floating-ui/floating-ui/issues/1722\n  useModernLayoutEffect(() => {\n    var _handleCloseRef$curre;\n    if (!enabled) return;\n    if (open && (_handleCloseRef$curre = handleCloseRef.current) != null && _handleCloseRef$curre.__options.blockPointerEvents && isHoverOpen()) {\n      performedPointerEventsMutationRef.current = true;\n      const floatingEl = elements.floating;\n      if (isElement(elements.domReference) && floatingEl) {\n        var _tree$nodesRef$curren;\n        const body = getDocument$1(elements.floating).body;\n        body.setAttribute(safePolygonIdentifier, '');\n        const ref = elements.domReference;\n        const parentFloating = tree == null || (_tree$nodesRef$curren = tree.nodesRef.current.find(node => node.id === parentId)) == null || (_tree$nodesRef$curren = _tree$nodesRef$curren.context) == null ? void 0 : _tree$nodesRef$curren.elements.floating;\n        if (parentFloating) {\n          parentFloating.style.pointerEvents = '';\n        }\n        body.style.pointerEvents = 'none';\n        ref.style.pointerEvents = 'auto';\n        floatingEl.style.pointerEvents = 'auto';\n        return () => {\n          body.style.pointerEvents = '';\n          ref.style.pointerEvents = '';\n          floatingEl.style.pointerEvents = '';\n        };\n      }\n    }\n  }, [enabled, open, parentId, elements, tree, handleCloseRef, isHoverOpen]);\n  useModernLayoutEffect(() => {\n    if (!open) {\n      pointerTypeRef.current = undefined;\n      restTimeoutPendingRef.current = false;\n      cleanupMouseMoveHandler();\n      clearPointerEvents();\n    }\n  }, [open, cleanupMouseMoveHandler, clearPointerEvents]);\n  React.useEffect(() => {\n    return () => {\n      cleanupMouseMoveHandler();\n      clearTimeoutIfSet(timeoutRef);\n      clearTimeoutIfSet(restTimeoutRef);\n      clearPointerEvents();\n    };\n  }, [enabled, elements.domReference, cleanupMouseMoveHandler, clearPointerEvents]);\n  const reference = React.useMemo(() => {\n    function setPointerRef(event) {\n      pointerTypeRef.current = event.pointerType;\n    }\n    return {\n      onPointerDown: setPointerRef,\n      onPointerEnter: setPointerRef,\n      onMouseMove(event) {\n        const {\n          nativeEvent\n        } = event;\n        function handleMouseMove() {\n          if (!blockMouseMoveRef.current && !openRef.current) {\n            onOpenChange(true, nativeEvent, 'hover');\n          }\n        }\n        if (mouseOnly && !isMouseLikePointerType(pointerTypeRef.current)) {\n          return;\n        }\n        if (open || getRestMs(restMsRef.current) === 0) {\n          return;\n        }\n\n        // Ignore insignificant movements to account for tremors.\n        if (restTimeoutPendingRef.current && event.movementX ** 2 + event.movementY ** 2 < 2) {\n          return;\n        }\n        clearTimeoutIfSet(restTimeoutRef);\n        if (pointerTypeRef.current === 'touch') {\n          handleMouseMove();\n        } else {\n          restTimeoutPendingRef.current = true;\n          restTimeoutRef.current = window.setTimeout(handleMouseMove, getRestMs(restMsRef.current));\n        }\n      }\n    };\n  }, [mouseOnly, onOpenChange, open, openRef, restMsRef]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nconst NOOP = () => {};\nconst FloatingDelayGroupContext = /*#__PURE__*/React.createContext({\n  delay: 0,\n  initialDelay: 0,\n  timeoutMs: 0,\n  currentId: null,\n  setCurrentId: NOOP,\n  setState: NOOP,\n  isInstantPhase: false\n});\n\n/**\n * @deprecated\n * Use the return value of `useDelayGroup()` instead.\n */\nconst useDelayGroupContext = () => React.useContext(FloatingDelayGroupContext);\n/**\n * Provides context for a group of floating elements that should share a\n * `delay`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction FloatingDelayGroup(props) {\n  const {\n    children,\n    delay,\n    timeoutMs = 0\n  } = props;\n  const [state, setState] = React.useReducer((prev, next) => ({\n    ...prev,\n    ...next\n  }), {\n    delay,\n    timeoutMs,\n    initialDelay: delay,\n    currentId: null,\n    isInstantPhase: false\n  });\n  const initialCurrentIdRef = React.useRef(null);\n  const setCurrentId = React.useCallback(currentId => {\n    setState({\n      currentId\n    });\n  }, []);\n  useModernLayoutEffect(() => {\n    if (state.currentId) {\n      if (initialCurrentIdRef.current === null) {\n        initialCurrentIdRef.current = state.currentId;\n      } else if (!state.isInstantPhase) {\n        setState({\n          isInstantPhase: true\n        });\n      }\n    } else {\n      if (state.isInstantPhase) {\n        setState({\n          isInstantPhase: false\n        });\n      }\n      initialCurrentIdRef.current = null;\n    }\n  }, [state.currentId, state.isInstantPhase]);\n  return /*#__PURE__*/jsx(FloatingDelayGroupContext.Provider, {\n    value: React.useMemo(() => ({\n      ...state,\n      setState,\n      setCurrentId\n    }), [state, setCurrentId]),\n    children: children\n  });\n}\n/**\n * Enables grouping when called inside a component that's a child of a\n * `FloatingDelayGroup`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction useDelayGroup(context, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    floatingId\n  } = context;\n  const {\n    id: optionId,\n    enabled = true\n  } = options;\n  const id = optionId != null ? optionId : floatingId;\n  const groupContext = useDelayGroupContext();\n  const {\n    currentId,\n    setCurrentId,\n    initialDelay,\n    setState,\n    timeoutMs\n  } = groupContext;\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (!currentId) return;\n    setState({\n      delay: {\n        open: 1,\n        close: getDelay(initialDelay, 'close')\n      }\n    });\n    if (currentId !== id) {\n      onOpenChange(false);\n    }\n  }, [enabled, id, onOpenChange, setState, currentId, initialDelay]);\n  useModernLayoutEffect(() => {\n    function unset() {\n      onOpenChange(false);\n      setState({\n        delay: initialDelay,\n        currentId: null\n      });\n    }\n    if (!enabled) return;\n    if (!currentId) return;\n    if (!open && currentId === id) {\n      if (timeoutMs) {\n        const timeout = window.setTimeout(unset, timeoutMs);\n        return () => {\n          clearTimeout(timeout);\n        };\n      }\n      unset();\n    }\n  }, [enabled, open, setState, currentId, id, onOpenChange, initialDelay, timeoutMs]);\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (setCurrentId === NOOP || !open) return;\n    setCurrentId(id);\n  }, [enabled, open, setCurrentId, id]);\n  return groupContext;\n}\n\nconst NextFloatingDelayGroupContext = /*#__PURE__*/React.createContext({\n  hasProvider: false,\n  timeoutMs: 0,\n  delayRef: {\n    current: 0\n  },\n  initialDelayRef: {\n    current: 0\n  },\n  timeoutIdRef: {\n    current: -1\n  },\n  currentIdRef: {\n    current: null\n  },\n  currentContextRef: {\n    current: null\n  }\n});\n/**\n * Experimental next version of `FloatingDelayGroup` to become the default\n * in the future. This component is not yet stable.\n * Provides context for a group of floating elements that should share a\n * `delay`. Unlike `FloatingDelayGroup`, `useNextDelayGroup` with this\n * component does not cause a re-render of unrelated consumers of the\n * context when the delay changes.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction NextFloatingDelayGroup(props) {\n  const {\n    children,\n    delay,\n    timeoutMs = 0\n  } = props;\n  const delayRef = React.useRef(delay);\n  const initialDelayRef = React.useRef(delay);\n  const currentIdRef = React.useRef(null);\n  const currentContextRef = React.useRef(null);\n  const timeoutIdRef = React.useRef(-1);\n  return /*#__PURE__*/jsx(NextFloatingDelayGroupContext.Provider, {\n    value: React.useMemo(() => ({\n      hasProvider: true,\n      delayRef,\n      initialDelayRef,\n      currentIdRef,\n      timeoutMs,\n      currentContextRef,\n      timeoutIdRef\n    }), [timeoutMs]),\n    children: children\n  });\n}\n/**\n * Enables grouping when called inside a component that's a child of a\n * `NextFloatingDelayGroup`.\n * @see https://floating-ui.com/docs/FloatingDelayGroup\n */\nfunction useNextDelayGroup(context, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    floatingId\n  } = context;\n  const {\n    enabled = true\n  } = options;\n  const groupContext = React.useContext(NextFloatingDelayGroupContext);\n  const {\n    currentIdRef,\n    delayRef,\n    timeoutMs,\n    initialDelayRef,\n    currentContextRef,\n    hasProvider,\n    timeoutIdRef\n  } = groupContext;\n  const [isInstantPhase, setIsInstantPhase] = React.useState(false);\n  useModernLayoutEffect(() => {\n    function unset() {\n      var _currentContextRef$cu;\n      setIsInstantPhase(false);\n      (_currentContextRef$cu = currentContextRef.current) == null || _currentContextRef$cu.setIsInstantPhase(false);\n      currentIdRef.current = null;\n      currentContextRef.current = null;\n      delayRef.current = initialDelayRef.current;\n    }\n    if (!enabled) return;\n    if (!currentIdRef.current) return;\n    if (!open && currentIdRef.current === floatingId) {\n      setIsInstantPhase(false);\n      if (timeoutMs) {\n        timeoutIdRef.current = window.setTimeout(unset, timeoutMs);\n        return () => {\n          clearTimeout(timeoutIdRef.current);\n        };\n      }\n      unset();\n    }\n  }, [enabled, open, floatingId, currentIdRef, delayRef, timeoutMs, initialDelayRef, currentContextRef, timeoutIdRef]);\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (!open) return;\n    const prevContext = currentContextRef.current;\n    const prevId = currentIdRef.current;\n    currentContextRef.current = {\n      onOpenChange,\n      setIsInstantPhase\n    };\n    currentIdRef.current = floatingId;\n    delayRef.current = {\n      open: 0,\n      close: getDelay(initialDelayRef.current, 'close')\n    };\n    if (prevId !== null && prevId !== floatingId) {\n      clearTimeoutIfSet(timeoutIdRef);\n      setIsInstantPhase(true);\n      prevContext == null || prevContext.setIsInstantPhase(true);\n      prevContext == null || prevContext.onOpenChange(false);\n    } else {\n      setIsInstantPhase(false);\n      prevContext == null || prevContext.setIsInstantPhase(false);\n    }\n  }, [enabled, open, floatingId, onOpenChange, currentIdRef, delayRef, timeoutMs, initialDelayRef, currentContextRef, timeoutIdRef]);\n  useModernLayoutEffect(() => {\n    return () => {\n      currentContextRef.current = null;\n    };\n  }, [currentContextRef]);\n  return React.useMemo(() => ({\n    hasProvider,\n    delayRef,\n    isInstantPhase\n  }), [hasProvider, delayRef, isInstantPhase]);\n}\n\nlet rafId = 0;\nfunction enqueueFocus(el, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    preventScroll = false,\n    cancelPrevious = true,\n    sync = false\n  } = options;\n  cancelPrevious && cancelAnimationFrame(rafId);\n  const exec = () => el == null ? void 0 : el.focus({\n    preventScroll\n  });\n  if (sync) {\n    exec();\n  } else {\n    rafId = requestAnimationFrame(exec);\n  }\n}\n\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode == null ? void 0 : child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n\n  // then fallback to custom implementation with Shadow DOM support\n  if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    while (next) {\n      if (parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    }\n  }\n\n  // Give up, the result is false\n  return false;\n}\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\n\n// Modified to add conditional `aria-hidden` support:\n// https://github.com/theKashey/aria-hidden/blob/9220c8f4a4fd35f63bee5510a9f41a37264382d4/src/index.ts\nlet counterMap = /*#__PURE__*/new WeakMap();\nlet uncontrolledElementsSet = /*#__PURE__*/new WeakSet();\nlet markerMap = {};\nlet lockCount$1 = 0;\nconst supportsInert = () => typeof HTMLElement !== 'undefined' && 'inert' in HTMLElement.prototype;\nconst unwrapHost = node => node && (node.host || unwrapHost(node.parentNode));\nconst correctElements = (parent, targets) => targets.map(target => {\n  if (parent.contains(target)) {\n    return target;\n  }\n  const correctedTarget = unwrapHost(target);\n  if (parent.contains(correctedTarget)) {\n    return correctedTarget;\n  }\n  return null;\n}).filter(x => x != null);\nfunction applyAttributeToOthers(uncorrectedAvoidElements, body, ariaHidden, inert) {\n  const markerName = 'data-floating-ui-inert';\n  const controlAttribute = inert ? 'inert' : ariaHidden ? 'aria-hidden' : null;\n  const avoidElements = correctElements(body, uncorrectedAvoidElements);\n  const elementsToKeep = new Set();\n  const elementsToStop = new Set(avoidElements);\n  const hiddenElements = [];\n  if (!markerMap[markerName]) {\n    markerMap[markerName] = new WeakMap();\n  }\n  const markerCounter = markerMap[markerName];\n  avoidElements.forEach(keep);\n  deep(body);\n  elementsToKeep.clear();\n  function keep(el) {\n    if (!el || elementsToKeep.has(el)) {\n      return;\n    }\n    elementsToKeep.add(el);\n    el.parentNode && keep(el.parentNode);\n  }\n  function deep(parent) {\n    if (!parent || elementsToStop.has(parent)) {\n      return;\n    }\n    [].forEach.call(parent.children, node => {\n      if (getNodeName(node) === 'script') return;\n      if (elementsToKeep.has(node)) {\n        deep(node);\n      } else {\n        const attr = controlAttribute ? node.getAttribute(controlAttribute) : null;\n        const alreadyHidden = attr !== null && attr !== 'false';\n        const currentCounterValue = counterMap.get(node) || 0;\n        const counterValue = controlAttribute ? currentCounterValue + 1 : currentCounterValue;\n        const markerValue = (markerCounter.get(node) || 0) + 1;\n        counterMap.set(node, counterValue);\n        markerCounter.set(node, markerValue);\n        hiddenElements.push(node);\n        if (counterValue === 1 && alreadyHidden) {\n          uncontrolledElementsSet.add(node);\n        }\n        if (markerValue === 1) {\n          node.setAttribute(markerName, '');\n        }\n        if (!alreadyHidden && controlAttribute) {\n          node.setAttribute(controlAttribute, controlAttribute === 'inert' ? '' : 'true');\n        }\n      }\n    });\n  }\n  lockCount$1++;\n  return () => {\n    hiddenElements.forEach(element => {\n      const currentCounterValue = counterMap.get(element) || 0;\n      const counterValue = controlAttribute ? currentCounterValue - 1 : currentCounterValue;\n      const markerValue = (markerCounter.get(element) || 0) - 1;\n      counterMap.set(element, counterValue);\n      markerCounter.set(element, markerValue);\n      if (!counterValue) {\n        if (!uncontrolledElementsSet.has(element) && controlAttribute) {\n          element.removeAttribute(controlAttribute);\n        }\n        uncontrolledElementsSet.delete(element);\n      }\n      if (!markerValue) {\n        element.removeAttribute(markerName);\n      }\n    });\n    lockCount$1--;\n    if (!lockCount$1) {\n      counterMap = new WeakMap();\n      counterMap = new WeakMap();\n      uncontrolledElementsSet = new WeakSet();\n      markerMap = {};\n    }\n  };\n}\nfunction markOthers(avoidElements, ariaHidden, inert) {\n  if (ariaHidden === void 0) {\n    ariaHidden = false;\n  }\n  if (inert === void 0) {\n    inert = false;\n  }\n  const body = getDocument(avoidElements[0]).body;\n  return applyAttributeToOthers(avoidElements.concat(Array.from(body.querySelectorAll('[aria-live]'))), body, ariaHidden, inert);\n}\n\nconst HIDDEN_STYLES = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'fixed',\n  whiteSpace: 'nowrap',\n  width: '1px',\n  top: 0,\n  left: 0\n};\nconst FocusGuard = /*#__PURE__*/React.forwardRef(function FocusGuard(props, ref) {\n  const [role, setRole] = React.useState();\n  useModernLayoutEffect(() => {\n    if (isSafari()) {\n      // Unlike other screen readers such as NVDA and JAWS, the virtual cursor\n      // on VoiceOver does trigger the onFocus event, so we can use the focus\n      // trap element. On Safari, only buttons trigger the onFocus event.\n      // NB: \"group\" role in the Sandbox no longer appears to work, must be a\n      // button role.\n      setRole('button');\n    }\n  }, []);\n  const restProps = {\n    ref,\n    tabIndex: 0,\n    // Role is only for VoiceOver\n    role,\n    'aria-hidden': role ? undefined : true,\n    [createAttribute('focus-guard')]: '',\n    style: HIDDEN_STYLES\n  };\n  return /*#__PURE__*/jsx(\"span\", {\n    ...props,\n    ...restProps\n  });\n});\n\nconst PortalContext = /*#__PURE__*/React.createContext(null);\nconst attr = /*#__PURE__*/createAttribute('portal');\n/**\n * @see https://floating-ui.com/docs/FloatingPortal#usefloatingportalnode\n */\nfunction useFloatingPortalNode(props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    id,\n    root\n  } = props;\n  const uniqueId = useId();\n  const portalContext = usePortalContext();\n  const [portalNode, setPortalNode] = React.useState(null);\n  const portalNodeRef = React.useRef(null);\n  useModernLayoutEffect(() => {\n    return () => {\n      portalNode == null || portalNode.remove();\n      // Allow the subsequent layout effects to create a new node on updates.\n      // The portal node will still be cleaned up on unmount.\n      // https://github.com/floating-ui/floating-ui/issues/2454\n      queueMicrotask(() => {\n        portalNodeRef.current = null;\n      });\n    };\n  }, [portalNode]);\n  useModernLayoutEffect(() => {\n    // Wait for the uniqueId to be generated before creating the portal node in\n    // React <18 (using `useFloatingId` instead of the native `useId`).\n    // https://github.com/floating-ui/floating-ui/issues/2778\n    if (!uniqueId) return;\n    if (portalNodeRef.current) return;\n    const existingIdRoot = id ? document.getElementById(id) : null;\n    if (!existingIdRoot) return;\n    const subRoot = document.createElement('div');\n    subRoot.id = uniqueId;\n    subRoot.setAttribute(attr, '');\n    existingIdRoot.appendChild(subRoot);\n    portalNodeRef.current = subRoot;\n    setPortalNode(subRoot);\n  }, [id, uniqueId]);\n  useModernLayoutEffect(() => {\n    // Wait for the root to exist before creating the portal node. The root must\n    // be stored in state, not a ref, for this to work reactively.\n    if (root === null) return;\n    if (!uniqueId) return;\n    if (portalNodeRef.current) return;\n    let container = root || (portalContext == null ? void 0 : portalContext.portalNode);\n    if (container && !isElement(container)) container = container.current;\n    container = container || document.body;\n    let idWrapper = null;\n    if (id) {\n      idWrapper = document.createElement('div');\n      idWrapper.id = id;\n      container.appendChild(idWrapper);\n    }\n    const subRoot = document.createElement('div');\n    subRoot.id = uniqueId;\n    subRoot.setAttribute(attr, '');\n    container = idWrapper || container;\n    container.appendChild(subRoot);\n    portalNodeRef.current = subRoot;\n    setPortalNode(subRoot);\n  }, [id, root, uniqueId, portalContext]);\n  return portalNode;\n}\n/**\n * Portals the floating element into a given container element — by default,\n * outside of the app root and into the body.\n * This is necessary to ensure the floating element can appear outside any\n * potential parent containers that cause clipping (such as `overflow: hidden`),\n * while retaining its location in the React tree.\n * @see https://floating-ui.com/docs/FloatingPortal\n */\nfunction FloatingPortal(props) {\n  const {\n    children,\n    id,\n    root,\n    preserveTabOrder = true\n  } = props;\n  const portalNode = useFloatingPortalNode({\n    id,\n    root\n  });\n  const [focusManagerState, setFocusManagerState] = React.useState(null);\n  const beforeOutsideRef = React.useRef(null);\n  const afterOutsideRef = React.useRef(null);\n  const beforeInsideRef = React.useRef(null);\n  const afterInsideRef = React.useRef(null);\n  const modal = focusManagerState == null ? void 0 : focusManagerState.modal;\n  const open = focusManagerState == null ? void 0 : focusManagerState.open;\n  const shouldRenderGuards =\n  // The FocusManager and therefore floating element are currently open/\n  // rendered.\n  !!focusManagerState &&\n  // Guards are only for non-modal focus management.\n  !focusManagerState.modal &&\n  // Don't render if unmount is transitioning.\n  focusManagerState.open && preserveTabOrder && !!(root || portalNode);\n\n  // https://codesandbox.io/s/tabbable-portal-f4tng?file=/src/TabbablePortal.tsx\n  React.useEffect(() => {\n    if (!portalNode || !preserveTabOrder || modal) {\n      return;\n    }\n\n    // Make sure elements inside the portal element are tabbable only when the\n    // portal has already been focused, either by tabbing into a focus trap\n    // element outside or using the mouse.\n    function onFocus(event) {\n      if (portalNode && isOutsideEvent(event)) {\n        const focusing = event.type === 'focusin';\n        const manageFocus = focusing ? enableFocusInside : disableFocusInside;\n        manageFocus(portalNode);\n      }\n    }\n    // Listen to the event on the capture phase so they run before the focus\n    // trap elements onFocus prop is called.\n    portalNode.addEventListener('focusin', onFocus, true);\n    portalNode.addEventListener('focusout', onFocus, true);\n    return () => {\n      portalNode.removeEventListener('focusin', onFocus, true);\n      portalNode.removeEventListener('focusout', onFocus, true);\n    };\n  }, [portalNode, preserveTabOrder, modal]);\n  React.useEffect(() => {\n    if (!portalNode) return;\n    if (open) return;\n    enableFocusInside(portalNode);\n  }, [open, portalNode]);\n  return /*#__PURE__*/jsxs(PortalContext.Provider, {\n    value: React.useMemo(() => ({\n      preserveTabOrder,\n      beforeOutsideRef,\n      afterOutsideRef,\n      beforeInsideRef,\n      afterInsideRef,\n      portalNode,\n      setFocusManagerState\n    }), [preserveTabOrder, portalNode]),\n    children: [shouldRenderGuards && portalNode && /*#__PURE__*/jsx(FocusGuard, {\n      \"data-type\": \"outside\",\n      ref: beforeOutsideRef,\n      onFocus: event => {\n        if (isOutsideEvent(event, portalNode)) {\n          var _beforeInsideRef$curr;\n          (_beforeInsideRef$curr = beforeInsideRef.current) == null || _beforeInsideRef$curr.focus();\n        } else {\n          const domReference = focusManagerState ? focusManagerState.domReference : null;\n          const prevTabbable = getPreviousTabbable(domReference);\n          prevTabbable == null || prevTabbable.focus();\n        }\n      }\n    }), shouldRenderGuards && portalNode && /*#__PURE__*/jsx(\"span\", {\n      \"aria-owns\": portalNode.id,\n      style: HIDDEN_STYLES\n    }), portalNode && /*#__PURE__*/ReactDOM.createPortal(children, portalNode), shouldRenderGuards && portalNode && /*#__PURE__*/jsx(FocusGuard, {\n      \"data-type\": \"outside\",\n      ref: afterOutsideRef,\n      onFocus: event => {\n        if (isOutsideEvent(event, portalNode)) {\n          var _afterInsideRef$curre;\n          (_afterInsideRef$curre = afterInsideRef.current) == null || _afterInsideRef$curre.focus();\n        } else {\n          const domReference = focusManagerState ? focusManagerState.domReference : null;\n          const nextTabbable = getNextTabbable(domReference);\n          nextTabbable == null || nextTabbable.focus();\n          (focusManagerState == null ? void 0 : focusManagerState.closeOnFocusOut) && (focusManagerState == null ? void 0 : focusManagerState.onOpenChange(false, event.nativeEvent, 'focus-out'));\n        }\n      }\n    })]\n  });\n}\nconst usePortalContext = () => React.useContext(PortalContext);\n\nfunction useLiteMergeRefs(refs) {\n  return React.useMemo(() => {\n    return value => {\n      refs.forEach(ref => {\n        if (ref) {\n          ref.current = value;\n        }\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}\n\nconst LIST_LIMIT = 20;\nlet previouslyFocusedElements = [];\nfunction addPreviouslyFocusedElement(element) {\n  previouslyFocusedElements = previouslyFocusedElements.filter(el => el.isConnected);\n  if (element && getNodeName(element) !== 'body') {\n    previouslyFocusedElements.push(element);\n    if (previouslyFocusedElements.length > LIST_LIMIT) {\n      previouslyFocusedElements = previouslyFocusedElements.slice(-20);\n    }\n  }\n}\nfunction getPreviouslyFocusedElement() {\n  return previouslyFocusedElements.slice().reverse().find(el => el.isConnected);\n}\nfunction getFirstTabbableElement(container) {\n  const tabbableOptions = getTabbableOptions();\n  if (isTabbable(container, tabbableOptions)) {\n    return container;\n  }\n  return tabbable(container, tabbableOptions)[0] || container;\n}\nfunction handleTabIndex(floatingFocusElement, orderRef) {\n  var _floatingFocusElement;\n  if (!orderRef.current.includes('floating') && !((_floatingFocusElement = floatingFocusElement.getAttribute('role')) != null && _floatingFocusElement.includes('dialog'))) {\n    return;\n  }\n  const options = getTabbableOptions();\n  const focusableElements = focusable(floatingFocusElement, options);\n  const tabbableContent = focusableElements.filter(element => {\n    const dataTabIndex = element.getAttribute('data-tabindex') || '';\n    return isTabbable(element, options) || element.hasAttribute('data-tabindex') && !dataTabIndex.startsWith('-');\n  });\n  const tabIndex = floatingFocusElement.getAttribute('tabindex');\n  if (orderRef.current.includes('floating') || tabbableContent.length === 0) {\n    if (tabIndex !== '0') {\n      floatingFocusElement.setAttribute('tabindex', '0');\n    }\n  } else if (tabIndex !== '-1' || floatingFocusElement.hasAttribute('data-tabindex') && floatingFocusElement.getAttribute('data-tabindex') !== '-1') {\n    floatingFocusElement.setAttribute('tabindex', '-1');\n    floatingFocusElement.setAttribute('data-tabindex', '-1');\n  }\n}\nconst VisuallyHiddenDismiss = /*#__PURE__*/React.forwardRef(function VisuallyHiddenDismiss(props, ref) {\n  return /*#__PURE__*/jsx(\"button\", {\n    ...props,\n    type: \"button\",\n    ref: ref,\n    tabIndex: -1,\n    style: HIDDEN_STYLES\n  });\n});\n/**\n * Provides focus management for the floating element.\n * @see https://floating-ui.com/docs/FloatingFocusManager\n */\nfunction FloatingFocusManager(props) {\n  const {\n    context,\n    children,\n    disabled = false,\n    order = ['content'],\n    guards: _guards = true,\n    initialFocus = 0,\n    returnFocus = true,\n    restoreFocus = false,\n    modal = true,\n    visuallyHiddenDismiss = false,\n    closeOnFocusOut = true,\n    outsideElementsInert = false,\n    getInsideElements: _getInsideElements = () => []\n  } = props;\n  const {\n    open,\n    onOpenChange,\n    events,\n    dataRef,\n    elements: {\n      domReference,\n      floating\n    }\n  } = context;\n  const getNodeId = useEffectEvent(() => {\n    var _dataRef$current$floa;\n    return (_dataRef$current$floa = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa.nodeId;\n  });\n  const getInsideElements = useEffectEvent(_getInsideElements);\n  const ignoreInitialFocus = typeof initialFocus === 'number' && initialFocus < 0;\n  // If the reference is a combobox and is typeable (e.g. input/textarea),\n  // there are different focus semantics. The guards should not be rendered, but\n  // aria-hidden should be applied to all nodes still. Further, the visually\n  // hidden dismiss button should only appear at the end of the list, not the\n  // start.\n  const isUntrappedTypeableCombobox = isTypeableCombobox(domReference) && ignoreInitialFocus;\n\n  // Force the guards to be rendered if the `inert` attribute is not supported.\n  const inertSupported = supportsInert();\n  const guards = inertSupported ? _guards : true;\n  const useInert = !guards || inertSupported && outsideElementsInert;\n  const orderRef = useLatestRef(order);\n  const initialFocusRef = useLatestRef(initialFocus);\n  const returnFocusRef = useLatestRef(returnFocus);\n  const tree = useFloatingTree();\n  const portalContext = usePortalContext();\n  const startDismissButtonRef = React.useRef(null);\n  const endDismissButtonRef = React.useRef(null);\n  const preventReturnFocusRef = React.useRef(false);\n  const isPointerDownRef = React.useRef(false);\n  const tabbableIndexRef = React.useRef(-1);\n  const isInsidePortal = portalContext != null;\n  const floatingFocusElement = getFloatingFocusElement(floating);\n  const getTabbableContent = useEffectEvent(function (container) {\n    if (container === void 0) {\n      container = floatingFocusElement;\n    }\n    return container ? tabbable(container, getTabbableOptions()) : [];\n  });\n  const getTabbableElements = useEffectEvent(container => {\n    const content = getTabbableContent(container);\n    return orderRef.current.map(type => {\n      if (domReference && type === 'reference') {\n        return domReference;\n      }\n      if (floatingFocusElement && type === 'floating') {\n        return floatingFocusElement;\n      }\n      return content;\n    }).filter(Boolean).flat();\n  });\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!modal) return;\n    function onKeyDown(event) {\n      if (event.key === 'Tab') {\n        // The focus guards have nothing to focus, so we need to stop the event.\n        if (contains$1(floatingFocusElement, activeElement(getDocument$1(floatingFocusElement))) && getTabbableContent().length === 0 && !isUntrappedTypeableCombobox) {\n          stopEvent(event);\n        }\n        const els = getTabbableElements();\n        const target = getTarget$1(event);\n        if (orderRef.current[0] === 'reference' && target === domReference) {\n          stopEvent(event);\n          if (event.shiftKey) {\n            enqueueFocus(els[els.length - 1]);\n          } else {\n            enqueueFocus(els[1]);\n          }\n        }\n        if (orderRef.current[1] === 'floating' && target === floatingFocusElement && event.shiftKey) {\n          stopEvent(event);\n          enqueueFocus(els[0]);\n        }\n      }\n    }\n    const doc = getDocument$1(floatingFocusElement);\n    doc.addEventListener('keydown', onKeyDown);\n    return () => {\n      doc.removeEventListener('keydown', onKeyDown);\n    };\n  }, [disabled, domReference, floatingFocusElement, modal, orderRef, isUntrappedTypeableCombobox, getTabbableContent, getTabbableElements]);\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!floating) return;\n    function handleFocusIn(event) {\n      const target = getTarget$1(event);\n      const tabbableContent = getTabbableContent();\n      const tabbableIndex = tabbableContent.indexOf(target);\n      if (tabbableIndex !== -1) {\n        tabbableIndexRef.current = tabbableIndex;\n      }\n    }\n    floating.addEventListener('focusin', handleFocusIn);\n    return () => {\n      floating.removeEventListener('focusin', handleFocusIn);\n    };\n  }, [disabled, floating, getTabbableContent]);\n  React.useEffect(() => {\n    if (disabled) return;\n    if (!closeOnFocusOut) return;\n\n    // In Safari, buttons lose focus when pressing them.\n    function handlePointerDown() {\n      isPointerDownRef.current = true;\n      setTimeout(() => {\n        isPointerDownRef.current = false;\n      });\n    }\n    function handleFocusOutside(event) {\n      const relatedTarget = event.relatedTarget;\n      const currentTarget = event.currentTarget;\n      queueMicrotask(() => {\n        const nodeId = getNodeId();\n        const movedToUnrelatedNode = !(contains$1(domReference, relatedTarget) || contains$1(floating, relatedTarget) || contains$1(relatedTarget, floating) || contains$1(portalContext == null ? void 0 : portalContext.portalNode, relatedTarget) || relatedTarget != null && relatedTarget.hasAttribute(createAttribute('focus-guard')) || tree && (getNodeChildren$1(tree.nodesRef.current, nodeId).find(node => {\n          var _node$context, _node$context2;\n          return contains$1((_node$context = node.context) == null ? void 0 : _node$context.elements.floating, relatedTarget) || contains$1((_node$context2 = node.context) == null ? void 0 : _node$context2.elements.domReference, relatedTarget);\n        }) || getNodeAncestors(tree.nodesRef.current, nodeId).find(node => {\n          var _node$context3, _node$context4, _node$context5;\n          return [(_node$context3 = node.context) == null ? void 0 : _node$context3.elements.floating, getFloatingFocusElement((_node$context4 = node.context) == null ? void 0 : _node$context4.elements.floating)].includes(relatedTarget) || ((_node$context5 = node.context) == null ? void 0 : _node$context5.elements.domReference) === relatedTarget;\n        })));\n        if (currentTarget === domReference && floatingFocusElement) {\n          handleTabIndex(floatingFocusElement, orderRef);\n        }\n\n        // Restore focus to the previous tabbable element index to prevent\n        // focus from being lost outside the floating tree.\n        if (restoreFocus && movedToUnrelatedNode && activeElement(getDocument$1(floatingFocusElement)) === getDocument$1(floatingFocusElement).body) {\n          // Let `FloatingPortal` effect knows that focus is still inside the\n          // floating tree.\n          if (isHTMLElement(floatingFocusElement)) {\n            floatingFocusElement.focus();\n          }\n          const prevTabbableIndex = tabbableIndexRef.current;\n          const tabbableContent = getTabbableContent();\n          const nodeToFocus = tabbableContent[prevTabbableIndex] || tabbableContent[tabbableContent.length - 1] || floatingFocusElement;\n          if (isHTMLElement(nodeToFocus)) {\n            nodeToFocus.focus();\n          }\n        }\n\n        // Focus did not move inside the floating tree, and there are no tabbable\n        // portal guards to handle closing.\n        if ((isUntrappedTypeableCombobox ? true : !modal) && relatedTarget && movedToUnrelatedNode && !isPointerDownRef.current &&\n        // Fix React 18 Strict Mode returnFocus due to double rendering.\n        relatedTarget !== getPreviouslyFocusedElement()) {\n          preventReturnFocusRef.current = true;\n          onOpenChange(false, event, 'focus-out');\n        }\n      });\n    }\n    if (floating && isHTMLElement(domReference)) {\n      domReference.addEventListener('focusout', handleFocusOutside);\n      domReference.addEventListener('pointerdown', handlePointerDown);\n      floating.addEventListener('focusout', handleFocusOutside);\n      return () => {\n        domReference.removeEventListener('focusout', handleFocusOutside);\n        domReference.removeEventListener('pointerdown', handlePointerDown);\n        floating.removeEventListener('focusout', handleFocusOutside);\n      };\n    }\n  }, [disabled, domReference, floating, floatingFocusElement, modal, tree, portalContext, onOpenChange, closeOnFocusOut, restoreFocus, getTabbableContent, isUntrappedTypeableCombobox, getNodeId, orderRef]);\n  const beforeGuardRef = React.useRef(null);\n  const afterGuardRef = React.useRef(null);\n  const mergedBeforeGuardRef = useLiteMergeRefs([beforeGuardRef, portalContext == null ? void 0 : portalContext.beforeInsideRef]);\n  const mergedAfterGuardRef = useLiteMergeRefs([afterGuardRef, portalContext == null ? void 0 : portalContext.afterInsideRef]);\n  React.useEffect(() => {\n    var _portalContext$portal, _ancestors$find;\n    if (disabled) return;\n    if (!floating) return;\n\n    // Don't hide portals nested within the parent portal.\n    const portalNodes = Array.from((portalContext == null || (_portalContext$portal = portalContext.portalNode) == null ? void 0 : _portalContext$portal.querySelectorAll(\"[\" + createAttribute('portal') + \"]\")) || []);\n    const ancestors = tree ? getNodeAncestors(tree.nodesRef.current, getNodeId()) : [];\n    const ancestorFloatingNodes = tree && !modal ? ancestors.map(node => {\n      var _node$context6;\n      return (_node$context6 = node.context) == null ? void 0 : _node$context6.elements.floating;\n    }) : [];\n    const rootAncestorComboboxDomReference = (_ancestors$find = ancestors.find(node => {\n      var _node$context7;\n      return isTypeableCombobox(((_node$context7 = node.context) == null ? void 0 : _node$context7.elements.domReference) || null);\n    })) == null || (_ancestors$find = _ancestors$find.context) == null ? void 0 : _ancestors$find.elements.domReference;\n    const insideElements = [floating, rootAncestorComboboxDomReference, ...portalNodes, ...ancestorFloatingNodes, ...getInsideElements(), startDismissButtonRef.current, endDismissButtonRef.current, beforeGuardRef.current, afterGuardRef.current, portalContext == null ? void 0 : portalContext.beforeOutsideRef.current, portalContext == null ? void 0 : portalContext.afterOutsideRef.current, orderRef.current.includes('reference') || isUntrappedTypeableCombobox ? domReference : null].filter(x => x != null);\n    const cleanup = modal || isUntrappedTypeableCombobox ? markOthers(insideElements, !useInert, useInert) : markOthers(insideElements);\n    return () => {\n      cleanup();\n    };\n  }, [disabled, domReference, floating, modal, orderRef, portalContext, isUntrappedTypeableCombobox, guards, useInert, tree, getNodeId, getInsideElements]);\n  useModernLayoutEffect(() => {\n    if (disabled || !isHTMLElement(floatingFocusElement)) return;\n    const doc = getDocument$1(floatingFocusElement);\n    const previouslyFocusedElement = activeElement(doc);\n\n    // Wait for any layout effect state setters to execute to set `tabIndex`.\n    queueMicrotask(() => {\n      const focusableElements = getTabbableElements(floatingFocusElement);\n      const initialFocusValue = initialFocusRef.current;\n      const elToFocus = (typeof initialFocusValue === 'number' ? focusableElements[initialFocusValue] : initialFocusValue.current) || floatingFocusElement;\n      const focusAlreadyInsideFloatingEl = contains$1(floatingFocusElement, previouslyFocusedElement);\n      if (!ignoreInitialFocus && !focusAlreadyInsideFloatingEl && open) {\n        enqueueFocus(elToFocus, {\n          preventScroll: elToFocus === floatingFocusElement\n        });\n      }\n    });\n  }, [disabled, open, floatingFocusElement, ignoreInitialFocus, getTabbableElements, initialFocusRef]);\n  useModernLayoutEffect(() => {\n    if (disabled || !floatingFocusElement) return;\n    let preventReturnFocusScroll = false;\n    const doc = getDocument$1(floatingFocusElement);\n    const previouslyFocusedElement = activeElement(doc);\n    addPreviouslyFocusedElement(previouslyFocusedElement);\n\n    // Dismissing via outside press should always ignore `returnFocus` to\n    // prevent unwanted scrolling.\n    function onOpenChange(_ref) {\n      let {\n        reason,\n        event,\n        nested\n      } = _ref;\n      if (['hover', 'safe-polygon'].includes(reason) && event.type === 'mouseleave') {\n        preventReturnFocusRef.current = true;\n      }\n      if (reason !== 'outside-press') return;\n      if (nested) {\n        preventReturnFocusRef.current = false;\n        preventReturnFocusScroll = true;\n      } else if (isVirtualClick(event) || isVirtualPointerEvent(event)) {\n        preventReturnFocusRef.current = false;\n      } else {\n        let isPreventScrollSupported = false;\n        document.createElement('div').focus({\n          get preventScroll() {\n            isPreventScrollSupported = true;\n            return false;\n          }\n        });\n        if (isPreventScrollSupported) {\n          preventReturnFocusRef.current = false;\n          preventReturnFocusScroll = true;\n        } else {\n          preventReturnFocusRef.current = true;\n        }\n      }\n    }\n    events.on('openchange', onOpenChange);\n    const fallbackEl = doc.createElement('span');\n    fallbackEl.setAttribute('tabindex', '-1');\n    fallbackEl.setAttribute('aria-hidden', 'true');\n    Object.assign(fallbackEl.style, HIDDEN_STYLES);\n    if (isInsidePortal && domReference) {\n      domReference.insertAdjacentElement('afterend', fallbackEl);\n    }\n    function getReturnElement() {\n      if (typeof returnFocusRef.current === 'boolean') {\n        const el = domReference || getPreviouslyFocusedElement();\n        return el && el.isConnected ? el : fallbackEl;\n      }\n      return returnFocusRef.current.current || fallbackEl;\n    }\n    return () => {\n      events.off('openchange', onOpenChange);\n      const activeEl = activeElement(doc);\n      const isFocusInsideFloatingTree = contains$1(floating, activeEl) || tree && getNodeChildren$1(tree.nodesRef.current, getNodeId()).some(node => {\n        var _node$context8;\n        return contains$1((_node$context8 = node.context) == null ? void 0 : _node$context8.elements.floating, activeEl);\n      });\n      const returnElement = getReturnElement();\n      queueMicrotask(() => {\n        // This is `returnElement`, if it's tabbable, or its first tabbable child.\n        const tabbableReturnElement = getFirstTabbableElement(returnElement);\n        if (\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        returnFocusRef.current && !preventReturnFocusRef.current && isHTMLElement(tabbableReturnElement) && (\n        // If the focus moved somewhere else after mount, avoid returning focus\n        // since it likely entered a different element which should be\n        // respected: https://github.com/floating-ui/floating-ui/issues/2607\n        tabbableReturnElement !== activeEl && activeEl !== doc.body ? isFocusInsideFloatingTree : true)) {\n          tabbableReturnElement.focus({\n            preventScroll: preventReturnFocusScroll\n          });\n        }\n        fallbackEl.remove();\n      });\n    };\n  }, [disabled, floating, floatingFocusElement, returnFocusRef, dataRef, events, tree, isInsidePortal, domReference, getNodeId]);\n  React.useEffect(() => {\n    // The `returnFocus` cleanup behavior is inside a microtask; ensure we\n    // wait for it to complete before resetting the flag.\n    queueMicrotask(() => {\n      preventReturnFocusRef.current = false;\n    });\n  }, [disabled]);\n\n  // Synchronize the `context` & `modal` value to the FloatingPortal context.\n  // It will decide whether or not it needs to render its own guards.\n  useModernLayoutEffect(() => {\n    if (disabled) return;\n    if (!portalContext) return;\n    portalContext.setFocusManagerState({\n      modal,\n      closeOnFocusOut,\n      open,\n      onOpenChange,\n      domReference\n    });\n    return () => {\n      portalContext.setFocusManagerState(null);\n    };\n  }, [disabled, portalContext, modal, open, onOpenChange, closeOnFocusOut, domReference]);\n  useModernLayoutEffect(() => {\n    if (disabled) return;\n    if (!floatingFocusElement) return;\n    handleTabIndex(floatingFocusElement, orderRef);\n  }, [disabled, floatingFocusElement, orderRef]);\n  function renderDismissButton(location) {\n    if (disabled || !visuallyHiddenDismiss || !modal) {\n      return null;\n    }\n    return /*#__PURE__*/jsx(VisuallyHiddenDismiss, {\n      ref: location === 'start' ? startDismissButtonRef : endDismissButtonRef,\n      onClick: event => onOpenChange(false, event.nativeEvent),\n      children: typeof visuallyHiddenDismiss === 'string' ? visuallyHiddenDismiss : 'Dismiss'\n    });\n  }\n  const shouldRenderGuards = !disabled && guards && (modal ? !isUntrappedTypeableCombobox : true) && (isInsidePortal || modal);\n  return /*#__PURE__*/jsxs(Fragment, {\n    children: [shouldRenderGuards && /*#__PURE__*/jsx(FocusGuard, {\n      \"data-type\": \"inside\",\n      ref: mergedBeforeGuardRef,\n      onFocus: event => {\n        if (modal) {\n          const els = getTabbableElements();\n          enqueueFocus(order[0] === 'reference' ? els[0] : els[els.length - 1]);\n        } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n          preventReturnFocusRef.current = false;\n          if (isOutsideEvent(event, portalContext.portalNode)) {\n            const nextTabbable = getNextTabbable(domReference);\n            nextTabbable == null || nextTabbable.focus();\n          } else {\n            var _portalContext$before;\n            (_portalContext$before = portalContext.beforeOutsideRef.current) == null || _portalContext$before.focus();\n          }\n        }\n      }\n    }), !isUntrappedTypeableCombobox && renderDismissButton('start'), children, renderDismissButton('end'), shouldRenderGuards && /*#__PURE__*/jsx(FocusGuard, {\n      \"data-type\": \"inside\",\n      ref: mergedAfterGuardRef,\n      onFocus: event => {\n        if (modal) {\n          enqueueFocus(getTabbableElements()[0]);\n        } else if (portalContext != null && portalContext.preserveTabOrder && portalContext.portalNode) {\n          if (closeOnFocusOut) {\n            preventReturnFocusRef.current = true;\n          }\n          if (isOutsideEvent(event, portalContext.portalNode)) {\n            const prevTabbable = getPreviousTabbable(domReference);\n            prevTabbable == null || prevTabbable.focus();\n          } else {\n            var _portalContext$afterO;\n            (_portalContext$afterO = portalContext.afterOutsideRef.current) == null || _portalContext$afterO.focus();\n          }\n        }\n      }\n    })]\n  });\n}\n\nlet lockCount = 0;\nfunction enableScrollLock() {\n  const isIOS = /iP(hone|ad|od)|iOS/.test(getPlatform());\n  const bodyStyle = document.body.style;\n  // RTL <body> scrollbar\n  const scrollbarX = Math.round(document.documentElement.getBoundingClientRect().left) + document.documentElement.scrollLeft;\n  const paddingProp = scrollbarX ? 'paddingLeft' : 'paddingRight';\n  const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;\n  const scrollX = bodyStyle.left ? parseFloat(bodyStyle.left) : window.scrollX;\n  const scrollY = bodyStyle.top ? parseFloat(bodyStyle.top) : window.scrollY;\n  bodyStyle.overflow = 'hidden';\n  if (scrollbarWidth) {\n    bodyStyle[paddingProp] = scrollbarWidth + \"px\";\n  }\n\n  // Only iOS doesn't respect `overflow: hidden` on document.body, and this\n  // technique has fewer side effects.\n  if (isIOS) {\n    var _window$visualViewpor, _window$visualViewpor2;\n    // iOS 12 does not support `visualViewport`.\n    const offsetLeft = ((_window$visualViewpor = window.visualViewport) == null ? void 0 : _window$visualViewpor.offsetLeft) || 0;\n    const offsetTop = ((_window$visualViewpor2 = window.visualViewport) == null ? void 0 : _window$visualViewpor2.offsetTop) || 0;\n    Object.assign(bodyStyle, {\n      position: 'fixed',\n      top: -(scrollY - Math.floor(offsetTop)) + \"px\",\n      left: -(scrollX - Math.floor(offsetLeft)) + \"px\",\n      right: '0'\n    });\n  }\n  return () => {\n    Object.assign(bodyStyle, {\n      overflow: '',\n      [paddingProp]: ''\n    });\n    if (isIOS) {\n      Object.assign(bodyStyle, {\n        position: '',\n        top: '',\n        left: '',\n        right: ''\n      });\n      window.scrollTo(scrollX, scrollY);\n    }\n  };\n}\nlet cleanup = () => {};\n\n/**\n * Provides base styling for a fixed overlay element to dim content or block\n * pointer events behind a floating element.\n * It's a regular `<div>`, so it can be styled via any CSS solution you prefer.\n * @see https://floating-ui.com/docs/FloatingOverlay\n */\nconst FloatingOverlay = /*#__PURE__*/React.forwardRef(function FloatingOverlay(props, ref) {\n  const {\n    lockScroll = false,\n    ...rest\n  } = props;\n  useModernLayoutEffect(() => {\n    if (!lockScroll) return;\n    lockCount++;\n    if (lockCount === 1) {\n      cleanup = enableScrollLock();\n    }\n    return () => {\n      lockCount--;\n      if (lockCount === 0) {\n        cleanup();\n      }\n    };\n  }, [lockScroll]);\n  return /*#__PURE__*/jsx(\"div\", {\n    ref: ref,\n    ...rest,\n    style: {\n      position: 'fixed',\n      overflow: 'auto',\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0,\n      ...rest.style\n    }\n  });\n});\n\nfunction isButtonTarget(event) {\n  return isHTMLElement(event.target) && event.target.tagName === 'BUTTON';\n}\nfunction isAnchorTarget(event) {\n  return isHTMLElement(event.target) && event.target.tagName === 'A';\n}\nfunction isSpaceIgnored(element) {\n  return isTypeableElement(element);\n}\n/**\n * Opens or closes the floating element when clicking the reference element.\n * @see https://floating-ui.com/docs/useClick\n */\nfunction useClick(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    dataRef,\n    elements: {\n      domReference\n    }\n  } = context;\n  const {\n    enabled = true,\n    event: eventOption = 'click',\n    toggle = true,\n    ignoreMouse = false,\n    keyboardHandlers = true,\n    stickIfOpen = true\n  } = props;\n  const pointerTypeRef = React.useRef();\n  const didKeyDownRef = React.useRef(false);\n  const reference = React.useMemo(() => ({\n    onPointerDown(event) {\n      pointerTypeRef.current = event.pointerType;\n    },\n    onMouseDown(event) {\n      const pointerType = pointerTypeRef.current;\n\n      // Ignore all buttons except for the \"main\" button.\n      // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/button\n      if (event.button !== 0) return;\n      if (eventOption === 'click') return;\n      if (isMouseLikePointerType(pointerType, true) && ignoreMouse) return;\n      if (open && toggle && (dataRef.current.openEvent && stickIfOpen ? dataRef.current.openEvent.type === 'mousedown' : true)) {\n        onOpenChange(false, event.nativeEvent, 'click');\n      } else {\n        // Prevent stealing focus from the floating element\n        event.preventDefault();\n        onOpenChange(true, event.nativeEvent, 'click');\n      }\n    },\n    onClick(event) {\n      const pointerType = pointerTypeRef.current;\n      if (eventOption === 'mousedown' && pointerTypeRef.current) {\n        pointerTypeRef.current = undefined;\n        return;\n      }\n      if (isMouseLikePointerType(pointerType, true) && ignoreMouse) return;\n      if (open && toggle && (dataRef.current.openEvent && stickIfOpen ? dataRef.current.openEvent.type === 'click' : true)) {\n        onOpenChange(false, event.nativeEvent, 'click');\n      } else {\n        onOpenChange(true, event.nativeEvent, 'click');\n      }\n    },\n    onKeyDown(event) {\n      pointerTypeRef.current = undefined;\n      if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event)) {\n        return;\n      }\n      if (event.key === ' ' && !isSpaceIgnored(domReference)) {\n        // Prevent scrolling\n        event.preventDefault();\n        didKeyDownRef.current = true;\n      }\n      if (isAnchorTarget(event)) {\n        return;\n      }\n      if (event.key === 'Enter') {\n        if (open && toggle) {\n          onOpenChange(false, event.nativeEvent, 'click');\n        } else {\n          onOpenChange(true, event.nativeEvent, 'click');\n        }\n      }\n    },\n    onKeyUp(event) {\n      if (event.defaultPrevented || !keyboardHandlers || isButtonTarget(event) || isSpaceIgnored(domReference)) {\n        return;\n      }\n      if (event.key === ' ' && didKeyDownRef.current) {\n        didKeyDownRef.current = false;\n        if (open && toggle) {\n          onOpenChange(false, event.nativeEvent, 'click');\n        } else {\n          onOpenChange(true, event.nativeEvent, 'click');\n        }\n      }\n    }\n  }), [dataRef, domReference, eventOption, ignoreMouse, keyboardHandlers, onOpenChange, open, stickIfOpen, toggle]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nfunction createVirtualElement(domElement, data) {\n  let offsetX = null;\n  let offsetY = null;\n  let isAutoUpdateEvent = false;\n  return {\n    contextElement: domElement || undefined,\n    getBoundingClientRect() {\n      var _data$dataRef$current;\n      const domRect = (domElement == null ? void 0 : domElement.getBoundingClientRect()) || {\n        width: 0,\n        height: 0,\n        x: 0,\n        y: 0\n      };\n      const isXAxis = data.axis === 'x' || data.axis === 'both';\n      const isYAxis = data.axis === 'y' || data.axis === 'both';\n      const canTrackCursorOnAutoUpdate = ['mouseenter', 'mousemove'].includes(((_data$dataRef$current = data.dataRef.current.openEvent) == null ? void 0 : _data$dataRef$current.type) || '') && data.pointerType !== 'touch';\n      let width = domRect.width;\n      let height = domRect.height;\n      let x = domRect.x;\n      let y = domRect.y;\n      if (offsetX == null && data.x && isXAxis) {\n        offsetX = domRect.x - data.x;\n      }\n      if (offsetY == null && data.y && isYAxis) {\n        offsetY = domRect.y - data.y;\n      }\n      x -= offsetX || 0;\n      y -= offsetY || 0;\n      width = 0;\n      height = 0;\n      if (!isAutoUpdateEvent || canTrackCursorOnAutoUpdate) {\n        width = data.axis === 'y' ? domRect.width : 0;\n        height = data.axis === 'x' ? domRect.height : 0;\n        x = isXAxis && data.x != null ? data.x : x;\n        y = isYAxis && data.y != null ? data.y : y;\n      } else if (isAutoUpdateEvent && !canTrackCursorOnAutoUpdate) {\n        height = data.axis === 'x' ? domRect.height : height;\n        width = data.axis === 'y' ? domRect.width : width;\n      }\n      isAutoUpdateEvent = true;\n      return {\n        width,\n        height,\n        x,\n        y,\n        top: y,\n        right: x + width,\n        bottom: y + height,\n        left: x\n      };\n    }\n  };\n}\nfunction isMouseBasedEvent(event) {\n  return event != null && event.clientX != null;\n}\n/**\n * Positions the floating element relative to a client point (in the viewport),\n * such as the mouse position. By default, it follows the mouse cursor.\n * @see https://floating-ui.com/docs/useClientPoint\n */\nfunction useClientPoint(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    dataRef,\n    elements: {\n      floating,\n      domReference\n    },\n    refs\n  } = context;\n  const {\n    enabled = true,\n    axis = 'both',\n    x = null,\n    y = null\n  } = props;\n  const initialRef = React.useRef(false);\n  const cleanupListenerRef = React.useRef(null);\n  const [pointerType, setPointerType] = React.useState();\n  const [reactive, setReactive] = React.useState([]);\n  const setReference = useEffectEvent((x, y) => {\n    if (initialRef.current) return;\n\n    // Prevent setting if the open event was not a mouse-like one\n    // (e.g. focus to open, then hover over the reference element).\n    // Only apply if the event exists.\n    if (dataRef.current.openEvent && !isMouseBasedEvent(dataRef.current.openEvent)) {\n      return;\n    }\n    refs.setPositionReference(createVirtualElement(domReference, {\n      x,\n      y,\n      axis,\n      dataRef,\n      pointerType\n    }));\n  });\n  const handleReferenceEnterOrMove = useEffectEvent(event => {\n    if (x != null || y != null) return;\n    if (!open) {\n      setReference(event.clientX, event.clientY);\n    } else if (!cleanupListenerRef.current) {\n      // If there's no cleanup, there's no listener, but we want to ensure\n      // we add the listener if the cursor landed on the floating element and\n      // then back on the reference (i.e. it's interactive).\n      setReactive([]);\n    }\n  });\n\n  // If the pointer is a mouse-like pointer, we want to continue following the\n  // mouse even if the floating element is transitioning out. On touch\n  // devices, this is undesirable because the floating element will move to\n  // the dismissal touch point.\n  const openCheck = isMouseLikePointerType(pointerType) ? floating : open;\n  const addListener = React.useCallback(() => {\n    // Explicitly specified `x`/`y` coordinates shouldn't add a listener.\n    if (!openCheck || !enabled || x != null || y != null) return;\n    const win = getWindow(floating);\n    function handleMouseMove(event) {\n      const target = getTarget$1(event);\n      if (!contains$1(floating, target)) {\n        setReference(event.clientX, event.clientY);\n      } else {\n        win.removeEventListener('mousemove', handleMouseMove);\n        cleanupListenerRef.current = null;\n      }\n    }\n    if (!dataRef.current.openEvent || isMouseBasedEvent(dataRef.current.openEvent)) {\n      win.addEventListener('mousemove', handleMouseMove);\n      const cleanup = () => {\n        win.removeEventListener('mousemove', handleMouseMove);\n        cleanupListenerRef.current = null;\n      };\n      cleanupListenerRef.current = cleanup;\n      return cleanup;\n    }\n    refs.setPositionReference(domReference);\n  }, [openCheck, enabled, x, y, floating, dataRef, refs, domReference, setReference]);\n  React.useEffect(() => {\n    return addListener();\n  }, [addListener, reactive]);\n  React.useEffect(() => {\n    if (enabled && !floating) {\n      initialRef.current = false;\n    }\n  }, [enabled, floating]);\n  React.useEffect(() => {\n    if (!enabled && open) {\n      initialRef.current = true;\n    }\n  }, [enabled, open]);\n  useModernLayoutEffect(() => {\n    if (enabled && (x != null || y != null)) {\n      initialRef.current = false;\n      setReference(x, y);\n    }\n  }, [enabled, x, y, setReference]);\n  const reference = React.useMemo(() => {\n    function setPointerTypeRef(_ref) {\n      let {\n        pointerType\n      } = _ref;\n      setPointerType(pointerType);\n    }\n    return {\n      onPointerDown: setPointerTypeRef,\n      onPointerEnter: setPointerTypeRef,\n      onMouseMove: handleReferenceEnterOrMove,\n      onMouseEnter: handleReferenceEnterOrMove\n    };\n  }, [handleReferenceEnterOrMove]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nconst bubbleHandlerKeys = {\n  pointerdown: 'onPointerDown',\n  mousedown: 'onMouseDown',\n  click: 'onClick'\n};\nconst captureHandlerKeys = {\n  pointerdown: 'onPointerDownCapture',\n  mousedown: 'onMouseDownCapture',\n  click: 'onClickCapture'\n};\nconst normalizeProp = normalizable => {\n  var _normalizable$escapeK, _normalizable$outside;\n  return {\n    escapeKey: typeof normalizable === 'boolean' ? normalizable : (_normalizable$escapeK = normalizable == null ? void 0 : normalizable.escapeKey) != null ? _normalizable$escapeK : false,\n    outsidePress: typeof normalizable === 'boolean' ? normalizable : (_normalizable$outside = normalizable == null ? void 0 : normalizable.outsidePress) != null ? _normalizable$outside : true\n  };\n};\n/**\n * Closes the floating element when a dismissal is requested — by default, when\n * the user presses the `escape` key or outside of the floating element.\n * @see https://floating-ui.com/docs/useDismiss\n */\nfunction useDismiss(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    elements,\n    dataRef\n  } = context;\n  const {\n    enabled = true,\n    escapeKey = true,\n    outsidePress: unstable_outsidePress = true,\n    outsidePressEvent = 'pointerdown',\n    referencePress = false,\n    referencePressEvent = 'pointerdown',\n    ancestorScroll = false,\n    bubbles,\n    capture\n  } = props;\n  const tree = useFloatingTree();\n  const outsidePressFn = useEffectEvent(typeof unstable_outsidePress === 'function' ? unstable_outsidePress : () => false);\n  const outsidePress = typeof unstable_outsidePress === 'function' ? outsidePressFn : unstable_outsidePress;\n  const insideReactTreeRef = React.useRef(false);\n  const endedOrStartedInsideRef = React.useRef(false);\n  const {\n    escapeKey: escapeKeyBubbles,\n    outsidePress: outsidePressBubbles\n  } = normalizeProp(bubbles);\n  const {\n    escapeKey: escapeKeyCapture,\n    outsidePress: outsidePressCapture\n  } = normalizeProp(capture);\n  const isComposingRef = React.useRef(false);\n  const closeOnEscapeKeyDown = useEffectEvent(event => {\n    var _dataRef$current$floa;\n    if (!open || !enabled || !escapeKey || event.key !== 'Escape') {\n      return;\n    }\n\n    // Wait until IME is settled. Pressing `Escape` while composing should\n    // close the compose menu, but not the floating element.\n    if (isComposingRef.current) {\n      return;\n    }\n    const nodeId = (_dataRef$current$floa = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa.nodeId;\n    const children = tree ? getNodeChildren$1(tree.nodesRef.current, nodeId) : [];\n    if (!escapeKeyBubbles) {\n      event.stopPropagation();\n      if (children.length > 0) {\n        let shouldDismiss = true;\n        children.forEach(child => {\n          var _child$context;\n          if ((_child$context = child.context) != null && _child$context.open && !child.context.dataRef.current.__escapeKeyBubbles) {\n            shouldDismiss = false;\n            return;\n          }\n        });\n        if (!shouldDismiss) {\n          return;\n        }\n      }\n    }\n    onOpenChange(false, isReactEvent(event) ? event.nativeEvent : event, 'escape-key');\n  });\n  const closeOnEscapeKeyDownCapture = useEffectEvent(event => {\n    var _getTarget2;\n    const callback = () => {\n      var _getTarget;\n      closeOnEscapeKeyDown(event);\n      (_getTarget = getTarget$1(event)) == null || _getTarget.removeEventListener('keydown', callback);\n    };\n    (_getTarget2 = getTarget$1(event)) == null || _getTarget2.addEventListener('keydown', callback);\n  });\n  const closeOnPressOutside = useEffectEvent(event => {\n    var _dataRef$current$floa2;\n    // Given developers can stop the propagation of the synthetic event,\n    // we can only be confident with a positive value.\n    const insideReactTree = insideReactTreeRef.current;\n    insideReactTreeRef.current = false;\n\n    // When click outside is lazy (`click` event), handle dragging.\n    // Don't close if:\n    // - The click started inside the floating element.\n    // - The click ended inside the floating element.\n    const endedOrStartedInside = endedOrStartedInsideRef.current;\n    endedOrStartedInsideRef.current = false;\n    if (outsidePressEvent === 'click' && endedOrStartedInside) {\n      return;\n    }\n    if (insideReactTree) {\n      return;\n    }\n    if (typeof outsidePress === 'function' && !outsidePress(event)) {\n      return;\n    }\n    const target = getTarget$1(event);\n    const inertSelector = \"[\" + createAttribute('inert') + \"]\";\n    const markers = getDocument$1(elements.floating).querySelectorAll(inertSelector);\n    let targetRootAncestor = isElement(target) ? target : null;\n    while (targetRootAncestor && !isLastTraversableNode(targetRootAncestor)) {\n      const nextParent = getParentNode(targetRootAncestor);\n      if (isLastTraversableNode(nextParent) || !isElement(nextParent)) {\n        break;\n      }\n      targetRootAncestor = nextParent;\n    }\n\n    // Check if the click occurred on a third-party element injected after the\n    // floating element rendered.\n    if (markers.length && isElement(target) && !isRootElement(target) &&\n    // Clicked on a direct ancestor (e.g. FloatingOverlay).\n    !contains$1(target, elements.floating) &&\n    // If the target root element contains none of the markers, then the\n    // element was injected after the floating element rendered.\n    Array.from(markers).every(marker => !contains$1(targetRootAncestor, marker))) {\n      return;\n    }\n\n    // Check if the click occurred on the scrollbar\n    if (isHTMLElement(target) && floating) {\n      const lastTraversableNode = isLastTraversableNode(target);\n      const style = getComputedStyle(target);\n      const scrollRe = /auto|scroll/;\n      const isScrollableX = lastTraversableNode || scrollRe.test(style.overflowX);\n      const isScrollableY = lastTraversableNode || scrollRe.test(style.overflowY);\n      const canScrollX = isScrollableX && target.clientWidth > 0 && target.scrollWidth > target.clientWidth;\n      const canScrollY = isScrollableY && target.clientHeight > 0 && target.scrollHeight > target.clientHeight;\n      const isRTL = style.direction === 'rtl';\n\n      // Check click position relative to scrollbar.\n      // In some browsers it is possible to change the <body> (or window)\n      // scrollbar to the left side, but is very rare and is difficult to\n      // check for. Plus, for modal dialogs with backdrops, it is more\n      // important that the backdrop is checked but not so much the window.\n      const pressedVerticalScrollbar = canScrollY && (isRTL ? event.offsetX <= target.offsetWidth - target.clientWidth : event.offsetX > target.clientWidth);\n      const pressedHorizontalScrollbar = canScrollX && event.offsetY > target.clientHeight;\n      if (pressedVerticalScrollbar || pressedHorizontalScrollbar) {\n        return;\n      }\n    }\n    const nodeId = (_dataRef$current$floa2 = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa2.nodeId;\n    const targetIsInsideChildren = tree && getNodeChildren$1(tree.nodesRef.current, nodeId).some(node => {\n      var _node$context;\n      return isEventTargetWithin(event, (_node$context = node.context) == null ? void 0 : _node$context.elements.floating);\n    });\n    if (isEventTargetWithin(event, elements.floating) || isEventTargetWithin(event, elements.domReference) || targetIsInsideChildren) {\n      return;\n    }\n    const children = tree ? getNodeChildren$1(tree.nodesRef.current, nodeId) : [];\n    if (children.length > 0) {\n      let shouldDismiss = true;\n      children.forEach(child => {\n        var _child$context2;\n        if ((_child$context2 = child.context) != null && _child$context2.open && !child.context.dataRef.current.__outsidePressBubbles) {\n          shouldDismiss = false;\n          return;\n        }\n      });\n      if (!shouldDismiss) {\n        return;\n      }\n    }\n    onOpenChange(false, event, 'outside-press');\n  });\n  const closeOnPressOutsideCapture = useEffectEvent(event => {\n    var _getTarget4;\n    const callback = () => {\n      var _getTarget3;\n      closeOnPressOutside(event);\n      (_getTarget3 = getTarget$1(event)) == null || _getTarget3.removeEventListener(outsidePressEvent, callback);\n    };\n    (_getTarget4 = getTarget$1(event)) == null || _getTarget4.addEventListener(outsidePressEvent, callback);\n  });\n  React.useEffect(() => {\n    if (!open || !enabled) {\n      return;\n    }\n    dataRef.current.__escapeKeyBubbles = escapeKeyBubbles;\n    dataRef.current.__outsidePressBubbles = outsidePressBubbles;\n    let compositionTimeout = -1;\n    function onScroll(event) {\n      onOpenChange(false, event, 'ancestor-scroll');\n    }\n    function handleCompositionStart() {\n      window.clearTimeout(compositionTimeout);\n      isComposingRef.current = true;\n    }\n    function handleCompositionEnd() {\n      // Safari fires `compositionend` before `keydown`, so we need to wait\n      // until the next tick to set `isComposing` to `false`.\n      // https://bugs.webkit.org/show_bug.cgi?id=165004\n      compositionTimeout = window.setTimeout(() => {\n        isComposingRef.current = false;\n      },\n      // 0ms or 1ms don't work in Safari. 5ms appears to consistently work.\n      // Only apply to WebKit for the test to remain 0ms.\n      isWebKit() ? 5 : 0);\n    }\n    const doc = getDocument$1(elements.floating);\n    if (escapeKey) {\n      doc.addEventListener('keydown', escapeKeyCapture ? closeOnEscapeKeyDownCapture : closeOnEscapeKeyDown, escapeKeyCapture);\n      doc.addEventListener('compositionstart', handleCompositionStart);\n      doc.addEventListener('compositionend', handleCompositionEnd);\n    }\n    outsidePress && doc.addEventListener(outsidePressEvent, outsidePressCapture ? closeOnPressOutsideCapture : closeOnPressOutside, outsidePressCapture);\n    let ancestors = [];\n    if (ancestorScroll) {\n      if (isElement(elements.domReference)) {\n        ancestors = getOverflowAncestors(elements.domReference);\n      }\n      if (isElement(elements.floating)) {\n        ancestors = ancestors.concat(getOverflowAncestors(elements.floating));\n      }\n      if (!isElement(elements.reference) && elements.reference && elements.reference.contextElement) {\n        ancestors = ancestors.concat(getOverflowAncestors(elements.reference.contextElement));\n      }\n    }\n\n    // Ignore the visual viewport for scrolling dismissal (allow pinch-zoom)\n    ancestors = ancestors.filter(ancestor => {\n      var _doc$defaultView;\n      return ancestor !== ((_doc$defaultView = doc.defaultView) == null ? void 0 : _doc$defaultView.visualViewport);\n    });\n    ancestors.forEach(ancestor => {\n      ancestor.addEventListener('scroll', onScroll, {\n        passive: true\n      });\n    });\n    return () => {\n      if (escapeKey) {\n        doc.removeEventListener('keydown', escapeKeyCapture ? closeOnEscapeKeyDownCapture : closeOnEscapeKeyDown, escapeKeyCapture);\n        doc.removeEventListener('compositionstart', handleCompositionStart);\n        doc.removeEventListener('compositionend', handleCompositionEnd);\n      }\n      outsidePress && doc.removeEventListener(outsidePressEvent, outsidePressCapture ? closeOnPressOutsideCapture : closeOnPressOutside, outsidePressCapture);\n      ancestors.forEach(ancestor => {\n        ancestor.removeEventListener('scroll', onScroll);\n      });\n      window.clearTimeout(compositionTimeout);\n    };\n  }, [dataRef, elements, escapeKey, outsidePress, outsidePressEvent, open, onOpenChange, ancestorScroll, enabled, escapeKeyBubbles, outsidePressBubbles, closeOnEscapeKeyDown, escapeKeyCapture, closeOnEscapeKeyDownCapture, closeOnPressOutside, outsidePressCapture, closeOnPressOutsideCapture]);\n  React.useEffect(() => {\n    insideReactTreeRef.current = false;\n  }, [outsidePress, outsidePressEvent]);\n  const reference = React.useMemo(() => ({\n    onKeyDown: closeOnEscapeKeyDown,\n    ...(referencePress && {\n      [bubbleHandlerKeys[referencePressEvent]]: event => {\n        onOpenChange(false, event.nativeEvent, 'reference-press');\n      },\n      ...(referencePressEvent !== 'click' && {\n        onClick(event) {\n          onOpenChange(false, event.nativeEvent, 'reference-press');\n        }\n      })\n    })\n  }), [closeOnEscapeKeyDown, onOpenChange, referencePress, referencePressEvent]);\n  const floating = React.useMemo(() => ({\n    onKeyDown: closeOnEscapeKeyDown,\n    onMouseDown() {\n      endedOrStartedInsideRef.current = true;\n    },\n    onMouseUp() {\n      endedOrStartedInsideRef.current = true;\n    },\n    [captureHandlerKeys[outsidePressEvent]]: () => {\n      insideReactTreeRef.current = true;\n    }\n  }), [closeOnEscapeKeyDown, outsidePressEvent]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating\n  } : {}, [enabled, reference, floating]);\n}\n\nfunction useFloatingRootContext(options) {\n  const {\n    open = false,\n    onOpenChange: onOpenChangeProp,\n    elements: elementsProp\n  } = options;\n  const floatingId = useId();\n  const dataRef = React.useRef({});\n  const [events] = React.useState(() => createEventEmitter());\n  const nested = useFloatingParentNodeId() != null;\n  if (process.env.NODE_ENV !== \"production\") {\n    const optionDomReference = elementsProp.reference;\n    if (optionDomReference && !isElement(optionDomReference)) {\n      error('Cannot pass a virtual element to the `elements.reference` option,', 'as it must be a real DOM element. Use `refs.setPositionReference()`', 'instead.');\n    }\n  }\n  const [positionReference, setPositionReference] = React.useState(elementsProp.reference);\n  const onOpenChange = useEffectEvent((open, event, reason) => {\n    dataRef.current.openEvent = open ? event : undefined;\n    events.emit('openchange', {\n      open,\n      event,\n      reason,\n      nested\n    });\n    onOpenChangeProp == null || onOpenChangeProp(open, event, reason);\n  });\n  const refs = React.useMemo(() => ({\n    setPositionReference\n  }), []);\n  const elements = React.useMemo(() => ({\n    reference: positionReference || elementsProp.reference || null,\n    floating: elementsProp.floating || null,\n    domReference: elementsProp.reference\n  }), [positionReference, elementsProp.reference, elementsProp.floating]);\n  return React.useMemo(() => ({\n    dataRef,\n    open,\n    onOpenChange,\n    elements,\n    events,\n    floatingId,\n    refs\n  }), [open, onOpenChange, elements, events, floatingId, refs]);\n}\n\n/**\n * Provides data to position a floating element and context to add interactions.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    nodeId\n  } = options;\n  const internalRootContext = useFloatingRootContext({\n    ...options,\n    elements: {\n      reference: null,\n      floating: null,\n      ...options.elements\n    }\n  });\n  const rootContext = options.rootContext || internalRootContext;\n  const computedElements = rootContext.elements;\n  const [_domReference, setDomReference] = React.useState(null);\n  const [positionReference, _setPositionReference] = React.useState(null);\n  const optionDomReference = computedElements == null ? void 0 : computedElements.domReference;\n  const domReference = optionDomReference || _domReference;\n  const domReferenceRef = React.useRef(null);\n  const tree = useFloatingTree();\n  useModernLayoutEffect(() => {\n    if (domReference) {\n      domReferenceRef.current = domReference;\n    }\n  }, [domReference]);\n  const position = useFloating$1({\n    ...options,\n    elements: {\n      ...computedElements,\n      ...(positionReference && {\n        reference: positionReference\n      })\n    }\n  });\n  const setPositionReference = React.useCallback(node => {\n    const computedPositionReference = isElement(node) ? {\n      getBoundingClientRect: () => node.getBoundingClientRect(),\n      getClientRects: () => node.getClientRects(),\n      contextElement: node\n    } : node;\n    // Store the positionReference in state if the DOM reference is specified externally via the\n    // `elements.reference` option. This ensures that it won't be overridden on future renders.\n    _setPositionReference(computedPositionReference);\n    position.refs.setReference(computedPositionReference);\n  }, [position.refs]);\n  const setReference = React.useCallback(node => {\n    if (isElement(node) || node === null) {\n      domReferenceRef.current = node;\n      setDomReference(node);\n    }\n\n    // Backwards-compatibility for passing a virtual element to `reference`\n    // after it has set the DOM reference.\n    if (isElement(position.refs.reference.current) || position.refs.reference.current === null ||\n    // Don't allow setting virtual elements using the old technique back to\n    // `null` to support `positionReference` + an unstable `reference`\n    // callback ref.\n    node !== null && !isElement(node)) {\n      position.refs.setReference(node);\n    }\n  }, [position.refs]);\n  const refs = React.useMemo(() => ({\n    ...position.refs,\n    setReference,\n    setPositionReference,\n    domReference: domReferenceRef\n  }), [position.refs, setReference, setPositionReference]);\n  const elements = React.useMemo(() => ({\n    ...position.elements,\n    domReference: domReference\n  }), [position.elements, domReference]);\n  const context = React.useMemo(() => ({\n    ...position,\n    ...rootContext,\n    refs,\n    elements,\n    nodeId\n  }), [position, refs, elements, nodeId, rootContext]);\n  useModernLayoutEffect(() => {\n    rootContext.dataRef.current.floatingContext = context;\n    const node = tree == null ? void 0 : tree.nodesRef.current.find(node => node.id === nodeId);\n    if (node) {\n      node.context = context;\n    }\n  });\n  return React.useMemo(() => ({\n    ...position,\n    context,\n    refs,\n    elements\n  }), [position, refs, elements, context]);\n}\n\nfunction isMacSafari() {\n  return isMac() && isSafari();\n}\n/**\n * Opens the floating element while the reference element has focus, like CSS\n * `:focus`.\n * @see https://floating-ui.com/docs/useFocus\n */\nfunction useFocus(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    onOpenChange,\n    events,\n    dataRef,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    visibleOnly = true\n  } = props;\n  const blockFocusRef = React.useRef(false);\n  const timeoutRef = React.useRef(-1);\n  const keyboardModalityRef = React.useRef(true);\n  React.useEffect(() => {\n    if (!enabled) return;\n    const win = getWindow(elements.domReference);\n\n    // If the reference was focused and the user left the tab/window, and the\n    // floating element was not open, the focus should be blocked when they\n    // return to the tab/window.\n    function onBlur() {\n      if (!open && isHTMLElement(elements.domReference) && elements.domReference === activeElement(getDocument$1(elements.domReference))) {\n        blockFocusRef.current = true;\n      }\n    }\n    function onKeyDown() {\n      keyboardModalityRef.current = true;\n    }\n    function onPointerDown() {\n      keyboardModalityRef.current = false;\n    }\n    win.addEventListener('blur', onBlur);\n    if (isMacSafari()) {\n      win.addEventListener('keydown', onKeyDown, true);\n      win.addEventListener('pointerdown', onPointerDown, true);\n    }\n    return () => {\n      win.removeEventListener('blur', onBlur);\n      if (isMacSafari()) {\n        win.removeEventListener('keydown', onKeyDown, true);\n        win.removeEventListener('pointerdown', onPointerDown, true);\n      }\n    };\n  }, [elements.domReference, open, enabled]);\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onOpenChange(_ref) {\n      let {\n        reason\n      } = _ref;\n      if (reason === 'reference-press' || reason === 'escape-key') {\n        blockFocusRef.current = true;\n      }\n    }\n    events.on('openchange', onOpenChange);\n    return () => {\n      events.off('openchange', onOpenChange);\n    };\n  }, [events, enabled]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeoutIfSet(timeoutRef);\n    };\n  }, []);\n  const reference = React.useMemo(() => ({\n    onMouseLeave() {\n      blockFocusRef.current = false;\n    },\n    onFocus(event) {\n      if (blockFocusRef.current) return;\n      const target = getTarget$1(event.nativeEvent);\n      if (visibleOnly && isElement(target)) {\n        // Safari fails to match `:focus-visible` if focus was initially\n        // outside the document.\n        if (isMacSafari() && !event.relatedTarget) {\n          if (!keyboardModalityRef.current && !isTypeableElement(target)) {\n            return;\n          }\n        } else if (!matchesFocusVisible(target)) {\n          return;\n        }\n      }\n      onOpenChange(true, event.nativeEvent, 'focus');\n    },\n    onBlur(event) {\n      blockFocusRef.current = false;\n      const relatedTarget = event.relatedTarget;\n      const nativeEvent = event.nativeEvent;\n\n      // Hit the non-modal focus management portal guard. Focus will be\n      // moved into the floating element immediately after.\n      const movedToFocusGuard = isElement(relatedTarget) && relatedTarget.hasAttribute(createAttribute('focus-guard')) && relatedTarget.getAttribute('data-type') === 'outside';\n\n      // Wait for the window blur listener to fire.\n      timeoutRef.current = window.setTimeout(() => {\n        var _dataRef$current$floa;\n        const activeEl = activeElement(elements.domReference ? elements.domReference.ownerDocument : document);\n\n        // Focus left the page, keep it open.\n        if (!relatedTarget && activeEl === elements.domReference) return;\n\n        // When focusing the reference element (e.g. regular click), then\n        // clicking into the floating element, prevent it from hiding.\n        // Note: it must be focusable, e.g. `tabindex=\"-1\"`.\n        // We can not rely on relatedTarget to point to the correct element\n        // as it will only point to the shadow host of the newly focused element\n        // and not the element that actually has received focus if it is located\n        // inside a shadow root.\n        if (contains$1((_dataRef$current$floa = dataRef.current.floatingContext) == null ? void 0 : _dataRef$current$floa.refs.floating.current, activeEl) || contains$1(elements.domReference, activeEl) || movedToFocusGuard) {\n          return;\n        }\n        onOpenChange(false, nativeEvent, 'focus');\n      });\n    }\n  }), [dataRef, elements.domReference, onOpenChange, visibleOnly]);\n  return React.useMemo(() => enabled ? {\n    reference\n  } : {}, [enabled, reference]);\n}\n\nfunction mergeProps(userProps, propsList, elementKey) {\n  const map = new Map();\n  const isItem = elementKey === 'item';\n  let domUserProps = userProps;\n  if (isItem && userProps) {\n    const {\n      [ACTIVE_KEY]: _,\n      [SELECTED_KEY]: __,\n      ...validProps\n    } = userProps;\n    domUserProps = validProps;\n  }\n  return {\n    ...(elementKey === 'floating' && {\n      tabIndex: -1,\n      [FOCUSABLE_ATTRIBUTE]: ''\n    }),\n    ...domUserProps,\n    ...propsList.map(value => {\n      const propsOrGetProps = value ? value[elementKey] : null;\n      if (typeof propsOrGetProps === 'function') {\n        return userProps ? propsOrGetProps(userProps) : null;\n      }\n      return propsOrGetProps;\n    }).concat(userProps).reduce((acc, props) => {\n      if (!props) {\n        return acc;\n      }\n      Object.entries(props).forEach(_ref => {\n        let [key, value] = _ref;\n        if (isItem && [ACTIVE_KEY, SELECTED_KEY].includes(key)) {\n          return;\n        }\n        if (key.indexOf('on') === 0) {\n          if (!map.has(key)) {\n            map.set(key, []);\n          }\n          if (typeof value === 'function') {\n            var _map$get;\n            (_map$get = map.get(key)) == null || _map$get.push(value);\n            acc[key] = function () {\n              var _map$get2;\n              for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n                args[_key] = arguments[_key];\n              }\n              return (_map$get2 = map.get(key)) == null ? void 0 : _map$get2.map(fn => fn(...args)).find(val => val !== undefined);\n            };\n          }\n        } else {\n          acc[key] = value;\n        }\n      });\n      return acc;\n    }, {})\n  };\n}\n/**\n * Merges an array of interaction hooks' props into prop getters, allowing\n * event handler functions to be composed together without overwriting one\n * another.\n * @see https://floating-ui.com/docs/useInteractions\n */\nfunction useInteractions(propsList) {\n  if (propsList === void 0) {\n    propsList = [];\n  }\n  const referenceDeps = propsList.map(key => key == null ? void 0 : key.reference);\n  const floatingDeps = propsList.map(key => key == null ? void 0 : key.floating);\n  const itemDeps = propsList.map(key => key == null ? void 0 : key.item);\n  const getReferenceProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'reference'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  referenceDeps);\n  const getFloatingProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'floating'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  floatingDeps);\n  const getItemProps = React.useCallback(userProps => mergeProps(userProps, propsList, 'item'),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  itemDeps);\n  return React.useMemo(() => ({\n    getReferenceProps,\n    getFloatingProps,\n    getItemProps\n  }), [getReferenceProps, getFloatingProps, getItemProps]);\n}\n\nconst ESCAPE = 'Escape';\nfunction doSwitch(orientation, vertical, horizontal) {\n  switch (orientation) {\n    case 'vertical':\n      return vertical;\n    case 'horizontal':\n      return horizontal;\n    default:\n      return vertical || horizontal;\n  }\n}\nfunction isMainOrientationKey(key, orientation) {\n  const vertical = key === ARROW_UP || key === ARROW_DOWN;\n  const horizontal = key === ARROW_LEFT || key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isMainOrientationToEndKey(key, orientation, rtl) {\n  const vertical = key === ARROW_DOWN;\n  const horizontal = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  return doSwitch(orientation, vertical, horizontal) || key === 'Enter' || key === ' ' || key === '';\n}\nfunction isCrossOrientationOpenKey(key, orientation, rtl) {\n  const vertical = rtl ? key === ARROW_LEFT : key === ARROW_RIGHT;\n  const horizontal = key === ARROW_DOWN;\n  return doSwitch(orientation, vertical, horizontal);\n}\nfunction isCrossOrientationCloseKey(key, orientation, rtl, cols) {\n  const vertical = rtl ? key === ARROW_RIGHT : key === ARROW_LEFT;\n  const horizontal = key === ARROW_UP;\n  if (orientation === 'both' || orientation === 'horizontal' && cols && cols > 1) {\n    return key === ESCAPE;\n  }\n  return doSwitch(orientation, vertical, horizontal);\n}\n/**\n * Adds arrow key-based navigation of a list of items, either using real DOM\n * focus or virtual focus.\n * @see https://floating-ui.com/docs/useListNavigation\n */\nfunction useListNavigation(context, props) {\n  const {\n    open,\n    onOpenChange,\n    elements,\n    floatingId\n  } = context;\n  const {\n    listRef,\n    activeIndex,\n    onNavigate: unstable_onNavigate = () => {},\n    enabled = true,\n    selectedIndex = null,\n    allowEscape = false,\n    loop = false,\n    nested = false,\n    rtl = false,\n    virtual = false,\n    focusItemOnOpen = 'auto',\n    focusItemOnHover = true,\n    openOnArrowKeyDown = true,\n    disabledIndices = undefined,\n    orientation = 'vertical',\n    parentOrientation,\n    cols = 1,\n    scrollItemIntoView = true,\n    virtualItemRef,\n    itemSizes,\n    dense = false\n  } = props;\n  if (process.env.NODE_ENV !== \"production\") {\n    if (allowEscape) {\n      if (!loop) {\n        warn('`useListNavigation` looping must be enabled to allow escaping.');\n      }\n      if (!virtual) {\n        warn('`useListNavigation` must be virtual to allow escaping.');\n      }\n    }\n    if (orientation === 'vertical' && cols > 1) {\n      warn('In grid list navigation mode (`cols` > 1), the `orientation` should', 'be either \"horizontal\" or \"both\".');\n    }\n  }\n  const floatingFocusElement = getFloatingFocusElement(elements.floating);\n  const floatingFocusElementRef = useLatestRef(floatingFocusElement);\n  const parentId = useFloatingParentNodeId();\n  const tree = useFloatingTree();\n  useModernLayoutEffect(() => {\n    context.dataRef.current.orientation = orientation;\n  }, [context, orientation]);\n  const onNavigate = useEffectEvent(() => {\n    unstable_onNavigate(indexRef.current === -1 ? null : indexRef.current);\n  });\n  const typeableComboboxReference = isTypeableCombobox(elements.domReference);\n  const focusItemOnOpenRef = React.useRef(focusItemOnOpen);\n  const indexRef = React.useRef(selectedIndex != null ? selectedIndex : -1);\n  const keyRef = React.useRef(null);\n  const isPointerModalityRef = React.useRef(true);\n  const previousOnNavigateRef = React.useRef(onNavigate);\n  const previousMountedRef = React.useRef(!!elements.floating);\n  const previousOpenRef = React.useRef(open);\n  const forceSyncFocusRef = React.useRef(false);\n  const forceScrollIntoViewRef = React.useRef(false);\n  const disabledIndicesRef = useLatestRef(disabledIndices);\n  const latestOpenRef = useLatestRef(open);\n  const scrollItemIntoViewRef = useLatestRef(scrollItemIntoView);\n  const selectedIndexRef = useLatestRef(selectedIndex);\n  const [activeId, setActiveId] = React.useState();\n  const [virtualId, setVirtualId] = React.useState();\n  const focusItem = useEffectEvent(() => {\n    function runFocus(item) {\n      if (virtual) {\n        var _item$id;\n        if ((_item$id = item.id) != null && _item$id.endsWith('-fui-option')) {\n          item.id = floatingId + \"-\" + Math.random().toString(16).slice(2, 10);\n        }\n        setActiveId(item.id);\n        tree == null || tree.events.emit('virtualfocus', item);\n        if (virtualItemRef) {\n          virtualItemRef.current = item;\n        }\n      } else {\n        enqueueFocus(item, {\n          sync: forceSyncFocusRef.current,\n          preventScroll: true\n        });\n      }\n    }\n    const initialItem = listRef.current[indexRef.current];\n    const forceScrollIntoView = forceScrollIntoViewRef.current;\n    if (initialItem) {\n      runFocus(initialItem);\n    }\n    const scheduler = forceSyncFocusRef.current ? v => v() : requestAnimationFrame;\n    scheduler(() => {\n      const waitedItem = listRef.current[indexRef.current] || initialItem;\n      if (!waitedItem) return;\n      if (!initialItem) {\n        runFocus(waitedItem);\n      }\n      const scrollIntoViewOptions = scrollItemIntoViewRef.current;\n      const shouldScrollIntoView = scrollIntoViewOptions && item && (forceScrollIntoView || !isPointerModalityRef.current);\n      if (shouldScrollIntoView) {\n        // JSDOM doesn't support `.scrollIntoView()` but it's widely supported\n        // by all browsers.\n        waitedItem.scrollIntoView == null || waitedItem.scrollIntoView(typeof scrollIntoViewOptions === 'boolean' ? {\n          block: 'nearest',\n          inline: 'nearest'\n        } : scrollIntoViewOptions);\n      }\n    });\n  });\n\n  // Sync `selectedIndex` to be the `activeIndex` upon opening the floating\n  // element. Also, reset `activeIndex` upon closing the floating element.\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (open && elements.floating) {\n      if (focusItemOnOpenRef.current && selectedIndex != null) {\n        // Regardless of the pointer modality, we want to ensure the selected\n        // item comes into view when the floating element is opened.\n        forceScrollIntoViewRef.current = true;\n        indexRef.current = selectedIndex;\n        onNavigate();\n      }\n    } else if (previousMountedRef.current) {\n      // Since the user can specify `onNavigate` conditionally\n      // (onNavigate: open ? setActiveIndex : setSelectedIndex),\n      // we store and call the previous function.\n      indexRef.current = -1;\n      previousOnNavigateRef.current();\n    }\n  }, [enabled, open, elements.floating, selectedIndex, onNavigate]);\n\n  // Sync `activeIndex` to be the focused item while the floating element is\n  // open.\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (!open) return;\n    if (!elements.floating) return;\n    if (activeIndex == null) {\n      forceSyncFocusRef.current = false;\n      if (selectedIndexRef.current != null) {\n        return;\n      }\n\n      // Reset while the floating element was open (e.g. the list changed).\n      if (previousMountedRef.current) {\n        indexRef.current = -1;\n        focusItem();\n      }\n\n      // Initial sync.\n      if ((!previousOpenRef.current || !previousMountedRef.current) && focusItemOnOpenRef.current && (keyRef.current != null || focusItemOnOpenRef.current === true && keyRef.current == null)) {\n        let runs = 0;\n        const waitForListPopulated = () => {\n          if (listRef.current[0] == null) {\n            // Avoid letting the browser paint if possible on the first try,\n            // otherwise use rAF. Don't try more than twice, since something\n            // is wrong otherwise.\n            if (runs < 2) {\n              const scheduler = runs ? requestAnimationFrame : queueMicrotask;\n              scheduler(waitForListPopulated);\n            }\n            runs++;\n          } else {\n            indexRef.current = keyRef.current == null || isMainOrientationToEndKey(keyRef.current, orientation, rtl) || nested ? getMinListIndex(listRef, disabledIndicesRef.current) : getMaxListIndex(listRef, disabledIndicesRef.current);\n            keyRef.current = null;\n            onNavigate();\n          }\n        };\n        waitForListPopulated();\n      }\n    } else if (!isIndexOutOfListBounds(listRef, activeIndex)) {\n      indexRef.current = activeIndex;\n      focusItem();\n      forceScrollIntoViewRef.current = false;\n    }\n  }, [enabled, open, elements.floating, activeIndex, selectedIndexRef, nested, listRef, orientation, rtl, onNavigate, focusItem, disabledIndicesRef]);\n\n  // Ensure the parent floating element has focus when a nested child closes\n  // to allow arrow key navigation to work after the pointer leaves the child.\n  useModernLayoutEffect(() => {\n    var _nodes$find;\n    if (!enabled || elements.floating || !tree || virtual || !previousMountedRef.current) {\n      return;\n    }\n    const nodes = tree.nodesRef.current;\n    const parent = (_nodes$find = nodes.find(node => node.id === parentId)) == null || (_nodes$find = _nodes$find.context) == null ? void 0 : _nodes$find.elements.floating;\n    const activeEl = activeElement(getDocument$1(elements.floating));\n    const treeContainsActiveEl = nodes.some(node => node.context && contains$1(node.context.elements.floating, activeEl));\n    if (parent && !treeContainsActiveEl && isPointerModalityRef.current) {\n      parent.focus({\n        preventScroll: true\n      });\n    }\n  }, [enabled, elements.floating, tree, parentId, virtual]);\n  useModernLayoutEffect(() => {\n    if (!enabled) return;\n    if (!tree) return;\n    if (!virtual) return;\n    if (parentId) return;\n    function handleVirtualFocus(item) {\n      setVirtualId(item.id);\n      if (virtualItemRef) {\n        virtualItemRef.current = item;\n      }\n    }\n    tree.events.on('virtualfocus', handleVirtualFocus);\n    return () => {\n      tree.events.off('virtualfocus', handleVirtualFocus);\n    };\n  }, [enabled, tree, virtual, parentId, virtualItemRef]);\n  useModernLayoutEffect(() => {\n    previousOnNavigateRef.current = onNavigate;\n    previousOpenRef.current = open;\n    previousMountedRef.current = !!elements.floating;\n  });\n  useModernLayoutEffect(() => {\n    if (!open) {\n      keyRef.current = null;\n    }\n  }, [open]);\n  const hasActiveIndex = activeIndex != null;\n  const item = React.useMemo(() => {\n    function syncCurrentTarget(currentTarget) {\n      if (!open) return;\n      const index = listRef.current.indexOf(currentTarget);\n      if (index !== -1 && indexRef.current !== index) {\n        indexRef.current = index;\n        onNavigate();\n      }\n    }\n    const props = {\n      onFocus(_ref) {\n        let {\n          currentTarget\n        } = _ref;\n        forceSyncFocusRef.current = true;\n        syncCurrentTarget(currentTarget);\n      },\n      onClick: _ref2 => {\n        let {\n          currentTarget\n        } = _ref2;\n        return currentTarget.focus({\n          preventScroll: true\n        });\n      },\n      // Safari\n      ...(focusItemOnHover && {\n        onMouseMove(_ref3) {\n          let {\n            currentTarget\n          } = _ref3;\n          forceSyncFocusRef.current = true;\n          forceScrollIntoViewRef.current = false;\n          syncCurrentTarget(currentTarget);\n        },\n        onPointerLeave(_ref4) {\n          let {\n            pointerType\n          } = _ref4;\n          if (!isPointerModalityRef.current || pointerType === 'touch') {\n            return;\n          }\n          forceSyncFocusRef.current = true;\n          indexRef.current = -1;\n          onNavigate();\n          if (!virtual) {\n            var _floatingFocusElement;\n            (_floatingFocusElement = floatingFocusElementRef.current) == null || _floatingFocusElement.focus({\n              preventScroll: true\n            });\n          }\n        }\n      })\n    };\n    return props;\n  }, [open, floatingFocusElementRef, focusItemOnHover, listRef, onNavigate, virtual]);\n  const getParentOrientation = React.useCallback(() => {\n    var _tree$nodesRef$curren;\n    return parentOrientation != null ? parentOrientation : tree == null || (_tree$nodesRef$curren = tree.nodesRef.current.find(node => node.id === parentId)) == null || (_tree$nodesRef$curren = _tree$nodesRef$curren.context) == null || (_tree$nodesRef$curren = _tree$nodesRef$curren.dataRef) == null ? void 0 : _tree$nodesRef$curren.current.orientation;\n  }, [parentId, tree, parentOrientation]);\n  const commonOnKeyDown = useEffectEvent(event => {\n    isPointerModalityRef.current = false;\n    forceSyncFocusRef.current = true;\n\n    // When composing a character, Chrome fires ArrowDown twice. Firefox/Safari\n    // don't appear to suffer from this. `event.isComposing` is avoided due to\n    // Safari not supporting it properly (although it's not needed in the first\n    // place for Safari, just avoiding any possible issues).\n    if (event.which === 229) {\n      return;\n    }\n\n    // If the floating element is animating out, ignore navigation. Otherwise,\n    // the `activeIndex` gets set to 0 despite not being open so the next time\n    // the user ArrowDowns, the first item won't be focused.\n    if (!latestOpenRef.current && event.currentTarget === floatingFocusElementRef.current) {\n      return;\n    }\n    if (nested && isCrossOrientationCloseKey(event.key, orientation, rtl, cols)) {\n      // If the nested list's close key is also the parent navigation key,\n      // let the parent navigate. Otherwise, stop propagating the event.\n      if (!isMainOrientationKey(event.key, getParentOrientation())) {\n        stopEvent(event);\n      }\n      onOpenChange(false, event.nativeEvent, 'list-navigation');\n      if (isHTMLElement(elements.domReference)) {\n        if (virtual) {\n          tree == null || tree.events.emit('virtualfocus', elements.domReference);\n        } else {\n          elements.domReference.focus();\n        }\n      }\n      return;\n    }\n    const currentIndex = indexRef.current;\n    const minIndex = getMinListIndex(listRef, disabledIndices);\n    const maxIndex = getMaxListIndex(listRef, disabledIndices);\n    if (!typeableComboboxReference) {\n      if (event.key === 'Home') {\n        stopEvent(event);\n        indexRef.current = minIndex;\n        onNavigate();\n      }\n      if (event.key === 'End') {\n        stopEvent(event);\n        indexRef.current = maxIndex;\n        onNavigate();\n      }\n    }\n\n    // Grid navigation.\n    if (cols > 1) {\n      const sizes = itemSizes || Array.from({\n        length: listRef.current.length\n      }, () => ({\n        width: 1,\n        height: 1\n      }));\n      // To calculate movements on the grid, we use hypothetical cell indices\n      // as if every item was 1x1, then convert back to real indices.\n      const cellMap = createGridCellMap(sizes, cols, dense);\n      const minGridIndex = cellMap.findIndex(index => index != null && !isListIndexDisabled(listRef, index, disabledIndices));\n      // last enabled index\n      const maxGridIndex = cellMap.reduce((foundIndex, index, cellIndex) => index != null && !isListIndexDisabled(listRef, index, disabledIndices) ? cellIndex : foundIndex, -1);\n      const index = cellMap[getGridNavigatedIndex({\n        current: cellMap.map(itemIndex => itemIndex != null ? listRef.current[itemIndex] : null)\n      }, {\n        event,\n        orientation,\n        loop,\n        rtl,\n        cols,\n        // treat undefined (empty grid spaces) as disabled indices so we\n        // don't end up in them\n        disabledIndices: getGridCellIndices([...(disabledIndices || listRef.current.map((_, index) => isListIndexDisabled(listRef, index) ? index : undefined)), undefined], cellMap),\n        minIndex: minGridIndex,\n        maxIndex: maxGridIndex,\n        prevIndex: getGridCellIndexOfCorner(indexRef.current > maxIndex ? minIndex : indexRef.current, sizes, cellMap, cols,\n        // use a corner matching the edge closest to the direction\n        // we're moving in so we don't end up in the same item. Prefer\n        // top/left over bottom/right.\n        event.key === ARROW_DOWN ? 'bl' : event.key === (rtl ? ARROW_LEFT : ARROW_RIGHT) ? 'tr' : 'tl'),\n        stopEvent: true\n      })];\n      if (index != null) {\n        indexRef.current = index;\n        onNavigate();\n      }\n      if (orientation === 'both') {\n        return;\n      }\n    }\n    if (isMainOrientationKey(event.key, orientation)) {\n      stopEvent(event);\n\n      // Reset the index if no item is focused.\n      if (open && !virtual && activeElement(event.currentTarget.ownerDocument) === event.currentTarget) {\n        indexRef.current = isMainOrientationToEndKey(event.key, orientation, rtl) ? minIndex : maxIndex;\n        onNavigate();\n        return;\n      }\n      if (isMainOrientationToEndKey(event.key, orientation, rtl)) {\n        if (loop) {\n          indexRef.current = currentIndex >= maxIndex ? allowEscape && currentIndex !== listRef.current.length ? -1 : minIndex : findNonDisabledListIndex(listRef, {\n            startingIndex: currentIndex,\n            disabledIndices\n          });\n        } else {\n          indexRef.current = Math.min(maxIndex, findNonDisabledListIndex(listRef, {\n            startingIndex: currentIndex,\n            disabledIndices\n          }));\n        }\n      } else {\n        if (loop) {\n          indexRef.current = currentIndex <= minIndex ? allowEscape && currentIndex !== -1 ? listRef.current.length : maxIndex : findNonDisabledListIndex(listRef, {\n            startingIndex: currentIndex,\n            decrement: true,\n            disabledIndices\n          });\n        } else {\n          indexRef.current = Math.max(minIndex, findNonDisabledListIndex(listRef, {\n            startingIndex: currentIndex,\n            decrement: true,\n            disabledIndices\n          }));\n        }\n      }\n      if (isIndexOutOfListBounds(listRef, indexRef.current)) {\n        indexRef.current = -1;\n      }\n      onNavigate();\n    }\n  });\n  const ariaActiveDescendantProp = React.useMemo(() => {\n    return virtual && open && hasActiveIndex && {\n      'aria-activedescendant': virtualId || activeId\n    };\n  }, [virtual, open, hasActiveIndex, virtualId, activeId]);\n  const floating = React.useMemo(() => {\n    return {\n      'aria-orientation': orientation === 'both' ? undefined : orientation,\n      ...(!typeableComboboxReference ? ariaActiveDescendantProp : {}),\n      onKeyDown: commonOnKeyDown,\n      onPointerMove() {\n        isPointerModalityRef.current = true;\n      }\n    };\n  }, [ariaActiveDescendantProp, commonOnKeyDown, orientation, typeableComboboxReference]);\n  const reference = React.useMemo(() => {\n    function checkVirtualMouse(event) {\n      if (focusItemOnOpen === 'auto' && isVirtualClick(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    function checkVirtualPointer(event) {\n      // `pointerdown` fires first, reset the state then perform the checks.\n      focusItemOnOpenRef.current = focusItemOnOpen;\n      if (focusItemOnOpen === 'auto' && isVirtualPointerEvent(event.nativeEvent)) {\n        focusItemOnOpenRef.current = true;\n      }\n    }\n    return {\n      ...ariaActiveDescendantProp,\n      onKeyDown(event) {\n        isPointerModalityRef.current = false;\n        const isArrowKey = event.key.startsWith('Arrow');\n        const isHomeOrEndKey = ['Home', 'End'].includes(event.key);\n        const isMoveKey = isArrowKey || isHomeOrEndKey;\n        const isCrossOpenKey = isCrossOrientationOpenKey(event.key, orientation, rtl);\n        const isCrossCloseKey = isCrossOrientationCloseKey(event.key, orientation, rtl, cols);\n        const isParentCrossOpenKey = isCrossOrientationOpenKey(event.key, getParentOrientation(), rtl);\n        const isMainKey = isMainOrientationKey(event.key, orientation);\n        const isNavigationKey = (nested ? isParentCrossOpenKey : isMainKey) || event.key === 'Enter' || event.key.trim() === '';\n        if (virtual && open) {\n          const rootNode = tree == null ? void 0 : tree.nodesRef.current.find(node => node.parentId == null);\n          const deepestNode = tree && rootNode ? getDeepestNode(tree.nodesRef.current, rootNode.id) : null;\n          if (isMoveKey && deepestNode && virtualItemRef) {\n            const eventObject = new KeyboardEvent('keydown', {\n              key: event.key,\n              bubbles: true\n            });\n            if (isCrossOpenKey || isCrossCloseKey) {\n              var _deepestNode$context, _deepestNode$context2;\n              const isCurrentTarget = ((_deepestNode$context = deepestNode.context) == null ? void 0 : _deepestNode$context.elements.domReference) === event.currentTarget;\n              const dispatchItem = isCrossCloseKey && !isCurrentTarget ? (_deepestNode$context2 = deepestNode.context) == null ? void 0 : _deepestNode$context2.elements.domReference : isCrossOpenKey ? listRef.current.find(item => (item == null ? void 0 : item.id) === activeId) : null;\n              if (dispatchItem) {\n                stopEvent(event);\n                dispatchItem.dispatchEvent(eventObject);\n                setVirtualId(undefined);\n              }\n            }\n            if ((isMainKey || isHomeOrEndKey) && deepestNode.context) {\n              if (deepestNode.context.open && deepestNode.parentId && event.currentTarget !== deepestNode.context.elements.domReference) {\n                var _deepestNode$context$;\n                stopEvent(event);\n                (_deepestNode$context$ = deepestNode.context.elements.domReference) == null || _deepestNode$context$.dispatchEvent(eventObject);\n                return;\n              }\n            }\n          }\n          return commonOnKeyDown(event);\n        }\n        // If a floating element should not open on arrow key down, avoid\n        // setting `activeIndex` while it's closed.\n        if (!open && !openOnArrowKeyDown && isArrowKey) {\n          return;\n        }\n        if (isNavigationKey) {\n          const isParentMainKey = isMainOrientationKey(event.key, getParentOrientation());\n          keyRef.current = nested && isParentMainKey ? null : event.key;\n        }\n        if (nested) {\n          if (isParentCrossOpenKey) {\n            stopEvent(event);\n            if (open) {\n              indexRef.current = getMinListIndex(listRef, disabledIndicesRef.current);\n              onNavigate();\n            } else {\n              onOpenChange(true, event.nativeEvent, 'list-navigation');\n            }\n          }\n          return;\n        }\n        if (isMainKey) {\n          if (selectedIndex != null) {\n            indexRef.current = selectedIndex;\n          }\n          stopEvent(event);\n          if (!open && openOnArrowKeyDown) {\n            onOpenChange(true, event.nativeEvent, 'list-navigation');\n          } else {\n            commonOnKeyDown(event);\n          }\n          if (open) {\n            onNavigate();\n          }\n        }\n      },\n      onFocus() {\n        if (open && !virtual) {\n          indexRef.current = -1;\n          onNavigate();\n        }\n      },\n      onPointerDown: checkVirtualPointer,\n      onPointerEnter: checkVirtualPointer,\n      onMouseDown: checkVirtualMouse,\n      onClick: checkVirtualMouse\n    };\n  }, [activeId, ariaActiveDescendantProp, cols, commonOnKeyDown, disabledIndicesRef, focusItemOnOpen, listRef, nested, onNavigate, onOpenChange, open, openOnArrowKeyDown, orientation, getParentOrientation, rtl, selectedIndex, tree, virtual, virtualItemRef]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating,\n    item\n  } : {}, [enabled, reference, floating, item]);\n}\n\nconst componentRoleToAriaRoleMap = /*#__PURE__*/new Map([['select', 'listbox'], ['combobox', 'listbox'], ['label', false]]);\n\n/**\n * Adds base screen reader props to the reference and floating elements for a\n * given floating element `role`.\n * @see https://floating-ui.com/docs/useRole\n */\nfunction useRole(context, props) {\n  var _elements$domReferenc, _componentRoleToAriaR;\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    elements,\n    floatingId: defaultFloatingId\n  } = context;\n  const {\n    enabled = true,\n    role = 'dialog'\n  } = props;\n  const defaultReferenceId = useId();\n  const referenceId = ((_elements$domReferenc = elements.domReference) == null ? void 0 : _elements$domReferenc.id) || defaultReferenceId;\n  const floatingId = React.useMemo(() => {\n    var _getFloatingFocusElem;\n    return ((_getFloatingFocusElem = getFloatingFocusElement(elements.floating)) == null ? void 0 : _getFloatingFocusElem.id) || defaultFloatingId;\n  }, [elements.floating, defaultFloatingId]);\n  const ariaRole = (_componentRoleToAriaR = componentRoleToAriaRoleMap.get(role)) != null ? _componentRoleToAriaR : role;\n  const parentId = useFloatingParentNodeId();\n  const isNested = parentId != null;\n  const reference = React.useMemo(() => {\n    if (ariaRole === 'tooltip' || role === 'label') {\n      return {\n        [\"aria-\" + (role === 'label' ? 'labelledby' : 'describedby')]: open ? floatingId : undefined\n      };\n    }\n    return {\n      'aria-expanded': open ? 'true' : 'false',\n      'aria-haspopup': ariaRole === 'alertdialog' ? 'dialog' : ariaRole,\n      'aria-controls': open ? floatingId : undefined,\n      ...(ariaRole === 'listbox' && {\n        role: 'combobox'\n      }),\n      ...(ariaRole === 'menu' && {\n        id: referenceId\n      }),\n      ...(ariaRole === 'menu' && isNested && {\n        role: 'menuitem'\n      }),\n      ...(role === 'select' && {\n        'aria-autocomplete': 'none'\n      }),\n      ...(role === 'combobox' && {\n        'aria-autocomplete': 'list'\n      })\n    };\n  }, [ariaRole, floatingId, isNested, open, referenceId, role]);\n  const floating = React.useMemo(() => {\n    const floatingProps = {\n      id: floatingId,\n      ...(ariaRole && {\n        role: ariaRole\n      })\n    };\n    if (ariaRole === 'tooltip' || role === 'label') {\n      return floatingProps;\n    }\n    return {\n      ...floatingProps,\n      ...(ariaRole === 'menu' && {\n        'aria-labelledby': referenceId\n      })\n    };\n  }, [ariaRole, floatingId, referenceId, role]);\n  const item = React.useCallback(_ref => {\n    let {\n      active,\n      selected\n    } = _ref;\n    const commonProps = {\n      role: 'option',\n      ...(active && {\n        id: floatingId + \"-fui-option\"\n      })\n    };\n\n    // For `menu`, we are unable to tell if the item is a `menuitemradio`\n    // or `menuitemcheckbox`. For backwards-compatibility reasons, also\n    // avoid defaulting to `menuitem` as it may overwrite custom role props.\n    switch (role) {\n      case 'select':\n        return {\n          ...commonProps,\n          'aria-selected': active && selected\n        };\n      case 'combobox':\n        {\n          return {\n            ...commonProps,\n            'aria-selected': selected\n          };\n        }\n    }\n    return {};\n  }, [floatingId, role]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating,\n    item\n  } : {}, [enabled, reference, floating, item]);\n}\n\n// Converts a JS style key like `backgroundColor` to a CSS transition-property\n// like `background-color`.\nconst camelCaseToKebabCase = str => str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, ($, ofs) => (ofs ? '-' : '') + $.toLowerCase());\nfunction execWithArgsOrReturn(valueOrFn, args) {\n  return typeof valueOrFn === 'function' ? valueOrFn(args) : valueOrFn;\n}\nfunction useDelayUnmount(open, durationMs) {\n  const [isMounted, setIsMounted] = React.useState(open);\n  if (open && !isMounted) {\n    setIsMounted(true);\n  }\n  React.useEffect(() => {\n    if (!open && isMounted) {\n      const timeout = setTimeout(() => setIsMounted(false), durationMs);\n      return () => clearTimeout(timeout);\n    }\n  }, [open, isMounted, durationMs]);\n  return isMounted;\n}\n/**\n * Provides a status string to apply CSS transitions to a floating element,\n * correctly handling placement-aware transitions.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstatus\n */\nfunction useTransitionStatus(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    open,\n    elements: {\n      floating\n    }\n  } = context;\n  const {\n    duration = 250\n  } = props;\n  const isNumberDuration = typeof duration === 'number';\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  const [status, setStatus] = React.useState('unmounted');\n  const isMounted = useDelayUnmount(open, closeDuration);\n  if (!isMounted && status === 'close') {\n    setStatus('unmounted');\n  }\n  useModernLayoutEffect(() => {\n    if (!floating) return;\n    if (open) {\n      setStatus('initial');\n      const frame = requestAnimationFrame(() => {\n        // Ensure it opens before paint. With `FloatingDelayGroup`,\n        // this avoids a flicker when moving between floating elements\n        // to ensure one is always open with no missing frames.\n        ReactDOM.flushSync(() => {\n          setStatus('open');\n        });\n      });\n      return () => {\n        cancelAnimationFrame(frame);\n      };\n    }\n    setStatus('close');\n  }, [open, floating]);\n  return {\n    isMounted,\n    status\n  };\n}\n/**\n * Provides styles to apply CSS transitions to a floating element, correctly\n * handling placement-aware transitions. Wrapper around `useTransitionStatus`.\n * @see https://floating-ui.com/docs/useTransition#usetransitionstyles\n */\nfunction useTransitionStyles(context, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  const {\n    initial: unstable_initial = {\n      opacity: 0\n    },\n    open: unstable_open,\n    close: unstable_close,\n    common: unstable_common,\n    duration = 250\n  } = props;\n  const placement = context.placement;\n  const side = placement.split('-')[0];\n  const fnArgs = React.useMemo(() => ({\n    side,\n    placement\n  }), [side, placement]);\n  const isNumberDuration = typeof duration === 'number';\n  const openDuration = (isNumberDuration ? duration : duration.open) || 0;\n  const closeDuration = (isNumberDuration ? duration : duration.close) || 0;\n  const [styles, setStyles] = React.useState(() => ({\n    ...execWithArgsOrReturn(unstable_common, fnArgs),\n    ...execWithArgsOrReturn(unstable_initial, fnArgs)\n  }));\n  const {\n    isMounted,\n    status\n  } = useTransitionStatus(context, {\n    duration\n  });\n  const initialRef = useLatestRef(unstable_initial);\n  const openRef = useLatestRef(unstable_open);\n  const closeRef = useLatestRef(unstable_close);\n  const commonRef = useLatestRef(unstable_common);\n  useModernLayoutEffect(() => {\n    const initialStyles = execWithArgsOrReturn(initialRef.current, fnArgs);\n    const closeStyles = execWithArgsOrReturn(closeRef.current, fnArgs);\n    const commonStyles = execWithArgsOrReturn(commonRef.current, fnArgs);\n    const openStyles = execWithArgsOrReturn(openRef.current, fnArgs) || Object.keys(initialStyles).reduce((acc, key) => {\n      acc[key] = '';\n      return acc;\n    }, {});\n    if (status === 'initial') {\n      setStyles(styles => ({\n        transitionProperty: styles.transitionProperty,\n        ...commonStyles,\n        ...initialStyles\n      }));\n    }\n    if (status === 'open') {\n      setStyles({\n        transitionProperty: Object.keys(openStyles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: openDuration + \"ms\",\n        ...commonStyles,\n        ...openStyles\n      });\n    }\n    if (status === 'close') {\n      const styles = closeStyles || initialStyles;\n      setStyles({\n        transitionProperty: Object.keys(styles).map(camelCaseToKebabCase).join(','),\n        transitionDuration: closeDuration + \"ms\",\n        ...commonStyles,\n        ...styles\n      });\n    }\n  }, [closeDuration, closeRef, initialRef, openRef, commonRef, openDuration, status, fnArgs]);\n  return {\n    isMounted,\n    styles\n  };\n}\n\n/**\n * Provides a matching callback that can be used to focus an item as the user\n * types, often used in tandem with `useListNavigation()`.\n * @see https://floating-ui.com/docs/useTypeahead\n */\nfunction useTypeahead(context, props) {\n  var _ref;\n  const {\n    open,\n    dataRef\n  } = context;\n  const {\n    listRef,\n    activeIndex,\n    onMatch: unstable_onMatch,\n    onTypingChange: unstable_onTypingChange,\n    enabled = true,\n    findMatch = null,\n    resetMs = 750,\n    ignoreKeys = [],\n    selectedIndex = null\n  } = props;\n  const timeoutIdRef = React.useRef(-1);\n  const stringRef = React.useRef('');\n  const prevIndexRef = React.useRef((_ref = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref : -1);\n  const matchIndexRef = React.useRef(null);\n  const onMatch = useEffectEvent(unstable_onMatch);\n  const onTypingChange = useEffectEvent(unstable_onTypingChange);\n  const findMatchRef = useLatestRef(findMatch);\n  const ignoreKeysRef = useLatestRef(ignoreKeys);\n  useModernLayoutEffect(() => {\n    if (open) {\n      clearTimeoutIfSet(timeoutIdRef);\n      matchIndexRef.current = null;\n      stringRef.current = '';\n    }\n  }, [open]);\n  useModernLayoutEffect(() => {\n    // Sync arrow key navigation but not typeahead navigation.\n    if (open && stringRef.current === '') {\n      var _ref2;\n      prevIndexRef.current = (_ref2 = selectedIndex != null ? selectedIndex : activeIndex) != null ? _ref2 : -1;\n    }\n  }, [open, selectedIndex, activeIndex]);\n  const setTypingChange = useEffectEvent(value => {\n    if (value) {\n      if (!dataRef.current.typing) {\n        dataRef.current.typing = value;\n        onTypingChange(value);\n      }\n    } else {\n      if (dataRef.current.typing) {\n        dataRef.current.typing = value;\n        onTypingChange(value);\n      }\n    }\n  });\n  const onKeyDown = useEffectEvent(event => {\n    function getMatchingIndex(list, orderedList, string) {\n      const str = findMatchRef.current ? findMatchRef.current(orderedList, string) : orderedList.find(text => (text == null ? void 0 : text.toLocaleLowerCase().indexOf(string.toLocaleLowerCase())) === 0);\n      return str ? list.indexOf(str) : -1;\n    }\n    const listContent = listRef.current;\n    if (stringRef.current.length > 0 && stringRef.current[0] !== ' ') {\n      if (getMatchingIndex(listContent, listContent, stringRef.current) === -1) {\n        setTypingChange(false);\n      } else if (event.key === ' ') {\n        stopEvent(event);\n      }\n    }\n    if (listContent == null || ignoreKeysRef.current.includes(event.key) ||\n    // Character key.\n    event.key.length !== 1 ||\n    // Modifier key.\n    event.ctrlKey || event.metaKey || event.altKey) {\n      return;\n    }\n    if (open && event.key !== ' ') {\n      stopEvent(event);\n      setTypingChange(true);\n    }\n\n    // Bail out if the list contains a word like \"llama\" or \"aaron\". TODO:\n    // allow it in this case, too.\n    const allowRapidSuccessionOfFirstLetter = listContent.every(text => {\n      var _text$, _text$2;\n      return text ? ((_text$ = text[0]) == null ? void 0 : _text$.toLocaleLowerCase()) !== ((_text$2 = text[1]) == null ? void 0 : _text$2.toLocaleLowerCase()) : true;\n    });\n\n    // Allows the user to cycle through items that start with the same letter\n    // in rapid succession.\n    if (allowRapidSuccessionOfFirstLetter && stringRef.current === event.key) {\n      stringRef.current = '';\n      prevIndexRef.current = matchIndexRef.current;\n    }\n    stringRef.current += event.key;\n    clearTimeoutIfSet(timeoutIdRef);\n    timeoutIdRef.current = window.setTimeout(() => {\n      stringRef.current = '';\n      prevIndexRef.current = matchIndexRef.current;\n      setTypingChange(false);\n    }, resetMs);\n    const prevIndex = prevIndexRef.current;\n    const index = getMatchingIndex(listContent, [...listContent.slice((prevIndex || 0) + 1), ...listContent.slice(0, (prevIndex || 0) + 1)], stringRef.current);\n    if (index !== -1) {\n      onMatch(index);\n      matchIndexRef.current = index;\n    } else if (event.key !== ' ') {\n      stringRef.current = '';\n      setTypingChange(false);\n    }\n  });\n  const reference = React.useMemo(() => ({\n    onKeyDown\n  }), [onKeyDown]);\n  const floating = React.useMemo(() => {\n    return {\n      onKeyDown,\n      onKeyUp(event) {\n        if (event.key === ' ') {\n          setTypingChange(false);\n        }\n      }\n    };\n  }, [onKeyDown, setTypingChange]);\n  return React.useMemo(() => enabled ? {\n    reference,\n    floating\n  } : {}, [enabled, reference, floating]);\n}\n\nfunction getArgsWithCustomFloatingHeight(state, height) {\n  return {\n    ...state,\n    rects: {\n      ...state.rects,\n      floating: {\n        ...state.rects.floating,\n        height\n      }\n    }\n  };\n}\n/**\n * Positions the floating element such that an inner element inside of it is\n * anchored to the reference element.\n * @see https://floating-ui.com/docs/inner\n * @deprecated\n */\nconst inner = props => ({\n  name: 'inner',\n  options: props,\n  async fn(state) {\n    const {\n      listRef,\n      overflowRef,\n      onFallbackChange,\n      offset: innerOffset = 0,\n      index = 0,\n      minItemsVisible = 4,\n      referenceOverflowThreshold = 0,\n      scrollRef,\n      ...detectOverflowOptions\n    } = evaluate(props, state);\n    const {\n      rects,\n      elements: {\n        floating\n      }\n    } = state;\n    const item = listRef.current[index];\n    const scrollEl = (scrollRef == null ? void 0 : scrollRef.current) || floating;\n\n    // Valid combinations:\n    // 1. Floating element is the scrollRef and has a border (default)\n    // 2. Floating element is not the scrollRef, floating element has a border\n    // 3. Floating element is not the scrollRef, scrollRef has a border\n    // Floating > {...getFloatingProps()} wrapper > scrollRef > items is not\n    // allowed as VoiceOver doesn't work.\n    const clientTop = floating.clientTop || scrollEl.clientTop;\n    const floatingIsBordered = floating.clientTop !== 0;\n    const scrollElIsBordered = scrollEl.clientTop !== 0;\n    const floatingIsScrollEl = floating === scrollEl;\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!state.placement.startsWith('bottom')) {\n        warn('`placement` side must be \"bottom\" when using the `inner`', 'middleware.');\n      }\n    }\n    if (!item) {\n      return {};\n    }\n    const nextArgs = {\n      ...state,\n      ...(await offset(-item.offsetTop - floating.clientTop - rects.reference.height / 2 - item.offsetHeight / 2 - innerOffset).fn(state))\n    };\n    const overflow = await detectOverflow(getArgsWithCustomFloatingHeight(nextArgs, scrollEl.scrollHeight + clientTop + floating.clientTop), detectOverflowOptions);\n    const refOverflow = await detectOverflow(nextArgs, {\n      ...detectOverflowOptions,\n      elementContext: 'reference'\n    });\n    const diffY = max(0, overflow.top);\n    const nextY = nextArgs.y + diffY;\n    const isScrollable = scrollEl.scrollHeight > scrollEl.clientHeight;\n    const rounder = isScrollable ? v => v : round;\n    const maxHeight = rounder(max(0, scrollEl.scrollHeight + (floatingIsBordered && floatingIsScrollEl || scrollElIsBordered ? clientTop * 2 : 0) - diffY - max(0, overflow.bottom)));\n    scrollEl.style.maxHeight = maxHeight + \"px\";\n    scrollEl.scrollTop = diffY;\n\n    // There is not enough space, fallback to standard anchored positioning\n    if (onFallbackChange) {\n      const shouldFallback = scrollEl.offsetHeight < item.offsetHeight * min(minItemsVisible, listRef.current.length) - 1 || refOverflow.top >= -referenceOverflowThreshold || refOverflow.bottom >= -referenceOverflowThreshold;\n      ReactDOM.flushSync(() => onFallbackChange(shouldFallback));\n    }\n    if (overflowRef) {\n      overflowRef.current = await detectOverflow(getArgsWithCustomFloatingHeight({\n        ...nextArgs,\n        y: nextY\n      }, scrollEl.offsetHeight + clientTop + floating.clientTop), detectOverflowOptions);\n    }\n    return {\n      y: nextY\n    };\n  }\n});\n/**\n * Changes the `inner` middleware's `offset` upon a `wheel` event to\n * expand the floating element's height, revealing more list items.\n * @see https://floating-ui.com/docs/inner\n * @deprecated\n */\nfunction useInnerOffset(context, props) {\n  const {\n    open,\n    elements\n  } = context;\n  const {\n    enabled = true,\n    overflowRef,\n    scrollRef,\n    onChange: unstable_onChange\n  } = props;\n  const onChange = useEffectEvent(unstable_onChange);\n  const controlledScrollingRef = React.useRef(false);\n  const prevScrollTopRef = React.useRef(null);\n  const initialOverflowRef = React.useRef(null);\n  React.useEffect(() => {\n    if (!enabled) return;\n    function onWheel(e) {\n      if (e.ctrlKey || !el || overflowRef.current == null) {\n        return;\n      }\n      const dY = e.deltaY;\n      const isAtTop = overflowRef.current.top >= -0.5;\n      const isAtBottom = overflowRef.current.bottom >= -0.5;\n      const remainingScroll = el.scrollHeight - el.clientHeight;\n      const sign = dY < 0 ? -1 : 1;\n      const method = dY < 0 ? 'max' : 'min';\n      if (el.scrollHeight <= el.clientHeight) {\n        return;\n      }\n      if (!isAtTop && dY > 0 || !isAtBottom && dY < 0) {\n        e.preventDefault();\n        ReactDOM.flushSync(() => {\n          onChange(d => d + Math[method](dY, remainingScroll * sign));\n        });\n      } else if (/firefox/i.test(getUserAgent())) {\n        // Needed to propagate scrolling during momentum scrolling phase once\n        // it gets limited by the boundary. UX improvement, not critical.\n        el.scrollTop += dY;\n      }\n    }\n    const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n    if (open && el) {\n      el.addEventListener('wheel', onWheel);\n\n      // Wait for the position to be ready.\n      requestAnimationFrame(() => {\n        prevScrollTopRef.current = el.scrollTop;\n        if (overflowRef.current != null) {\n          initialOverflowRef.current = {\n            ...overflowRef.current\n          };\n        }\n      });\n      return () => {\n        prevScrollTopRef.current = null;\n        initialOverflowRef.current = null;\n        el.removeEventListener('wheel', onWheel);\n      };\n    }\n  }, [enabled, open, elements.floating, overflowRef, scrollRef, onChange]);\n  const floating = React.useMemo(() => ({\n    onKeyDown() {\n      controlledScrollingRef.current = true;\n    },\n    onWheel() {\n      controlledScrollingRef.current = false;\n    },\n    onPointerMove() {\n      controlledScrollingRef.current = false;\n    },\n    onScroll() {\n      const el = (scrollRef == null ? void 0 : scrollRef.current) || elements.floating;\n      if (!overflowRef.current || !el || !controlledScrollingRef.current) {\n        return;\n      }\n      if (prevScrollTopRef.current !== null) {\n        const scrollDiff = el.scrollTop - prevScrollTopRef.current;\n        if (overflowRef.current.bottom < -0.5 && scrollDiff < -1 || overflowRef.current.top < -0.5 && scrollDiff > 1) {\n          ReactDOM.flushSync(() => onChange(d => d + scrollDiff));\n        }\n      }\n\n      // [Firefox] Wait for the height change to have been applied.\n      requestAnimationFrame(() => {\n        prevScrollTopRef.current = el.scrollTop;\n      });\n    }\n  }), [elements.floating, onChange, overflowRef, scrollRef]);\n  return React.useMemo(() => enabled ? {\n    floating\n  } : {}, [enabled, floating]);\n}\n\nfunction getNodeChildren(nodes, id, onlyOpenChildren) {\n  if (onlyOpenChildren === void 0) {\n    onlyOpenChildren = true;\n  }\n  let allChildren = nodes.filter(node => {\n    var _node$context;\n    return node.parentId === id && ((_node$context = node.context) == null ? void 0 : _node$context.open);\n  });\n  let currentChildren = allChildren;\n  while (currentChildren.length) {\n    currentChildren = onlyOpenChildren ? nodes.filter(node => {\n      var _currentChildren;\n      return (_currentChildren = currentChildren) == null ? void 0 : _currentChildren.some(n => {\n        var _node$context2;\n        return node.parentId === n.id && ((_node$context2 = node.context) == null ? void 0 : _node$context2.open);\n      });\n    }) : nodes;\n    allChildren = allChildren.concat(currentChildren);\n  }\n  return allChildren;\n}\n\nfunction isPointInPolygon(point, polygon) {\n  const [x, y] = point;\n  let isInside = false;\n  const length = polygon.length;\n  for (let i = 0, j = length - 1; i < length; j = i++) {\n    const [xi, yi] = polygon[i] || [0, 0];\n    const [xj, yj] = polygon[j] || [0, 0];\n    const intersect = yi >= y !== yj >= y && x <= (xj - xi) * (y - yi) / (yj - yi) + xi;\n    if (intersect) {\n      isInside = !isInside;\n    }\n  }\n  return isInside;\n}\nfunction isInside(point, rect) {\n  return point[0] >= rect.x && point[0] <= rect.x + rect.width && point[1] >= rect.y && point[1] <= rect.y + rect.height;\n}\n/**\n * Generates a safe polygon area that the user can traverse without closing the\n * floating element once leaving the reference element.\n * @see https://floating-ui.com/docs/useHover#safepolygon\n */\nfunction safePolygon(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    buffer = 0.5,\n    blockPointerEvents = false,\n    requireIntent = true\n  } = options;\n  let timeoutId;\n  let hasLanded = false;\n  let lastX = null;\n  let lastY = null;\n  let lastCursorTime = performance.now();\n  function getCursorSpeed(x, y) {\n    const currentTime = performance.now();\n    const elapsedTime = currentTime - lastCursorTime;\n    if (lastX === null || lastY === null || elapsedTime === 0) {\n      lastX = x;\n      lastY = y;\n      lastCursorTime = currentTime;\n      return null;\n    }\n    const deltaX = x - lastX;\n    const deltaY = y - lastY;\n    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\n    const speed = distance / elapsedTime; // px / ms\n\n    lastX = x;\n    lastY = y;\n    lastCursorTime = currentTime;\n    return speed;\n  }\n  const fn = _ref => {\n    let {\n      x,\n      y,\n      placement,\n      elements,\n      onClose,\n      nodeId,\n      tree\n    } = _ref;\n    return function onMouseMove(event) {\n      function close() {\n        clearTimeout(timeoutId);\n        onClose();\n      }\n      clearTimeout(timeoutId);\n      if (!elements.domReference || !elements.floating || placement == null || x == null || y == null) {\n        return;\n      }\n      const {\n        clientX,\n        clientY\n      } = event;\n      const clientPoint = [clientX, clientY];\n      const target = getTarget(event);\n      const isLeave = event.type === 'mouseleave';\n      const isOverFloatingEl = contains(elements.floating, target);\n      const isOverReferenceEl = contains(elements.domReference, target);\n      const refRect = elements.domReference.getBoundingClientRect();\n      const rect = elements.floating.getBoundingClientRect();\n      const side = placement.split('-')[0];\n      const cursorLeaveFromRight = x > rect.right - rect.width / 2;\n      const cursorLeaveFromBottom = y > rect.bottom - rect.height / 2;\n      const isOverReferenceRect = isInside(clientPoint, refRect);\n      const isFloatingWider = rect.width > refRect.width;\n      const isFloatingTaller = rect.height > refRect.height;\n      const left = (isFloatingWider ? refRect : rect).left;\n      const right = (isFloatingWider ? refRect : rect).right;\n      const top = (isFloatingTaller ? refRect : rect).top;\n      const bottom = (isFloatingTaller ? refRect : rect).bottom;\n      if (isOverFloatingEl) {\n        hasLanded = true;\n        if (!isLeave) {\n          return;\n        }\n      }\n      if (isOverReferenceEl) {\n        hasLanded = false;\n      }\n      if (isOverReferenceEl && !isLeave) {\n        hasLanded = true;\n        return;\n      }\n\n      // Prevent overlapping floating element from being stuck in an open-close\n      // loop: https://github.com/floating-ui/floating-ui/issues/1910\n      if (isLeave && isElement(event.relatedTarget) && contains(elements.floating, event.relatedTarget)) {\n        return;\n      }\n\n      // If any nested child is open, abort.\n      if (tree && getNodeChildren(tree.nodesRef.current, nodeId).some(_ref2 => {\n        let {\n          context\n        } = _ref2;\n        return context == null ? void 0 : context.open;\n      })) {\n        return;\n      }\n\n      // If the pointer is leaving from the opposite side, the \"buffer\" logic\n      // creates a point where the floating element remains open, but should be\n      // ignored.\n      // A constant of 1 handles floating point rounding errors.\n      if (side === 'top' && y >= refRect.bottom - 1 || side === 'bottom' && y <= refRect.top + 1 || side === 'left' && x >= refRect.right - 1 || side === 'right' && x <= refRect.left + 1) {\n        return close();\n      }\n\n      // Ignore when the cursor is within the rectangular trough between the\n      // two elements. Since the triangle is created from the cursor point,\n      // which can start beyond the ref element's edge, traversing back and\n      // forth from the ref to the floating element can cause it to close. This\n      // ensures it always remains open in that case.\n      let rectPoly = [];\n      switch (side) {\n        case 'top':\n          rectPoly = [[left, refRect.top + 1], [left, rect.bottom - 1], [right, rect.bottom - 1], [right, refRect.top + 1]];\n          break;\n        case 'bottom':\n          rectPoly = [[left, rect.top + 1], [left, refRect.bottom - 1], [right, refRect.bottom - 1], [right, rect.top + 1]];\n          break;\n        case 'left':\n          rectPoly = [[rect.right - 1, bottom], [rect.right - 1, top], [refRect.left + 1, top], [refRect.left + 1, bottom]];\n          break;\n        case 'right':\n          rectPoly = [[refRect.right - 1, bottom], [refRect.right - 1, top], [rect.left + 1, top], [rect.left + 1, bottom]];\n          break;\n      }\n      function getPolygon(_ref3) {\n        let [x, y] = _ref3;\n        switch (side) {\n          case 'top':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y + buffer + 1];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.bottom - buffer : isFloatingWider ? rect.bottom - buffer : rect.top], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.bottom - buffer : rect.top : rect.bottom - buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'bottom':\n            {\n              const cursorPointOne = [isFloatingWider ? x + buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const cursorPointTwo = [isFloatingWider ? x - buffer / 2 : cursorLeaveFromRight ? x + buffer * 4 : x - buffer * 4, y - buffer];\n              const commonPoints = [[rect.left, cursorLeaveFromRight ? rect.top + buffer : isFloatingWider ? rect.top + buffer : rect.bottom], [rect.right, cursorLeaveFromRight ? isFloatingWider ? rect.top + buffer : rect.bottom : rect.top + buffer]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n          case 'left':\n            {\n              const cursorPointOne = [x + buffer + 1, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x + buffer + 1, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.right - buffer : isFloatingTaller ? rect.right - buffer : rect.left, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.right - buffer : rect.left : rect.right - buffer, rect.bottom]];\n              return [...commonPoints, cursorPointOne, cursorPointTwo];\n            }\n          case 'right':\n            {\n              const cursorPointOne = [x - buffer, isFloatingTaller ? y + buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const cursorPointTwo = [x - buffer, isFloatingTaller ? y - buffer / 2 : cursorLeaveFromBottom ? y + buffer * 4 : y - buffer * 4];\n              const commonPoints = [[cursorLeaveFromBottom ? rect.left + buffer : isFloatingTaller ? rect.left + buffer : rect.right, rect.top], [cursorLeaveFromBottom ? isFloatingTaller ? rect.left + buffer : rect.right : rect.left + buffer, rect.bottom]];\n              return [cursorPointOne, cursorPointTwo, ...commonPoints];\n            }\n        }\n      }\n      if (isPointInPolygon([clientX, clientY], rectPoly)) {\n        return;\n      }\n      if (hasLanded && !isOverReferenceRect) {\n        return close();\n      }\n      if (!isLeave && requireIntent) {\n        const cursorSpeed = getCursorSpeed(event.clientX, event.clientY);\n        const cursorSpeedThreshold = 0.1;\n        if (cursorSpeed !== null && cursorSpeed < cursorSpeedThreshold) {\n          return close();\n        }\n      }\n      if (!isPointInPolygon([clientX, clientY], getPolygon([x, y]))) {\n        close();\n      } else if (!hasLanded && requireIntent) {\n        timeoutId = window.setTimeout(close, 40);\n      }\n    };\n  };\n  fn.__options = {\n    blockPointerEvents\n  };\n  return fn;\n}\n\nexport { Composite, CompositeItem, FloatingArrow, FloatingDelayGroup, FloatingFocusManager, FloatingList, FloatingNode, FloatingOverlay, FloatingPortal, FloatingTree, NextFloatingDelayGroup, inner, safePolygon, useClick, useClientPoint, useDelayGroup, useDelayGroupContext, useDismiss, useFloating, useFloatingNodeId, useFloatingParentNodeId, useFloatingPortalNode, useFloatingRootContext, useFloatingTree, useFocus, useHover, useId, useInnerOffset, useInteractions, useListItem, useListNavigation, useMergeRefs, useNextDelayGroup, useRole, useTransitionStatus, useTransitionStyles, useTypeahead };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { isShadowRoot, isHTMLElement } from '@floating-ui/utils/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport { floor } from '@floating-ui/utils';\nimport { tabbable } from 'tabbable';\n\n// Avoid Chrome DevTools blue warning.\nfunction getPlatform() {\n  const uaData = navigator.userAgentData;\n  if (uaData != null && uaData.platform) {\n    return uaData.platform;\n  }\n  return navigator.platform;\n}\nfunction getUserAgent() {\n  const uaData = navigator.userAgentData;\n  if (uaData && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(_ref => {\n      let {\n        brand,\n        version\n      } = _ref;\n      return brand + \"/\" + version;\n    }).join(' ');\n  }\n  return navigator.userAgent;\n}\nfunction isSafari() {\n  // Chrome DevTools does not complain about navigator.vendor\n  return /apple/i.test(navigator.vendor);\n}\nfunction isAndroid() {\n  const re = /android/i;\n  return re.test(getPlatform()) || re.test(getUserAgent());\n}\nfunction isMac() {\n  return getPlatform().toLowerCase().startsWith('mac') && !navigator.maxTouchPoints;\n}\nfunction isJSDOM() {\n  return getUserAgent().includes('jsdom/');\n}\n\nconst FOCUSABLE_ATTRIBUTE = 'data-floating-ui-focusable';\nconst TYPEABLE_SELECTOR = \"input:not([type='hidden']):not([disabled]),\" + \"[contenteditable]:not([contenteditable='false']),textarea:not([disabled])\";\nconst ARROW_LEFT = 'ArrowLeft';\nconst ARROW_RIGHT = 'ArrowRight';\nconst ARROW_UP = 'ArrowUp';\nconst ARROW_DOWN = 'ArrowDown';\n\nfunction activeElement(doc) {\n  let activeElement = doc.activeElement;\n  while (((_activeElement = activeElement) == null || (_activeElement = _activeElement.shadowRoot) == null ? void 0 : _activeElement.activeElement) != null) {\n    var _activeElement;\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n  return activeElement;\n}\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode == null ? void 0 : child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n\n  // then fallback to custom implementation with Shadow DOM support\n  if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    while (next) {\n      if (parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    }\n  }\n\n  // Give up, the result is false\n  return false;\n}\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\nfunction isEventTargetWithin(event, node) {\n  if (node == null) {\n    return false;\n  }\n  if ('composedPath' in event) {\n    return event.composedPath().includes(node);\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support composedPath, but browsers without shadow dom don't\n  const e = event;\n  return e.target != null && node.contains(e.target);\n}\nfunction isRootElement(element) {\n  return element.matches('html,body');\n}\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\nfunction isTypeableElement(element) {\n  return isHTMLElement(element) && element.matches(TYPEABLE_SELECTOR);\n}\nfunction isTypeableCombobox(element) {\n  if (!element) return false;\n  return element.getAttribute('role') === 'combobox' && isTypeableElement(element);\n}\nfunction matchesFocusVisible(element) {\n  // We don't want to block focus from working with `visibleOnly`\n  // (JSDOM doesn't match `:focus-visible` when the element has `:focus`)\n  if (!element || isJSDOM()) return true;\n  try {\n    return element.matches(':focus-visible');\n  } catch (_e) {\n    return true;\n  }\n}\nfunction getFloatingFocusElement(floatingElement) {\n  if (!floatingElement) {\n    return null;\n  }\n  // Try to find the element that has `{...getFloatingProps()}` spread on it.\n  // This indicates the floating element is acting as a positioning wrapper, and\n  // so focus should be managed on the child element with the event handlers and\n  // aria props.\n  return floatingElement.hasAttribute(FOCUSABLE_ATTRIBUTE) ? floatingElement : floatingElement.querySelector(\"[\" + FOCUSABLE_ATTRIBUTE + \"]\") || floatingElement;\n}\n\nfunction getNodeChildren(nodes, id, onlyOpenChildren) {\n  if (onlyOpenChildren === void 0) {\n    onlyOpenChildren = true;\n  }\n  let allChildren = nodes.filter(node => {\n    var _node$context;\n    return node.parentId === id && ((_node$context = node.context) == null ? void 0 : _node$context.open);\n  });\n  let currentChildren = allChildren;\n  while (currentChildren.length) {\n    currentChildren = onlyOpenChildren ? nodes.filter(node => {\n      var _currentChildren;\n      return (_currentChildren = currentChildren) == null ? void 0 : _currentChildren.some(n => {\n        var _node$context2;\n        return node.parentId === n.id && ((_node$context2 = node.context) == null ? void 0 : _node$context2.open);\n      });\n    }) : nodes;\n    allChildren = allChildren.concat(currentChildren);\n  }\n  return allChildren;\n}\nfunction getDeepestNode(nodes, id) {\n  let deepestNodeId;\n  let maxDepth = -1;\n  function findDeepest(nodeId, depth) {\n    if (depth > maxDepth) {\n      deepestNodeId = nodeId;\n      maxDepth = depth;\n    }\n    const children = getNodeChildren(nodes, nodeId);\n    children.forEach(child => {\n      findDeepest(child.id, depth + 1);\n    });\n  }\n  findDeepest(id, 0);\n  return nodes.find(node => node.id === deepestNodeId);\n}\nfunction getNodeAncestors(nodes, id) {\n  var _nodes$find;\n  let allAncestors = [];\n  let currentParentId = (_nodes$find = nodes.find(node => node.id === id)) == null ? void 0 : _nodes$find.parentId;\n  while (currentParentId) {\n    const currentNode = nodes.find(node => node.id === currentParentId);\n    currentParentId = currentNode == null ? void 0 : currentNode.parentId;\n    if (currentNode) {\n      allAncestors = allAncestors.concat(currentNode);\n    }\n  }\n  return allAncestors;\n}\n\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopPropagation();\n}\nfunction isReactEvent(event) {\n  return 'nativeEvent' in event;\n}\n\n// License: https://github.com/adobe/react-spectrum/blob/b35d5c02fe900badccd0cf1a8f23bb593419f238/packages/@react-aria/utils/src/isVirtualEvent.ts\nfunction isVirtualClick(event) {\n  // FIXME: Firefox is now emitting a deprecation warning for `mozInputSource`.\n  // Try to find a workaround for this. `react-aria` source still has the check.\n  if (event.mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n  if (isAndroid() && event.pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n  return event.detail === 0 && !event.pointerType;\n}\nfunction isVirtualPointerEvent(event) {\n  if (isJSDOM()) return false;\n  return !isAndroid() && event.width === 0 && event.height === 0 || isAndroid() && event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'mouse' ||\n  // iOS VoiceOver returns 0.333• for width/height.\n  event.width < 1 && event.height < 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'touch';\n}\nfunction isMouseLikePointerType(pointerType, strict) {\n  // On some Linux machines with Chromium, mouse inputs return a `pointerType`\n  // of \"pen\": https://github.com/floating-ui/floating-ui/issues/2015\n  const values = ['mouse', 'pen'];\n  if (!strict) {\n    values.push('', undefined);\n  }\n  return values.includes(pointerType);\n}\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379\nconst SafeReact = {\n  ...React\n};\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\nconst useInsertionEffect = SafeReact.useInsertionEffect;\nconst useSafeInsertionEffect = useInsertionEffect || (fn => fn());\nfunction useEffectEvent(callback) {\n  const ref = React.useRef(() => {\n    if (process.env.NODE_ENV !== \"production\") {\n      throw new Error('Cannot call an event handler while rendering.');\n    }\n  });\n  useSafeInsertionEffect(() => {\n    ref.current = callback;\n  });\n  return React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current == null ? void 0 : ref.current(...args);\n  }, []);\n}\n\nfunction isDifferentGridRow(index, cols, prevRow) {\n  return Math.floor(index / cols) !== prevRow;\n}\nfunction isIndexOutOfListBounds(listRef, index) {\n  return index < 0 || index >= listRef.current.length;\n}\nfunction getMinListIndex(listRef, disabledIndices) {\n  return findNonDisabledListIndex(listRef, {\n    disabledIndices\n  });\n}\nfunction getMaxListIndex(listRef, disabledIndices) {\n  return findNonDisabledListIndex(listRef, {\n    decrement: true,\n    startingIndex: listRef.current.length,\n    disabledIndices\n  });\n}\nfunction findNonDisabledListIndex(listRef, _temp) {\n  let {\n    startingIndex = -1,\n    decrement = false,\n    disabledIndices,\n    amount = 1\n  } = _temp === void 0 ? {} : _temp;\n  let index = startingIndex;\n  do {\n    index += decrement ? -amount : amount;\n  } while (index >= 0 && index <= listRef.current.length - 1 && isListIndexDisabled(listRef, index, disabledIndices));\n  return index;\n}\nfunction getGridNavigatedIndex(listRef, _ref) {\n  let {\n    event,\n    orientation,\n    loop,\n    rtl,\n    cols,\n    disabledIndices,\n    minIndex,\n    maxIndex,\n    prevIndex,\n    stopEvent: stop = false\n  } = _ref;\n  let nextIndex = prevIndex;\n  if (event.key === ARROW_UP) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = maxIndex;\n    } else {\n      nextIndex = findNonDisabledListIndex(listRef, {\n        startingIndex: nextIndex,\n        amount: cols,\n        decrement: true,\n        disabledIndices\n      });\n      if (loop && (prevIndex - cols < minIndex || nextIndex < 0)) {\n        const col = prevIndex % cols;\n        const maxCol = maxIndex % cols;\n        const offset = maxIndex - (maxCol - col);\n        if (maxCol === col) {\n          nextIndex = maxIndex;\n        } else {\n          nextIndex = maxCol > col ? offset : offset - cols;\n        }\n      }\n    }\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n  if (event.key === ARROW_DOWN) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = minIndex;\n    } else {\n      nextIndex = findNonDisabledListIndex(listRef, {\n        startingIndex: prevIndex,\n        amount: cols,\n        disabledIndices\n      });\n      if (loop && prevIndex + cols > maxIndex) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex % cols - cols,\n          amount: cols,\n          disabledIndices\n        });\n      }\n    }\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n\n  // Remains on the same row/column.\n  if (orientation === 'both') {\n    const prevRow = floor(prevIndex / cols);\n    if (event.key === (rtl ? ARROW_LEFT : ARROW_RIGHT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== cols - 1) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex,\n          disabledIndices\n        });\n        if (loop && isDifferentGridRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledListIndex(listRef, {\n            startingIndex: prevIndex - prevIndex % cols - 1,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      }\n      if (isDifferentGridRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    if (event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== 0) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex,\n          decrement: true,\n          disabledIndices\n        });\n        if (loop && isDifferentGridRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledListIndex(listRef, {\n            startingIndex: prevIndex + (cols - prevIndex % cols),\n            decrement: true,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex + (cols - prevIndex % cols),\n          decrement: true,\n          disabledIndices\n        });\n      }\n      if (isDifferentGridRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    const lastRow = floor(maxIndex / cols) === prevRow;\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      if (loop && lastRow) {\n        nextIndex = event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT) ? maxIndex : findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      } else {\n        nextIndex = prevIndex;\n      }\n    }\n  }\n  return nextIndex;\n}\n\n/** For each cell index, gets the item index that occupies that cell */\nfunction createGridCellMap(sizes, cols, dense) {\n  const cellMap = [];\n  let startIndex = 0;\n  sizes.forEach((_ref2, index) => {\n    let {\n      width,\n      height\n    } = _ref2;\n    if (width > cols) {\n      if (process.env.NODE_ENV !== \"production\") {\n        throw new Error(\"[Floating UI]: Invalid grid - item width at index \" + index + \" is greater than grid columns\");\n      }\n    }\n    let itemPlaced = false;\n    if (dense) {\n      startIndex = 0;\n    }\n    while (!itemPlaced) {\n      const targetCells = [];\n      for (let i = 0; i < width; i++) {\n        for (let j = 0; j < height; j++) {\n          targetCells.push(startIndex + i + j * cols);\n        }\n      }\n      if (startIndex % cols + width <= cols && targetCells.every(cell => cellMap[cell] == null)) {\n        targetCells.forEach(cell => {\n          cellMap[cell] = index;\n        });\n        itemPlaced = true;\n      } else {\n        startIndex++;\n      }\n    }\n  });\n\n  // convert into a non-sparse array\n  return [...cellMap];\n}\n\n/** Gets cell index of an item's corner or -1 when index is -1. */\nfunction getGridCellIndexOfCorner(index, sizes, cellMap, cols, corner) {\n  if (index === -1) return -1;\n  const firstCellIndex = cellMap.indexOf(index);\n  const sizeItem = sizes[index];\n  switch (corner) {\n    case 'tl':\n      return firstCellIndex;\n    case 'tr':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + sizeItem.width - 1;\n    case 'bl':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + (sizeItem.height - 1) * cols;\n    case 'br':\n      return cellMap.lastIndexOf(index);\n  }\n}\n\n/** Gets all cell indices that correspond to the specified indices */\nfunction getGridCellIndices(indices, cellMap) {\n  return cellMap.flatMap((index, cellIndex) => indices.includes(index) ? [cellIndex] : []);\n}\nfunction isListIndexDisabled(listRef, index, disabledIndices) {\n  if (disabledIndices) {\n    return disabledIndices.includes(index);\n  }\n  const element = listRef.current[index];\n  return element == null || element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true';\n}\n\nconst getTabbableOptions = () => ({\n  getShadowRoot: true,\n  displayCheck:\n  // JSDOM does not support the `tabbable` library. To solve this we can\n  // check if `ResizeObserver` is a real function (not polyfilled), which\n  // determines if the current environment is JSDOM-like.\n  typeof ResizeObserver === 'function' && ResizeObserver.toString().includes('[native code]') ? 'full' : 'none'\n});\nfunction getTabbableIn(container, dir) {\n  const list = tabbable(container, getTabbableOptions());\n  const len = list.length;\n  if (len === 0) return;\n  const active = activeElement(getDocument(container));\n  const index = list.indexOf(active);\n  const nextIndex = index === -1 ? dir === 1 ? 0 : len - 1 : index + dir;\n  return list[nextIndex];\n}\nfunction getNextTabbable(referenceElement) {\n  return getTabbableIn(getDocument(referenceElement).body, 1) || referenceElement;\n}\nfunction getPreviousTabbable(referenceElement) {\n  return getTabbableIn(getDocument(referenceElement).body, -1) || referenceElement;\n}\nfunction isOutsideEvent(event, container) {\n  const containerElement = container || event.currentTarget;\n  const relatedTarget = event.relatedTarget;\n  return !relatedTarget || !contains(containerElement, relatedTarget);\n}\nfunction disableFocusInside(container) {\n  const tabbableElements = tabbable(container, getTabbableOptions());\n  tabbableElements.forEach(element => {\n    element.dataset.tabindex = element.getAttribute('tabindex') || '';\n    element.setAttribute('tabindex', '-1');\n  });\n}\nfunction enableFocusInside(container) {\n  const elements = container.querySelectorAll('[data-tabindex]');\n  elements.forEach(element => {\n    const tabindex = element.dataset.tabindex;\n    delete element.dataset.tabindex;\n    if (tabindex) {\n      element.setAttribute('tabindex', tabindex);\n    } else {\n      element.removeAttribute('tabindex');\n    }\n  });\n}\n\nexport { activeElement, contains, createGridCellMap, disableFocusInside, enableFocusInside, findNonDisabledListIndex, getDeepestNode, getDocument, getFloatingFocusElement, getGridCellIndexOfCorner, getGridCellIndices, getGridNavigatedIndex, getMaxListIndex, getMinListIndex, getNextTabbable, getNodeAncestors, getNodeChildren, getPlatform, getPreviousTabbable, getTabbableOptions, getTarget, getUserAgent, isAndroid, isDifferentGridRow, isEventTargetWithin, isIndexOutOfListBounds, isJSDOM, isListIndexDisabled, isMac, isMouseLikePointerType, isOutsideEvent, isReactEvent, isRootElement, isSafari, isTypeableCombobox, isTypeableElement, isVirtualClick, isVirtualPointerEvent, matchesFocusVisible, stopEvent, useEffectEvent, useLatestRef, index as useModernLayoutEffect };\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nconst candidateSelectors = [\n  'input:not([inert])',\n  'select:not([inert])',\n  'textarea:not([inert])',\n  'a[href]:not([inert])',\n  'button:not([inert])',\n  '[tabindex]:not(slot):not([inert])',\n  'audio[controls]:not([inert])',\n  'video[controls]:not([inert])',\n  '[contenteditable]:not([contenteditable=\"false\"]):not([inert])',\n  'details>summary:first-of-type:not([inert])',\n  'details:not([inert])',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element?.getRootNode?.()\n    : (element) => element?.ownerDocument;\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nconst isInert = function (node, lookUp = true) {\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  const inertAtt = node?.getAttribute?.('inert');\n  const inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  const result = inert || (lookUp && node && isInert(node.parentNode)); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nconst isContentEditable = function (node) {\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  const attValue = node?.getAttribute?.('contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      const validShadowRoot =\n        !isInert(shadowRoot, false) &&\n        (!options.shadowRootFilter || options.shadowRootFilter(element));\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nconst hasTabIndex = function (node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nconst getTabIndex = function (node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if (\n      (/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        isContentEditable(node)) &&\n      !hasTabIndex(node)\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nconst getSortOrderTabIndex = function (node, isScope) {\n  const tabIndex = getTabIndex(node);\n\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n\n  return tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nconst isNodeAttached = function (node) {\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  let nodeRoot = node && getRootNode(node);\n  let nodeRootHost = nodeRoot?.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  let attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    attached = !!(\n      nodeRootHost?.ownerDocument?.contains(nodeRootHost) ||\n      node?.ownerDocument?.contains(node)\n    );\n\n    while (!attached && nodeRootHost) {\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = nodeRoot?.host;\n      attached = !!nodeRootHost?.ownerDocument?.contains(nodeRootHost);\n    }\n  }\n\n  return attached;\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  if (\n    !displayCheck ||\n    displayCheck === 'full' ||\n    displayCheck === 'legacy-full'\n  ) {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    // we must do an inert look up to filter out any elements inside an inert ancestor\n    //  because we're limited in the type of selectors we can use in JSDom (see related\n    //  note related to `candidateSelectors`)\n    isInert(node) ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabIndex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scopeParent;\n    const element = isScope ? item.scopeParent : item;\n    const candidateTabindex = getSortOrderTabIndex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorTabbable.bind(null, options),\n        flatten: false,\n        getShadowRoot: options.getShadowRoot,\n        shadowRootFilter: isValidShadowRootTabbable,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (container, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively(\n      [container],\n      options.includeContainer,\n      {\n        filter: isNodeMatchingSelectorFocusable.bind(null, options),\n        flatten: true,\n        getShadowRoot: options.getShadowRoot,\n      }\n    );\n  } else {\n    candidates = getCandidates(\n      container,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable, getTabIndex };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          // Try next placement and re-run the lifecycle.\n          return {\n            data: {\n              index: nextIndex,\n              overflows: overflowsData\n            },\n            reset: {\n              placement: nextPlacement\n            }\n          };\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "import { rectToClientRect, detectOverflow as detectOverflow$1, offset as offset$1, autoPlacement as autoPlacement$1, shift as shift$1, flip as flip$1, size as size$1, hide as hide$1, arrow as arrow$1, inline as inline$1, limitShift as limitShift$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n      // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n", "import { computePosition, arrow as arrow$2, offset as offset$1, shift as shift$1, limitShift as limitShift$1, flip as flip$1, size as size$1, autoPlacement as autoPlacement$1, hide as hide$1, inline as inline$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\r\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\r\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nvar ownKeys = function(o) {\r\n    ownKeys = Object.getOwnPropertyNames || function (o) {\r\n        var ar = [];\r\n        for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\r\n        return ar;\r\n    };\r\n    return ownKeys(o);\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose, inner;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n            if (async) inner = dispose;\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    var r, s = 0;\r\n    function next() {\r\n        while (r = env.stack.pop()) {\r\n            try {\r\n                if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\r\n                if (r.dispose) {\r\n                    var result = r.dispose.call(r.value);\r\n                    if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n                }\r\n                else s |= 1;\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\r\n    if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\r\n        return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\r\n            return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\r\n        });\r\n    }\r\n    return path;\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __esDecorate: __esDecorate,\r\n    __runInitializers: __runInitializers,\r\n    __propKey: __propKey,\r\n    __setFunctionName: __setFunctionName,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n    __rewriteRelativeImportExtension: __rewriteRelativeImportExtension,\r\n};\r\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAuB;;;ACAvB,SAAS,YAAY;AACnB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,MAAM;AACzB,MAAI,OAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,SAAS,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AAC5H;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQ,OAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAAS,OAAO,OAAO;AACrB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,QAAQ,iBAAiB,UAAU,KAAK,EAAE;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,WAAW,iBAAiB,UAAU,KAAK,EAAE;AACvE;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,eAAe,iBAAiB,UAAU,KAAK,EAAE;AAC3E;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,UAAU,KAAK,OAAO,eAAe,aAAa;AACrD,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiB,UAAU,KAAK,EAAE;AAC1E;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAIC,kBAAiB,OAAO;AAC5B,SAAO,kCAAkC,KAAK,WAAW,YAAY,SAAS,KAAK,CAAC,CAAC,UAAU,UAAU,EAAE,SAAS,OAAO;AAC7H;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,SAAS,YAAY,OAAO,CAAC;AAC5D;AACA,SAAS,WAAW,SAAS;AAC3B,SAAO,CAAC,iBAAiB,QAAQ,EAAE,KAAK,cAAY;AAClD,QAAI;AACF,aAAO,QAAQ,QAAQ,QAAQ;AAAA,IACjC,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,SAAS,kBAAkB,cAAc;AACvC,QAAM,SAAS,SAAS;AACxB,QAAM,MAAM,UAAU,YAAY,IAAIA,kBAAiB,YAAY,IAAI;AAIvE,SAAO,CAAC,aAAa,aAAa,SAAS,UAAU,aAAa,EAAE,KAAK,WAAS,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,SAAS,KAAK,MAAM,IAAI,gBAAgB,IAAI,kBAAkB,WAAW,UAAU,CAAC,WAAW,IAAI,iBAAiB,IAAI,mBAAmB,SAAS,UAAU,CAAC,WAAW,IAAI,SAAS,IAAI,WAAW,SAAS,UAAU,CAAC,aAAa,aAAa,SAAS,UAAU,eAAe,QAAQ,EAAE,KAAK,YAAU,IAAI,cAAc,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,SAAS,UAAU,UAAU,SAAS,EAAE,KAAK,YAAU,IAAI,WAAW,IAAI,SAAS,KAAK,CAAC;AACniB;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,cAAc,cAAc,OAAO;AACvC,SAAO,cAAc,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACxE,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO;AAAA,IACT,WAAW,WAAW,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,MAAI,OAAO,QAAQ,eAAe,CAAC,IAAI,SAAU,QAAO;AACxD,SAAO,IAAI,SAAS,2BAA2B,MAAM;AACvD;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAS,YAAY,IAAI,CAAC;AACjE;AACA,SAASA,kBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,UAAU,OAAO,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,YAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAEL,aAAa,IAAI,KAAK,KAAK;AAAA,IAE3B,mBAAmB,IAAI;AAAA;AACvB,SAAO,aAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;AACA,SAAS,2BAA2B,MAAM;AACxC,QAAM,aAAa,cAAc,IAAI;AACrC,MAAI,sBAAsB,UAAU,GAAG;AACrC,WAAO,KAAK,gBAAgB,KAAK,cAAc,OAAO,KAAK;AAAA,EAC7D;AACA,MAAI,cAAc,UAAU,KAAK,kBAAkB,UAAU,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,UAAU;AAC9C;AACA,SAAS,qBAAqB,MAAM,MAAM,iBAAiB;AACzD,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,qBAAqB,2BAA2B,IAAI;AAC1D,QAAM,SAAS,yBAAyB,uBAAuB,KAAK,kBAAkB,OAAO,SAAS,qBAAqB;AAC3H,QAAM,MAAM,UAAU,kBAAkB;AACxC,MAAI,QAAQ;AACV,UAAM,eAAe,gBAAgB,GAAG;AACxC,WAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,GAAG,kBAAkB,kBAAkB,IAAI,qBAAqB,CAAC,GAAG,gBAAgB,kBAAkB,qBAAqB,YAAY,IAAI,CAAC,CAAC;AAAA,EAC9L;AACA,SAAO,KAAK,OAAO,oBAAoB,qBAAqB,oBAAoB,CAAC,GAAG,eAAe,CAAC;AACtG;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,UAAU,OAAO,eAAe,IAAI,MAAM,IAAI,IAAI,eAAe;AAC9E;;;ACtJA,YAAuB;AACvB,mBAA2C;;;ACG3C,IAAM,QAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAM,aAAa,CAAC,SAAS,KAAK;AAClC,IAAM,aAA0B,MAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,WAAW,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AACxI,IAAMC,OAAM,KAAK;AACjB,IAAMC,OAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,eAAe,QAAM;AAAA,EACzB,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,KAAK;AACP;AACA,SAAS,MAAM,OAAO,OAAO,KAAK;AAChC,SAAOA,KAAI,OAAOD,KAAI,OAAO,GAAG,CAAC;AACnC;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,SAAS,MAAM,WAAW;AACnC;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,CAAC,OAAO,QAAQ,EAAE,SAAS,QAAQ,SAAS,CAAC,IAAI,MAAM;AAChE;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,gBAAgB,YAAY,SAAS,CAAC;AAC/C;AACA,SAAS,kBAAkB,WAAW,OAAO,KAAK;AAChD,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,SAAS,cAAc,aAAa;AAC1C,MAAI,oBAAoB,kBAAkB,MAAM,eAAe,MAAM,QAAQ,WAAW,UAAU,SAAS,cAAc,UAAU,WAAW;AAC9I,MAAI,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG;AACpD,wBAAoB,qBAAqB,iBAAiB;AAAA,EAC5D;AACA,SAAO,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACpE;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,oBAAoB,qBAAqB,SAAS;AACxD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,eAAa,qBAAqB,SAAS,CAAC;AACrF;AACA,SAAS,YAAY,MAAM,SAAS,KAAK;AACvC,QAAM,KAAK,CAAC,QAAQ,OAAO;AAC3B,QAAM,KAAK,CAAC,SAAS,MAAM;AAC3B,QAAM,KAAK,CAAC,OAAO,QAAQ;AAC3B,QAAM,KAAK,CAAC,UAAU,KAAK;AAC3B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,UAAI,IAAK,QAAO,UAAU,KAAK;AAC/B,aAAO,UAAU,KAAK;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,KAAK;AAAA,IACxB;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AACA,SAAS,0BAA0B,WAAW,eAAe,WAAW,KAAK;AAC3E,QAAM,YAAY,aAAa,SAAS;AACxC,MAAI,OAAO,YAAY,QAAQ,SAAS,GAAG,cAAc,SAAS,GAAG;AACrE,MAAI,WAAW;AACb,WAAO,KAAK,IAAI,UAAQ,OAAO,MAAM,SAAS;AAC9C,QAAI,eAAe;AACjB,aAAO,KAAK,OAAO,KAAK,IAAI,6BAA6B,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,UAAQ,gBAAgB,IAAI,CAAC;AAClF;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,GAAG;AAAA,EACL;AACF;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,OAAO,YAAY,WAAW,oBAAoB,OAAO,IAAI;AAAA,IAClE,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACF;;;AClIA,IAAME,qBAAqB,CACzB,sBACA,uBACA,yBACA,wBACA,uBACA,qCACA,gCACA,gCACA,iEACA,8CACA,sBAAsB;AAExB,IAAMC,oBAAoCD,mBAAmBE,KAAK,GAAG;AAErE,IAAMC,YAAY,OAAOC,YAAY;AAErC,IAAMC,UAAUF,YACZ,WAAY;AAAA,IACZC,QAAQE,UAAUD,WAClBD,QAAQE,UAAUC,qBAClBH,QAAQE,UAAUE;AAEtB,IAAMC,cACJ,CAACN,aAAaC,QAAQE,UAAUG,cAC5B,SAACC,SAAO;AAAA,MAAAC;AAAA,SAAKD,YAAAA,QAAAA,YAAOC,SAAAA,UAAAA,uBAAPD,QAASD,iBAAW,QAAAE,yBAApBA,SAAAA,SAAAA,qBAAAC,KAAAF,OAAuB;AAAC,IACrC,SAACA,SAAO;AAAA,SAAKA,YAAAA,QAAAA,YAAAA,SAAAA,SAAAA,QAASG;AAAa;AAioBzC,IAAMC,6BAA6CC,mBAChDC,OAAO,QAAQ,EACfC,KAAK,GAAG;;;AF3pBX,SAAS,cAAc;AACrB,QAAM,SAAS,UAAU;AACzB,MAAI,UAAU,QAAQ,OAAO,UAAU;AACrC,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,UAAU;AACnB;AAcA,SAAS,WAAW;AAElB,SAAO,SAAS,KAAK,UAAU,MAAM;AACvC;AAcA,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,WAAW;AACjB,IAAM,aAAa;AA8InB,SAAS,UAAU,OAAO;AACxB,QAAM,eAAe;AACrB,QAAM,gBAAgB;AACxB;AAiCA,IAAI,QAAQ,OAAO,aAAa,cAAc,+BAAkB;AAGhE,IAAM,YAAY;AAAA,EAChB,GAAG;AACL;AASA,IAAM,qBAAqB,UAAU;AACrC,IAAM,yBAAyB,uBAAuB,QAAM,GAAG;AAC/D,SAAS,eAAe,UAAU;AAChC,QAAM,MAAY,aAAO,MAAM;AAC7B,QAAI,MAAuC;AACzC,YAAM,IAAI,MAAM,+CAA+C;AAAA,IACjE;AAAA,EACF,CAAC;AACD,yBAAuB,MAAM;AAC3B,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAa,kBAAY,WAAY;AACnC,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AACA,WAAO,IAAI,WAAW,OAAO,SAAS,IAAI,QAAQ,GAAG,IAAI;AAAA,EAC3D,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,mBAAmBC,QAAO,MAAM,SAAS;AAChD,SAAO,KAAK,MAAMA,SAAQ,IAAI,MAAM;AACtC;AACA,SAAS,uBAAuB,SAASA,QAAO;AAC9C,SAAOA,SAAQ,KAAKA,UAAS,QAAQ,QAAQ;AAC/C;AACA,SAAS,gBAAgB,SAAS,iBAAiB;AACjD,SAAO,yBAAyB,SAAS;AAAA,IACvC;AAAA,EACF,CAAC;AACH;AACA,SAAS,gBAAgB,SAAS,iBAAiB;AACjD,SAAO,yBAAyB,SAAS;AAAA,IACvC,WAAW;AAAA,IACX,eAAe,QAAQ,QAAQ;AAAA,IAC/B;AAAA,EACF,CAAC;AACH;AACA,SAAS,yBAAyB,SAAS,OAAO;AAChD,MAAI;AAAA,IACF,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,EACX,IAAI,UAAU,SAAS,CAAC,IAAI;AAC5B,MAAIA,SAAQ;AACZ,KAAG;AACD,IAAAA,UAAS,YAAY,CAAC,SAAS;AAAA,EACjC,SAASA,UAAS,KAAKA,UAAS,QAAQ,QAAQ,SAAS,KAAK,oBAAoB,SAASA,QAAO,eAAe;AACjH,SAAOA;AACT;AACA,SAAS,sBAAsB,SAAS,MAAM;AAC5C,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,OAAO;AAAA,EACpB,IAAI;AACJ,MAAI,YAAY;AAChB,MAAI,MAAM,QAAQ,UAAU;AAC1B,YAAQ,UAAU,KAAK;AACvB,QAAI,cAAc,IAAI;AACpB,kBAAY;AAAA,IACd,OAAO;AACL,kBAAY,yBAAyB,SAAS;AAAA,QAC5C,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,WAAW;AAAA,QACX;AAAA,MACF,CAAC;AACD,UAAI,SAAS,YAAY,OAAO,YAAY,YAAY,IAAI;AAC1D,cAAM,MAAM,YAAY;AACxB,cAAM,SAAS,WAAW;AAC1B,cAAMC,UAAS,YAAY,SAAS;AACpC,YAAI,WAAW,KAAK;AAClB,sBAAY;AAAA,QACd,OAAO;AACL,sBAAY,SAAS,MAAMA,UAASA,UAAS;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AACA,QAAI,uBAAuB,SAAS,SAAS,GAAG;AAC9C,kBAAY;AAAA,IACd;AAAA,EACF;AACA,MAAI,MAAM,QAAQ,YAAY;AAC5B,YAAQ,UAAU,KAAK;AACvB,QAAI,cAAc,IAAI;AACpB,kBAAY;AAAA,IACd,OAAO;AACL,kBAAY,yBAAyB,SAAS;AAAA,QAC5C,eAAe;AAAA,QACf,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AACD,UAAI,QAAQ,YAAY,OAAO,UAAU;AACvC,oBAAY,yBAAyB,SAAS;AAAA,UAC5C,eAAe,YAAY,OAAO;AAAA,UAClC,QAAQ;AAAA,UACR;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,uBAAuB,SAAS,SAAS,GAAG;AAC9C,kBAAY;AAAA,IACd;AAAA,EACF;AAGA,MAAI,gBAAgB,QAAQ;AAC1B,UAAM,UAAU,MAAM,YAAY,IAAI;AACtC,QAAI,MAAM,SAAS,MAAM,aAAa,cAAc;AAClD,cAAQ,UAAU,KAAK;AACvB,UAAI,YAAY,SAAS,OAAO,GAAG;AACjC,oBAAY,yBAAyB,SAAS;AAAA,UAC5C,eAAe;AAAA,UACf;AAAA,QACF,CAAC;AACD,YAAI,QAAQ,mBAAmB,WAAW,MAAM,OAAO,GAAG;AACxD,sBAAY,yBAAyB,SAAS;AAAA,YAC5C,eAAe,YAAY,YAAY,OAAO;AAAA,YAC9C;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,WAAW,MAAM;AACf,oBAAY,yBAAyB,SAAS;AAAA,UAC5C,eAAe,YAAY,YAAY,OAAO;AAAA,UAC9C;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,mBAAmB,WAAW,MAAM,OAAO,GAAG;AAChD,oBAAY;AAAA,MACd;AAAA,IACF;AACA,QAAI,MAAM,SAAS,MAAM,cAAc,aAAa;AAClD,cAAQ,UAAU,KAAK;AACvB,UAAI,YAAY,SAAS,GAAG;AAC1B,oBAAY,yBAAyB,SAAS;AAAA,UAC5C,eAAe;AAAA,UACf,WAAW;AAAA,UACX;AAAA,QACF,CAAC;AACD,YAAI,QAAQ,mBAAmB,WAAW,MAAM,OAAO,GAAG;AACxD,sBAAY,yBAAyB,SAAS;AAAA,YAC5C,eAAe,aAAa,OAAO,YAAY;AAAA,YAC/C,WAAW;AAAA,YACX;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,WAAW,MAAM;AACf,oBAAY,yBAAyB,SAAS;AAAA,UAC5C,eAAe,aAAa,OAAO,YAAY;AAAA,UAC/C,WAAW;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,mBAAmB,WAAW,MAAM,OAAO,GAAG;AAChD,oBAAY;AAAA,MACd;AAAA,IACF;AACA,UAAM,UAAU,MAAM,WAAW,IAAI,MAAM;AAC3C,QAAI,uBAAuB,SAAS,SAAS,GAAG;AAC9C,UAAI,QAAQ,SAAS;AACnB,oBAAY,MAAM,SAAS,MAAM,cAAc,cAAc,WAAW,yBAAyB,SAAS;AAAA,UACxG,eAAe,YAAY,YAAY,OAAO;AAAA,UAC9C;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,kBAAkB,OAAO,MAAM,OAAO;AAC7C,QAAM,UAAU,CAAC;AACjB,MAAI,aAAa;AACjB,QAAM,QAAQ,CAAC,OAAOD,WAAU;AAC9B,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,QAAQ,MAAM;AAChB,UAAI,MAAuC;AACzC,cAAM,IAAI,MAAM,uDAAuDA,SAAQ,+BAA+B;AAAA,MAChH;AAAA,IACF;AACA,QAAI,aAAa;AACjB,QAAI,OAAO;AACT,mBAAa;AAAA,IACf;AACA,WAAO,CAAC,YAAY;AAClB,YAAM,cAAc,CAAC;AACrB,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,sBAAY,KAAK,aAAa,IAAI,IAAI,IAAI;AAAA,QAC5C;AAAA,MACF;AACA,UAAI,aAAa,OAAO,SAAS,QAAQ,YAAY,MAAM,UAAQ,QAAQ,IAAI,KAAK,IAAI,GAAG;AACzF,oBAAY,QAAQ,UAAQ;AAC1B,kBAAQ,IAAI,IAAIA;AAAA,QAClB,CAAC;AACD,qBAAa;AAAA,MACf,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAGD,SAAO,CAAC,GAAG,OAAO;AACpB;AAGA,SAAS,yBAAyBA,QAAO,OAAO,SAAS,MAAM,QAAQ;AACrE,MAAIA,WAAU,GAAI,QAAO;AACzB,QAAM,iBAAiB,QAAQ,QAAQA,MAAK;AAC5C,QAAM,WAAW,MAAMA,MAAK;AAC5B,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AACA,aAAO,iBAAiB,SAAS,QAAQ;AAAA,IAC3C,KAAK;AACH,UAAI,CAAC,UAAU;AACb,eAAO;AAAA,MACT;AACA,aAAO,kBAAkB,SAAS,SAAS,KAAK;AAAA,IAClD,KAAK;AACH,aAAO,QAAQ,YAAYA,MAAK;AAAA,EACpC;AACF;AAGA,SAAS,mBAAmB,SAAS,SAAS;AAC5C,SAAO,QAAQ,QAAQ,CAACA,QAAO,cAAc,QAAQ,SAASA,MAAK,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC;AACzF;AACA,SAAS,oBAAoB,SAASA,QAAO,iBAAiB;AAC5D,MAAI,iBAAiB;AACnB,WAAO,gBAAgB,SAASA,MAAK;AAAA,EACvC;AACA,QAAM,UAAU,QAAQ,QAAQA,MAAK;AACrC,SAAO,WAAW,QAAQ,QAAQ,aAAa,UAAU,KAAK,QAAQ,aAAa,eAAe,MAAM;AAC1G;;;AF1eA,yBAAoC;AAGpC,IAAAE,YAA0B;;;AKF1B,SAAS,2BAA2B,MAAM,WAAW,KAAK;AACxD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,SAAS,QAAQ;AACrE,QAAM,UAAU,UAAU,IAAI,UAAU,SAAS,IAAI,SAAS,SAAS;AACvE,QAAM,cAAc,UAAU,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI;AACzE,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,SAAS;AAAA,MAC5B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,UAAU;AAAA,MAC7B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,UAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,SAAS;AAAA,QAC1B,GAAG;AAAA,MACL;AACA;AAAA,IACF;AACE,eAAS;AAAA,QACP,GAAG,UAAU;AAAA,QACb,GAAG,UAAU;AAAA,MACf;AAAA,EACJ;AACA,UAAQ,aAAa,SAAS,GAAG;AAAA,IAC/B,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,IACF,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,EACJ;AACA,SAAO;AACT;AASA,IAAM,kBAAkB,OAAO,WAAW,UAAU,WAAW;AAC7D,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAC;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,WAAW,OAAO,OAAO;AACjD,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,QAAQ;AAC5E,MAAI,QAAQ,MAAMA,UAAS,gBAAgB;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,2BAA2B,OAAO,WAAW,GAAG;AACpD,MAAI,oBAAoB;AACxB,MAAI,iBAAiB,CAAC;AACtB,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,CAAC;AACrB,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI,MAAM,GAAG;AAAA,MACX;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,SAAS,OAAO,QAAQ;AAC5B,qBAAiB;AAAA,MACf,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,QACN,GAAG,eAAe,IAAI;AAAA,QACtB,GAAG;AAAA,MACL;AAAA,IACF;AACA,QAAI,SAAS,cAAc,IAAI;AAC7B;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,WAAW;AACnB,8BAAoB,MAAM;AAAA,QAC5B;AACA,YAAI,MAAM,OAAO;AACf,kBAAQ,MAAM,UAAU,OAAO,MAAMA,UAAS,gBAAgB;AAAA,YAC5D;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC,IAAI,MAAM;AAAA,QACb;AACA,SAAC;AAAA,UACC;AAAA,UACA;AAAA,QACF,IAAI,2BAA2B,OAAO,mBAAmB,GAAG;AAAA,MAC9D;AACA,UAAI;AAAA,IACN;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;AAUA,eAAe,eAAe,OAAO,SAAS;AAC5C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,UAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,IAAI,SAAS,SAAS,KAAK;AAC3B,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,aAAa,mBAAmB,aAAa,cAAc;AACjE,QAAM,UAAU,SAAS,cAAc,aAAa,cAAc;AAClE,QAAM,qBAAqB,iBAAiB,MAAMA,UAAS,gBAAgB;AAAA,IACzE,WAAW,wBAAwB,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,OAAO,OAAO,OAAO,wBAAwB,QAAQ,UAAU,QAAQ,kBAAmB,OAAOA,UAAS,sBAAsB,OAAO,SAASA,UAAS,mBAAmB,SAAS,QAAQ;AAAA,IAChS;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACF,QAAM,OAAO,mBAAmB,aAAa;AAAA,IAC3C;AAAA,IACA;AAAA,IACA,OAAO,MAAM,SAAS;AAAA,IACtB,QAAQ,MAAM,SAAS;AAAA,EACzB,IAAI,MAAM;AACV,QAAM,eAAe,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,SAAS,QAAQ;AAClH,QAAM,cAAe,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,YAAY,KAAO,OAAOA,UAAS,YAAY,OAAO,SAASA,UAAS,SAAS,YAAY,MAAO;AAAA,IACvL,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB,iBAAiBA,UAAS,wDAAwD,MAAMA,UAAS,sDAAsD;AAAA,IAC/K;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,IAAI,IAAI;AACT,SAAO;AAAA,IACL,MAAM,mBAAmB,MAAM,kBAAkB,MAAM,cAAc,OAAO,YAAY;AAAA,IACxF,SAAS,kBAAkB,SAAS,mBAAmB,SAAS,cAAc,UAAU,YAAY;AAAA,IACpG,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,cAAc,QAAQ,YAAY;AAAA,IAC5F,QAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc,SAAS,YAAY;AAAA,EAClG;AACF;AAOA,IAAM,QAAQ,cAAY;AAAA,EACxB,MAAM;AAAA,EACN;AAAA,EACA,MAAM,GAAG,OAAO;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,IACZ,IAAI,SAAS,SAAS,KAAK,KAAK,CAAC;AACjC,QAAI,WAAW,MAAM;AACnB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO,iBAAiB,SAAS;AACvC,UAAM,SAAS,cAAc,IAAI;AACjC,UAAM,kBAAkB,MAAMA,UAAS,cAAc,OAAO;AAC5D,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,UAAU,UAAU,WAAW;AACrC,UAAM,aAAa,UAAU,iBAAiB;AAC9C,UAAM,UAAU,MAAM,UAAU,MAAM,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,SAAS,MAAM;AACtG,UAAM,YAAY,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AACrD,UAAM,oBAAoB,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,OAAO;AAC7G,QAAI,aAAa,oBAAoB,kBAAkB,UAAU,IAAI;AAGrE,QAAI,CAAC,cAAc,CAAE,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,iBAAiB,IAAK;AACzG,mBAAa,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,MAAM;AAAA,IACrE;AACA,UAAM,oBAAoB,UAAU,IAAI,YAAY;AAIpD,UAAM,yBAAyB,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9E,UAAM,aAAaC,KAAI,cAAc,OAAO,GAAG,sBAAsB;AACrE,UAAM,aAAaA,KAAI,cAAc,OAAO,GAAG,sBAAsB;AAIrE,UAAM,QAAQ;AACd,UAAMC,OAAM,aAAa,gBAAgB,MAAM,IAAI;AACnD,UAAM,SAAS,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9D,UAAMC,UAAS,MAAM,OAAO,QAAQD,IAAG;AAMvC,UAAM,kBAAkB,CAAC,eAAe,SAAS,aAAa,SAAS,KAAK,QAAQ,WAAWC,WAAU,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,QAAQ,aAAa,cAAc,gBAAgB,MAAM,IAAI,IAAI;AAClN,UAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAASD,OAAM;AAC3F,WAAO;AAAA,MACL,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,MACvB,MAAM;AAAA,QACJ,CAAC,IAAI,GAAGC;AAAA,QACR,cAAc,SAASA,UAAS;AAAA,QAChC,GAAI,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO;AAAA,IACT;AAAA,EACF;AACF;AA+GA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAC;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,4BAA4B;AAAA,QAC5B,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAM3B,WAAK,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACnG,eAAO,CAAC;AAAA,MACV;AACA,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,kBAAkB,YAAY,gBAAgB;AACpD,YAAM,kBAAkB,QAAQ,gBAAgB,MAAM;AACtD,YAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,YAAM,qBAAqB,gCAAgC,mBAAmB,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,sBAAsB,gBAAgB;AAChL,YAAM,+BAA+B,8BAA8B;AACnE,UAAI,CAAC,+BAA+B,8BAA8B;AAChE,2BAAmB,KAAK,GAAG,0BAA0B,kBAAkB,eAAe,2BAA2B,GAAG,CAAC;AAAA,MACvH;AACA,YAAMC,cAAa,CAAC,kBAAkB,GAAG,kBAAkB;AAC3D,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,CAAC;AACnB,UAAI,kBAAkB,uBAAuB,eAAe,SAAS,OAAO,SAAS,qBAAqB,cAAc,CAAC;AACzH,UAAI,eAAe;AACjB,kBAAU,KAAK,SAAS,IAAI,CAAC;AAAA,MAC/B;AACA,UAAI,gBAAgB;AAClB,cAAMC,SAAQ,kBAAkB,WAAW,OAAO,GAAG;AACrD,kBAAU,KAAK,SAASA,OAAM,CAAC,CAAC,GAAG,SAASA,OAAM,CAAC,CAAC,CAAC;AAAA,MACvD;AACA,sBAAgB,CAAC,GAAG,eAAe;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AAGD,UAAI,CAAC,UAAU,MAAM,CAAAC,UAAQA,SAAQ,CAAC,GAAG;AACvC,YAAI,uBAAuB;AAC3B,cAAM,eAAe,wBAAwB,eAAe,SAAS,OAAO,SAAS,sBAAsB,UAAU,KAAK;AAC1H,cAAM,gBAAgBF,YAAW,SAAS;AAC1C,YAAI,eAAe;AAEjB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,YACA,OAAO;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAIA,YAAI,kBAAkB,wBAAwB,cAAc,OAAO,OAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB;AAG1L,YAAI,CAAC,gBAAgB;AACnB,kBAAQ,kBAAkB;AAAA,YACxB,KAAK,WACH;AACE,kBAAI;AACJ,oBAAMG,cAAa,yBAAyB,cAAc,OAAO,OAAK;AACpE,oBAAI,8BAA8B;AAChC,wBAAM,kBAAkB,YAAY,EAAE,SAAS;AAC/C,yBAAO,oBAAoB;AAAA;AAAA,kBAG3B,oBAAoB;AAAA,gBACtB;AACA,uBAAO;AAAA,cACT,CAAC,EAAE,IAAI,OAAK,CAAC,EAAE,WAAW,EAAE,UAAU,OAAO,CAAAC,cAAYA,YAAW,CAAC,EAAE,OAAO,CAAC,KAAKA,cAAa,MAAMA,WAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,uBAAuB,CAAC;AACjM,kBAAID,YAAW;AACb,iCAAiBA;AAAA,cACnB;AACA;AAAA,YACF;AAAA,YACF,KAAK;AACH,+BAAiB;AACjB;AAAA,UACJ;AAAA,QACF;AACA,YAAI,cAAc,gBAAgB;AAChC,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AA6MA,eAAe,qBAAqB,OAAO,SAAS;AAClD,QAAM;AAAA,IACJ;AAAA,IACA,UAAAE;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,YAAY,SAAS,MAAM;AAC9C,QAAM,gBAAgB,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK;AAC5D,QAAM,iBAAiB,OAAO,aAAa,KAAK;AAChD,QAAM,WAAW,SAAS,SAAS,KAAK;AAGxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO,aAAa,WAAW;AAAA,IACjC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,IAAI;AAAA,IACF,UAAU,SAAS,YAAY;AAAA,IAC/B,WAAW,SAAS,aAAa;AAAA,IACjC,eAAe,SAAS;AAAA,EAC1B;AACA,MAAI,aAAa,OAAO,kBAAkB,UAAU;AAClD,gBAAY,cAAc,QAAQ,gBAAgB,KAAK;AAAA,EACzD;AACA,SAAO,aAAa;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,WAAW;AAAA,EAChB,IAAI;AAAA,IACF,GAAG,WAAW;AAAA,IACd,GAAG,YAAY;AAAA,EACjB;AACF;AASA,IAAM,SAAS,SAAU,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,aAAa,MAAM,qBAAqB,OAAO,OAAO;AAI5D,UAAI,gBAAgB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,eAAe,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACzN,eAAO,CAAC;AAAA,MACV;AACA,aAAO;AAAA,QACL,GAAG,IAAI,WAAW;AAAA,QAClB,GAAG,IAAI,WAAW;AAAA,QAClB,MAAM;AAAA,UACJ,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ACnyBA,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAMC,kBAAiB,OAAO;AAGpC,MAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;AACrC,MAAI,SAAS,WAAW,IAAI,MAAM,KAAK;AACvC,QAAM,YAAY,cAAc,OAAO;AACvC,QAAM,cAAc,YAAY,QAAQ,cAAc;AACtD,QAAM,eAAe,YAAY,QAAQ,eAAe;AACxD,QAAM,iBAAiB,MAAM,KAAK,MAAM,eAAe,MAAM,MAAM,MAAM;AACzE,MAAI,gBAAgB;AAClB,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,CAAC,UAAU,OAAO,IAAI,QAAQ,iBAAiB;AACxD;AAEA,SAAS,SAAS,SAAS;AACzB,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,WAAO,aAAa,CAAC;AAAA,EACvB;AACA,QAAM,OAAO,WAAW,sBAAsB;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,UAAU;AAC/B,MAAI,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AAC/C,MAAI,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,KAAK,UAAU;AAIjD,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAyB,aAAa,CAAC;AAC7C,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,CAAC,SAAS,KAAK,CAAC,IAAI,gBAAgB;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG,IAAI,eAAe;AAAA,IACtB,GAAG,IAAI,eAAe;AAAA,EACxB;AACF;AACA,SAAS,uBAAuB,SAAS,SAAS,sBAAsB;AACtE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,CAAC,wBAAwB,WAAW,yBAAyB,UAAU,OAAO,GAAG;AACnF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,SAAS,cAAc,iBAAiB,cAAc;AACnF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,aAAa,QAAQ,sBAAsB;AACjD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,QAAQ,aAAa,CAAC;AAC1B,MAAI,cAAc;AAChB,QAAI,cAAc;AAChB,UAAI,UAAU,YAAY,GAAG;AAC3B,gBAAQ,SAAS,YAAY;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,cAAQ,SAAS,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,YAAY,IAAI,iBAAiB,UAAU,IAAI,aAAa,CAAC;AACvI,MAAI,KAAK,WAAW,OAAO,cAAc,KAAK,MAAM;AACpD,MAAI,KAAK,WAAW,MAAM,cAAc,KAAK,MAAM;AACnD,MAAI,QAAQ,WAAW,QAAQ,MAAM;AACrC,MAAI,SAAS,WAAW,SAAS,MAAM;AACvC,MAAI,YAAY;AACd,UAAM,MAAM,UAAU,UAAU;AAChC,UAAM,YAAY,gBAAgB,UAAU,YAAY,IAAI,UAAU,YAAY,IAAI;AACtF,QAAI,aAAa;AACjB,QAAI,gBAAgB,gBAAgB,UAAU;AAC9C,WAAO,iBAAiB,gBAAgB,cAAc,YAAY;AAChE,YAAM,cAAc,SAAS,aAAa;AAC1C,YAAM,aAAa,cAAc,sBAAsB;AACvD,YAAM,MAAMA,kBAAiB,aAAa;AAC1C,YAAM,OAAO,WAAW,QAAQ,cAAc,aAAa,WAAW,IAAI,WAAW,KAAK,YAAY;AACtG,YAAM,MAAM,WAAW,OAAO,cAAc,YAAY,WAAW,IAAI,UAAU,KAAK,YAAY;AAClG,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,eAAS,YAAY;AACrB,gBAAU,YAAY;AACtB,WAAK;AACL,WAAK;AACL,mBAAa,UAAU,aAAa;AACpC,sBAAgB,gBAAgB,UAAU;AAAA,IAC5C;AAAA,EACF;AACA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,oBAAoB,SAAS,MAAM;AAC1C,QAAM,aAAa,cAAc,OAAO,EAAE;AAC1C,MAAI,CAAC,MAAM;AACT,WAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO;AAAA,EACnE;AACA,SAAO,KAAK,OAAO;AACrB;AAEA,SAAS,cAAc,iBAAiB,QAAQ,kBAAkB;AAChE,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,EACrB;AACA,QAAM,WAAW,gBAAgB,sBAAsB;AACvD,QAAM,IAAI,SAAS,OAAO,OAAO,cAAc,mBAAmB;AAAA;AAAA,IAElE,oBAAoB,iBAAiB,QAAQ;AAAA;AAC7C,QAAM,IAAI,SAAS,MAAM,OAAO;AAChC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,sDAAsD,MAAM;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,aAAa;AAC7B,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,WAAW,WAAW,WAAW,SAAS,QAAQ,IAAI;AAC5D,MAAI,iBAAiB,mBAAmB,YAAY,SAAS;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,QAAQ,aAAa,CAAC;AAC1B,QAAM,UAAU,aAAa,CAAC;AAC9B,QAAM,0BAA0B,cAAc,YAAY;AAC1D,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,cAAc,YAAY,GAAG;AAC/B,YAAM,aAAa,sBAAsB,YAAY;AACrD,cAAQ,SAAS,YAAY;AAC7B,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,QAAQ,IAAI,IAAI,aAAa,CAAC;AAC1I,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ,MAAM;AAAA,IAC1B,QAAQ,KAAK,SAAS,MAAM;AAAA,IAC5B,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,aAAa,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,IAC3E,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,YAAY,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,EAC5E;AACF;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,MAAM,KAAK,QAAQ,eAAe,CAAC;AAC5C;AAIA,SAAS,gBAAgB,SAAS;AAChC,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,SAAS,cAAc,OAAO;AACpC,QAAM,OAAO,QAAQ,cAAc;AACnC,QAAM,QAAQC,KAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,WAAW;AACxF,QAAM,SAASA,KAAI,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,YAAY;AAC7F,MAAI,IAAI,CAAC,OAAO,aAAa,oBAAoB,OAAO;AACxD,QAAM,IAAI,CAAC,OAAO;AAClB,MAAID,kBAAiB,IAAI,EAAE,cAAc,OAAO;AAC9C,SAAKC,KAAI,KAAK,aAAa,KAAK,WAAW,IAAI;AAAA,EACjD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,iBAAiB,IAAI;AAC3B,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,UAAM,sBAAsB,SAAS;AACrC,QAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;AACvE,UAAI,eAAe;AACnB,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,SAAS,2BAA2B,SAAS,UAAU;AACrD,QAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa,OAAO;AAC5E,QAAM,MAAM,WAAW,MAAM,QAAQ;AACrC,QAAM,OAAO,WAAW,OAAO,QAAQ;AACvC,QAAM,QAAQ,cAAc,OAAO,IAAI,SAAS,OAAO,IAAI,aAAa,CAAC;AACzE,QAAM,QAAQ,QAAQ,cAAc,MAAM;AAC1C,QAAM,SAAS,QAAQ,eAAe,MAAM;AAC5C,QAAM,IAAI,OAAO,MAAM;AACvB,QAAM,IAAI,MAAM,MAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,kCAAkC,SAAS,kBAAkB,UAAU;AAC9E,MAAI;AACJ,MAAI,qBAAqB,YAAY;AACnC,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C,WAAW,qBAAqB,YAAY;AAC1C,WAAO,gBAAgB,mBAAmB,OAAO,CAAC;AAAA,EACpD,WAAW,UAAU,gBAAgB,GAAG;AACtC,WAAO,2BAA2B,kBAAkB,QAAQ;AAAA,EAC9D,OAAO;AACL,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,WAAO;AAAA,MACL,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,OAAO,iBAAiB;AAAA,MACxB,QAAQ,iBAAiB;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,iBAAiB,IAAI;AAC9B;AACA,SAAS,yBAAyB,SAAS,UAAU;AACnD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,eAAe,YAAY,CAAC,UAAU,UAAU,KAAK,sBAAsB,UAAU,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,SAAOD,kBAAiB,UAAU,EAAE,aAAa,WAAW,yBAAyB,YAAY,QAAQ;AAC3G;AAKA,SAAS,4BAA4B,SAAS,OAAO;AACnD,QAAM,eAAe,MAAM,IAAI,OAAO;AACtC,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,qBAAqB,SAAS,CAAC,GAAG,KAAK,EAAE,OAAO,QAAM,UAAU,EAAE,KAAK,YAAY,EAAE,MAAM,MAAM;AAC9G,MAAI,sCAAsC;AAC1C,QAAM,iBAAiBA,kBAAiB,OAAO,EAAE,aAAa;AAC9D,MAAI,cAAc,iBAAiB,cAAc,OAAO,IAAI;AAG5D,SAAO,UAAU,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACpE,UAAM,gBAAgBA,kBAAiB,WAAW;AAClD,UAAM,0BAA0B,kBAAkB,WAAW;AAC7D,QAAI,CAAC,2BAA2B,cAAc,aAAa,SAAS;AAClE,4CAAsC;AAAA,IACxC;AACA,UAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,aAAa,YAAY,CAAC,CAAC,uCAAuC,CAAC,YAAY,OAAO,EAAE,SAAS,oCAAoC,QAAQ,KAAK,kBAAkB,WAAW,KAAK,CAAC,2BAA2B,yBAAyB,SAAS,WAAW;AACzZ,QAAI,uBAAuB;AAEzB,eAAS,OAAO,OAAO,cAAY,aAAa,WAAW;AAAA,IAC7D,OAAO;AAEL,4CAAsC;AAAA,IACxC;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,QAAM,IAAI,SAAS,MAAM;AACzB,SAAO;AACT;AAIA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,2BAA2B,aAAa,sBAAsB,WAAW,OAAO,IAAI,CAAC,IAAI,4BAA4B,SAAS,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;AACjK,QAAM,oBAAoB,CAAC,GAAG,0BAA0B,YAAY;AACpE,QAAM,wBAAwB,kBAAkB,CAAC;AACjD,QAAM,eAAe,kBAAkB,OAAO,CAAC,SAAS,qBAAqB;AAC3E,UAAM,OAAO,kCAAkC,SAAS,kBAAkB,QAAQ;AAClF,YAAQ,MAAMC,KAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQC,KAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAASA,KAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAOD,KAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,kCAAkC,SAAS,uBAAuB,QAAQ,CAAC;AAC9E,SAAO;AAAA,IACL,OAAO,aAAa,QAAQ,aAAa;AAAA,IACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,IAC3C,GAAG,aAAa;AAAA,IAChB,GAAG,aAAa;AAAA,EAClB;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,SAAS,cAAc,UAAU;AACtE,QAAM,0BAA0B,cAAc,YAAY;AAC1D,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,UAAU,aAAa;AAC7B,QAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS,YAAY;AACvE,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,QAAM,UAAU,aAAa,CAAC;AAC9B,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,yBAAyB;AAC3B,YAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS,YAAY;AAClF,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C,WAAW,iBAAiB;AAG1B,cAAQ,IAAI,oBAAoB,eAAe;AAAA,IACjD;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,MAAM,IAAI,aAAa,CAAC;AACpI,QAAM,IAAI,KAAK,OAAO,OAAO,aAAa,QAAQ,IAAI,WAAW;AACjE,QAAM,IAAI,KAAK,MAAM,OAAO,YAAY,QAAQ,IAAI,WAAW;AAC/D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AAEA,SAAS,mBAAmB,SAAS;AACnC,SAAOD,kBAAiB,OAAO,EAAE,aAAa;AAChD;AAEA,SAAS,oBAAoB,SAAS,UAAU;AAC9C,MAAI,CAAC,cAAc,OAAO,KAAKA,kBAAiB,OAAO,EAAE,aAAa,SAAS;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACZ,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,MAAI,kBAAkB,QAAQ;AAM9B,MAAI,mBAAmB,OAAO,MAAM,iBAAiB;AACnD,sBAAkB,gBAAgB,cAAc;AAAA,EAClD;AACA,SAAO;AACT;AAIA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,OAAO,GAAG;AAC3B,QAAI,kBAAkB,cAAc,OAAO;AAC3C,WAAO,mBAAmB,CAAC,sBAAsB,eAAe,GAAG;AACjE,UAAI,UAAU,eAAe,KAAK,CAAC,mBAAmB,eAAe,GAAG;AACtE,eAAO;AAAA,MACT;AACA,wBAAkB,cAAc,eAAe;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACA,MAAI,eAAe,oBAAoB,SAAS,QAAQ;AACxD,SAAO,gBAAgB,eAAe,YAAY,KAAK,mBAAmB,YAAY,GAAG;AACvF,mBAAe,oBAAoB,cAAc,QAAQ;AAAA,EAC3D;AACA,MAAI,gBAAgB,sBAAsB,YAAY,KAAK,mBAAmB,YAAY,KAAK,CAAC,kBAAkB,YAAY,GAAG;AAC/H,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,mBAAmB,OAAO,KAAK;AACxD;AAEA,IAAM,kBAAkB,eAAgB,MAAM;AAC5C,QAAM,oBAAoB,KAAK,mBAAmB;AAClD,QAAM,kBAAkB,KAAK;AAC7B,QAAM,qBAAqB,MAAM,gBAAgB,KAAK,QAAQ;AAC9D,SAAO;AAAA,IACL,WAAW,8BAA8B,KAAK,WAAW,MAAM,kBAAkB,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,IAC9G,UAAU;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO,mBAAmB;AAAA,MAC1B,QAAQ,mBAAmB;AAAA,IAC7B;AAAA,EACF;AACF;AAEA,SAAS,MAAM,SAAS;AACtB,SAAOA,kBAAiB,OAAO,EAAE,cAAc;AACjD;AAEA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE;AAC7E;AAGA,SAAS,YAAY,SAAS,QAAQ;AACpC,MAAI,KAAK;AACT,MAAI;AACJ,QAAM,OAAO,mBAAmB,OAAO;AACvC,WAASG,WAAU;AACjB,QAAI;AACJ,iBAAa,SAAS;AACtB,KAAC,MAAM,OAAO,QAAQ,IAAI,WAAW;AACrC,SAAK;AAAA,EACP;AACA,WAAS,QAAQ,MAAM,WAAW;AAChC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,IAAAA,SAAQ;AACR,UAAM,2BAA2B,QAAQ,sBAAsB;AAC/D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,CAAC,QAAQ;AACrB;AAAA,IACF;AACA,UAAM,WAAW,MAAM,GAAG;AAC1B,UAAM,aAAa,MAAM,KAAK,eAAe,OAAO,MAAM;AAC1D,UAAM,cAAc,MAAM,KAAK,gBAAgB,MAAM,OAAO;AAC5D,UAAM,YAAY,MAAM,IAAI;AAC5B,UAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;AACjG,UAAM,UAAU;AAAA,MACd;AAAA,MACA,WAAWF,KAAI,GAAGC,KAAI,GAAG,SAAS,CAAC,KAAK;AAAA,IAC1C;AACA,QAAI,gBAAgB;AACpB,aAAS,cAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,UAAI,UAAU,WAAW;AACvB,YAAI,CAAC,eAAe;AAClB,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,CAAC,OAAO;AAGV,sBAAY,WAAW,MAAM;AAC3B,oBAAQ,OAAO,IAAI;AAAA,UACrB,GAAG,GAAI;AAAA,QACT,OAAO;AACL,kBAAQ,OAAO,KAAK;AAAA,QACtB;AAAA,MACF;AACA,UAAI,UAAU,KAAK,CAAC,cAAc,0BAA0B,QAAQ,sBAAsB,CAAC,GAAG;AAQ5F,gBAAQ;AAAA,MACV;AACA,sBAAgB;AAAA,IAClB;AAIA,QAAI;AACF,WAAK,IAAI,qBAAqB,eAAe;AAAA,QAC3C,GAAG;AAAA;AAAA,QAEH,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH,SAAS,GAAG;AACV,WAAK,IAAI,qBAAqB,eAAe,OAAO;AAAA,IACtD;AACA,OAAG,QAAQ,OAAO;AAAA,EACpB;AACA,UAAQ,IAAI;AACZ,SAAOC;AACT;AAUA,SAAS,WAAW,WAAW,UAAU,QAAQ,SAAS;AACxD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,gBAAgB,OAAO,mBAAmB;AAAA,IAC1C,cAAc,OAAO,yBAAyB;AAAA,IAC9C,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAM,cAAc,cAAc,SAAS;AAC3C,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,GAAI,cAAc,qBAAqB,WAAW,IAAI,CAAC,GAAI,GAAG,qBAAqB,QAAQ,CAAC,IAAI,CAAC;AACvJ,YAAU,QAAQ,cAAY;AAC5B,sBAAkB,SAAS,iBAAiB,UAAU,QAAQ;AAAA,MAC5D,SAAS;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS,iBAAiB,UAAU,MAAM;AAAA,EAC9D,CAAC;AACD,QAAM,YAAY,eAAe,cAAc,YAAY,aAAa,MAAM,IAAI;AAClF,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACjB,qBAAiB,IAAI,eAAe,UAAQ;AAC1C,UAAI,CAAC,UAAU,IAAI;AACnB,UAAI,cAAc,WAAW,WAAW,eAAe,gBAAgB;AAGrE,uBAAe,UAAU,QAAQ;AACjC,6BAAqB,cAAc;AACnC,yBAAiB,sBAAsB,MAAM;AAC3C,cAAI;AACJ,WAAC,kBAAkB,mBAAmB,QAAQ,gBAAgB,QAAQ,QAAQ;AAAA,QAChF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,eAAe,CAAC,gBAAgB;AAClC,qBAAe,QAAQ,WAAW;AAAA,IACpC;AACA,mBAAe,QAAQ,QAAQ;AAAA,EACjC;AACA,MAAI;AACJ,MAAI,cAAc,iBAAiB,sBAAsB,SAAS,IAAI;AACtE,MAAI,gBAAgB;AAClB,cAAU;AAAA,EACZ;AACA,WAAS,YAAY;AACnB,UAAM,cAAc,sBAAsB,SAAS;AACnD,QAAI,eAAe,CAAC,cAAc,aAAa,WAAW,GAAG;AAC3D,aAAO;AAAA,IACT;AACA,kBAAc;AACd,cAAU,sBAAsB,SAAS;AAAA,EAC3C;AACA,SAAO;AACP,SAAO,MAAM;AACX,QAAI;AACJ,cAAU,QAAQ,cAAY;AAC5B,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAC/D,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAAA,IACjE,CAAC;AACD,iBAAa,QAAQ,UAAU;AAC/B,KAAC,mBAAmB,mBAAmB,QAAQ,iBAAiB,WAAW;AAC3E,qBAAiB;AACjB,QAAI,gBAAgB;AAClB,2BAAqB,OAAO;AAAA,IAC9B;AAAA,EACF;AACF;AAmBA,IAAMC,UAAS;AAuBf,IAAMC,QAAO;AAsBb,IAAMC,SAAQ;AAkBd,IAAMC,mBAAkB,CAAC,WAAW,UAAU,YAAY;AAIxD,QAAM,QAAQ,oBAAI,IAAI;AACtB,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB;AAAA,IACxB,GAAG,cAAc;AAAA,IACjB,IAAI;AAAA,EACN;AACA,SAAO,gBAAkB,WAAW,UAAU;AAAA,IAC5C,GAAG;AAAA,IACH,UAAU;AAAA,EACZ,CAAC;AACH;;;AC3uBA,IAAAC,SAAuB;AACvB,IAAAC,gBAA2C;AAC3C,eAA0B;AAE1B,IAAIC,SAAQ,OAAO,aAAa,cAAc,gCAAkB;AAIhE,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,cAAc,EAAE,SAAS,MAAM,EAAE,SAAS,GAAG;AAC5D,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,KAAK,KAAK,OAAO,MAAM,UAAU;AACnC,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,eAAS,EAAE;AACX,UAAI,WAAW,EAAE,OAAQ,QAAO;AAChC,WAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,YAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG;AAC1B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO,OAAO,KAAK,CAAC;AACpB,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE,QAAQ;AACpC,aAAO;AAAA,IACT;AACA,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,UAAI,CAAC,CAAC,EAAE,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG;AACvC,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,YAAM,MAAM,KAAK,CAAC;AAClB,UAAI,QAAQ,YAAY,EAAE,UAAU;AAClC;AAAA,MACF;AACA,UAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,MAAM,KAAK,MAAM;AAC1B;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,QAAQ,cAAc,eAAe;AACjD,SAAO,IAAI,oBAAoB;AACjC;AAEA,SAAS,WAAW,SAAS,OAAO;AAClC,QAAM,MAAM,OAAO,OAAO;AAC1B,SAAO,KAAK,MAAM,QAAQ,GAAG,IAAI;AACnC;AAEA,SAAS,aAAa,OAAO;AAC3B,QAAM,MAAY,cAAO,KAAK;AAC9B,EAAAA,OAAM,MAAM;AACV,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AAMA,SAAS,YAAY,SAAS;AAC5B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAC;AAAA,IACA,UAAU;AAAA,MACR,WAAW;AAAA,MACX,UAAU;AAAA,IACZ,IAAI,CAAC;AAAA,IACL,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,MAAM,OAAO,IAAU,gBAAS;AAAA,IACrC,GAAG;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA,gBAAgB,CAAC;AAAA,IACjB,cAAc;AAAA,EAChB,CAAC;AACD,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,gBAAS,UAAU;AACzE,MAAI,CAAC,UAAU,kBAAkB,UAAU,GAAG;AAC5C,wBAAoB,UAAU;AAAA,EAChC;AACA,QAAM,CAAC,YAAY,aAAa,IAAU,gBAAS,IAAI;AACvD,QAAM,CAAC,WAAW,YAAY,IAAU,gBAAS,IAAI;AACrD,QAAM,eAAqB,mBAAY,UAAQ;AAC7C,QAAI,SAAS,aAAa,SAAS;AACjC,mBAAa,UAAU;AACvB,oBAAc,IAAI;AAAA,IACpB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,cAAoB,mBAAY,UAAQ;AAC5C,QAAI,SAAS,YAAY,SAAS;AAChC,kBAAY,UAAU;AACtB,mBAAa,IAAI;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,cAAc,qBAAqB;AACzC,QAAM,aAAa,oBAAoB;AACvC,QAAM,eAAqB,cAAO,IAAI;AACtC,QAAM,cAAoB,cAAO,IAAI;AACrC,QAAM,UAAgB,cAAO,IAAI;AACjC,QAAM,0BAA0B,wBAAwB;AACxD,QAAM,0BAA0B,aAAa,oBAAoB;AACjE,QAAM,cAAc,aAAaA,SAAQ;AACzC,QAAM,UAAU,aAAa,IAAI;AACjC,QAAM,SAAe,mBAAY,MAAM;AACrC,QAAI,CAAC,aAAa,WAAW,CAAC,YAAY,SAAS;AACjD;AAAA,IACF;AACA,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd;AACA,QAAI,YAAY,SAAS;AACvB,aAAO,WAAW,YAAY;AAAA,IAChC;AACA,IAAAC,iBAAgB,aAAa,SAAS,YAAY,SAAS,MAAM,EAAE,KAAK,CAAAC,UAAQ;AAC9E,YAAM,WAAW;AAAA,QACf,GAAGA;AAAA;AAAA;AAAA;AAAA;AAAA,QAKH,cAAc,QAAQ,YAAY;AAAA,MACpC;AACA,UAAI,aAAa,WAAW,CAAC,UAAU,QAAQ,SAAS,QAAQ,GAAG;AACjE,gBAAQ,UAAU;AAClB,QAAS,mBAAU,MAAM;AACvB,kBAAQ,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,kBAAkB,WAAW,UAAU,aAAa,OAAO,CAAC;AAChE,EAAAH,OAAM,MAAM;AACV,QAAI,SAAS,SAAS,QAAQ,QAAQ,cAAc;AAClD,cAAQ,QAAQ,eAAe;AAC/B,cAAQ,CAAAG,WAAS;AAAA,QACf,GAAGA;AAAA,QACH,cAAc;AAAA,MAChB,EAAE;AAAA,IACJ;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,QAAM,eAAqB,cAAO,KAAK;AACvC,EAAAH,OAAM,MAAM;AACV,iBAAa,UAAU;AACvB,WAAO,MAAM;AACX,mBAAa,UAAU;AAAA,IACzB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,EAAAA,OAAM,MAAM;AACV,QAAI,YAAa,cAAa,UAAU;AACxC,QAAI,WAAY,aAAY,UAAU;AACtC,QAAI,eAAe,YAAY;AAC7B,UAAI,wBAAwB,SAAS;AACnC,eAAO,wBAAwB,QAAQ,aAAa,YAAY,MAAM;AAAA,MACxE;AACA,aAAO;AAAA,IACT;AAAA,EACF,GAAG,CAAC,aAAa,YAAY,QAAQ,yBAAyB,uBAAuB,CAAC;AACtF,QAAM,OAAa,eAAQ,OAAO;AAAA,IAChC,WAAW;AAAA,IACX,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACF,IAAI,CAAC,cAAc,WAAW,CAAC;AAC/B,QAAM,WAAiB,eAAQ,OAAO;AAAA,IACpC,WAAW;AAAA,IACX,UAAU;AAAA,EACZ,IAAI,CAAC,aAAa,UAAU,CAAC;AAC7B,QAAM,iBAAuB,eAAQ,MAAM;AACzC,UAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AACA,QAAI,CAAC,SAAS,UAAU;AACtB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,WAAW,SAAS,UAAU,KAAK,CAAC;AAC9C,UAAM,IAAI,WAAW,SAAS,UAAU,KAAK,CAAC;AAC9C,QAAI,WAAW;AACb,aAAO;AAAA,QACL,GAAG;AAAA,QACH,WAAW,eAAe,IAAI,SAAS,IAAI;AAAA,QAC3C,GAAI,OAAO,SAAS,QAAQ,KAAK,OAAO;AAAA,UACtC,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AAAA,EACF,GAAG,CAAC,UAAU,WAAW,SAAS,UAAU,KAAK,GAAG,KAAK,CAAC,CAAC;AAC3D,SAAa,eAAQ,OAAO;AAAA,IAC1B,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,MAAM,QAAQ,MAAM,UAAU,cAAc,CAAC;AACpD;AAQA,IAAM,UAAU,aAAW;AACzB,WAAS,MAAM,OAAO;AACpB,WAAO,CAAC,EAAE,eAAe,KAAK,OAAO,SAAS;AAAA,EAChD;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,GAAG,OAAO;AACR,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACrD,UAAI,WAAW,MAAM,OAAO,GAAG;AAC7B,YAAI,QAAQ,WAAW,MAAM;AAC3B,iBAAOI,OAAQ;AAAA,YACb,SAAS,QAAQ;AAAA,YACjB;AAAA,UACF,CAAC,EAAE,GAAG,KAAK;AAAA,QACb;AACA,eAAO,CAAC;AAAA,MACV;AACA,UAAI,SAAS;AACX,eAAOA,OAAQ;AAAA,UACb;AAAA,UACA;AAAA,QACF,CAAC,EAAE,GAAG,KAAK;AAAA,MACb;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AASA,IAAMC,UAAS,CAAC,SAAS,UAAU;AAAA,EACjC,GAAGA,QAAS,OAAO;AAAA,EACnB,SAAS,CAAC,SAAS,IAAI;AACzB;AA0BA,IAAMC,QAAO,CAAC,SAAS,UAAU;AAAA,EAC/B,GAAGA,MAAO,OAAO;AAAA,EACjB,SAAS,CAAC,SAAS,IAAI;AACzB;AAkDA,IAAMC,SAAQ,CAAC,SAAS,UAAU;AAAA,EAChC,GAAG,QAAQ,OAAO;AAAA,EAClB,SAAS,CAAC,SAAS,IAAI;AACzB;;;AP/VA,SAAS,aAAa,MAAM;AAC1B,QAAM,aAAmB,cAAO,MAAS;AACzC,QAAM,YAAkB,mBAAY,cAAY;AAC9C,UAAM,WAAW,KAAK,IAAI,SAAO;AAC/B,UAAI,OAAO,MAAM;AACf;AAAA,MACF;AACA,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,cAAc;AACpB,cAAM,aAAa,YAAY,QAAQ;AACvC,eAAO,OAAO,eAAe,aAAa,aAAa,MAAM;AAC3D,sBAAY,IAAI;AAAA,QAClB;AAAA,MACF;AACA,UAAI,UAAU;AACd,aAAO,MAAM;AACX,YAAI,UAAU;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AACX,eAAS,QAAQ,gBAAc,cAAc,OAAO,SAAS,WAAW,CAAC;AAAA,IAC3E;AAAA,EAEF,GAAG,IAAI;AACP,SAAa,eAAQ,MAAM;AACzB,QAAI,KAAK,MAAM,SAAO,OAAO,IAAI,GAAG;AAClC,aAAO;AAAA,IACT;AACA,WAAO,WAAS;AACd,UAAI,WAAW,SAAS;AACtB,mBAAW,QAAQ;AACnB,mBAAW,UAAU;AAAA,MACvB;AACA,UAAI,SAAS,MAAM;AACjB,mBAAW,UAAU,UAAU,KAAK;AAAA,MACtC;AAAA,IACF;AAAA,EAEF,GAAG,IAAI;AACT;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,QAAM,WAAW,EAAE,wBAAwB,CAAC;AAC5C,MAAI,WAAW,KAAK,+BAA+B,WAAW,KAAK,gCAAgC;AACjG,WAAO;AAAA,EACT;AACA,MAAI,WAAW,KAAK,+BAA+B,WAAW,KAAK,4BAA4B;AAC7F,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,IAAM,sBAAyC,qBAAc;AAAA,EAC3D,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,YAAY,MAAM;AAAA,EAAC;AAAA,EACnB,KAAkB,oBAAI,IAAI;AAAA,EAC1B,aAAa;AAAA,IACX,SAAS,CAAC;AAAA,EACZ;AACF,CAAC;AAKD,SAAS,aAAa,OAAO;AAC3B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,OAAO,QAAQ,IAAU,gBAAS,MAAM,oBAAI,IAAI,CAAC;AACxD,QAAM,WAAiB,mBAAY,UAAQ;AACzC,aAAS,aAAW,IAAI,IAAI,OAAO,EAAE,IAAI,IAAI,CAAC;AAAA,EAChD,GAAG,CAAC,CAAC;AACL,QAAM,aAAmB,mBAAY,UAAQ;AAC3C,aAAS,aAAW;AAClB,YAAMC,OAAM,IAAI,IAAI,OAAO;AAC3B,MAAAA,KAAI,OAAO,IAAI;AACf,aAAOA;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,QAAM,MAAY,eAAQ,MAAM;AAC9B,UAAM,SAAS,oBAAI,IAAI;AACvB,UAAM,cAAc,MAAM,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,sBAAsB;AACxE,gBAAY,QAAQ,CAAC,MAAMC,WAAU;AACnC,aAAO,IAAI,MAAMA,MAAK;AAAA,IACxB,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,KAAK,CAAC;AACV,aAAoB,wBAAI,oBAAoB,UAAU;AAAA,IACpD,OAAa,eAAQ,OAAO;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,CAAC,UAAU,YAAY,KAAK,aAAa,SAAS,CAAC;AAAA,IACvD;AAAA,EACF,CAAC;AACH;AAMA,SAAS,YAAY,OAAO;AAC1B,MAAI,UAAU,QAAQ;AACpB,YAAQ,CAAC;AAAA,EACX;AACA,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAU,kBAAW,mBAAmB;AACxC,QAAM,CAACA,QAAO,QAAQ,IAAU,gBAAS,IAAI;AAC7C,QAAM,eAAqB,cAAO,IAAI;AACtC,QAAM,MAAY,mBAAY,UAAQ;AACpC,iBAAa,UAAU;AACvB,QAAIA,WAAU,MAAM;AAClB,kBAAY,QAAQA,MAAK,IAAI;AAC7B,UAAI,WAAW;AACb,YAAI;AACJ,cAAM,iBAAiB,UAAU;AACjC,kBAAU,QAAQA,MAAK,IAAI,iBAAiB,SAAS,oBAAoB,QAAQ,OAAO,SAAS,KAAK,gBAAgB,OAAO,oBAAoB;AAAA,MACnJ;AAAA,IACF;AAAA,EACF,GAAG,CAACA,QAAO,aAAa,WAAW,KAAK,CAAC;AACzC,QAAsB,MAAM;AAC1B,UAAM,OAAO,aAAa;AAC1B,QAAI,MAAM;AACR,eAAS,IAAI;AACb,aAAO,MAAM;AACX,mBAAW,IAAI;AAAA,MACjB;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,UAAU,CAAC;AACzB,QAAsB,MAAM;AAC1B,UAAMA,SAAQ,aAAa,UAAU,IAAI,IAAI,aAAa,OAAO,IAAI;AACrE,QAAIA,UAAS,MAAM;AACjB,eAASA,MAAK;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,GAAG,CAAC;AACR,SAAa,eAAQ,OAAO;AAAA,IAC1B;AAAA,IACA,OAAOA,UAAS,OAAO,KAAKA;AAAA,EAC9B,IAAI,CAACA,QAAO,GAAG,CAAC;AAClB;AAKA,IAAMC,cAAa;AACnB,IAAMC,eAAc;AACpB,IAAMC,YAAW;AACjB,IAAMC,cAAa;AAEnB,SAAS,UAAU,QAAQ,eAAe;AACxC,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,OAAO,aAAa;AAAA,EAC7B;AACA,MAAI,QAAQ;AACV,WAA0B,oBAAa,QAAQ,aAAa;AAAA,EAC9D;AACA,aAAoB,wBAAI,OAAO;AAAA,IAC7B,GAAG;AAAA,EACL,CAAC;AACH;AACA,IAAM,mBAAsC,qBAAc;AAAA,EACxD,aAAa;AAAA,EACb,YAAY,MAAM;AAAA,EAAC;AACrB,CAAC;AACD,IAAM,iBAAiB,CAACH,aAAYC,YAAW;AAC/C,IAAM,eAAe,CAACC,WAAUC,WAAU;AAC1C,IAAM,UAAU,CAAC,GAAG,gBAAgB,GAAG,YAAY;AAWnD,IAAM,YAA+B,kBAAW,SAASC,WAAU,OAAO,cAAc;AACtF,QAAM;AAAA,IACJ;AAAA,IACA,cAAc;AAAA,IACd,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP;AAAA,IACA,aAAa;AAAA,IACb,YAAY;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,IACR,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,CAAC,qBAAqB,sBAAsB,IAAU,gBAAS,CAAC;AACtE,QAAM,cAAc,uBAAuB,OAAO,sBAAsB;AACxE,QAAM,aAAa,eAAe,0BAA0B,OAAO,yBAAyB,sBAAsB;AAClH,QAAM,cAAoB,cAAO,CAAC,CAAC;AACnC,QAAM,qBAAqB,UAAU,OAAO,WAAW,aAAa,OAAO,QAAQ,CAAC;AACpF,QAAM,eAAqB,eAAQ,OAAO;AAAA,IACxC;AAAA,IACA;AAAA,EACF,IAAI,CAAC,aAAa,UAAU,CAAC;AAC7B,QAAM,SAAS,OAAO;AACtB,WAAS,cAAc,OAAO;AAC5B,QAAI,CAAC,QAAQ,SAAS,MAAM,GAAG,EAAG;AAClC,QAAI,YAAY;AAChB,UAAM,WAAW,gBAAgB,aAAa,eAAe;AAC7D,UAAM,WAAW,gBAAgB,aAAa,eAAe;AAC7D,UAAM,mBAAmB,MAAMJ,cAAaC;AAC5C,UAAM,qBAAqB,MAAMA,eAAcD;AAC/C,QAAI,QAAQ;AACV,YAAM,QAAQ,aAAa,MAAM,KAAK;AAAA,QACpC,QAAQ,YAAY,QAAQ;AAAA,MAC9B,GAAG,OAAO;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,EAAE;AAGF,YAAM,UAAU,kBAAkB,OAAO,MAAM,KAAK;AACpD,YAAM,eAAe,QAAQ,UAAU,CAAAK,WAASA,UAAS,QAAQ,CAAC,oBAAoB,aAAaA,QAAO,eAAe,CAAC;AAE1H,YAAM,eAAe,QAAQ,OAAO,CAAC,YAAYA,QAAO,cAAcA,UAAS,QAAQ,CAAC,oBAAoB,aAAaA,QAAO,eAAe,IAAI,YAAY,YAAY,EAAE;AAC7K,YAAM,iBAAiB,QAAQ,sBAAsB;AAAA,QACnD,SAAS,QAAQ,IAAI,eAAa,YAAY,YAAY,QAAQ,SAAS,IAAI,IAAI;AAAA,MACrF,GAAG;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA;AAAA,QAGA,iBAAiB,mBAAmB,CAAC,GAAI,mBAAmB,YAAY,QAAQ,IAAI,CAAC,GAAGA,WAAU,oBAAoB,aAAaA,MAAK,IAAIA,SAAQ,MAAS,GAAI,MAAS,GAAG,OAAO;AAAA,QACpL,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,UAAyB,cAAc,WAAW,WAAW;AAAA,UAAa;AAAA,UAAO;AAAA,UAAS;AAAA;AAAA;AAAA;AAAA,UAIrG,MAAM,QAAQF,cAAa,OAAO,MAAM,QAAQ,mBAAmB,OAAO;AAAA,QAAI;AAAA,MAChF,CAAC,CAAC;AACF,UAAI,kBAAkB,MAAM;AAC1B,oBAAY;AAAA,MACd;AAAA,IACF;AACA,UAAM,YAAY;AAAA,MAChB,YAAY,CAAC,gBAAgB;AAAA,MAC7B,UAAU,CAACA,WAAU;AAAA,MACrB,MAAM,CAAC,kBAAkBA,WAAU;AAAA,IACrC,EAAE,WAAW;AACb,UAAM,cAAc;AAAA,MAClB,YAAY,CAAC,kBAAkB;AAAA,MAC/B,UAAU,CAACD,SAAQ;AAAA,MACnB,MAAM,CAAC,oBAAoBA,SAAQ;AAAA,IACrC,EAAE,WAAW;AACb,UAAM,gBAAgB,SAAS,UAAU;AAAA,MACvC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,MAAM;AAAA,IACR,EAAE,WAAW;AACb,QAAI,cAAc,eAAe,CAAC,GAAG,WAAW,GAAG,WAAW,EAAE,SAAS,MAAM,GAAG,GAAG;AACnF,UAAI,QAAQ,cAAc,YAAY,UAAU,SAAS,MAAM,GAAG,GAAG;AACnE,oBAAY;AAAA,MACd,WAAW,QAAQ,cAAc,YAAY,YAAY,SAAS,MAAM,GAAG,GAAG;AAC5E,oBAAY;AAAA,MACd,OAAO;AACL,oBAAY,yBAAyB,aAAa;AAAA,UAChD,eAAe;AAAA,UACf,WAAW,YAAY,SAAS,MAAM,GAAG;AAAA,UACzC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,cAAc,eAAe,CAAC,uBAAuB,aAAa,SAAS,GAAG;AAChF,UAAI;AACJ,YAAM,gBAAgB;AACtB,UAAI,cAAc,SAAS,MAAM,GAAG,GAAG;AACrC,cAAM,eAAe;AAAA,MACvB;AACA,iBAAW,SAAS;AACpB,OAAC,wBAAwB,YAAY,QAAQ,SAAS,MAAM,QAAQ,sBAAsB,MAAM;AAAA,IAClG;AAAA,EACF;AACA,QAAM,gBAAgB;AAAA,IACpB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,KAAK;AAAA,IACL,oBAAoB,gBAAgB,SAAS,SAAY;AAAA,IACzD,UAAU,GAAG;AACX,eAAS,aAAa,QAAQ,SAAS,UAAU,CAAC;AAClD,yBAAmB,aAAa,QAAQ,mBAAmB,UAAU,CAAC;AACtE,oBAAc,CAAC;AAAA,IACjB;AAAA,EACF;AACA,aAAoB,wBAAI,iBAAiB,UAAU;AAAA,IACjD,OAAO;AAAA,IACP,cAAuB,wBAAI,cAAc;AAAA,MACvC;AAAA,MACA,UAAU,UAAU,QAAQ,aAAa;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AAID,IAAM,gBAAmC,kBAAW,SAASI,eAAc,OAAO,cAAc;AAC9F,QAAM;AAAA,IACJ;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,qBAAqB,UAAU,OAAO,WAAW,aAAa,OAAO,QAAQ,CAAC;AACpF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAU,kBAAW,gBAAgB;AACrC,QAAM;AAAA,IACJ;AAAA,IACA,OAAAD;AAAA,EACF,IAAI,YAAY;AAChB,QAAM,YAAY,aAAa,CAAC,KAAK,cAAc,mBAAmB,GAAG,CAAC;AAC1E,QAAM,WAAW,gBAAgBA;AACjC,QAAM,gBAAgB;AAAA,IACpB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,KAAK;AAAA,IACL,UAAU,WAAW,IAAI;AAAA,IACzB,eAAe,WAAW,KAAK;AAAA,IAC/B,QAAQ,GAAG;AACT,eAAS,WAAW,QAAQ,SAAS,QAAQ,CAAC;AAC9C,yBAAmB,WAAW,QAAQ,mBAAmB,QAAQ,CAAC;AAClE,iBAAWA,MAAK;AAAA,IAClB;AAAA,EACF;AACA,SAAO,UAAU,QAAQ,aAAa;AACxC,CAAC;AAGD,IAAME,aAAY;AAAA,EAChB,GAAGC;AACL;AAEA,IAAI,wBAAwB;AAC5B,IAAI,QAAQ;AACZ,IAAM,QAAQ;AAAA;AAAA;AAAA,EAEd,iBAAiB,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,GAAG,CAAC,IAAI;AAAA;AAC1D,SAAS,gBAAgB;AACvB,QAAM,CAAC,IAAI,KAAK,IAAU,gBAAS,MAAM,wBAAwB,MAAM,IAAI,MAAS;AACpF,QAAsB,MAAM;AAC1B,QAAI,MAAM,MAAM;AACd,YAAM,MAAM,CAAC;AAAA,IACf;AAAA,EAEF,GAAG,CAAC,CAAC;AACL,EAAM,iBAAU,MAAM;AACpB,4BAAwB;AAAA,EAC1B,GAAG,CAAC,CAAC;AACL,SAAO;AACT;AACA,IAAM,aAAaD,WAAU;AAQ7B,IAAM,QAAQ,cAAc;AAE5B,IAAI;AACJ,IAAI,MAAuC;AACzC,kBAA6B,oBAAI,IAAI;AACvC;AACA,SAAS,OAAO;AACd,MAAI;AACJ,WAAS,OAAO,UAAU,QAAQ,WAAW,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC3F,aAAS,IAAI,IAAI,UAAU,IAAI;AAAA,EACjC;AACA,QAAM,UAAU,kBAAkB,SAAS,KAAK,GAAG;AACnD,MAAI,GAAG,iBAAiB,kBAAkB,QAAQ,eAAe,IAAI,OAAO,IAAI;AAC9E,QAAI;AACJ,KAAC,kBAAkB,kBAAkB,QAAQ,gBAAgB,IAAI,OAAO;AACxE,YAAQ,KAAK,OAAO;AAAA,EACtB;AACF;AACA,SAAS,QAAQ;AACf,MAAI;AACJ,WAAS,QAAQ,UAAU,QAAQ,WAAW,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjG,aAAS,KAAK,IAAI,UAAU,KAAK;AAAA,EACnC;AACA,QAAM,UAAU,kBAAkB,SAAS,KAAK,GAAG;AACnD,MAAI,GAAG,kBAAkB,kBAAkB,QAAQ,gBAAgB,IAAI,OAAO,IAAI;AAChF,QAAI;AACJ,KAAC,kBAAkB,kBAAkB,QAAQ,gBAAgB,IAAI,OAAO;AACxE,YAAQ,MAAM,OAAO;AAAA,EACvB;AACF;AAMA,IAAM,gBAAmC,kBAAW,SAASE,eAAc,OAAO,KAAK;AACrF,QAAM;AAAA,IACJ,SAAS;AAAA,MACP;AAAA,MACA,UAAU;AAAA,QACR;AAAA,MACF;AAAA,MACA,gBAAgB;AAAA,QACd,OAAAC;AAAA,QACA,OAAAC;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,MACL;AAAA,MACA,GAAG;AAAA,IACL,IAAI,CAAC;AAAA,IACL,GAAG;AAAA,EACL,IAAI;AACJ,MAAI,MAAuC;AACzC,QAAI,CAAC,KAAK;AACR,WAAK,iDAAiD;AAAA,IACxD;AAAA,EACF;AACA,QAAM,aAAa,MAAM;AACzB,QAAM,CAACC,QAAO,QAAQ,IAAU,gBAAS,KAAK;AAG9C,QAAsB,MAAM;AAC1B,QAAI,CAAC,SAAU;AACf,UAAMA,SAAQC,kBAAiB,QAAQ,EAAE,cAAc;AACvD,QAAID,QAAO;AACT,eAAS,IAAI;AAAA,IACf;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AACb,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,QAAM,CAAC,MAAM,SAAS,IAAI,UAAU,MAAM,GAAG;AAC7C,QAAM,iBAAiB,SAAS,SAAS,SAAS;AAClD,MAAI,uBAAuB;AAC3B,MAAI,kBAAkBD,UAAS,QAAQA,OAAM,KAAK,CAAC,kBAAkBA,UAAS,QAAQA,OAAM,GAAG;AAC7F,2BAAuB;AAAA,EACzB;AAIA,QAAM,sBAAsB,cAAc;AAC1C,QAAM,kBAAkB,sBAAsB;AAC9C,QAAM,OAAO,QAAQ,KAAK,YAAY,KAAK;AAC3C,QAAM,OAAO,SAAS,IAAI,YAAY;AACtC,QAAM,gBAAgB,CAAC,CAAC;AACxB,QAAM,cAAc,wBAAwB,cAAc,QAAQ,WAAW;AAC7E,MAAI,cAAc,wBAAwB,cAAc,QAAQ,UAAU;AAC1E,MAAI,wBAAwBC,QAAO;AACjC,kBAAc,cAAc,QAAQ,SAAS;AAAA,EAC/C;AACA,QAAM,UAAUF,UAAS,OAAO,SAASA,OAAM,MAAM,OAAO,wBAAwBA,OAAM,IAAI;AAC9F,QAAM,UAAUA,UAAS,OAAO,SAASA,OAAM,MAAM,OAAO,wBAAwBA,OAAM,IAAI;AAC9F,QAAM,SAAS,KAAK,UAAU,OAAO,UAAU,QAAQ,QAAQ,QAAQ,OAAO,SAAS,UAAU,OAAO,QAAQ,IAAI,MAAM,SAAS,MAAM,OAAO,OAAO,SAAS,SAAS;AACzK,QAAM,WAAW;AAAA,IACf,KAAK,gBAAgB,mBAAmB;AAAA,IACxC,MAAM,gBAAgB,kBAAkB;AAAA,IACxC,QAAQ,gBAAgB,KAAK;AAAA,IAC7B,OAAO,gBAAgB,mBAAmB;AAAA,EAC5C,EAAE,IAAI;AACN,aAAoB,yBAAK,OAAO;AAAA,IAC9B,GAAG;AAAA,IACH,eAAe;AAAA,IACf;AAAA,IACA,OAAO,gBAAgB,QAAQ,QAAQ;AAAA,IACvC,QAAQ;AAAA,IACR,SAAS,SAAS,QAAQ,OAAO,SAAS,QAAQ,SAAS;AAAA,IAC3D,OAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,WAAW,GAAG;AAAA,MACf,CAAC,IAAI,GAAG,kBAAkB,gBAAgB,SAAS,iBAAiB,sBAAsB,IAAI;AAAA,MAC9F,WAAW,CAAC,UAAU,SAAS,EAAE,OAAO,OAAK,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,MAC1D,GAAG;AAAA,IACL;AAAA,IACA,UAAU,CAAC,sBAAsB,SAAkB,wBAAI,QAAQ;AAAA,MAC7D,UAAU,UAAU,aAAa;AAAA,MACjC,MAAM;AAAA,MACN;AAAA,MAGA,aAAa,uBAAuB,IAAI,IAAI;AAAA,MAC5C,GAAG;AAAA,IACL,CAAC,OAAgB,wBAAI,QAAQ;AAAA,MAC3B,QAAQ,uBAAuB,CAAC,IAAI,KAAK,OAAO;AAAA,MAChD,GAAG;AAAA,IACL,CAAC,OAAgB,wBAAI,YAAY;AAAA,MAC/B,IAAI;AAAA,MACJ,cAAuB,wBAAI,QAAQ;AAAA,QACjC,GAAG,CAAC;AAAA,QACJ,GAAG,mBAAmB,gBAAgB,KAAK;AAAA,QAC3C,OAAO,QAAQ;AAAA,QACf,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AAED,SAAS,qBAAqB;AAC5B,QAAM,MAAM,oBAAI,IAAI;AACpB,SAAO;AAAA,IACL,KAAK,OAAO,MAAM;AAChB,UAAI;AACJ,OAAC,WAAW,IAAI,IAAI,KAAK,MAAM,QAAQ,SAAS,QAAQ,cAAY,SAAS,IAAI,CAAC;AAAA,IACpF;AAAA,IACA,GAAG,OAAO,UAAU;AAClB,UAAI,CAAC,IAAI,IAAI,KAAK,GAAG;AACnB,YAAI,IAAI,OAAO,oBAAI,IAAI,CAAC;AAAA,MAC1B;AACA,UAAI,IAAI,KAAK,EAAE,IAAI,QAAQ;AAAA,IAC7B;AAAA,IACA,IAAI,OAAO,UAAU;AACnB,UAAI;AACJ,OAAC,YAAY,IAAI,IAAI,KAAK,MAAM,QAAQ,UAAU,OAAO,QAAQ;AAAA,IACnE;AAAA,EACF;AACF;AAEA,IAAM,sBAAyC,qBAAc,IAAI;AACjE,IAAM,sBAAyC,qBAAc,IAAI;AAMjE,IAAM,0BAA0B,MAAM;AACpC,MAAI;AACJ,WAAS,oBAA0B,kBAAW,mBAAmB,MAAM,OAAO,SAAS,kBAAkB,OAAO;AAClH;AAKA,IAAM,kBAAkB,MAAY,kBAAW,mBAAmB;AA2ElE,SAAS,gBAAgB,MAAM;AAC7B,SAAO,sBAAsB;AAC/B;AASA,IAAM,wBAAqC,gBAAgB,cAAc;AAgWzE,IAAM,OAAO,MAAM;AAAC;AACpB,IAAM,4BAA+C,qBAAc;AAAA,EACjE,OAAO;AAAA,EACP,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAAA,EACd,UAAU;AAAA,EACV,gBAAgB;AAClB,CAAC;AAiID,IAAM,gCAAmD,qBAAc;AAAA,EACrE,aAAa;AAAA,EACb,WAAW;AAAA,EACX,UAAU;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,iBAAiB;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,mBAAmB;AAAA,IACjB,SAAS;AAAA,EACX;AACF,CAAC;AA8RD,IAAM,gBAAgB;AAAA,EACpB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AACR;AACA,IAAM,aAAgC,kBAAW,SAASI,YAAW,OAAO,KAAK;AAC/E,QAAM,CAAC,MAAM,OAAO,IAAU,gBAAS;AACvC,QAAsB,MAAM;AAC1B,QAAI,SAAS,GAAG;AAMd,cAAQ,QAAQ;AAAA,IAClB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,YAAY;AAAA,IAChB;AAAA,IACA,UAAU;AAAA;AAAA,IAEV;AAAA,IACA,eAAe,OAAO,SAAY;AAAA,IAClC,CAAC,gBAAgB,aAAa,CAAC,GAAG;AAAA,IAClC,OAAO;AAAA,EACT;AACA,aAAoB,wBAAI,QAAQ;AAAA,IAC9B,GAAG;AAAA,IACH,GAAG;AAAA,EACL,CAAC;AACH,CAAC;AAED,IAAM,gBAAmC,qBAAc,IAAI;AAC3D,IAAM,OAAoB,gBAAgB,QAAQ;AAwOlD,IAAM,wBAA2C,kBAAW,SAASC,uBAAsB,OAAO,KAAK;AACrG,aAAoB,wBAAI,UAAU;AAAA,IAChC,GAAG;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,UAAU;AAAA,IACV,OAAO;AAAA,EACT,CAAC;AACH,CAAC;AA4YD,IAAI,YAAY;AAChB,SAAS,mBAAmB;AAC1B,QAAM,QAAQ,qBAAqB,KAAK,YAAY,CAAC;AACrD,QAAM,YAAY,SAAS,KAAK;AAEhC,QAAM,aAAa,KAAK,MAAM,SAAS,gBAAgB,sBAAsB,EAAE,IAAI,IAAI,SAAS,gBAAgB;AAChH,QAAM,cAAc,aAAa,gBAAgB;AACjD,QAAM,iBAAiB,OAAO,aAAa,SAAS,gBAAgB;AACpE,QAAM,UAAU,UAAU,OAAO,WAAW,UAAU,IAAI,IAAI,OAAO;AACrE,QAAM,UAAU,UAAU,MAAM,WAAW,UAAU,GAAG,IAAI,OAAO;AACnE,YAAU,WAAW;AACrB,MAAI,gBAAgB;AAClB,cAAU,WAAW,IAAI,iBAAiB;AAAA,EAC5C;AAIA,MAAI,OAAO;AACT,QAAI,uBAAuB;AAE3B,UAAM,eAAe,wBAAwB,OAAO,mBAAmB,OAAO,SAAS,sBAAsB,eAAe;AAC5H,UAAM,cAAc,yBAAyB,OAAO,mBAAmB,OAAO,SAAS,uBAAuB,cAAc;AAC5H,WAAO,OAAO,WAAW;AAAA,MACvB,UAAU;AAAA,MACV,KAAK,EAAE,UAAU,KAAK,MAAM,SAAS,KAAK;AAAA,MAC1C,MAAM,EAAE,UAAU,KAAK,MAAM,UAAU,KAAK;AAAA,MAC5C,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,SAAO,MAAM;AACX,WAAO,OAAO,WAAW;AAAA,MACvB,UAAU;AAAA,MACV,CAAC,WAAW,GAAG;AAAA,IACjB,CAAC;AACD,QAAI,OAAO;AACT,aAAO,OAAO,WAAW;AAAA,QACvB,UAAU;AAAA,QACV,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,MACT,CAAC;AACD,aAAO,SAAS,SAAS,OAAO;AAAA,IAClC;AAAA,EACF;AACF;AACA,IAAI,UAAU,MAAM;AAAC;AAQrB,IAAM,kBAAqC,kBAAW,SAASC,iBAAgB,OAAO,KAAK;AACzF,QAAM;AAAA,IACJ,aAAa;AAAA,IACb,GAAG;AAAA,EACL,IAAI;AACJ,QAAsB,MAAM;AAC1B,QAAI,CAAC,WAAY;AACjB;AACA,QAAI,cAAc,GAAG;AACnB,gBAAU,iBAAiB;AAAA,IAC7B;AACA,WAAO,MAAM;AACX;AACA,UAAI,cAAc,GAAG;AACnB,gBAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,GAAG,CAAC,UAAU,CAAC;AACf,aAAoB,wBAAI,OAAO;AAAA,IAC7B;AAAA,IACA,GAAG;AAAA,IACH,OAAO;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,GAAG,KAAK;AAAA,IACV;AAAA,EACF,CAAC;AACH,CAAC;AA8kBD,SAAS,uBAAuB,SAAS;AACvC,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,IAAI;AACJ,QAAM,aAAa,MAAM;AACzB,QAAM,UAAgB,cAAO,CAAC,CAAC;AAC/B,QAAM,CAAC,MAAM,IAAU,gBAAS,MAAM,mBAAmB,CAAC;AAC1D,QAAM,SAAS,wBAAwB,KAAK;AAC5C,MAAI,MAAuC;AACzC,UAAM,qBAAqB,aAAa;AACxC,QAAI,sBAAsB,CAAC,UAAU,kBAAkB,GAAG;AACxD,YAAM,qEAAqE,uEAAuE,UAAU;AAAA,IAC9J;AAAA,EACF;AACA,QAAM,CAAC,mBAAmB,oBAAoB,IAAU,gBAAS,aAAa,SAAS;AACvF,QAAM,eAAe,eAAe,CAACC,OAAM,OAAO,WAAW;AAC3D,YAAQ,QAAQ,YAAYA,QAAO,QAAQ;AAC3C,WAAO,KAAK,cAAc;AAAA,MACxB,MAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,wBAAoB,QAAQ,iBAAiBA,OAAM,OAAO,MAAM;AAAA,EAClE,CAAC;AACD,QAAM,OAAa,eAAQ,OAAO;AAAA,IAChC;AAAA,EACF,IAAI,CAAC,CAAC;AACN,QAAM,WAAiB,eAAQ,OAAO;AAAA,IACpC,WAAW,qBAAqB,aAAa,aAAa;AAAA,IAC1D,UAAU,aAAa,YAAY;AAAA,IACnC,cAAc,aAAa;AAAA,EAC7B,IAAI,CAAC,mBAAmB,aAAa,WAAW,aAAa,QAAQ,CAAC;AACtE,SAAa,eAAQ,OAAO;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,MAAM,cAAc,UAAU,QAAQ,YAAY,IAAI,CAAC;AAC9D;AAMA,SAASC,aAAY,SAAS;AAC5B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,sBAAsB,uBAAuB;AAAA,IACjD,GAAG;AAAA,IACH,UAAU;AAAA,MACR,WAAW;AAAA,MACX,UAAU;AAAA,MACV,GAAG,QAAQ;AAAA,IACb;AAAA,EACF,CAAC;AACD,QAAM,cAAc,QAAQ,eAAe;AAC3C,QAAM,mBAAmB,YAAY;AACrC,QAAM,CAAC,eAAe,eAAe,IAAU,gBAAS,IAAI;AAC5D,QAAM,CAAC,mBAAmB,qBAAqB,IAAU,gBAAS,IAAI;AACtE,QAAM,qBAAqB,oBAAoB,OAAO,SAAS,iBAAiB;AAChF,QAAM,eAAe,sBAAsB;AAC3C,QAAM,kBAAwB,cAAO,IAAI;AACzC,QAAM,OAAO,gBAAgB;AAC7B,QAAsB,MAAM;AAC1B,QAAI,cAAc;AAChB,sBAAgB,UAAU;AAAA,IAC5B;AAAA,EACF,GAAG,CAAC,YAAY,CAAC;AACjB,QAAM,WAAW,YAAc;AAAA,IAC7B,GAAG;AAAA,IACH,UAAU;AAAA,MACR,GAAG;AAAA,MACH,GAAI,qBAAqB;AAAA,QACvB,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,uBAA6B,mBAAY,UAAQ;AACrD,UAAM,4BAA4B,UAAU,IAAI,IAAI;AAAA,MAClD,uBAAuB,MAAM,KAAK,sBAAsB;AAAA,MACxD,gBAAgB,MAAM,KAAK,eAAe;AAAA,MAC1C,gBAAgB;AAAA,IAClB,IAAI;AAGJ,0BAAsB,yBAAyB;AAC/C,aAAS,KAAK,aAAa,yBAAyB;AAAA,EACtD,GAAG,CAAC,SAAS,IAAI,CAAC;AAClB,QAAM,eAAqB,mBAAY,UAAQ;AAC7C,QAAI,UAAU,IAAI,KAAK,SAAS,MAAM;AACpC,sBAAgB,UAAU;AAC1B,sBAAgB,IAAI;AAAA,IACtB;AAIA,QAAI,UAAU,SAAS,KAAK,UAAU,OAAO,KAAK,SAAS,KAAK,UAAU,YAAY;AAAA;AAAA;AAAA,IAItF,SAAS,QAAQ,CAAC,UAAU,IAAI,GAAG;AACjC,eAAS,KAAK,aAAa,IAAI;AAAA,IACjC;AAAA,EACF,GAAG,CAAC,SAAS,IAAI,CAAC;AAClB,QAAM,OAAa,eAAQ,OAAO;AAAA,IAChC,GAAG,SAAS;AAAA,IACZ;AAAA,IACA;AAAA,IACA,cAAc;AAAA,EAChB,IAAI,CAAC,SAAS,MAAM,cAAc,oBAAoB,CAAC;AACvD,QAAM,WAAiB,eAAQ,OAAO;AAAA,IACpC,GAAG,SAAS;AAAA,IACZ;AAAA,EACF,IAAI,CAAC,SAAS,UAAU,YAAY,CAAC;AACrC,QAAM,UAAgB,eAAQ,OAAO;AAAA,IACnC,GAAG;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,UAAU,MAAM,UAAU,QAAQ,WAAW,CAAC;AACnD,QAAsB,MAAM;AAC1B,gBAAY,QAAQ,QAAQ,kBAAkB;AAC9C,UAAM,OAAO,QAAQ,OAAO,SAAS,KAAK,SAAS,QAAQ,KAAK,CAAAC,UAAQA,MAAK,OAAO,MAAM;AAC1F,QAAI,MAAM;AACR,WAAK,UAAU;AAAA,IACjB;AAAA,EACF,CAAC;AACD,SAAa,eAAQ,OAAO;AAAA,IAC1B,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,UAAU,MAAM,UAAU,OAAO,CAAC;AACzC;;;;AQn3FA,IAAIC,iBAAgB,SAAhBA,cAAyBC,GAAGC,GAAG;AAC/BF,mBAAgBG,OAAOC,kBAClB;IAAEC,WAAW,CAAA;EAAG,aAAaC,SAAS,SAAUL,IAAGC,IAAG;AAAED,IAAAA,GAAEI,YAAYH;EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAASK,KAAKL,GAAG,KAAIC,OAAOK,UAAUC,eAAeC,KAAKR,IAAGK,CAAC,EAAGN,CAAAA,GAAEM,CAAC,IAAIL,GAAEK,CAAC;;AACjG,SAAOP,eAAcC,GAAGC,CAAC;AAC7B;AAEO,SAASS,UAAUV,GAAGC,GAAG;AAC5B,MAAI,OAAOA,MAAM,cAAcA,MAAM,KACjC,OAAM,IAAIU,UAAU,yBAAyBC,OAAOX,CAAC,IAAI,+BAA+B;AAC5FF,iBAAcC,GAAGC,CAAC;AAClB,WAASY,KAAK;AAAE,SAAKC,cAAcd;EAAG;AACtCA,IAAEO,YAAYN,MAAM,OAAOC,OAAOa,OAAOd,CAAC,KAAKY,GAAGN,YAAYN,EAAEM,WAAW,IAAIM,GAAE;AACrF;AAEO,IAAIG,UAAW,SAAXA,WAAsB;AAC7BA,YAAWd,OAAOe,UAAU,SAASD,UAASE,GAAG;AAC7C,aAASC,GAAGC,IAAI,GAAGC,IAAIC,UAAUC,QAAQH,IAAIC,GAAGD,KAAK;AACjDD,UAAIG,UAAUF,CAAC;AACf,eAASd,KAAKa,EAAG,KAAIjB,OAAOK,UAAUC,eAAeC,KAAKU,GAAGb,CAAC,EAAGY,GAAEZ,CAAC,IAAIa,EAAEb,CAAC;IAC/E;AACA,WAAOY;;AAEX,SAAOF,QAASQ,MAAM,MAAMF,SAAS;AACzC;AA6KO,SAASG,cAAcC,IAAIC,MAAMC,MAAM;AAC1C,MAAIA,QAAQN,UAAUC,WAAW,EAAG,UAASH,IAAI,GAAGS,IAAIF,KAAKJ,QAAQO,IAAIV,IAAIS,GAAGT,KAAK;AACjF,QAAIU,MAAM,EAAEV,KAAKO,OAAO;AACpB,UAAI,CAACG,GAAIA,MAAKzB,MAAME,UAAUwB,MAAMtB,KAAKkB,MAAM,GAAGP,CAAC;AACnDU,SAAGV,CAAC,IAAIO,KAAKP,CAAC;IAClB;EACJ;AACA,SAAOM,GAAGM,OAAOF,MAAMzB,MAAME,UAAUwB,MAAMtB,KAAKkB,IAAI,CAAC;AAC3D;ACrNM,IAAA,oBAAsD,SAAUM,KAK7C;AAJvB,MAAA,KAAAA,IAAA,oBAAA,qBAAkB,OAAA,SAAG,QAAK,IAC1B,KAAgBA,IAAA,UAAhB,WAAQ,OAAA,SAAG,QAAK,IAChB,YAASA,IAAA,WACT,WAAQA,IAAA;AAER,MAAM,YAAY,qBACd,gBACA,cAAA,OAAc,WAAW,cAAc,EAAE;AAE7C,SACE,cAAAC,QACE,cAAA,OAAA,EAAA,WACA,MAAK,UAAQ,cACD,WACD,cAAA,OAAM,GAEhB,QAAQ;AAGf;ACfA,IAAM,wBAAwB,SAC5B,gBACA,aAAoB;AAEpB,MAAM,UAAM,sBAA8B,IAAI;AAC9C,MAAM,wBAAoB,sBAAO,cAAc;AAC/C,oBAAkB,UAAU;AAC5B,MAAM,yBAAqB,2BACzB,SAAC,OAAiB;;AAChB,QAAI,IAAI,WAAW,CAAC,IAAI,QAAQ,SAAS,MAAM,MAAc,GAAG;AAC9D,UACE,EACE,eACA,MAAM,kBAAkB,eACxB,MAAM,OAAO,UAAU,SAAS,WAAW,IAE7C;AACA,SAAAD,MAAA,kBAAkB,aAAU,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,mBAAA,KAAK;;;EAGvC,GACA,CAAC,WAAW,CAAC;AAEf,+BAAU,WAAA;AACR,aAAS,iBAAiB,aAAa,kBAAkB;AACzD,WAAO,WAAA;AACL,eAAS,oBAAoB,aAAa,kBAAkB;IAC9D;EACF,GAAG,CAAC,kBAAkB,CAAC;AACvB,SAAO;AACT;AAEO,IAAM,sBAA0D,SAACA,KAOvE;AANC,MAAA,WAAQA,IAAA,UACR,iBAAcA,IAAA,gBACd,YAASA,IAAA,WACT,eAAYA,IAAA,cACZ,QAAKA,IAAA,OACL,cAAWA,IAAA;AAEX,MAAM,YAAY,sBAAsB,gBAAgB,WAAW;AACnE,SACE,cAAAC,QAAA,cAAA,OAAA,EACE,WACA,OACA,KAAK,SAAC,MAA2B;AAC/B,cAAU,UAAU;AACpB,QAAI,cAAc;AAChB,mBAAa,UAAU;;EAE3B,EAAC,GAEA,QAAQ;AAGf;ACGA,IAAY;CAAZ,SAAYC,UAAO;AACjB,EAAAA,SAAA,SAAA,IAAA;AACA,EAAAA,SAAA,WAAA,IAAA;AACA,EAAAA,SAAA,WAAA,IAAA;AACA,EAAAA,SAAA,YAAA,IAAA;AACA,EAAAA,SAAA,QAAA,IAAA;AACA,EAAAA,SAAA,UAAA,IAAA;AACA,EAAAA,SAAA,MAAA,IAAA;AACA,EAAAA,SAAA,KAAA,IAAA;AACA,EAAAA,SAAA,OAAA,IAAA;AACA,EAAAA,SAAA,OAAA,IAAA;AACA,EAAAA,SAAA,KAAA,IAAA;AACA,EAAAA,SAAA,QAAA,IAAA;AACA,EAAAA,SAAA,WAAA,IAAA;AACA,EAAAA,SAAA,GAAA,IAAA;AACF,GAfY,YAAA,UAeX,CAAA,EAAA;AAED,SAAS,iBAAc;AAErB,MAAM,QAAS,OAAO,WAAW,cAC7B,SACA;AAKJ,SAAO;AACT;AAEO,IAAM,2BAA2B;AAIxC,IAAM,6BAA6B;AAI7B,SAAU,QAAQ,OAAqC;AAC3D,MAAI,SAAS,MAAM;AACjB,WAAO,oBAAI,KAAI;;AAGjB,MAAM,IAAI,OAAO,UAAU,WAAW,SAAS,KAAK,IAAI,OAAO,KAAK;AACpE,SAAOC,SAAQ,CAAC,IAAI,IAAI,oBAAI,KAAI;AAClC;AAYM,SAAU,UACd,OACA,YACA,QACA,eACA,SAAc;;AAEd,MAAI,aAAa;AACjB,MAAM,eACJ,gBAAgB,MAAM,KAAK,gBAAgB,iBAAgB,CAAE;AAC/D,MAAI,0BAA0B;AAC9B,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC7B,eAAW,QAAQ,SAAC,IAAE;AACpB,UAAM,eAAe,MAAM,OAAO,IAAI,oBAAI,KAAI,GAAI;QAChD,QAAQ;QACR,6BAA6B;QAC7B,8BAA8B;MAC/B,CAAA;AACD,UAAI,eAAe;AACjB,kCACEA,SAAQ,cAAc,OAAO,KAC7B,UAAU,WAAW,cAAc,IAAI,MAAM;;AAEjD,UAAIA,SAAQ,cAAc,OAAO,KAAK,yBAAyB;AAC7D,qBAAa;;IAEjB,CAAC;AACD,WAAO;;AAGT,eAAa,MAAM,OAAO,YAAY,oBAAI,KAAI,GAAI;IAChD,QAAQ;IACR,6BAA6B;IAC7B,8BAA8B;EAC/B,CAAA;AAED,MAAI,eAAe;AACjB,8BACEA,SAAQ,UAAU,KAClB,UAAU,WAAW,YAAY,YAAY,MAAM;aAC5C,CAACA,SAAQ,UAAU,GAAG;AAC/B,QAAM,aAAUH,MAAA,WAAW,MAAM,0BAA0B,OAAK,QAAAA,QAAA,SAAAA,MAAA,CAAA,GAC7D,IAAI,SAAU,WAAS;AACtB,UAAM,iBAAiB,UAAU,CAAC;AAClC,UAAI,mBAAmB,OAAO,mBAAmB,KAAK;AAEpD,YAAM,gBAAgB,eAAe,cAAc;AACnD,eAAO,eACH,cAAc,WAAW,aAAa,UAAU,IAChD;;AAEN,aAAO;IACT,CAAC,EACA,KAAK,EAAE;AAEV,QAAI,MAAM,SAAS,GAAG;AACpB,mBAAa,MAAM,OAAO,SAAO,MAAM,GAAG,MAAM,MAAM,GAAG,oBAAI,KAAI,GAAI;QACnE,6BAA6B;QAC7B,8BAA8B;MAC/B,CAAA;;AAGH,QAAI,CAACG,SAAQ,UAAU,GAAG;AACxB,mBAAa,IAAI,KAAK,KAAK;;;AAI/B,SAAOA,SAAQ,UAAU,KAAK,0BAA0B,aAAa;AACvE;AAYgB,SAAAA,SAAQ,MAAY,SAAc;AAKhD,SAAOC,QAAY,IAAI,KAAK,CAAC,SAAS,MAAM,YAAO,QAAP,YAAO,SAAP,UAAW,oBAAI,KAAK,UAAU,CAAC;AAC7E;SAYgB,WACd,MACA,WACA,QAAe;AAEf,MAAI,WAAW,MAAM;AACnB,WAAO,OAAO,MAAM,WAAW;MAC7B,6BAA6B;MAC7B,8BAA8B;IAC/B,CAAA;;AAEH,MAAI,YAAY,SAAS,gBAAgB,MAAM,IAAI;AACnD,MAAI,UAAU,CAAC,WAAW;AACxB,YAAQ,KACN,2DAAA,OAA2D,QAAM,KAAA,CAAK;;AAG1E,MACE,CAAC,aACD,CAAC,CAAC,iBAAgB,KAClB,CAAC,CAAC,gBAAgB,iBAAgB,CAAE,GACpC;AACA,gBAAY,gBAAgB,iBAAgB,CAAE;;AAEhD,SAAO,OAAO,MAAM,WAAW;IAC7B,QAAQ;IACR,6BAA6B;IAC7B,8BAA8B;EAC/B,CAAA;AACH;AASgB,SAAA,eACd,MACAJ,KAA0E;MAAxE,aAAUA,IAAA,YAAE,SAAMA,IAAA;AAEpB,MAAM,YACJ,MAAM,QAAQ,UAAU,KAAK,WAAW,SAAS,IAC7C,WAAW,CAAC,IACZ;AAEN,SAAQ,QAAQ,WAAW,MAAM,WAAW,MAAM,KAAM;AAC1D;SAUgB,oBACd,WACA,SACA,OAAyD;AAEzD,MAAI,CAAC,WAAW;AACd,WAAO;;AAGT,MAAM,qBAAqB,eAAe,WAAW,KAAK;AAC1D,MAAM,mBAAmB,UAAU,eAAe,SAAS,KAAK,IAAI;AAEpE,SAAO,GAAG,OAAA,oBAAwB,KAAA,EAAA,OAAA,gBAAgB;AACpD;AASgB,SAAA,wBACd,OACA,OAAyD;AAEzD,MAAI,EAAC,UAAA,QAAA,UAAA,SAAA,SAAA,MAAO,SAAQ;AAClB,WAAO;;AAGT,MAAM,qBAAqB,MAAM,CAAC,IAAI,eAAe,MAAM,CAAC,GAAG,KAAK,IAAI;AACxE,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO;;AAGT,MAAI,MAAM,WAAW,KAAK,MAAM,CAAC,GAAG;AAClC,QAAM,sBAAsB,eAAe,MAAM,CAAC,GAAG,KAAK;AAC1D,WAAO,GAAG,OAAA,oBAAuB,IAAA,EAAA,OAAA,mBAAmB;;AAGtD,MAAM,kBAAkB,MAAM,SAAS;AACvC,SAAO,GAAG,OAAA,oBAAwB,KAAA,EAAA,OAAA,iBAAe,GAAA;AACnD;AAUgB,SAAA,QACd,MACAA,KAAoC;AAAlC,MAAA,KAAAA,IAAA,MAAA,OAAI,OAAA,SAAG,IAAC,IAAE,KAAAA,IAAA,QAAA,SAAS,OAAA,SAAA,IAAC,IAAE,KAAAA,IAAA,QAAA,SAAM,OAAA,SAAG,IAAC;AAElC,SAAO,SAAS,WAAW,WAAW,MAAM,MAAM,GAAG,MAAM,GAAG,IAAI;AACpE;AAyBM,SAAU,QAAQ,MAAU;AAChC,SAAO,WAAW,IAAI;AACxB;AASgB,SAAA,iBAAiB,KAAW,QAAe;AACzD,SAAO,WAAW,KAAK,OAAO,MAAM;AACtC;AAUM,SAAU,cAAc,MAAU;AACtC,SAAO,WAAW,IAAI;AACxB;SAUgB,eACd,MACA,QACA,kBAAsB;AAEtB,MAAM,YAAY,SACd,gBAAgB,MAAM,IACtB,gBAAgB,iBAAgB,CAAE;AACtC,SAAO,YAAY,MAAM;IACvB,QAAQ;IACR,cAAc;EACf,CAAA;AACH;AAQM,SAAU,gBAAgB,MAAU;AACxC,SAAO,aAAa,IAAI;AAC1B;AAQM,SAAU,eAAe,MAAU;AACvC,SAAO,YAAY,IAAI;AACzB;AAQM,SAAU,kBAAkB,MAAU;AAC1C,SAAO,eAAe,IAAI;AAC5B;SAOgB,kBAAe;AAC7B,SAAO,WAAW,QAAO,CAAE;AAC7B;AASM,SAAU,YAAY,MAAU;AACpC,SAAO,SAAS,IAAI;AACtB;AAQM,SAAU,aAAa,MAAU;AACrC,SAAO,UAAU,IAAI;AACvB;AAQM,SAAU,cAAc,MAAU;AACtC,SAAO,WAAW,IAAI;AACxB;AA+BgB,SAAAK,YAAW,OAAoB,OAAkB;AAC/D,MAAI,SAAS,OAAO;AAClB,WAAOC,WAAa,OAAO,KAAK;SAC3B;AACL,WAAO,CAAC,SAAS,CAAC;;AAEtB;AASgB,SAAAC,aAAY,OAAoB,OAAmB;AACjE,MAAI,SAAS,OAAO;AAClB,WAAOC,YAAc,OAAO,KAAK;SAC5B;AACL,WAAO,CAAC,SAAS,CAAC;;AAEtB;AASgB,SAAAC,eAAc,OAAoB,OAAkB;AAClE,MAAI,SAAS,OAAO;AAClB,WAAOC,cAAgB,OAAO,KAAK;SAC9B;AACL,WAAO,CAAC,SAAS,CAAC;;AAEtB;AASgB,SAAAC,WAAU,OAAqB,OAAmB;AAChE,MAAI,SAAS,OAAO;AAClB,WAAOC,UAAY,OAAO,KAAK;SAC1B;AACL,WAAO,CAAC,SAAS,CAAC;;AAEtB;AASgB,SAAAC,SACd,OACA,OAA8B;AAE9B,MAAI,SAAS,OAAO;AAClB,WAAOC,QAAU,OAAO,KAAK;SACxB;AACL,WAAO,CAAC,SAAS,CAAC;;AAEtB;SAUgB,aACd,KACA,WACA,SAAa;AAEb,MAAI;AACJ,MAAM,QAAQ,WAAW,SAAS;AAClC,MAAM,MAAM,SAAS,OAAO;AAE5B,MAAI;AACF,YAAQ,iBAAiB,KAAK,EAAE,OAAO,IAAG,CAAE;WACrC,KAAK;AACZ,YAAQ;;AAEV,SAAO;AACT;AAwBgB,SAAA,eACd,YACA,YAAqB;AAErB,MAAM,QAAQ,eAAc;AAE5B,MAAI,CAAC,MAAM,gBAAgB;AACzB,UAAM,iBAAiB,CAAA;;AAEzB,QAAM,eAAe,UAAU,IAAI;AACrC;AAOM,SAAU,iBAAiB,YAAmB;AAClD,MAAM,QAAQ,eAAc;AAE5B,QAAM,eAAe;AACvB;SAOgB,mBAAgB;AAC9B,MAAM,QAAQ,eAAc;AAE5B,SAAO,MAAM;AACf;AAQM,SAAU,gBAAgB,YAAmB;AACjD,MAAI,OAAO,eAAe,UAAU;AAElC,QAAM,QAAQ,eAAc;AAE5B,WAAO,MAAM,iBAAiB,MAAM,eAAe,UAAU,IAAI;SAC5D;AAEL,WAAO;;AAEX;SAUgB,4BACd,MACA,YACA,QAAe;AAEf,SAAO,WAAW,WAAW,MAAM,QAAQ,MAAM,CAAC;AACpD;AASgB,SAAA,sBAAsB,MAAY,QAAe;AAC/D,SAAO,WAAW,MAAM,UAAU,MAAM;AAC1C;AASgB,SAAA,wBAAwB,MAAY,QAAe;AACjE,SAAO,WAAW,MAAM,OAAO,MAAM;AACvC;AASgB,SAAA,iBAAiB,OAAe,QAAe;AAC7D,SAAO,WAAW,SAAS,QAAO,GAAI,KAAK,GAAG,QAAQ,MAAM;AAC9D;AASgB,SAAA,sBAAsB,OAAe,QAAe;AAClE,SAAO,WAAW,SAAS,QAAO,GAAI,KAAK,GAAG,OAAO,MAAM;AAC7D;AASgB,SAAA,wBACd,SACA,QAAe;AAEf,SAAO,WAAW,WAAW,QAAO,GAAI,OAAO,GAAG,OAAO,MAAM;AACjE;AAsBgB,SAAA,cACd,KACAd,KAQyB;MARzB,KAQuBA,QAAA,SAAA,CAAA,IAAEA,KAPvB,UAAO,GAAA,SACP,UAAO,GAAA,SACP,eAAY,GAAA,cACZ,uBAAoB,GAAA,sBACpB,eAAY,GAAA,cACZ,uBAAoB,GAAA,sBACpB,aAAU,GAAA;AAGZ,SACE,cAAc,KAAK,EAAE,SAAS,QAAO,CAAE,KACtC,gBACC,aAAa,KAAK,SAAC,aAAW;AAC5B,QAAI,uBAAuB,MAAM;AAC/B,aAAOW,WAAU,KAAK,WAAW;WAC5B;AACL,aAAOA,WAAU,KAAK,YAAY,IAAI;;EAE1C,CAAC,KACF,wBACC,qBAAqB,KAAK,SAACX,KAAc;QAAZ,QAAKA,IAAA,OAAE,MAAGA,IAAA;AACrC,WAAA,iBAAiB,KAAK,EAAE,OAAO,IAAG,CAAE;EAApC,CAAqC,KAExC,gBACC,CAAC,aAAa,KAAK,SAAC,aAAgB;AAAA,WAAAW,WAAU,KAAK,WAAW;EAA1B,CAA2B,KAChE,wBACC,CAAC,qBAAqB,KAAK,SAACX,KAAc;QAAZ,QAAKA,IAAA,OAAE,MAAGA,IAAA;AACtC,WAAA,iBAAiB,KAAK,EAAE,OAAO,IAAG,CAAE;EAApC,CAAqC,KAExC,cAAc,CAAC,WAAW,QAAQ,GAAG,CAAC,KACvC;AAEJ;AASgB,SAAA,cACd,KACAA,KAGwE;AAHxE,MAAA,KAAAA,QAAA,SAGsE,CAAA,IAAEA,KAFtE,eAAY,GAAA,cACZ,uBAAoB,GAAA;AAGtB,MAAI,wBAAwB,qBAAqB,SAAS,GAAG;AAC3D,WAAO,qBAAqB,KAAK,SAACA,KAAc;UAAZ,QAAKA,IAAA,OAAE,MAAGA,IAAA;AAC5C,aAAA,iBAAiB,KAAK,EAAE,OAAO,IAAG,CAAE;IAApC,CAAqC;;AAGzC,SACG,gBACC,aAAa,KAAK,SAAC,aAAW;;AAC5B,QAAI,uBAAuB,MAAM;AAC/B,aAAOW,WAAU,KAAK,WAAW;WAC5B;AACL,aAAOA,WAAU,MAAKX,MAAA,YAAY,UAAQ,QAAAA,QAAA,SAAAA,MAAA,oBAAI,KAAI,CAAE;;EAExD,CAAC,KACH;AAEJ;AAEgB,SAAA,gBACd,OACAA,KASM;AATN,MAAA,KAAAA,QAAA,SASI,CAAA,IAAEA,KARJ,UAAO,GAAA,SACP,UAAO,GAAA,SACP,eAAY,GAAA,cACZ,eAAY,GAAA,cACZ,aAAU,GAAA;AAMZ,SACE,cAAc,OAAO;IACnB,SAAS,UAAU,aAAa,OAAO,IAAI;IAC3C,SAAS,UAAU,WAAW,OAAO,IAAI;GAC1C,MACD,iBAAY,QAAZ,iBAAA,SAAA,SAAA,aAAc,KAAK,SAAC,aAAW;AAC7B,WAAAO,aACE,OACA,uBAAuB,OAAO,cAAc,YAAY,IAAI;EAF9D,CAGC,MAEF,gBACC,CAAC,aAAa,KAAK,SAAC,aAAgB;AAAA,WAAAA,aAAY,OAAO,WAAW;EAA9B,CAA+B,KACpE,cAAc,CAAC,WAAW,QAAQ,KAAK,CAAC,KACzC;AAEJ;AAEM,SAAU,eACd,WACA,SACA,GACA,KAAS;AAET,MAAM,gBAAgB,QAAQ,SAAS;AACvC,MAAM,iBAAiB,SAAS,SAAS;AACzC,MAAM,cAAc,QAAQ,OAAO;AACnC,MAAM,eAAe,SAAS,OAAO;AACrC,MAAM,UAAU,QAAQ,GAAG;AAC3B,MAAI,kBAAkB,eAAe,kBAAkB,SAAS;AAC9D,WAAO,kBAAkB,KAAK,KAAK;aAC1B,gBAAgB,aAAa;AACtC,WACG,YAAY,iBAAiB,kBAAkB,KAC/C,YAAY,eAAe,gBAAgB,KAC3C,UAAU,eAAe,UAAU;;AAGxC,SAAO;AACT;AAOgB,SAAA,oBACd,MACAP,KAQM;AARN,MAAA,KAAAA,QAAA,SAQI,CAAA,IAAEA,KAPJ,UAAO,GAAA,SACP,UAAO,GAAA,SACP,eAAY,GAAA,cACZ,eAAY,GAAA;AAMd,SACE,cAAc,MAAM,EAAE,SAAS,QAAO,CAAE,KACvC,gBACC,aAAa,KAAK,SAAC,cAAY;AAC7B,WAAAO,aACE,wBAAwB,OAAO,eAAe,aAAa,MAC3D,IAAI;EAFN,CAGC,KAEJ,gBACC,CAAC,aAAa,KAAK,SAAC,cAAiB;AAAA,WAAAA,aAAY,cAAc,IAAI;EAA9B,CAA+B,KACtE;AAEJ;AAEgB,SAAA,kBACd,SACAP,KASM;AATN,MAAA,KAAAA,QAAA,SASI,CAAA,IAAEA,KARJ,UAAO,GAAA,SACP,UAAO,GAAA,SACP,eAAY,GAAA,cACZ,eAAY,GAAA,cACZ,aAAU,GAAA;AAMZ,SACE,cAAc,SAAS,EAAE,SAAS,QAAO,CAAE,MAC3C,iBAAY,QAAZ,iBAAA,SAAA,SAAA,aAAc,KAAK,SAAC,aAAW;AAC7B,WAAAS,eACE,SACA,uBAAuB,OAAO,cAAc,YAAY,IAAI;EAF9D,CAGC,MAEF,gBACC,CAAC,aAAa,KAAK,SAAC,aAAW;AAC7B,WAAAA,eAAc,SAAS,WAAW;EAAlC,CAAmC,KAEtC,cAAc,CAAC,WAAW,QAAQ,OAAO,CAAC,KAC3C;AAEJ;SAEgB,cACd,MACA,OACA,KAAiB;AAEjB,MAAI,CAAC,SAAS,CAAC;AAAK,WAAO;AAC3B,MAAI,CAACL,QAAY,KAAK,KAAK,CAACA,QAAY,GAAG;AAAG,WAAO;AACrD,MAAM,YAAY,QAAQ,KAAK;AAC/B,MAAM,UAAU,QAAQ,GAAG;AAE3B,SAAO,aAAa,QAAQ,WAAW;AACzC;AAEgB,SAAA,eACd,MACAJ,KASM;AATN,MAAA,KAAAA,QAAA,SASI,CAAA,IAAEA,KARJ,UAAO,GAAA,SACP,UAAO,GAAA,SACP,eAAY,GAAA,cACZ,eAAY,GAAA,cACZ,aAAU,GAAA;AAMZ,MAAM,OAAO,IAAI,KAAK,MAAM,GAAG,CAAC;AAChC,SACE,cAAc,MAAM;IAClB,SAAS,UAAU,YAAY,OAAO,IAAI;IAC1C,SAAS,UAAU,UAAU,OAAO,IAAI;GACzC,MACD,iBAAY,QAAZ,iBAAA,SAAA,SAAA,aAAc,KAAK,SAAC,aAAW;AAC7B,WAAAK,YACE,MACA,uBAAuB,OAAO,cAAc,YAAY,IAAI;EAF9D,CAGC,MAEF,gBACC,CAAC,aAAa,KAAK,SAAC,aAAgB;AAAA,WAAAA,YAAW,MAAM,WAAW;EAA5B,CAA6B,KAClE,cAAc,CAAC,WAAW,QAAQ,IAAI,CAAC,KACxC;AAEJ;AAEM,SAAU,iBACd,WACA,SACA,GACA,KAAS;AAET,MAAM,gBAAgB,QAAQ,SAAS;AACvC,MAAM,mBAAmB,WAAW,SAAS;AAC7C,MAAM,cAAc,QAAQ,OAAO;AACnC,MAAM,iBAAiB,WAAW,OAAO;AACzC,MAAM,UAAU,QAAQ,GAAG;AAC3B,MAAI,kBAAkB,eAAe,kBAAkB,SAAS;AAC9D,WAAO,oBAAoB,KAAK,KAAK;aAC5B,gBAAgB,aAAa;AACtC,WACG,YAAY,iBAAiB,oBAAoB,KACjD,YAAY,eAAe,kBAAkB,KAC7C,UAAU,eAAe,UAAU;;AAGxC,SAAO;AACT;AAEgB,SAAA,cACd,KACAL,KAAyE;;AAAzE,MAAA,KAAAA,QAAA,SAAuE,CAAA,IAAEA,KAAvE,UAAO,GAAA,SAAE,UAAO,GAAA;AAElB,UACE,KAAE,WAAW,yBAAyB,KAAK,OAAO,IAAI,KACnD,WAAW,yBAAyB,KAAK,OAAO,IAAI,OACvD,QAAA,OAAA,SAAA,KAAA;AAEJ;AAEgB,SAAA,aAAa,MAAY,OAAa;AACpD,SAAO,MAAM,KACX,SAAC,UAAQ;AACP,WAAA,SAAS,QAAQ,MAAM,SAAS,IAAI,KACpC,WAAW,QAAQ,MAAM,WAAW,IAAI,KACxC,WAAW,QAAQ,MAAM,WAAW,IAAI;EAFxC,CAEyC;AAE/C;AAUgB,SAAA,eACd,MACAA,KAOM;MAPN,KAOIA,QAAA,SAAA,CAAA,IAAEA,KANJ,eAAY,GAAA,cACZ,eAAY,GAAA,cACZ,aAAU,GAAA;AAMZ,SACG,gBAAgB,aAAa,MAAM,YAAY,KAC/C,gBAAgB,CAAC,aAAa,MAAM,YAAY,KAChD,cAAc,CAAC,WAAW,IAAI,KAC/B;AAEJ;AAEgB,SAAA,sBACd,MACAA,KAAoE;MAAlE,UAAOA,IAAA,SAAE,UAAOA,IAAA;AAElB,MAAI,CAAC,WAAW,CAAC,SAAS;AACxB,UAAM,IAAI,MAAM,yCAAyC;;AAE3D,MAAI,WAAW,QAAO;AACtB,aAAW,SAAS,UAAU,SAAS,IAAI,CAAC;AAC5C,aAAW,WAAW,UAAU,WAAW,IAAI,CAAC;AAChD,aAAW,WAAW,UAAU,WAAW,IAAI,CAAC;AAEhD,MAAIe,OAAM,QAAO;AACjB,EAAAA,OAAM,SAASA,MAAK,SAAS,OAAO,CAAC;AACrC,EAAAA,OAAM,WAAWA,MAAK,WAAW,OAAO,CAAC;AACzC,EAAAA,OAAM,WAAWA,MAAK,WAAW,OAAO,CAAC;AAEzC,MAAIC,OAAM,QAAO;AACjB,EAAAA,OAAM,SAASA,MAAK,SAAS,OAAO,CAAC;AACrC,EAAAA,OAAM,WAAWA,MAAK,WAAW,OAAO,CAAC;AACzC,EAAAA,OAAM,WAAWA,MAAK,WAAW,OAAO,CAAC;AAEzC,MAAI;AACJ,MAAI;AACF,YAAQ,CAAC,iBAAiB,UAAU,EAAE,OAAOD,MAAK,KAAKC,KAAG,CAAE;WACrD,KAAK;AACZ,YAAQ;;AAEV,SAAO;AACT;AAEgB,SAAA,oBACd,KACAhB,KAG2D;AAH3D,MAAA,KAAAA,QAAA,SAGyD,CAAA,IAAEA,KAFzD,UAAO,GAAA,SACP,eAAY,GAAA;AAGd,MAAM,gBAAgB,UAAU,KAAK,CAAC;AACtC,SACG,WAAW,2BAA2B,SAAS,aAAa,IAAI,KAChE,gBACC,aAAa,MACX,SAAC,aAAW;AACV,WAAA,2BAA2B,aAAa,aAAa,IAAI;EAAzD,CAA0D,KAEhE;AAEJ;AAEgB,SAAA,mBACd,KACAA,KAG2D;AAH3D,MAAA,KAAAA,QAAA,SAGyD,CAAA,IAAEA,KAFzD,UAAO,GAAA,SACP,eAAY,GAAA;AAGd,MAAM,YAAY,UAAU,KAAK,CAAC;AAClC,SACG,WAAW,2BAA2B,WAAW,OAAO,IAAI,KAC5D,gBACC,aAAa,MACX,SAAC,aAAW;AAAK,WAAA,2BAA2B,WAAW,WAAW,IAAI;EAArD,CAAsD,KAE3E;AAEJ;AAEgB,SAAA,sBACd,MACAA,KAG2D;AAH3D,MAAA,KAAAA,QAAA,SAGyD,CAAA,IAAEA,KAFzD,UAAO,GAAA,SACP,eAAY,GAAA;AAGd,MAAM,kBAAkB,YAAY,IAAI;AACxC,MAAM,kBAAkB,YAAY,iBAAiB,CAAC;AAEtD,SACG,WAAW,6BAA6B,SAAS,eAAe,IAAI,KACpE,gBACC,aAAa,MACX,SAAC,aAAW;AACV,WAAA,6BAA6B,aAAa,eAAe,IAAI;EAA7D,CAA8D,KAEpE;AAEJ;AAEgB,SAAA,qBACd,MACAA,KAG2D;AAH3D,MAAA,KAAAA,QAAA,SAGyD,CAAA,IAAEA,KAFzD,UAAO,GAAA,SACP,eAAY,GAAA;AAGd,MAAM,iBAAiB,UAAU,IAAI;AACrC,MAAM,cAAc,YAAY,gBAAgB,CAAC;AAEjD,SACG,WAAW,6BAA6B,aAAa,OAAO,IAAI,KAChE,gBACC,aAAa,MACX,SAAC,aAAW;AACV,WAAA,6BAA6B,aAAa,WAAW,IAAI;EAAzD,CAA0D,KAEhE;AAEJ;AAEgB,SAAA,mBACd,KACAA,KAG2D;AAH3D,MAAA,KAAAA,QAAA,SAGyD,CAAA,IAAEA,KAFzD,UAAO,GAAA,SACP,eAAY,GAAA;AAGd,MAAM,eAAe,SAAS,KAAK,CAAC;AACpC,SACG,WAAW,0BAA0B,SAAS,YAAY,IAAI,KAC9D,gBACC,aAAa,MACX,SAAC,aAAW;AACV,WAAA,0BAA0B,aAAa,YAAY,IAAI;EAAvD,CAAwD,KAE9D;AAEJ;AAEgB,SAAA,oBACd,KACAA,KAG6D;MAH7D,KAG2DA,QAAA,SAAA,CAAA,IAAEA,KAF3D,UAAO,GAAA,SACP,KAAA,GAAA,gBAAA,iBAAiB,OAAA,SAAA,2BAAwB;AAG3C,MAAM,eAAe,eAAe,SAAS,KAAK,cAAc,CAAC;AACzD,MAAA,YAAc,eAAe,cAAc,cAAc,EAAC;AAClE,MAAM,cAAc,WAAW,QAAQ,OAAO;AAC9C,SAAQ,eAAe,cAAc,aAAc;AACrD;AAEgB,SAAA,kBACd,KACAA,KAG2D;AAH3D,MAAA,KAAAA,QAAA,SAGyD,CAAA,IAAEA,KAFzD,UAAO,GAAA,SACP,eAAY,GAAA;AAGd,MAAM,WAAW,SAAS,KAAK,CAAC;AAChC,SACG,WAAW,0BAA0B,UAAU,OAAO,IAAI,KAC1D,gBACC,aAAa,MACX,SAAC,aAAW;AAAK,WAAA,0BAA0B,UAAU,WAAW,IAAI;EAAnD,CAAoD,KAEzE;AAEJ;AAEgB,SAAA,mBACd,KACAA,KAG6D;MAH7D,KAG2DA,QAAA,SAAA,CAAA,IAAEA,KAF3D,UAAO,GAAA,SACP,KAAA,GAAA,gBAAA,iBAAiB,OAAA,SAAA,2BAAwB;AAG3C,MAAM,WAAW,SAAS,KAAK,cAAc;AACrC,MAAA,cAAgB,eAAe,UAAU,cAAc,EAAC;AAChE,MAAM,cAAc,WAAW,QAAQ,OAAO;AAC9C,SAAQ,eAAe,cAAc,eAAgB;AACvD;AAEM,SAAU,oBAAoBA,KAGkB;MAFpD,UAAOA,IAAA,SACP,eAAYA,IAAA;AAEZ,MAAI,gBAAgB,SAAS;AAC3B,QAAM,WAAW,aAAa,OAC5B,SAAC,aAAgB;AAAA,aAAA,yBAAyB,aAAa,OAAO,KAAK;IAAC,CAAA;AAEtE,WAAO,IAAI,QAAQ;aACV,cAAc;AACvB,WAAO,IAAI,YAAY;SAClB;AACL,WAAO;;AAEX;AAEM,SAAU,oBAAoBA,KAGkB;MAFpD,UAAOA,IAAA,SACP,eAAYA,IAAA;AAEZ,MAAI,gBAAgB,SAAS;AAC3B,QAAM,WAAW,aAAa,OAC5B,SAAC,aAAgB;AAAA,aAAA,yBAAyB,aAAa,OAAO,KAAK;IAAC,CAAA;AAEtE,WAAO,IAAI,QAAQ;aACV,cAAc;AACvB,WAAO,IAAI,YAAY;SAClB;AACL,WAAO;;AAEX;AAYgB,SAAA,oBACd,gBACA,kBAA+D;;AAD/D,MAAA,mBAAA,QAAA;AAAA,qBAA6C,CAAA;EAAA;AAC7C,MAAA,qBAAA,QAAA;AAAA,uBAA+D;EAAA;AAE/D,MAAM,cAAc,oBAAI,IAAG;AAC3B,WAAS,IAAI,GAAG,MAAM,eAAe,QAAQ,IAAI,KAAK,KAAK;AACzD,QAAM,MAAM,eAAe,CAAC;AAC5B,QAAI,OAAO,GAAG,GAAG;AACf,UAAM,MAAM,WAAW,KAAK,YAAY;AACxC,UAAM,gBAAgB,YAAY,IAAI,GAAG,KAAK,CAAA;AAC9C,UAAI,CAAC,cAAc,SAAS,gBAAgB,GAAG;AAC7C,sBAAc,KAAK,gBAAgB;AACnC,oBAAY,IAAI,KAAK,aAAa;;eAE3B,OAAO,QAAQ,UAAU;AAClC,UAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,UAAM,aAAYA,MAAA,KAAK,CAAC,OAAK,QAAAA,QAAA,SAAAA,MAAA;AAC7B,UAAM,aAAa,IAAI,SAAS;AAChC,UAAI,OAAO,cAAc,YAAY,MAAM,QAAQ,UAAU,GAAG;AAC9D,iBAAS,IAAI,GAAG,QAAM,WAAW,QAAQ,IAAI,OAAK,KAAK;AACrD,cAAM,QAAQ,WAAW,CAAC;AAC1B,cAAI,OAAO;AACT,gBAAM,MAAM,WAAW,OAAO,YAAY;AAC1C,gBAAM,gBAAgB,YAAY,IAAI,GAAG,KAAK,CAAA;AAC9C,gBAAI,CAAC,cAAc,SAAS,SAAS,GAAG;AACtC,4BAAc,KAAK,SAAS;AAC5B,0BAAY,IAAI,KAAK,aAAa;;;;;;;AAO9C,SAAO;AACT;AAQgB,SAAA,eAAkB,QAAa,QAAW;AACxD,MAAI,OAAO,WAAW,OAAO,QAAQ;AACnC,WAAO;;AAGT,SAAO,OAAO,MAAM,SAAC,OAAOiB,QAAK;AAAK,WAAA,UAAU,OAAOA,MAAK;EAAtB,CAAuB;AAC/D;AAoBgB,SAAA,eACd,cACA,kBAA4D;AAD5D,MAAA,iBAAA,QAAA;AAAA,mBAAgC,CAAA;EAAA;AAChC,MAAA,qBAAA,QAAA;AAAA,uBAA4D;EAAA;AAE5D,MAAM,cAAc,oBAAI,IAAG;AAC3B,eAAa,QAAQ,SAAC,SAAO;AACnB,QAAM,UAAyB,QAAO,MAAvB,cAAgB,QAAO;AAC9C,QAAI,CAAC,OAAO,OAAO,GAAG;AACpB;;AAGF,QAAM,MAAM,WAAW,SAAS,YAAY;AAC5C,QAAM,gBAAgB,YAAY,IAAI,GAAG,KAAK;MAC5C,WAAW;MACX,cAAc,CAAA;;AAEhB,QACE,eAAe,iBACf,cAAc,WAAW,MAAM,oBAC/B,eAAe,cAAc,cAAc,GAAG,CAAC,WAAW,CAAC,GAC3D;AACA;;AAGF,kBAAc,WAAW,IAAI;AAC7B,QAAM,iBAAiB,cAAc,cAAc;AACnD,kBAAc,cAAc,IAAI,iBAC7B,cAAA,cAAA,CAAA,GAAK,gBAAc,IAAA,GAAA,CAAE,WAAW,GAAA,KAAA,IAC/B,CAAC,WAAW;AAChB,gBAAY,IAAI,KAAK,aAAa;EACpC,CAAC;AACD,SAAO;AACT;AAWM,SAAU,mBACdC,aACA,aACA,mBACA,WACA,eAAqB;AAErB,MAAM,IAAI,cAAc;AACxB,MAAM,QAAgB,CAAA;AACtB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,QAAI,eAAeA;AACnB,QAAM,oBAAoB,cAAc,CAAC;AACzC,QAAI,mBAAmB;AACrB,qBAAe,SAAS,cAAc,SAAS,iBAAiB,CAAC;AACjE,qBAAe,WAAW,cAAc,WAAW,iBAAiB,CAAC;AACrE,qBAAe,WAAW,cAAc,WAAW,iBAAiB,CAAC;;AAGvE,QAAM,WAAW,WACfA,cACC,oBAAoB,KAAK,SAAS;AAGrC,QACE,QAAQ,cAAc,WAAW,KACjC,SAAS,cAAc,QAAQ,KAC/B,qBAAqB,QACrB;AACA,YAAM,KAAK,iBAAiB;;;AAIhC,SAAO;AACT;AAOM,SAAU,QAAQ,GAAS;AAC/B,SAAO,IAAI,KAAK,IAAA,OAAI,CAAC,IAAK,GAAG,OAAA,CAAC;AAChC;AAQgB,SAAA,eACd,MACA,gBAAiD;AAAjD,MAAA,mBAAA,QAAA;AAAA,qBAAiD;EAAA;AAEjD,MAAM,YAAY,KAAK,KAAK,QAAQ,IAAI,IAAI,cAAc,IAAI;AAC9D,MAAM,cAAc,aAAa,iBAAiB;AAClD,SAAO,EAAE,aAAa,UAAS;AACjC;AAOM,SAAU,cAAc,GAAO;AACnC,MAAMA,cAAa,IAAI,KAAK,EAAE,YAAW,GAAI,EAAE,SAAQ,GAAI,EAAE,QAAO,CAAE;AACtE,MAAM,oBAAoB,IAAI,KAC5B,EAAE,YAAW,GACb,EAAE,SAAQ,GACV,EAAE,QAAO,GACT,EAAE;AAGJ,SAAO,KAAK,OAAO,CAAC,oBAAoB,CAACA,eAAc,IAAS;AAClE;AAcM,SAAU,cAAc,GAAO;AACnC,MAAM,UAAU,EAAE,WAAU;AAC5B,MAAM,eAAe,EAAE,gBAAe;AAEtC,SAAO,OAAO,EAAE,QAAO,IAAK,UAAU,MAAO,YAAY;AAC3D;AAWgB,SAAA,aAAa,IAAU,IAAQ;AAC7C,SAAO,cAAc,EAAE,EAAE,QAAO,MAAO,cAAc,EAAE,EAAE,QAAO;AAClE;AAOM,SAAU,gBAAgB,MAAU;AACxC,MAAI,CAAC,OAAO,IAAI,GAAG;AACjB,UAAM,IAAI,MAAM,cAAc;;AAGhC,MAAM,kBAAkB,IAAI,KAAK,IAAI;AACrC,kBAAgB,SAAS,GAAG,GAAG,GAAG,CAAC;AACnC,SAAO;AACT;AAYgB,SAAA,aAAa,MAAY,eAAmB;AAC1D,MAAI,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,aAAa,GAAG;AAC3C,UAAM,IAAI,MAAM,uBAAuB;;AAGzC,MAAM,eAAe,gBAAgB,IAAI;AACzC,MAAM,wBAAwB,gBAAgB,aAAa;AAE3D,SAAO,SAAS,cAAc,qBAAqB;AACrD;AAQM,SAAU,eACd,OAA0C;AAE1C,SAAO,MAAM,QAAQ,QAAQ;AAC/B;ACv/CA,IAAA;;EAAA,SAAA,QAAA;AAAuC,cAGtCC,YAAA,MAAA;AAGC,aAAAA,WAAY,OAAqB;AAC/B,UAAA,QAAA,OAAK,KAAA,MAAC,KAAK,KAAE;AAHf,YAAA,WAA8C,cAAAlB,QAAM,UAAS;AAwB7D,YAAY,eAAG,SAAC,MAA4B;;AAC1C,cAAK,SAAS,EAAE,KAAI,CAAE;AAEd,YAAM,WAAa,MAAK,MAAK;AACrC,YAAM,kBAAkB,oBAAoB,QAAQ,CAAC,MAAM,CAAC,QAAQ;AACpE,YAAM,OAAO,kBAAkB,WAAW,oBAAI,KAAI;AAElD,YAAI,SAAA,QAAA,SAAA,SAAA,SAAA,KAAM,SAAS,GAAG,GAAG;AACjB,cAAA,KAAmB,KAAK,MAAM,GAAG,GAAhC,QAAK,GAAA,CAAA,GAAE,UAAO,GAAA,CAAA;AACrB,eAAK,SAAS,OAAO,KAAK,CAAC;AAC3B,eAAK,WAAW,OAAO,OAAO,CAAC;;AAGjC,SAAA,MAAAD,MAAA,MAAK,OAAM,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,IAAI;MAC5B;AAEA,YAAA,kBAAkB,WAAA;AACR,YAAA,OAAS,MAAK,MAAK;AACrB,YAAAA,MAAwC,MAAK,OAA3C,OAAIA,IAAA,MAAE,aAAUA,IAAA,YAAE,kBAAeA,IAAA;AAEzC,YAAI,iBAAiB;AACnB,qBAAO,4BAAa,iBAAiB;YACnC;YACA,OAAO;YACP,UAAU,MAAK;UAChB,CAAA;;AAGH,eACE,cAAAC,QACE,cAAA,SAAA,EAAA,MAAK,QACL,WAAU,gCACV,aAAY,QACZ,MAAK,cACL,KAAK,MAAK,UACV,SAAS,WAAA;;AACP,WAAAD,MAAA,MAAK,SAAS,aAAS,QAAAA,QAAA,SAAA,SAAAA,IAAA,MAAK;WAE9B,UAAQ,MACR,OAAO,MACP,UAAU,SAAC,OAAK;AACd,gBAAK,aAAa,MAAM,OAAO,SAAS,UAAU;UACnD,CAAA;MAGP;AAhEE,YAAK,QAAQ;QACX,MAAM,MAAK,MAAM;;;;AAId,IAAAmB,WAAA,2BAAP,SACE,OACA,OAAqB;AAErB,UAAI,MAAM,eAAe,MAAM,MAAM;AACnC,eAAO;UACL,MAAM,MAAM;;;AAKhB,aAAO;;AAkDT,IAAAA,WAAA,UAAA,SAAA,WAAA;AACE,aACE,cAAAlB,QAAA;QAAA;QAAA,EAAK,WAAU,yCAAwC;QACrD,cAAAA,QAAK,cAAA,OAAA,EAAA,WAAU,iCAAgC,GAC5C,KAAK,MAAM,cAAc;QAE5B,cAAAA,QAAK;UAAA;UAAA,EAAA,WAAU,yCAAwC;UACrD,cAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,+BAA8B,GAC1C,KAAK,gBAAe,CAAE;QACnB;MACF;;AAId,WAACkB;EAAD,EAzFuC,uBAAS;;ACmHhD,IAAA;;EAAA,SAAA,QAAA;AAAiC,cAAmBC,MAAA,MAAA;AAApD,aAAAA,OAAA;;AASE,YAAK,YAAG,yBAAS;AAEjB,YAAW,cAAwB,SAAC,OAAK;AACvC,YAAI,CAAC,MAAK,WAAU,KAAM,MAAK,MAAM,SAAS;AAC5C,gBAAK,MAAM,QAAQ,KAAK;;MAE5B;AAEA,YAAgB,mBAA6B,SAAC,OAAK;AACjD,YAAI,CAAC,MAAK,WAAU,KAAM,MAAK,MAAM,cAAc;AACjD,gBAAK,MAAM,aAAa,KAAK;;MAEjC;AAEA,YAAe,kBAA+C,SAAC,OAAK;;AAClE,YAAM,WAAW,MAAM;AACvB,YAAI,aAAa,QAAQ,OAAO;AAC9B,gBAAM,eAAc;AACpB,gBAAM,MAAM,QAAQ;;AAGtB,SAAA,MAAApB,MAAA,MAAK,OAAM,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,KAAK;MACpC;AAEA,YAAS,YAAG,SAAC,OAA8B;AACzC,eAAAW,WAAU,MAAK,MAAM,KAAK,KAAK;MAA/B;AAEF,YAAA,qBAAqB,WAAA;;AACnB,YAAI,MAAK,MAAM,4BAA4B;AACzC,iBAAO;;AAGT,YAAM,iBAAiB,MAAK,MAAM,mBAC9BX,MAAA,MAAK,MAAM,mBAAe,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAK,SAAC,MAAS;AAAA,iBAAA,MAAK,gBAAgB,IAAI;QAAzB,CAA0B,IACnE,MAAK,gBAAgB,MAAK,MAAM,QAAQ;AAE5C,YAAM,aACJ,MAAK,MAAM,gBAAgB,MAAK,WAAW,MAAK,MAAM,YAAY;AAEpE,eACE,CAAC,kBACD,MAAK,gBAAgB,MAAK,MAAM,YAAY,KAC5C,CAAC;MAEL;AAEA,YAAU,aAAG,SAAC,KAAoB;AAApB,YAAA,QAAA,QAAA;AAAA,gBAAM,MAAK,MAAM;QAAG;AAGhC,eAAA,cAAc,KAAK;UACjB,SAAS,MAAK,MAAM;UACpB,SAAS,MAAK,MAAM;UACpB,cAAc,MAAK,MAAM;UACzB,sBAAsB,MAAK,MAAM;UACjC,sBAAsB,MAAK,MAAM;UACjC,cAAc,MAAK,MAAM;UACzB,YAAY,MAAK,MAAM;SACxB;MARD;AAUF,YAAA,aAAa,WAAA;AAGX,eAAA,cAAc,MAAK,MAAM,KAAK;UAC5B,cAAc,MAAK,MAAM;UACzB,sBAAsB,MAAK,MAAM;SAClC;MAHD;AAKF,YAAA,gBAAgB,WAAA;AACd,eAAAW,WACE,MAAK,MAAM,KACX,eACE,MAAK,MAAM,KACX,MAAK,MAAM,QACX,MAAK,MAAM,gBAAgB,CAC5B;MANH;AASF,YAAU,aAAG,SAAC,OAAmB;AAC/B,eAAA,MAAK,MAAM,kBACXA,WACE,OACA,eACE,MAAK,MAAM,KACX,MAAK,MAAM,QACX,MAAK,MAAM,gBAAgB,CAC5B;MAPH;AAUF,YAAe,kBAAG,SAAC,OAAmB;AACpC,eAAA,MAAK,UAAU,KAAK,KAAK,MAAK,WAAW,KAAK;MAA9C;AAEF,YAAA,sBAAsB,WAAA;AACd,YAAAX,MAA0B,MAAK,OAA7B,MAAGA,IAAA,KAAE,iBAAcA,IAAA;AAE3B,YAAI,CAAC,gBAAgB;AACnB,iBAAO;;AAIT,YAAM,SAAS,WAAW,KAAK,YAAY;AAC3C,eAAO,eAAe,IAAI,MAAM;MAClC;AAGA,YAAA,mBAAmB,WAAA;;AACX,YAAA,KAAoB,MAAK,OAAvB,MAAG,GAAA,KAAE,WAAQ,GAAA;AACrB,YAAI,CAAC,UAAU;AAEb,iBAAO,CAAC,MAAS;;AAEnB,YAAM,SAAS,WAAW,KAAK,YAAY;AAE3C,YAAI,SAAS,IAAI,MAAM,GAAG;AACxB,iBAAO,EAACA,MAAA,SAAS,IAAI,MAAM,OAAC,QAAAA,QAAA,SAAA,SAAAA,IAAE,SAAS;;AAIzC,eAAO,CAAC,MAAS;MACnB;AAEA,YAAA,YAAY,WAAA;AACJ,YAAAA,MAA8B,MAAK,OAAjC,MAAGA,IAAA,KAAE,YAASA,IAAA,WAAE,UAAOA,IAAA;AAC/B,YAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,iBAAO;;AAET,eAAO,aAAa,KAAK,WAAW,OAAO;MAC7C;AAEA,YAAA,qBAAqB,WAAA;;AACb,YAAA,KAQF,MAAK,OAPP,MAAG,GAAA,KACH,eAAY,GAAA,cACZ,aAAU,GAAA,YACV,eAAY,GAAA,cACZ,6BAA0B,GAAA,4BAC1B,YAAS,GAAA,WACT,UAAO,GAAA;AAGT,YAAM,iBAAgBA,MAAA,MAAK,MAAM,mBAAa,QAAAA,QAAA,SAAAA,MAAI,MAAK,MAAM;AAE7D,YACE,EAAE,gBAAgB,cAAc,iBAChC,CAAC,iBACA,CAAC,8BAA8B,MAAK,WAAU,GAC/C;AACA,iBAAO;;AAGT,YACE,gBACA,YACC,SAAS,eAAe,OAAO,KAAKa,SAAQ,eAAe,OAAO,IACnE;AACA,iBAAO,aAAa,KAAK,eAAe,OAAO;;AAGjD,YACE,cACA,cACC,QAAQ,eAAe,SAAS,KAAKA,SAAQ,eAAe,SAAS,IACtE;AACA,iBAAO,aAAa,KAAK,WAAW,aAAa;;AAGnD,YACE,gBACA,aACA,CAAC,YACA,QAAQ,eAAe,SAAS,KAAKA,SAAQ,eAAe,SAAS,IACtE;AACA,iBAAO,aAAa,KAAK,WAAW,aAAa;;AAGnD,eAAO;MACT;AAEA,YAAA,wBAAwB,WAAA;;AACtB,YAAI,CAAC,MAAK,mBAAkB,GAAI;AAC9B,iBAAO;;AAGH,YAAA,KAAmC,MAAK,OAAtC,MAAG,GAAA,KAAE,YAAS,GAAA,WAAE,eAAY,GAAA;AACpC,YAAM,iBAAgBb,MAAA,MAAK,MAAM,mBAAa,QAAAA,QAAA,SAAAA,MAAI,MAAK,MAAM;AAE7D,YAAI,cAAc;AAChB,iBAAOW,WAAU,KAAK,aAAa;eAC9B;AACL,iBAAOA,WAAU,KAAK,SAAS;;MAEnC;AAEA,YAAA,sBAAsB,WAAA;;AACpB,YAAI,CAAC,MAAK,mBAAkB,GAAI;AAC9B,iBAAO;;AAGH,YAAA,KAA6C,MAAK,OAAhD,MAAG,GAAA,KAAE,UAAO,GAAA,SAAE,aAAU,GAAA,YAAE,eAAY,GAAA;AAC9C,YAAM,iBAAgBX,MAAA,MAAK,MAAM,mBAAa,QAAAA,QAAA,SAAAA,MAAI,MAAK,MAAM;AAE7D,YAAI,cAAc,cAAc;AAC9B,iBAAOW,WAAU,KAAK,aAAa;eAC9B;AACL,iBAAOA,WAAU,KAAK,OAAO;;MAEjC;AAEA,YAAA,eAAe,WAAA;AACP,YAAAX,MAA8B,MAAK,OAAjC,MAAGA,IAAA,KAAE,YAASA,IAAA,WAAE,UAAOA,IAAA;AAC/B,YAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,iBAAO;;AAET,eAAOW,WAAU,WAAW,GAAG;MACjC;AAEA,YAAA,aAAa,WAAA;AACL,YAAAX,MAA8B,MAAK,OAAjC,MAAGA,IAAA,KAAE,YAASA,IAAA,WAAE,UAAOA,IAAA;AAC/B,YAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,iBAAO;;AAET,eAAOW,WAAU,SAAS,GAAG;MAC/B;AAEA,YAAA,YAAY,WAAA;AACV,YAAM,UAAU,OAAO,MAAK,MAAM,GAAG;AACrC,eAAO,YAAY,KAAK,YAAY;MACtC;AAEA,YAAA,eAAe,WAAA;AACb,eACE,MAAK,MAAM,UAAU,WACpB,MAAK,MAAM,QAAQ,KAAK,OAAO,SAAS,MAAK,MAAM,GAAG;MAE3D;AAEA,YAAA,gBAAgB,WAAA;AACd,eACE,MAAK,MAAM,UAAU,WACpB,SAAS,MAAK,MAAM,GAAG,IAAI,KAAK,OAAO,MAAK,MAAM;MAEvD;AAEA,YAAA,eAAe,WAAA;AAAM,eAAA,MAAK,UAAU,QAAO,CAAE;MAAxB;AAErB,YAAA,aAAa,WAAA;;AACX,YAAI,MAAK,MAAM,iBAAiB;AAC9B,kBAAOX,MAAA,MAAK,MAAM,mBAAe,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAK,SAAC,MAAI;AACzC,mBAAA,MAAK,gBAAgB,IAAI;UAAzB,CAA0B;;AAG9B,eAAO,MAAK,gBAAgB,MAAK,MAAM,QAAQ;MACjD;AAEA,YAAa,gBAAG,SAAC,MAAU;AACzB,YAAM,eAAe,MAAK,MAAM,eAC5B,MAAK,MAAM,aAAa,IAAI,IAC5B;AACJ,eAAO,KACL,yBACA,cACA,4BAA4B,iBAAiB,MAAK,MAAM,GAAG,GAC3D;UACE,mCAAmC,MAAK,WAAU;UAClD,mCAAmC,MAAK,WAAU;UAClD,mCAAmC,MAAK,WAAU;UAClD,4CAA4C,MAAK,mBAAkB;UACnE,sCAAsC,MAAK,aAAY;UACvD,oCAAoC,MAAK,WAAU;UACnD,mCAAmC,MAAK,UAAS;UACjD,6CAA6C,MAAK,mBAAkB;UACpE,gDACE,MAAK,sBAAqB;UAC5B,8CACE,MAAK,oBAAmB;UAC1B,gCAAgC,MAAK,aAAY;UACjD,kCAAkC,MAAK,UAAS;UAChD,wCACE,MAAK,aAAY,KAAM,MAAK,cAAa;WAE7C,MAAK,oBAAmB,GACxB,MAAK,iBAAgB,CAAE;MAE3B;AAEA,YAAA,eAAe,WAAA;AACP,YAAAA,MAIF,MAAK,OAHP,MAAGA,IAAA,KACH,KAAqCA,IAAA,4BAArC,6BAA6B,OAAA,SAAA,WAAQ,IACrC,KAAAA,IAAA,6BAAA,8BAA2B,OAAA,SAAG,kBAAe;AAG/C,YAAM,SACJ,MAAK,WAAU,KAAM,MAAK,WAAU,IAChC,8BACA;AAEN,eAAO,GAAA,OAAG,QAAM,GAAA,EAAA,OAAI,WAAW,KAAK,QAAQ,MAAK,MAAM,MAAM,CAAC;MAChE;AAGA,YAAA,WAAW,WAAA;AACH,YAAAA,MAA8C,MAAK,OAAjD,MAAGA,IAAA,KAAE,KAAAA,IAAA,UAAA,WAAQ,OAAA,SAAG,oBAAI,IAAG,IAAE,IAAE,eAAYA,IAAA;AAC/C,YAAM,YAAY,WAAW,KAAK,YAAY;AAC9C,YAAM,SAAS,CAAA;AACf,YAAI,SAAS,IAAI,SAAS,GAAG;AAC3B,iBAAO,KAAP,MAAA,QAAe,SAAS,IAAI,SAAS,EAAE,YAAY;;AAErD,YAAI,MAAK,WAAU,GAAI;AACrB,iBAAO,KACL,iBAAA,QAAA,iBAAA,SAAA,SAAA,aACI,OAAO,SAAC,aAAW;AACnB,gBAAI,uBAAuB,MAAM;AAC/B,qBAAOW,WAAU,aAAa,GAAG;;AAEnC,mBAAOA,WAAU,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,MAAM,GAAG;UACzC,CAAC,EACA,IAAI,SAAC,aAAW;AACf,gBAAI,uBAAuB,MAAM;AAC/B,qBAAO;;AAET,mBAAO,gBAAA,QAAA,gBAAW,SAAA,SAAX,YAAa;WACrB,CAAC;;AAIR,eAAO,OAAO,KAAK,IAAI;MACzB;AAEA,YAAA,cAAc,WAAA;AACZ,YAAM,cAAc,MAAK,MAAM;AAC/B,YAAM,kBAAkB,MAAK,MAAM;AACnC,YAAM,WACJ,EACE,MAAK,MAAM,mBACV,MAAK,MAAM,kBAAkB,CAAC,MAAK,cAAa,QAElD,MAAK,mBAAkB,KACrB,MAAK,UAAU,WAAW,KACzBA,WAAU,iBAAiB,WAAW,KACtC,IACA;AAEN,eAAO;MACT;AAKA,YAAA,iBAAiB,WAAA;;AAGf,cAAK,eAAc,OAAMX,MAAA,MAAK,MAAM,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAE,MAAM,EAAE,eAAe,KAAI,CAAE;MAC5E;AAyCA,YAAA,oBAAoB,WAAA;AAClB,YAAI,MAAK,MAAM,8BAA8B,MAAK,aAAY;AAC5D,iBAAO;AACT,YAAI,MAAK,MAAM,gCAAgC,MAAK,cAAa;AAC/D,iBAAO;AACT,eAAO,MAAK,MAAM,oBACd,MAAK,MAAM,kBAAkB,QAAQ,MAAK,MAAM,GAAG,GAAG,MAAK,MAAM,GAAG,IACpE,QAAQ,MAAK,MAAM,GAAG;MAC5B;AAEA,YAAM,SAAG,WAAA;AAAM;;UAEb,cAAAC,QACE;YAAA;YAAA,EAAA,KAAK,MAAK,OACV,WAAW,MAAK,cAAc,MAAK,MAAM,GAAG,GAC5C,WAAW,MAAK,iBAChB,SAAS,MAAK,aACd,cACE,CAAC,MAAK,MAAM,kBAAkB,MAAK,mBAAmB,QAExD,gBACE,MAAK,MAAM,kBAAkB,MAAK,mBAAmB,QAEvD,UAAU,MAAK,YAAW,GACd,cAAA,MAAK,aAAY,GAC7B,MAAK,UACL,OAAO,MAAK,SAAQ,GAAE,iBACP,MAAK,WAAU,GAChB,gBAAA,MAAK,aAAY,IAAK,SAAS,QAAS,iBACvC,MAAK,WAAU,KAAM,MAAK,UAAS,EAAE;YAEnD,MAAK,kBAAiB;YACtB,MAAK,SAAQ,MAAO,MACnB,cAAAA,QAAM,cAAA,QAAA,EAAA,WAAU,UAAS,GAAE,MAAK,SAAQ,CAAE;UAC3C;;MAxBU;;;AA3Zf,IAAAmB,KAAA,UAAA,oBAAA,WAAA;AACE,WAAK,eAAc;;AAGrB,IAAAA,KAAA,UAAA,qBAAA,WAAA;AACE,WAAK,eAAc;;AAqWb,IAAAA,KAAA,UAAA,iBAAR,WAAA;AACE,UAAI,iBAAiB;AACrB,UAAI,KAAK,YAAW,MAAO,KAAK,KAAK,UAAU,KAAK,MAAM,YAAY,GAAG;AAEvE,YAAI,CAAC,SAAS,iBAAiB,SAAS,kBAAkB,SAAS,MAAM;AACvE,2BAAiB;;AAKnB,YAAI,KAAK,MAAM,UAAU,CAAC,KAAK,MAAM,sBAAsB;AACzD,2BAAiB;;AAEnB,YAAI,KAAK,mBAAkB,GAAI;AAC7B,2BAAiB;;AAEnB,YAAI,KAAK,eAAc,GAAI;AACzB,2BAAiB;;;AAGrB,aAAO;;AAID,IAAAA,KAAA,UAAA,qBAAR,WAAA;;AACE,eACE,MAAApB,MAAA,KAAK,MAAM,kBAAY,QAAAA,QAAA,SAAA,SAAAA,IAAE,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,SAAS,SAAS,aAAa,QACjE,KAAA,SAAS,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,UAAU,SAAS,uBAAuB;;AAI9D,IAAAoB,KAAA,UAAA,iBAAR,WAAA;AACE;;QAEG,KAAK,MAAM,8BAA8B,KAAK,aAAY,KAC1D,KAAK,MAAM,gCAAgC,KAAK,cAAa;;;AAyCpE,WAACA;EAAD,EAvbiC,uBAAS;;AC1H1C,IAAA;;EAAA,SAAA,QAAA;AAAwC,cAA0BC,aAAA,MAAA;AAAlE,aAAAA,cAAA;;AAeE,YAAY,mBAAG,yBAAS;AAExB,YAAW,cAAG,SAAC,OAAuC;AACpD,YAAI,MAAK,MAAM,SAAS;AACtB,gBAAK,MAAM,QAAQ,KAAK;;MAE5B;AAEA,YAAe,kBAAG,SAAC,OAA0C;;AAC3D,YAAM,WAAW,MAAM;AACvB,YAAI,aAAa,QAAQ,OAAO;AAC9B,gBAAM,eAAc;AACpB,gBAAM,MAAM,QAAQ;;AAGtB,SAAA,MAAArB,MAAA,MAAK,OAAM,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,KAAK;MACpC;AAEA,YAAA,qBAAqB,WAAA;AACnB,eAAA,CAAC,MAAK,MAAM,8BACZ,CAACW,WAAU,MAAK,MAAM,MAAM,MAAK,MAAM,QAAQ,KAC/CA,WAAU,MAAK,MAAM,MAAM,MAAK,MAAM,YAAY;MAFlD;AAIF,YAAA,cAAc,WAAA;AACZ,eAAA,MAAK,MAAM,kBACX,MAAK,MAAM,mBACV,MAAK,mBAAkB,KACrBA,WAAU,MAAK,MAAM,MAAM,MAAK,MAAM,QAAQ,KAC7CA,WAAU,MAAK,MAAM,cAAc,MAAK,MAAM,QAAQ,KACtD,IACA;MANJ;AAWF,YAAqB,wBAAG,SAAC,WAAoC;AAC3D,YAAI,wBAAwB;AAG5B,YACE,MAAK,YAAW,MAAO,KACvB,EAAC,cAAA,QAAA,cAAS,SAAA,SAAT,UAAW,mBACZA,WAAU,MAAK,MAAM,MAAM,MAAK,MAAM,YAAY,GAClD;AAEA,cAAI,CAAC,SAAS,iBAAiB,SAAS,kBAAkB,SAAS,MAAM;AACvE,oCAAwB;;AAK1B,cAAI,MAAK,MAAM,UAAU,CAAC,MAAK,MAAM,sBAAsB;AACzD,oCAAwB;;AAG1B,cACE,MAAK,MAAM,gBACX,MAAK,MAAM,aAAa,WACxB,MAAK,MAAM,aAAa,QAAQ,SAAS,SAAS,aAAa,KAC/D,SAAS,iBACT,SAAS,cAAc,UAAU,SAC/B,+BAA+B,GAEjC;AACA,oCAAwB;;;AAI5B,iCACE,MAAK,aAAa,WAClB,MAAK,aAAa,QAAQ,MAAM,EAAE,eAAe,KAAI,CAAE;MAC3D;;;AArFA,WAAA,eAAWU,aAAY,gBAAA;MAAvB,KAAA,WAAA;AACE,eAAO;UACL,iBAAiB;;;;;IAEpB,CAAA;AAED,IAAAA,YAAA,UAAA,oBAAA,WAAA;AACE,WAAK,sBAAqB;;AAG5B,IAAAA,YAAkB,UAAA,qBAAlB,SAAmB,WAA0B;AAC3C,WAAK,sBAAsB,SAAS;;AA4EtC,IAAAA,YAAA,UAAA,SAAA,WAAA;AACQ,UAAArB,MAKF,KAAK,OAJP,aAAUA,IAAA,YACV,iBAAcA,IAAA,gBACd,KAAAA,IAAA,iBAAA,kBAAe,OAAA,SAAGqB,YAAW,aAAa,kBAAe,IACzD,UAAOrB,IAAA;AAGT,UAAM,oBAAoB;QACxB,iCAAiC;QACjC,4CAA4C,CAAC,CAAC,WAAW,CAAC;QAC1D,2CACE,CAAC,CAAC,WAAWW,WAAU,KAAK,MAAM,MAAM,KAAK,MAAM,QAAQ;;AAE/D,aACE,cAAAV,QAAA,cAAA,OAAA,EACE,KAAK,KAAK,cACV,WAAW,KAAK,iBAAiB,GACrB,cAAA,GAAA,OAAG,iBAAmB,GAAA,EAAA,OAAA,KAAK,MAAM,UAAU,GACvD,SAAS,KAAK,aACd,WAAW,KAAK,iBAChB,UAAU,KAAK,YAAW,EAAE,GAE3B,UAAU;;AAInB,WAACoB;EAAD,EAnHwC,uBAAS;;ACmBjD,IAAA;;EAAA,SAAA,QAAA;AAAkC,cAAoBC,OAAA,MAAA;AAAtD,aAAAA,QAAA;;AAOE,YAAU,aAAG,SAAC,KAAS;AACrB,eAAA,cAAc,KAAK;UACjB,SAAS,MAAK,MAAM;UACpB,SAAS,MAAK,MAAM;UACpB,cAAc,MAAK,MAAM;UACzB,sBAAsB,MAAK,MAAM;UACjC,sBAAsB,MAAK,MAAM;UACjC,cAAc,MAAK,MAAM;UACzB,YAAY,MAAK,MAAM;SACxB;MARD;AAUF,YAAA,iBAAiB,SACf,KACA,OAAuC;AAEvC,YAAI,MAAK,MAAM,YAAY;AACzB,gBAAK,MAAM,WAAW,KAAK,KAAK;;MAEpC;AAEA,YAAmB,sBAAG,SAAC,KAAS;AAC9B,YAAI,MAAK,MAAM,iBAAiB;AAC9B,gBAAK,MAAM,gBAAgB,GAAG;;MAElC;AAEA,YAAA,kBAAkB,SAChB,KACA,YACA,OAAuC;;AAEvC,YAAI,iBAAiB,IAAI,KAAK,GAAG;AAEjC,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,gBAAgB,IAAI,KAAK,GAAG;AAClC,wBAAc,QAAQ,cAAc,QAAO,IAAK,CAAC;AAEjD,cAAM,YAAY,CAAC,MAAK,WAAW,aAAa;AAEhD,cAAI,WAAW;AACb,6BAAiB;AACjB;;;AAIJ,YAAI,OAAO,MAAK,MAAM,iBAAiB,YAAY;AACjD,gBAAK,MAAM,aAAa,gBAAgB,YAAY,KAAK;;AAE3D,YAAI,MAAK,MAAM,gBAAgB;AAC7B,gBAAK,eAAe,gBAAgB,KAAK;;AAE3C,aACEtB,MAAA,MAAK,MAAM,yBACX,QAAAA,QAAA,SAAAA,MAAAsB,MAAK,aAAa,qBAClB;AACA,WAAA,MAAA,KAAA,MAAK,OAAM,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,KAAK;;MAE9B;AAEA,YAAgB,mBAAG,SAAC,MAAU;AAC5B,YAAI,MAAK,MAAM,kBAAkB;AAC/B,iBAAO,MAAK,MAAM,iBAAiB,IAAI;;AAEzC,eAAO,QAAQ,IAAI;MACrB;AAEA,YAAA,iBAAiB,WAAA;AACf,YAAMC,eAAc,MAAK,YAAW;AACpC,YAAMC,aAAY,QAAQD,cAAa,CAAC;AAExC,YAAI,iBAAiB,IAAI,KAAKA,YAAW;AACzC,eAAO,kBAAkBC,YAAW;AAClC,cAAI,CAAC,MAAK,WAAW,cAAc;AAAG,mBAAO;AAE7C,2BAAiB,QAAQ,gBAAgB,CAAC;;AAG5C,eAAO;MACT;AAEA,YAAA,aAAa,WAAA;AACX,YAAMD,eAAc,MAAK,YAAW;AACpC,YAAM,OAAO,CAAA;AACb,YAAM,aAAa,MAAK,iBAAiBA,YAAW;AACpD,YAAI,MAAK,MAAM,gBAAgB;AAC7B,cAAM,gBACJ,MAAK,MAAM,gBAAgB,MAAK,MAAM,iBAClC,MAAK,gBAAgB,KAAK,OAAMA,cAAa,UAAU,IACvD;AACN,eAAK,KACH,cAAAtB,QAAC,cAAA,YAAU,QAAA,EACT,KAAI,IAAG,GACHqB,MAAK,cACL,MAAK,OACT,EAAA,YACA,gBAAgB,MAAK,eAAc,GACnC,MAAMC,cACN,SAAS,cAAa,CAAA,CAAA,CACtB;;AAGN,eAAO,KAAK,OACV,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,IACpB,SAACE,SAAc;AACb,cAAM,MAAM,QAAQF,cAAaE,OAAM;AACvC,iBACE,cAAAxB,QAAA,cAAC,KAAGyB,QAAA,CAAA,GACEJ,MAAK,cACL,MAAK,OAAK,EACd,4BAA4B,MAAK,MAAM,0BACvC,6BACE,MAAK,MAAM,4BAEb,KAAK,IAAI,QAAO,GAChB,KACA,SAAS,MAAK,eAAe,KAAK,OAAM,GAAG,GAC3C,cAAc,MAAK,oBAAoB,KAAK,OAAM,GAAG,EAAC,CAAA,CAAA;SAG3D,CACF;MAEL;AAEA,YAAA,cAAc,WAAA;AACZ,eAAA,eACE,MAAK,MAAM,KACX,MAAK,MAAM,QACX,MAAK,MAAM,gBAAgB;MAH7B;AAMF,YAAA,qBAAqB,WAAA;AACnB,eAAA,CAAC,MAAK,MAAM,8BACZ,CAACX,WAAU,MAAK,YAAW,GAAI,MAAK,MAAM,QAAQ,KAClDA,WAAU,MAAK,YAAW,GAAI,MAAK,MAAM,YAAY;MAFrD;;;AA1IF,WAAA,eAAWW,OAAY,gBAAA;MAAvB,KAAA,WAAA;AACE,eAAO;UACL,qBAAqB;;;;;IAExB,CAAA;AA0ID,IAAAA,MAAA,UAAA,SAAA,WAAA;AACE,UAAM,oBAAoB;QACxB,0BAA0B;QAC1B,oCAAoCX,WAClC,KAAK,YAAW,GAChB,KAAK,MAAM,QAAQ;QAErB,6CAA6C,KAAK,mBAAkB;;AAEtE,aAAO,cAAAV,QAAK,cAAA,OAAA,EAAA,WAAW,KAAK,iBAAiB,EAAC,GAAG,KAAK,WAAU,CAAE;;AAEtE,WAACqB;EAAD,EA1JkC,uBAAS;;;ACL3C,IAAM,mCAAmC;AAEzC,IAAM,uBAAuB;EAC3B,aAAa;EACb,eAAe;EACf,cAAc;;AAEhB,IAAM,iBAAa,KAAA,CAAA,GACjB,GAAC,qBAAqB,WAAW,IAAG;EAClC,MAAM;IACJ,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;IACL,CAAC,GAAG,CAAC;IACL,CAAC,IAAI,EAAE;EACR;EACD,0BAA0B;AAC3B,GACD,GAAC,qBAAqB,aAAa,IAAG;EACpC,MAAM;IACJ,CAAC,GAAG,GAAG,CAAC;IACR,CAAC,GAAG,GAAG,CAAC;IACR,CAAC,GAAG,GAAG,CAAC;IACR,CAAC,GAAG,IAAI,EAAE;EACX;EACD,0BAA0B;AAC3B,GACD,GAAC,qBAAqB,YAAY,IAAG;EACnC,MAAM;IACJ,CAAC,GAAG,GAAG,GAAG,CAAC;IACX,CAAC,GAAG,GAAG,GAAG,CAAC;IACX,CAAC,GAAG,GAAG,IAAI,EAAE;EACd;EACD,0BAA0B;AAC3B;AAEH,IAAM,qCAAqC;AAE3C,SAAS,sBACP,+BACA,8BAAsC;AAEtC,MAAI,+BAA+B;AACjC,WAAO,qBAAqB;;AAE9B,MAAI,8BAA8B;AAChC,WAAO,qBAAqB;;AAE9B,SAAO,qBAAqB;AAC9B;AAqJA,IAAA;;EAAA,SAAA,QAAA;AAAmC,cAAqBK,QAAA,MAAA;AAAxD,aAAAA,SAAA;;AACE,YAAA,aAAa,cAAI,CAAA,GAAA,MAAM,EAAE,GAAG,IAAA,EAAA,IAAI,WAAA;AAAM,mBAAA,yBAAS;MAAT,CAA2B;AACjE,YAAA,eAAe,cAAI,CAAA,GAAA,MAAM,CAAC,GAAG,IAAA,EAAA,IAAI,WAAA;AAAM,mBAAA,yBAAS;MAAT,CAA2B;AAElE,YAAU,aAAG,SAAC,KAAS;AAGrB,eAAA,cAAc,KAAK;UACjB,SAAS,MAAK,MAAM;UACpB,SAAS,MAAK,MAAM;UACpB,cAAc,MAAK,MAAM;UACzB,sBAAsB,MAAK,MAAM;UACjC,sBAAsB,MAAK,MAAM;UACjC,cAAc,MAAK,MAAM;UACzB,YAAY,MAAK,MAAM;SACxB;MARD;AAUF,YAAU,aAAG,SAAC,KAAS;AAGrB,eAAA,cAAc,KAAK;UACjB,cAAc,MAAK,MAAM;UACzB,sBAAsB,MAAK,MAAM;SAClC;MAHD;AAKF,YAAA,iBAAiB,SACf,KACA,OAEuC;;AAEvC,SAAA,MAAA3B,MAAA,MAAK,OAAM,gBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAA,KAAK,OAAO,MAAK,MAAM,cAAc;MAC/D;AAEA,YAAmB,sBAAG,SAAC,KAAS;;AAC9B,SAAA,MAAAA,MAAA,MAAK,OAAM,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,GAAG;MAClC;AAEA,YAAA,mBAAmB,WAAA;;AACjB,SAAA,MAAAA,MAAA,MAAK,OAAM,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,GAAA;MACzB;AAEA,YAAiB,oBAAG,SAAC,GAAS;AACtB,YAAAA,MAA8B,MAAK,OAAjC,MAAGA,IAAA,KAAE,YAASA,IAAA,WAAE,UAAOA,IAAA;AAC/B,YAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,iBAAO;;AAET,eAAOO,aAAY,SAAS,KAAK,CAAC,GAAG,SAAS;MAChD;AAEA,YAAmB,sBAAG,SAAC,GAAS;AACxB,YAAAP,MAA8B,MAAK,OAAjC,MAAGA,IAAA,KAAE,YAASA,IAAA,WAAE,UAAOA,IAAA;AAC/B,YAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,iBAAO;;AAET,eAAOS,eAAc,WAAW,KAAK,CAAC,GAAG,SAAS;MACpD;AAEA,YAAe,kBAAG,SAAC,GAAS;AACpB,YAAAT,MAA8B,MAAK,OAAjC,MAAGA,IAAA,KAAE,YAASA,IAAA,WAAE,UAAOA,IAAA;AAC/B,YAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,iBAAO;;AAET,eAAOO,aAAY,SAAS,KAAK,CAAC,GAAG,OAAO;MAC9C;AAEA,YAAiB,oBAAG,SAAC,GAAS;AACtB,YAAAP,MAA8B,MAAK,OAAjC,MAAGA,IAAA,KAAE,YAASA,IAAA,WAAE,UAAOA,IAAA;AAC/B,YAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,iBAAO;;AAET,eAAOS,eAAc,WAAW,KAAK,CAAC,GAAG,OAAO;MAClD;AAEA,YAAuB,0BAAG,SAAC,GAAS;;AAC5B,YAAA,KACJ,MAAK,OADC,MAAG,GAAA,KAAE,eAAY,GAAA,cAAE,aAAU,GAAA,YAAE,eAAY,GAAA,cAAE,YAAS,GAAA,WAAE,UAAO,GAAA;AAGvE,YAAM,iBAAgBT,MAAA,MAAK,MAAM,mBAAa,QAAAA,QAAA,SAAAA,MAAI,MAAK,MAAM;AAE7D,YAAI,EAAE,gBAAgB,cAAc,iBAAiB,CAAC,eAAe;AACnE,iBAAO;;AAGT,YAAI,gBAAgB,SAAS;AAC3B,iBAAO,eAAe,eAAe,SAAS,GAAG,GAAG;;AAGtD,YAAI,cAAc,WAAW;AAC3B,iBAAO,eAAe,WAAW,eAAe,GAAG,GAAG;;AAGxD,YAAI,gBAAgB,aAAa,CAAC,SAAS;AACzC,iBAAO,eAAe,WAAW,eAAe,GAAG,GAAG;;AAGxD,eAAO;MACT;AAEA,YAA0B,6BAAG,SAAC,GAAS;;AACrC,YAAI,CAAC,MAAK,wBAAwB,CAAC,GAAG;AACpC,iBAAO;;AAGH,YAAA,KAAmC,MAAK,OAAtC,MAAG,GAAA,KAAE,YAAS,GAAA,WAAE,eAAY,GAAA;AACpC,YAAM,SAAS,SAAS,KAAK,CAAC;AAC9B,YAAM,iBAAgBA,MAAA,MAAK,MAAM,mBAAa,QAAAA,QAAA,SAAAA,MAAI,MAAK,MAAM;AAE7D,YAAI,cAAc;AAChB,iBAAOO,aAAY,QAAQ,aAAa;eACnC;AACL,iBAAOA,aAAY,QAAQ,SAAS;;MAExC;AAEA,YAAwB,2BAAG,SAAC,GAAS;;AACnC,YAAI,CAAC,MAAK,wBAAwB,CAAC,GAAG;AACpC,iBAAO;;AAGH,YAAA,KAA6C,MAAK,OAAhD,MAAG,GAAA,KAAE,UAAO,GAAA,SAAE,aAAU,GAAA,YAAE,eAAY,GAAA;AAC9C,YAAM,SAAS,SAAS,KAAK,CAAC;AAC9B,YAAM,iBAAgBP,MAAA,MAAK,MAAM,mBAAa,QAAAA,QAAA,SAAAA,MAAI,MAAK,MAAM;AAE7D,YAAI,cAAc,cAAc;AAC9B,iBAAOO,aAAY,QAAQ,aAAa;eACnC;AACL,iBAAOA,aAAY,QAAQ,OAAO;;MAEtC;AAEA,YAAyB,4BAAG,SAAC,GAAS;;AAC9B,YAAA,KACJ,MAAK,OADC,MAAG,GAAA,KAAE,eAAY,GAAA,cAAE,aAAU,GAAA,YAAE,eAAY,GAAA,cAAE,YAAS,GAAA,WAAE,UAAO,GAAA;AAGvE,YAAM,iBAAgBP,MAAA,MAAK,MAAM,mBAAa,QAAAA,QAAA,SAAAA,MAAI,MAAK,MAAM;AAE7D,YAAI,EAAE,gBAAgB,cAAc,iBAAiB,CAAC,eAAe;AACnE,iBAAO;;AAGT,YAAI,gBAAgB,SAAS;AAC3B,iBAAO,iBAAiB,eAAe,SAAS,GAAG,GAAG;;AAGxD,YAAI,cAAc,WAAW;AAC3B,iBAAO,iBAAiB,WAAW,eAAe,GAAG,GAAG;;AAG1D,YAAI,gBAAgB,aAAa,CAAC,SAAS;AACzC,iBAAO,iBAAiB,WAAW,eAAe,GAAG,GAAG;;AAG1D,eAAO;MACT;AAEA,YAAa,gBAAG,SAACuB,cAAiB;AAChC,YAAM,MAAM,MAAK,MAAM;AACvB,YAAMC,aAAY,QAAQD,cAAa,CAAC;AACxC,eAAOhB,aAAYgB,cAAa,GAAG,KAAKhB,aAAYiB,YAAW,GAAG;MACpE;AAEA,YAAA,iBAAiB,SAAC,KAAW,GAAS;AACpC,eAAA,QAAQ,GAAG,MAAM,QAAQ,QAAO,CAAE,KAAK,MAAM,SAAS,QAAO,CAAE;MAA/D;AAEF,YAAA,mBAAmB,SAAC,KAAW,GAAS;AACtC,eAAA,QAAQ,GAAG,MAAM,QAAQ,QAAO,CAAE,KAAK,MAAM,WAAW,QAAO,CAAE;MAAjE;AAEF,YAAA,kBAAkB,SAAC,KAAW,GAAW,UAAc;AACrD,eAAA,SAAS,QAAQ,MAAM,KAAK,QAAQ,GAAG,MAAM,QAAQ,QAAQ;MAA7D;AAEF,YAAA,sBAAsB,SAAC,KAAW,GAAW,eAAqB;AAChE,eAAA,cAAc,KAAK,SAAC,cAAY;AAC9B,iBAAA,MAAK,gBAAgB,KAAK,GAAG,YAAY;QAAzC,CAA0C;MAD5C;AAIF,YAAA,oBAAoB,SAAC,KAAW,GAAW,UAAc;AACvD,eAAA,WAAW,GAAG,MAAM,KAAK,QAAQ,GAAG,MAAM,QAAQ,QAAQ;MAA1D;AAEF,YAAA,cAAc,WAAA;AACZ,YAAM,QAAQ,CAAA;AACd,YAAM,gBAAgB,MAAK,MAAM;AAEjC,YAAI,IAAI;AACR,YAAI,qBAAqB;AACzB,YAAI,mBAAmB,eACrB,gBAAgB,MAAK,MAAM,GAAG,GAC9B,MAAK,MAAM,QACX,MAAK,MAAM,gBAAgB;AAG7B,YAAM,gBAAgB,SAACI,eAAkB;AACvC,iBAAA,MAAK,MAAM,iBACP,eACEA,eACA,MAAK,MAAM,QACX,MAAK,MAAM,gBAAgB,IAE7B,MAAK,MAAM;QANf;AAQF,YAAM,aAAa,SAACC,WAAc;AAChC,iBAAA,MAAK,MAAM,iBACP,eACEA,WACA,MAAK,MAAM,QACX,MAAK,MAAM,gBAAgB,IAE7B,MAAK,MAAM;QANf;AAQF,YAAM,WAAW,MAAK,MAAM,WACxB,WAAW,MAAK,MAAM,QAAQ,IAC9B;AAEJ,YAAM,eAAe,MAAK,MAAM,eAC5B,cAAc,MAAK,MAAM,YAAY,IACrC;AAGJ,eAAO,MAAM;AACX,gBAAM,KACJ,cAAA5B,QAAA,cAAC,MAAIyB,QAAA,CAAA,GACC,MAAK,OAAK,EACd,iBAAiB,MAAK,MAAM,qBAC5B,KAAK,GACL,KAAK,kBACL,OAAO,SAAS,MAAK,MAAM,GAAG,GAC9B,YAAY,MAAK,gBACjB,iBAAiB,MAAK,qBACtB,UACA,cACA,gBAAgB,MAAK,MAAM,gBAAe,CAAA,CAAA,CAC1C;AAGJ,cAAI;AAAoB;AAExB;AACA,6BAAmB,SAAS,kBAAkB,CAAC;AAI/C,cAAM,sBACJ,iBAAiB,KAAK;AACxB,cAAM,0BACJ,CAAC,iBAAiB,CAAC,MAAK,cAAc,gBAAgB;AAExD,cAAI,uBAAuB,yBAAyB;AAClD,gBAAI,MAAK,MAAM,eAAe;AAC5B,mCAAqB;mBAChB;AACL;;;;AAKN,eAAO;MACT;AAEA,YAAA,eAAe,SACb,OAGA,GAAS;AAEH,YAAA1B,MAA4B,MAAK,4BAA4B,CAAC,GAA5D,aAAUA,IAAA,YAAE,YAASA,IAAA;AAE7B,YAAI,YAAY;AACd;;AAGF,cAAK,eAAe,gBAAgB,SAAS,GAAG,KAAK;MACvD;AAEA,YAAiB,oBAAG,SAAC,GAAS;AACtB,YAAAA,MAA4B,MAAK,4BAA4B,CAAC,GAA5D,aAAUA,IAAA,YAAE,YAASA,IAAA;AAE7B,YAAI,YAAY;AACd;;AAGF,cAAK,oBAAoB,gBAAgB,SAAS,CAAC;MACrD;AAEA,YAAA,wBAAwB,SAAC,UAAkB8B,UAAa;;AACtD,SAAA,MAAA9B,MAAA,MAAK,OAAM,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG8B,QAAO;AAEpC,SAAA,MAAA,KAAA,MAAK,WAAW,QAAQ,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,MAAK;MAC3C;AAEA,YAAA,2BAA2B,SACzB,OACA,UACA,OAAa;;AAEP,YAAA,KAQF,MAAK,OAPP,WAAQ,GAAA,UACR,eAAY,GAAA,cACZ,kBAAe,GAAA,iBACf,UAAO,GAAA,SACP,UAAO,GAAA,SACP,gCAA6B,GAAA,+BAC7B,+BAA4B,GAAA;AAE9B,YAAI,CAAC;AAAc;AAEnB,YAAM,qBAAqB,sBACzB,+BACA,4BAA4B;AAG9B,YAAM,iBAAiB,MAAK,kBAAkB,kBAAkB;AAEhE,YAAM,cAAa9B,MAAA,cAAc,kBAAkB,OAAG,QAAAA,QAAA,SAAA,SAAAA,IAAA;AAEtD,YAAM,2BAA2B,SAC/B+B,WACA,MACAC,QAAa;;AAEb,cAAIC,qBAAoB;AACxB,cAAIC,sBAAqBF;AACzB,kBAAQD,WAAQ;YACd,KAAK,QAAQ;AACX,cAAAE,qBAAoB,UAClB,MACA,kCAAkC;AAEpC,cAAAC,sBACEF,WAAU,KAAK,IAAIA,SAAQ;AAC7B;YACF,KAAK,QAAQ;AACX,cAAAC,qBAAoB,UAClB,MACA,kCAAkC;AAEpC,cAAAC,sBACEF,WAAU,IAAI,KAAKA,SAAQ;AAC7B;YACF,KAAK,QAAQ;AACX,cAAAC,qBAAoB,UAAU,MAAM,cAAc;AAClD,cAAAC,wBAAqBlC,MAAA,eAAU,QAAV,eAAU,SAAA,SAAV,WAAa,CAAC,OAAC,QAAAA,QAAA,SAAA,SAAAA,IAAE,SAASgC,MAAK,KAChDA,SAAQ,KAAK,iBACbA,SAAQ;AACZ;YACF,KAAK,QAAQ;AACX,cAAAC,qBAAoB,UAAU,MAAM,cAAc;AAClD,cAAAC,wBAAqBC,MAAA,eAAA,QAAA,eAAU,SAAA,SAAV,WAAa,WAAW,SAAS,CAAC,OAAC,QAAAA,QAAA,SAAA,SAAAA,IAAE,SACxDH,MAAK,KAEHA,SAAQ,KAAK,iBACbA,SAAQ;AACZ;;AAGJ,iBAAO,EAAE,mBAAiBC,oBAAE,oBAAkBC,oBAAA;QAChD;AAEA,YAAM,qBAAqB,SACzBH,WACA,cACAC,QAAa;AAEb,cAAM,iBAAiB;AACvB,cAAI,eAAeD;AACnB,cAAI,iBAAiB;AACrB,cAAI,aAAa;AACb,cAAA/B,MAA4C,yBAC9C,cACA,cACAgC,MAAK,GAHDC,qBAAiBjC,IAAA,mBAAEkC,sBAAkBlC,IAAA;AAM3C,iBAAO,CAAC,gBAAgB;AACtB,gBAAI,cAAc,gBAAgB;AAChC,cAAAiC,qBAAoB;AACpB,cAAAC,sBAAqBF;AACrB;;AAGF,gBAAI,WAAWC,qBAAoB,SAAS;AAC1C,6BAAe,QAAQ;AACvB,kBAAM,MAAM,yBACV,cACAA,oBACAC,mBAAkB;AAEpB,cAAAD,qBAAoB,IAAI;AACxB,cAAAC,sBAAqB,IAAI;;AAI3B,gBAAI,WAAWD,qBAAoB,SAAS;AAC1C,6BAAe,QAAQ;AACvB,kBAAM,MAAM,yBACV,cACAA,oBACAC,mBAAkB;AAEpB,cAAAD,qBAAoB,IAAI;AACxB,cAAAC,sBAAqB,IAAI;;AAG3B,gBAAI,oBAAoBD,oBAAmB,MAAK,KAAK,GAAG;AACtD,kBAAM,MAAM,yBACV,cACAA,oBACAC,mBAAkB;AAEpB,cAAAD,qBAAoB,IAAI;AACxB,cAAAC,sBAAqB,IAAI;mBACpB;AACL,+BAAiB;;AAEnB;;AAGF,iBAAO,EAAE,mBAAiBD,oBAAE,oBAAkBC,oBAAA;QAChD;AAEA,YAAI,aAAa,QAAQ,OAAO;AAC9B,cAAI,CAAC,MAAK,gBAAgB,KAAK,GAAG;AAChC,kBAAK,aAAa,OAAO,KAAK;AAC9B,gCAAe,QAAf,oBAAe,SAAA,SAAf,gBAAkB,QAAQ;;AAE5B;;AAGI,YAAA,KAA4C,mBAChD,UACA,cACA,KAAK,GAHC,oBAAiB,GAAA,mBAAE,qBAAkB,GAAA;AAM7C,gBAAQ,UAAQ;UACd,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;AACX,kBAAK,sBAAsB,oBAAoB,iBAAiB;AAChE;;MAEN;AAEA,YAAiB,oBAAG,SAAC,oBAA0B;;AAC7C,gBAAO,MAAAlC,MAAA,cAAc,kBAAkB,OAAG,QAAAA,QAAA,SAAA,SAAAA,IAAA,8BAA4B,QAAA,OAAA,SAAA,KAAA;MACxE;AAEA,YAAA,iBAAiB,SACf,OACA,OAAa;AAEP,YAAAA,MAAuD,MAAK,OAA1D,6BAA0BA,IAAA,4BAAE,uBAAoBA,IAAA;AACxD,YAAM,WAAW,MAAM;AACvB,YAAI,aAAa,QAAQ,KAAK;AAE5B,gBAAM,eAAc;;AAEtB,YAAI,CAAC,4BAA4B;AAC/B,gBAAK,yBAAyB,OAAO,UAAU,KAAK;;AAGtD,gCAAwB,qBAAqB,KAAK;MACpD;AAEA,YAAA,iBAAiB,SACf,OAGA,GAAS;AAET,YAAM,YAAY,WAAW,MAAK,MAAM,KAAK,CAAC;AAE9C,YAAI,kBAAkB,WAAW,MAAK,KAAK,GAAG;AAC5C;;AAGF,cAAK,eAAe,kBAAkB,SAAS,GAAG,KAAK;MACzD;AAEA,YAAmB,sBAAG,SAAC,GAAS;AAC9B,YAAM,YAAY,WAAW,MAAK,MAAM,KAAK,CAAC;AAE9C,YAAI,kBAAkB,WAAW,MAAK,KAAK,GAAG;AAC5C;;AAGF,cAAK,oBAAoB,kBAAkB,SAAS,CAAC;MACvD;AAEA,YAAA,0BAA0B,SAAC,YAAoB8B,UAAa;;AAC1D,YAAI,MAAK,WAAWA,QAAO,KAAK,MAAK,WAAWA,QAAO,GAAG;AACxD;;AAEF,SAAA,MAAA9B,MAAA,MAAK,OAAM,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG8B,QAAO;AACpC,SAAA,MAAA,KAAA,MAAK,aAAa,aAAa,CAAC,OAAG,QAAA,OAAA,SAAA,SAAA,GAAA,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,MAAK;MACnD;AAEA,YAAA,mBAAmB,SACjB,OACA,SAAe;;AAEf,YAAM,WAAW,MAAM;AACvB,YAAI,CAAC,MAAK,MAAM,4BAA4B;AAC1C,kBAAQ,UAAQ;YACd,KAAK,QAAQ;AACX,oBAAK,eAAe,OAAO,OAAO;AAClC,eAAA,MAAA9B,MAAA,MAAK,OAAM,qBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAA,MAAK,MAAM,QAAQ;AAChD;YACF,KAAK,QAAQ;AACX,kBAAI,CAAC,MAAK,MAAM,cAAc;AAC5B;;AAEF,oBAAK,wBACH,YAAY,IAAI,IAAI,UAAU,GAC9B,YAAY,MAAK,MAAM,cAAc,CAAC,CAAC;AAEzC;YACF,KAAK,QAAQ;AACX,kBAAI,CAAC,MAAK,MAAM,cAAc;AAC5B;;AAEF,oBAAK,wBACH,YAAY,IAAI,IAAI,UAAU,GAC9B,YAAY,MAAK,MAAM,cAAc,CAAC,CAAC;AAEzC;;;MAGR;AAEA,YAA2B,8BAAG,SAC5B,OAAa;;AAKP,YAAA,KAAwD,MAAK,OAA3D,MAAG,GAAA,KAAE,UAAO,GAAA,SAAE,UAAO,GAAA,SAAE,eAAY,GAAA,cAAE,eAAY,GAAA;AACzD,YAAM,YAAY,SAAS,KAAK,KAAK;AACrC,eAAO;UACL,aACEA,OAAE,WAAW,WAAW,gBAAgB,iBACtC,gBAAgB,WAAW,MAAK,KAAK,OAAE,QAAAA,QAAA,SAAAA,MACzC;UACF;;MAEJ;AAEA,YAAe,kBAAG,SAAC,OAAa;AACtB,YAAA,aAAe,MAAK,4BAA4B,KAAK,EAAC;AAC9D,eAAO;MACT;AAgBA,YAAkB,qBAAG,SAAC,GAAS;AACvB,YAAAA,MACJ,MAAK,OADC,MAAGA,IAAA,KAAE,YAASA,IAAA,WAAE,UAAOA,IAAA,SAAE,eAAYA,IAAA,cAAE,iBAAcA,IAAA;AAE7D,YAAM,kBAAkB,iBACpB,eAAe,SAAS,KAAK,CAAC,CAAC,IAC/B;AAEJ,YAAM,YAAY,MAAK,aAAY;AAEnC,eAAO,KACL,gCACA,2BAAA,OAA2B,CAAC,GAC5B,iBACA;UACE,0CAA0C,MAAK,gBAAgB,CAAC;UAChE,0CAA0C,YACtC,MAAK,oBAAoB,KAAK,GAAG,SAAS,IAC1C;UACJ,mDACE,CAAC,MAAK,MAAM,8BACZ,gBACA,MAAK,gBAAgB,KAAK,GAAG,YAAY,KACzC,CAAC,MAAK,gBAAgB,CAAC;UACzB,oDACE,MAAK,wBAAwB,CAAC;UAChC,0CACE,aAAa,UACT,eAAe,WAAW,SAAS,GAAG,GAAG,IACzC;UACN,6CAA6C,MAAK,kBAAkB,CAAC;UACrE,2CAA2C,MAAK,gBAAgB,CAAC;UACjE,uDACE,MAAK,2BAA2B,CAAC;UACnC,qDACE,MAAK,yBAAyB,CAAC;UACjC,uCAAuC,MAAK,eAAe,KAAK,CAAC;QAClE,CAAA;MAEL;AAEA,YAAW,cAAG,SAAC,GAAS;AACtB,YAAI,MAAK,MAAM,gBAAgB,MAAM;AACnC,iBAAO;;AAET,YAAM,mBAAmB,SAAS,MAAK,MAAM,YAAY;AACjD,YAAY,6BAClB,MAAK,4BAA4B,gBAAgB,EAAC;AAEpD,YAAM,WACJ,MAAM,oBACN,EAAE,8BAA8B,MAAK,MAAM,8BACvC,MACA;AAEN,eAAO;MACT;AAEA,YAAkB,qBAAG,SAAC,GAAS;AAC7B,YAAI,MAAK,MAAM,gBAAgB,MAAM;AACnC,iBAAO;;AAET,YAAM,qBAAqB,WAAW,MAAK,MAAM,YAAY;AAC7D,YAAM,2BAA2B,kBAC/B,MAAK,MAAM,KACX,MAAK,KAAK;AAGZ,YAAM,WACJ,MAAM,sBACN,EAAE,4BAA4B,MAAK,MAAM,8BACrC,MACA;AAEN,eAAO;MACT;AAEA,YAAY,eAAG,SAAC,OAAa;AACrB,YAAAA,MAKF,MAAK,OAJP,KAAAA,IAAA,0BAAA,2BAA2B,OAAA,SAAA,WAAQ,IACnC,KAAAA,IAAA,4BAAA,6BAA6B,OAAA,SAAA,kBAAe,IAC5C,MAAGA,IAAA,KACH,SAAMA,IAAA;AAER,YAAM,YAAY,SAAS,KAAK,KAAK;AACrC,YAAM,SACJ,MAAK,WAAW,SAAS,KAAK,MAAK,WAAW,SAAS,IACnD,6BACA;AAEN,eAAO,GAAG,OAAA,QAAU,GAAA,EAAA,OAAA,WAAW,WAAW,aAAa,MAAM,CAAC;MAChE;AAEA,YAAoB,uBAAG,SAAC,GAAS;AACzB,YAAAA,MAYF,MAAK,OAXP,MAAGA,IAAA,KACH,YAASA,IAAA,WACT,UAAOA,IAAA,SACP,WAAQA,IAAA,UACR,UAAOA,IAAA,SACP,UAAOA,IAAA,SACP,eAAYA,IAAA,cACZ,eAAYA,IAAA,cACZ,aAAUA,IAAA,YACV,eAAYA,IAAA,cACZ,6BAA0BA,IAAA;AAG5B,YAAM,cACH,WAAW,WAAW,gBAAgB,gBAAgB,eACvD,kBAAkB,WAAW,KAAK,CAAC,GAAG,MAAK,KAAK;AAElD,eAAO,KACL,kCACA,6BAA6B,OAAA,CAAC,GAC9B;UACE,4CAA4C;UAC5C,4CAA4C,WACxC,MAAK,kBAAkB,KAAK,GAAG,QAAQ,IACvC;UACJ,qDACE,CAAC,8BACD,gBACA,MAAK,kBAAkB,KAAK,GAAG,YAAY,KAC3C,CAAC;UACH,sDACE,MAAK,0BAA0B,CAAC;UAClC,4CACE,aAAa,UACT,iBAAiB,WAAW,SAAS,GAAG,GAAG,IAC3C;UACN,+CACE,MAAK,oBAAoB,CAAC;UAC5B,6CAA6C,MAAK,kBAAkB,CAAC;QACtE,CAAA;MAEL;AAEA,YAAe,kBAAG,SAAC,GAAS;AACpB,YAAAA,MACJ,MAAK,OADC,0BAAuBA,IAAA,yBAAE,qBAAkBA,IAAA,oBAAE,SAAMA,IAAA,QAAE,MAAGA,IAAA;AAEhE,YAAM,iBAAiB,sBAAsB,GAAG,MAAM;AACtD,YAAM,gBAAgB,iBAAiB,GAAG,MAAM;AAChD,YAAI,oBAAoB;AACtB,iBAAO,mBAAmB,GAAG,gBAAgB,eAAe,GAAG;;AAEjE,eAAO,0BAA0B,gBAAgB;MACnD;AAEA,YAAiB,oBAAG,SAAC,GAAS;;AACtB,YAAA,KAAmC,MAAK,OAAtC,uBAAoB,GAAA,sBAAE,SAAM,GAAA;AACpC,YAAM,eAAe,wBAAwB,GAAG,MAAM;AACtD,gBAAOA,MAAA,yBAAA,QAAA,yBAAA,SAAA,SAAA,qBAAuB,GAAG,YAAY,OAAC,QAAAA,QAAA,SAAAA,MAAI;MACpD;AAEA,YAAA,eAAe,WAAA;;AACP,YAAA,KAKF,MAAK,OAJP,+BAA4B,GAAA,8BAC5B,gCAA6B,GAAA,+BAC7B,MAAG,GAAA,KACH,WAAQ,GAAA;AAGV,YAAM,gBACJA,MAAA,cACE,sBACE,+BACA,4BAA4B,CAC7B,OACF,QAAAA,QAAA,SAAA,SAAAA,IAAE;AACL,eAAO,iBAAA,QAAA,iBAAA,SAAA,SAAA,aAAc,IAAI,SAAC,OAAO,GAAC;AAAK,iBACrC,cAAAC,QAAK,cAAA,OAAA,EAAA,WAAU,mCAAkC,KAAK,EAAC,GACpD,MAAM,IAAI,SAAC,GAAG,GAAC;AAAK,mBACnB,cAAAA,QAAA,cAAA,OAAA,EACE,KAAK,MAAK,WAAW,CAAC,GACtB,KAAK,GACL,SAAS,SAAC,OAAK;AACb,oBAAK,aAAa,OAAO,CAAC;YAC5B,GACA,WAAW,SAAC,OAAK;AACf,kBAAI,eAAe,KAAK,GAAG;AACzB,sBAAM,eAAc;AACpB,sBAAM,MAAM,QAAQ;;AAGtB,oBAAK,eAAe,OAAO,CAAC;eAE9B,cACE,CAAC,MAAK,MAAM,kBACR,WAAM;AAAA,qBAAA,MAAK,kBAAkB,CAAC;YAAC,IAC/B,QAEN,gBACE,MAAK,MAAM,kBACP,WAAM;AAAA,qBAAA,MAAK,kBAAkB,CAAC;YAAC,IAC/B,QAEN,UAAU,OAAO,MAAK,YAAY,CAAC,CAAC,GACpC,WAAW,MAAK,mBAAmB,CAAC,GACrB,iBAAA,MAAK,gBAAgB,CAAC,GACrC,MAAK,UAAQ,cACD,MAAK,aAAa,CAAC,GACjB,gBAAA,MAAK,eAAe,KAAK,CAAC,IAAI,SAAS,QAAS,iBAE5D,WAAW,MAAK,gBAAgB,KAAK,GAAG,QAAQ,IAAI,OAAS,GAG9D,MAAK,gBAAgB,CAAC,CAAC;UACpB,CACP,CAAC;QAEL,CAAA;MACH;AAEA,YAAA,iBAAiB,WAAA;AACT,YAAAD,MAAoB,MAAK,OAAvB,MAAGA,IAAA,KAAE,WAAQA,IAAA;AACrB,YAAM,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;AAC5B,eACE,cAAAC,QAAK,cAAA,OAAA,EAAA,WAAU,oCAAmC,GAC/C,SAAS,IAAI,SAAC,GAAG,GAAM;AAAA,iBACtB,cAAAA,QACE,cAAA,OAAA,EAAA,KAAK,GACL,KAAK,MAAK,aAAa,CAAC,GACxB,MAAK,UACL,SAAS,SAAC,OAAK;AACb,kBAAK,eAAe,OAAO,CAAC;UAC9B,GACA,WAAW,SAAC,OAAK;AACf,kBAAK,iBAAiB,OAAO,CAAC;aAEhC,cACE,CAAC,MAAK,MAAM,kBACR,WAAM;AAAA,mBAAA,MAAK,oBAAoB,CAAC;UAAC,IACjC,QAEN,gBACE,MAAK,MAAM,kBACP,WAAM;AAAA,mBAAA,MAAK,oBAAoB,CAAC;UAAC,IACjC,QAEN,WAAW,MAAK,qBAAqB,CAAC,GAAC,iBAErC,WAAW,MAAK,kBAAkB,KAAK,GAAG,QAAQ,IAAI,QAExD,UAAU,OAAO,MAAK,mBAAmB,CAAC,CAAC,GAAC,gBAC9B,MAAK,iBAAiB,KAAK,CAAC,IAAI,SAAS,OAAS,GAE/D,MAAK,kBAAkB,CAAC,CAAC;QA5BN,CA8BvB,CAAC;MAGR;AAEA,YAAA,gBAAgB,WAAA;AACR,YAAAD,MAOF,MAAK,OANP,gBAAaA,IAAA,eACb,eAAYA,IAAA,cACZ,aAAUA,IAAA,YACV,sBAAmBA,IAAA,qBACnB,wBAAqBA,IAAA,uBACrB,iBAAcA,IAAA;AAGhB,eAAO,KACL,2BACA;UACE,4CACE,kBAAkB,gBAAgB;QACrC,GACD,EAAE,iCAAiC,oBAAmB,GACtD,EAAE,mCAAmC,sBAAqB,GAC1D,EAAE,gCAAgC,eAAc,CAAE;MAEtD;;;AA/RA,IAAA2B,OAAA,UAAA,eAAA,WAAA;AACQ,UAAA3B,MAA+C,KAAK,OAAlD,WAAQA,IAAA,UAAE,gBAAaA,IAAA,eAAE,kBAAeA,IAAA;AAEhD,UAAI,iBAAiB;AACnB,eAAO;;AAGT,UAAI,UAAU;AACZ,eAAO,CAAC,QAAQ;;AAGlB,aAAO;;AAsRT,IAAA2B,OAAA,UAAA,SAAA,WAAA;AACQ,UAAA3B,MAKF,KAAK,OAJP,sBAAmBA,IAAA,qBACnB,wBAAqBA,IAAA,uBACrB,MAAGA,IAAA,KACH,KAA0BA,IAAA,iBAA1B,kBAAkB,OAAA,SAAA,WAAQ;AAG5B,UAAM,2BAA2B,kBAC7B,gBAAgB,KAAI,IAAK,MACzB;AAEJ,aACE,cAAAC,QAAA,cAAA,OAAA,EACE,WAAW,KAAK,cAAa,GAC7B,cACE,CAAC,KAAK,MAAM,kBAAkB,KAAK,mBAAmB,QAExD,gBACE,KAAK,MAAM,kBAAkB,KAAK,mBAAmB,QAAS,cAEpD,GAAG,OAAA,wBAAwB,EAAA,OAAG,WAAW,KAAK,cAAc,KAAK,MAAM,MAAM,CAAC,GAC1F,MAAK,UAAS,GAEb,sBACG,KAAK,aAAY,IACjB,wBACE,KAAK,eAAc,IACnB,KAAK,YAAW,CAAE;;AAIhC,WAAC0B;EAAD,EAz2BmC,uBAAS;;ACjO5C,IAAA;;EAAA,SAAA,QAAA;AAAkD,cAAoCS,uBAAA,MAAA;AAAtF,aAAAA,wBAAA;;AACE,YAAA,kBAAkB,SAAC,GAAS;AAAc,eAAA,MAAK,MAAM,UAAU;MAAC;AAEhE,YAAA,gBAAgB,WAAA;AACd,eAAO,MAAK,MAAM,WAAW,IAC3B,SAAC,OAAe,GAAkC;AAAA,iBAChD,cAAAnC,QAAA;YAAA;YAAA,EACE,WACE,MAAK,gBAAgB,CAAC,IAClB,kFACA,kCAEN,KAAK,OACL,SAAS,MAAK,SAAS,KAAK,OAAM,CAAC,GAAC,iBACrB,MAAK,gBAAgB,CAAC,IAAI,SAAS,OAAS;YAE1D,MAAK,gBAAgB,CAAC,IACrB,cAAAA,QAAA,cAAA,QAAA,EAAM,WAAU,2CAA0C,GAAA,GAAA,IAE1D;YAED;UAAK;QAhBwC,CAkBjD;MAEL;AAEA,YAAA,WAAW,SAAC,OAAa;AAAW,eAAA,MAAK,MAAM,SAAS,KAAK;MAAC;AAE9D,YAAkB,qBAAG,WAAY;AAAA,eAAA,MAAK,MAAM,SAAQ;MAAnB;;;AAEjC,IAAAmC,sBAAA,UAAA,SAAA,WAAA;AACE,aACE,cAAAnC,QAAA,cAAC,qBAAmB,EAClB,WAAU,oCACV,gBAAgB,KAAK,mBAAkB,GAEtC,KAAK,cAAa,CAAE;;AAI7B,WAACmC;EAAD,EAzCkD,uBAAS;;ACgB3D,IAAA;;EAAA,SAAA,QAAA;AAA2C,cAG1CC,gBAAA,MAAA;AAHD,aAAAA,iBAAA;;AAIE,YAAA,QAA4B;QAC1B,iBAAiB;;AAGnB,YAAmB,sBAAG,SAAC,YAAoB;AACzC,eAAA,WAAW,IACT,SAAC,GAAW,GAAkC;AAAA,iBAC5C,cAAApC,QAAA,cAAA,UAAA,EAAQ,KAAK,GAAG,OAAO,EAAC,GACrB,CAAC;QAFwC,CAI7C;MALH;AAQF,YAAgB,mBAAG,SAAC,YAAoB;AAAyB,eAC/D,cAAAA,QAAA,cAAA,UAAA,EACE,OAAO,MAAK,MAAM,OAClB,WAAU,kCACV,UAAU,SAAC,GAAM;AAAA,iBAAA,MAAK,SAAS,SAAS,EAAE,OAAO,KAAK,CAAC;QAAC,EAAA,GAEvD,MAAK,oBAAoB,UAAU,CAAC;MANwB;AAUjE,YAAA,iBAAiB,SACf,SACA,YAAoB;AACG,eACvB,cAAAA,QACE;UAAA;UAAA,EAAA,KAAI,QACJ,OAAO,EAAE,YAAY,UAAU,YAAY,SAAQ,GACnD,WAAU,qCACV,SAAS,MAAK,eAAc;UAE5B,cAAAA,QAAM,cAAA,QAAA,EAAA,WAAU,gDAA+C,CAAG;UAClE,cAAAA,QAAA,cAAA,QAAA,EAAM,WAAU,oDAAmD,GAChE,WAAW,MAAK,MAAM,KAAK,CAAC;QACxB;MAEV;AAED,YAAA,iBAAiB,SAAC,YAA6C;AAAA,eAC7D,cAAAA,QAAC,cAAA,sBACCyB,QAAA,EAAA,KAAI,WAAU,GACV,MAAK,OACT,EAAA,YACA,UAAU,MAAK,UACf,UAAU,MAAK,eAAc,CAAA,CAAA;MAEhC;AAED,YAAgB,mBAAG,SAAC,YAAoB;AAC9B,YAAA,kBAAoB,MAAK,MAAK;AACtC,YAAM,SAAS,CAAC,MAAK,eAAe,CAAC,iBAAiB,UAAU,CAAC;AACjE,YAAI,iBAAiB;AACnB,iBAAO,QAAQ,MAAK,eAAe,UAAU,CAAC;;AAEhD,eAAO;MACT;AAEA,YAAQ,WAAG,SAAC,OAAa;AACvB,cAAK,eAAc;AACnB,YAAI,UAAU,MAAK,MAAM,OAAO;AAC9B,gBAAK,MAAM,SAAS,KAAK;;MAE7B;AAEA,YAAA,iBAAiB,WAAA;AACf,eAAA,MAAK,SAAS;UACZ,iBAAiB,CAAC,MAAK,MAAM;SAC9B;MAFD;;;AAIF,IAAAW,eAAA,UAAA,SAAA,WAAA;AAAA,UAwBC,QAAA;AAvBC,UAAM,aAAuB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,EAAE,IAClE,KAAK,MAAM,0BACP,SAAC,GAAsB;AAAA,eAAA,sBAAsB,GAAG,MAAK,MAAM,MAAM;MAAC,IAClE,SAAC,GAAS;AAAa,eAAA,iBAAiB,GAAG,MAAK,MAAM,MAAM;MAArC,CAAsC;AAGnE,UAAI;AACJ,cAAQ,KAAK,MAAM,cAAY;QAC7B,KAAK;AACH,6BAAmB,KAAK,iBAAiB,UAAU;AACnD;QACF,KAAK;AACH,6BAAmB,KAAK,iBAAiB,UAAU;AACnD;;AAGJ,aACE,cAAApC,QAAA,cAAA,OAAA,EACE,WAAW,0FAAA,OAA0F,KAAK,MAAM,YAAY,EAAE,GAE7H,gBAAgB;;AAIzB,WAACoC;EAAD,EApG2C,uBAAS;;ACXpD,SAAS,mBAAmB,SAAe,SAAa;AACtD,MAAM,OAAO,CAAA;AAEb,MAAI,WAAW,gBAAgB,OAAO;AACtC,MAAM,WAAW,gBAAgB,OAAO;AAExC,SAAO,CAAC,QAAQ,UAAU,QAAQ,GAAG;AACnC,SAAK,KAAK,QAAQ,QAAQ,CAAC;AAE3B,eAAW,UAAU,UAAU,CAAC;;AAElC,SAAO;AACT;AAiBA,IAAA;;EAAA,SAAA,QAAA;AAAsD,cAGrDC,2BAAA,MAAA;AACC,aAAAA,0BAAY,OAAoC;AAC9C,UAAA,QAAA,OAAK,KAAA,MAAC,KAAK,KAAE;AAUf,YAAA,gBAAgB,WAAA;AACd,eAAO,MAAK,MAAM,eAAe,IAC/B,SAAC,WAAe;AACd,cAAM,iBAAiB,QAAQ,SAAS;AACxC,cAAM,kBACJjC,YAAW,MAAK,MAAM,MAAM,SAAS,KACrCE,aAAY,MAAK,MAAM,MAAM,SAAS;AAExC,iBACE,cAAAN,QAAA;YAAA;YAAA,EACE,WACE,kBACI,6DACA,uCAEN,KAAK,gBACL,SAAS,MAAK,SAAS,KAAK,OAAM,cAAc,GAAC,iBAClC,kBAAkB,SAAS,OAAS;YAElD,kBACC,cAAAA,QAAA,cAAA,QAAA,EAAM,WAAU,gDAA+C,GAAA,GAAA,IAI/D;YAED,WAAW,WAAW,MAAK,MAAM,YAAY,MAAK,MAAM,MAAM;UAAC;QAGtE,CAAC;MAEL;AAEA,YAAA,WAAW,SAAC,WAAiB;AAAW,eAAA,MAAK,MAAM,SAAS,SAAS;MAAC;AAEtE,YAAA,qBAAqB,WAAA;AACnB,cAAK,MAAM,SAAQ;MACrB;AA7CE,YAAK,QAAQ;QACX,gBAAgB,mBACd,MAAK,MAAM,SACX,MAAK,MAAM,OAAO;;;;AA4CxB,IAAAqC,0BAAA,UAAA,SAAA,WAAA;AACE,UAAM,gBAAgB,KAAK;QACzB,yCAAyC;QACzC,qDACE,KAAK,MAAM;MACd,CAAA;AAED,aACE,cAAArC,QAAA,cAAC,qBAAmB,EAClB,WAAW,eACX,gBAAgB,KAAK,mBAAkB,GAEtC,KAAK,cAAa,CAAE;;AAI7B,WAACqC;EAAD,EAtEsD,uBAAS;;AChB/D,IAAA;;EAAA,SAAA,QAAA;AAA+C,cAG9CC,oBAAA,MAAA;AAHD,aAAAA,qBAAA;;AAIE,YAAA,QAAgC;QAC9B,iBAAiB;;AAGnB,YAAA,sBAAsB,WAAA;AACpB,YAAI,WAAW,gBAAgB,MAAK,MAAM,OAAO;AACjD,YAAM,WAAW,gBAAgB,MAAK,MAAM,OAAO;AACnD,YAAM,UAAU,CAAA;AAEhB,eAAO,CAAC,QAAQ,UAAU,QAAQ,GAAG;AACnC,cAAM,YAAY,QAAQ,QAAQ;AAClC,kBAAQ,KACN,cAAAtC,QAAA,cAAA,UAAA,EAAQ,KAAK,WAAW,OAAO,UAAS,GACrC,WAAW,UAAU,MAAK,MAAM,YAAY,MAAK,MAAM,MAAM,CAAC,CACxD;AAGX,qBAAW,UAAU,UAAU,CAAC;;AAGlC,eAAO;MACT;AAEA,YAAc,iBAAG,SAAC,OAA2C;AAC3D,cAAK,SAAS,SAAS,MAAM,OAAO,KAAK,CAAC;MAC5C;AAEA,YAAA,mBAAmB,WAA0B;AAAA,eAC3C,cAAAA,QAAA,cAAA,UAAA,EACE,OAAO,QAAQ,gBAAgB,MAAK,MAAM,IAAI,CAAC,GAC/C,WAAU,uCACV,UAAU,MAAK,eAAc,GAE5B,MAAK,oBAAmB,CAAE;MAE9B;AAED,YAAc,iBAAG,SAAC,SAAgB;AAChC,YAAM,YAAY,WAChB,MAAK,MAAM,MACX,MAAK,MAAM,YACX,MAAK,MAAM,MAAM;AAGnB,eACE,cAAAA,QAAA;UAAA;UAAA,EACE,KAAI,QACJ,OAAO,EAAE,YAAY,UAAU,YAAY,SAAQ,GACnD,WAAU,0CACV,SAAS,MAAK,eAAc;UAE5B,cAAAA,QAAM,cAAA,QAAA,EAAA,WAAU,qDAAoD,CAAG;UACvE,cAAAA,QAAM,cAAA,QAAA,EAAA,WAAU,8DAA6D,GAC1E,SAAS;QACL;MAGb;AAEA,YAAA,iBAAiB,WAAA;AAA0B,eACzC,cAAAA,QAAC,cAAA,0BACCyB,QAAA,EAAA,KAAI,WAAU,GACV,MAAK,OAAK,EACd,UAAU,MAAK,UACf,UAAU,MAAK,eAAc,CAAA,CAAA;MAEhC;AAED,YAAA,mBAAmB,WAAA;AACT,YAAA,kBAAoB,MAAK,MAAK;AACtC,YAAM,SAAS,CAAC,MAAK,eAAe,CAAC,eAAe,CAAC;AACrD,YAAI,iBAAiB;AACnB,iBAAO,QAAQ,MAAK,eAAc,CAAE;;AAEtC,eAAO;MACT;AAEA,YAAQ,WAAG,SAAC,gBAAsB;AAChC,cAAK,eAAc;AAEnB,YAAM,cAAc,QAAQ,cAAc;AAE1C,YACErB,YAAW,MAAK,MAAM,MAAM,WAAW,KACvCE,aAAY,MAAK,MAAM,MAAM,WAAW,GACxC;AACA;;AAGF,cAAK,MAAM,SAAS,WAAW;MACjC;AAEA,YAAA,iBAAiB,WAAA;AACf,eAAA,MAAK,SAAS;UACZ,iBAAiB,CAAC,MAAK,MAAM;SAC9B;MAFD;;;AAIF,IAAAgC,mBAAA,UAAA,SAAA,WAAA;AACE,UAAI;AACJ,cAAQ,KAAK,MAAM,cAAY;QAC7B,KAAK;AACH,6BAAmB,KAAK,iBAAgB;AACxC;QACF,KAAK;AACH,6BAAmB,KAAK,iBAAgB;AACxC;;AAGJ,aACE,cAAAtC,QAAA,cAAA,OAAA,EACE,WAAW,oGAAA,OAAoG,KAAK,MAAM,YAAY,EAAE,GAEvI,gBAAgB;;AAIzB,WAACsC;EAAD,EAxH+C,uBAAS;;ACgBxD,IAAA;;EAAA,SAAA,QAAA;AAAkC,cAA+BC,OAAA,MAAA;AAAjE,aAAAA,QAAA;;AAmBE,YAAA,QAAmB;QACjB,QAAQ;;AAmBV,YAAA,0BAA0B,WAAA;AACxB,8BAAsB,WAAA;;AACpB,cAAI,CAAC,MAAK;AAAM;AAEhB,gBAAK,KAAK,aACR,KAAC,MAAK,YACJA,MAAK,mBACH,MAAK,MAAM,WACP,MAAK,MAAM,SAAS,iBACjB,MAAAxC,MAAA,MAAK,YAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAA,kBAAgB,QAAA,OAAA,SAAA,KAAA,KAChC,MAAK,KAAK,cACd,MAAK,QAAQ,OACb,QAAA,OAAA,SAAA,KACJ;QACJ,CAAC;MACH;AAEA,YAAW,cAAG,SAAC,MAAU;;AACvB,aACI,MAAK,MAAM,WAAW,MAAK,MAAM,YACjC,sBAAsB,MAAM,MAAK,KAAK,MACtC,MAAK,MAAM,gBACX,MAAK,MAAM,gBACX,MAAK,MAAM,eACX,eAAe,MAAM,MAAK,KAAK,GACjC;AACA;;AAEF,SAAA,MAAAA,MAAA,MAAK,OAAM,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,IAAI;MAC5B;AAEA,YAAc,iBAAG,SAAC,MAAU;AAC1B,eAAA,MAAK,MAAM,YAAY,aAAa,MAAK,MAAM,UAAU,IAAI;MAA7D;AAEF,YAAc,iBAAG,SAAC,MAAU;AAC1B,gBAAE,MAAK,MAAM,WAAW,MAAK,MAAM,YACjC,sBAAsB,MAAM,MAAK,KAAK,MACtC,MAAK,MAAM,gBACX,MAAK,MAAM,gBACX,MAAK,MAAM,eACX,eAAe,MAAM,MAAK,KAAK;MALjC;AAOF,YAAS,YAAG,SAAC,MAAU;;AACrB,YAAM,UAAU;UACd;UACA,MAAK,MAAM,gBAAgB,MAAK,MAAM,cAAc,IAAI,IAAI;;AAG9D,YAAI,MAAK,eAAe,IAAI,GAAG;AAC7B,kBAAQ,KAAK,4CAA4C;;AAG3D,YAAI,MAAK,eAAe,IAAI,GAAG;AAC7B,kBAAQ,KAAK,4CAA4C;;AAI3D,YACE,MAAK,MAAM,gBACV,SAAS,IAAI,IAAI,OAAO,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,QAC5DA,MAAA,MAAK,MAAM,eAAS,QAAAA,QAAA,SAAAA,MAAIwC,MAAK,aAAa,aAAa,QACzD,GACF;AACA,kBAAQ,KAAK,4CAA4C;;AAG3D,eAAO,QAAQ,KAAK,GAAG;MACzB;AAEA,YAAA,kBAAkB,SAChB,OACA,MAAU;;AAEV,YAAI,MAAM,QAAQ,QAAQ,OAAO;AAC/B,gBAAM,eAAc;AACpB,gBAAM,MAAM,QAAQ;;AAGtB,aACG,MAAM,QAAQ,QAAQ,WAAW,MAAM,QAAQ,QAAQ,cACxD,MAAM,kBAAkB,eACxB,MAAM,OAAO,iBACb;AACA,gBAAM,eAAc;AACpB,gBAAM,OAAO,2BAA2B,eACtC,MAAM,OAAO,gBAAgB,MAAK;;AAEtC,aACG,MAAM,QAAQ,QAAQ,aAAa,MAAM,QAAQ,QAAQ,eAC1D,MAAM,kBAAkB,eACxB,MAAM,OAAO,aACb;AACA,gBAAM,eAAc;AACpB,gBAAM,OAAO,uBAAuB,eAClC,MAAM,OAAO,YAAY,MAAK;;AAGlC,YAAI,MAAM,QAAQ,QAAQ,OAAO;AAC/B,gBAAK,YAAY,IAAI;;AAEvB,SAAA,MAAAxC,MAAA,MAAK,OAAM,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,KAAK;MACpC;AAEA,YAAA,cAAc,WAAA;;AACZ,YAAI,QAAgB,CAAA;AACpB,YAAMyC,UACJ,OAAO,MAAK,MAAM,WAAW,WAAW,MAAK,MAAM,SAAS;AAC9D,YAAM,aAAYzC,MAAA,MAAK,MAAM,eAAS,QAAAA,QAAA,SAAAA,MAAIwC,MAAK,aAAa;AAE5D,YAAM,aACJ,MAAK,MAAM,YAAY,MAAK,MAAM,cAAc,QAAO;AAEzD,YAAM,OAAO,cAAc,UAAU;AACrC,YAAM,oBACJ,MAAK,MAAM,eACX,MAAK,MAAM,YAAY,KAAK,SAAU,GAAS,GAAO;AACpD,iBAAO,EAAE,QAAO,IAAK,EAAE,QAAO;QAChC,CAAC;AAEH,YAAM,eAAe,KAAK,cAAc,UAAU;AAClD,YAAM,aAAa,eAAe;AAElC,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,cAAM,cAAc,WAAW,MAAM,IAAI,SAAS;AAClD,gBAAM,KAAK,WAAW;AAEtB,cAAI,mBAAmB;AACrB,gBAAM,gBAAgB,mBACpB,MACA,aACA,GACA,WACA,iBAAiB;AAEnB,oBAAQ,MAAM,OAAO,aAAa;;;AAKtC,YAAM,cAAc,MAAM,OAAyB,SAAC,MAAM,MAAI;AAC5D,cAAI,KAAK,QAAO,KAAM,WAAW,QAAO,GAAI;AAC1C,mBAAO;;AAET,iBAAO;QACT,GAAG,MAAM,CAAC,CAAC;AAEX,eAAO,MAAM,IAAwB,SAAC,MAAI;AACxC,iBACE,cAAAvC,QAAA,cAAA,MAAA,EACE,KAAK,KAAK,QAAO,GACjB,SAAS,MAAK,YAAY,KAAK,OAAM,IAAI,GACzC,WAAW,MAAK,UAAU,IAAI,GAC9B,KAAK,SAAC,IAAiB;AACrB,gBAAI,SAAS,aAAa;AACxB,oBAAK,WAAW;;UAEpB,GACA,WAAW,SAAC,OAAyC;AACnD,kBAAK,gBAAgB,OAAO,IAAI;UAClC,GACA,UAAU,SAAS,cAAc,IAAI,IACrC,MAAK,UACU,iBAAA,MAAK,eAAe,IAAI,IAAI,SAAS,QACrC,iBAAA,MAAK,eAAe,IAAI,IAAI,SAAS,OAAS,GAE5D,WAAW,MAAMwC,SAAQ,MAAK,MAAM,MAAM,CAAC;QAGlD,CAAC;MACH;AAEA,YAAA,oBAAoB,WAAA;AAClB,YAAI,MAAK,MAAM,oBAAoB,OAAO;AACxC,iBAAO,cAAAxC,QAAA,cAAA,cAAAA,QAAA,UAAA,IAAA;;AAGT,eACE,cAAAA,QAAA;UAAA;UAAA,EACE,WAAW,2DAAA,OACT,MAAK,MAAM,qBACP,yCACA,EAAE,GAER,KAAK,SAAC,QAAsB;AAC1B,kBAAK,SAAS;YACf;UAED,cAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,gCAA+B,GAC3C,MAAK,MAAM,WAAW;QACnB;MAGZ;;;AAtOA,WAAA,eAAWuC,OAAY,gBAAA;MAAvB,KAAA,WAAA;AACE,eAAO;UACL,WAAW;UACX,aAAa;UACb,aAAa;UACb,iBAAiB;;;;;IAEpB,CAAA;AAeD,IAAAA,MAAA,UAAA,oBAAA,WAAA;AAEE,WAAK,wBAAuB;AAC5B,UAAI,KAAK,MAAM,YAAY,KAAK,QAAQ;AACtC,aAAK,SAAS;UACZ,QAAQ,KAAK,MAAM,SAAS,eAAe,KAAK,OAAO;QACxD,CAAA;;;AA4ML,IAAAA,MAAA,UAAA,SAAA,WAAA;AAAA,UA6BC,QAAA;;AA5BS,UAAA,SAAW,KAAK,MAAK;AAE7B,aACE,cAAAvC,QACE;QAAA;QAAA,EAAA,WAAW,oCACT,SAACD,MAAA,KAAK,MAAM,iBAAe,QAAAA,QAAA,SAAAA,MAAAwC,MAAK,aAAa,eACzC,wDACA,EAAE,EACN;QAED,KAAK,kBAAiB;QACvB,cAAAvC,QAAK;UAAA;UAAA,EAAA,WAAU,yBAAwB;UACrC,cAAAA,QAAK;YAAA;YAAA,EAAA,WAAU,6BAA4B;YACzC,cAAAA,QAAA,cAAA,MAAA,EACE,WAAU,+BACV,KAAK,SAAC,MAAsB;AAC1B,oBAAK,OAAO;YACd,GACA,OAAO,SAAS,EAAE,OAAM,IAAK,CAAA,GAC7B,MAAK,WAAS,cACF,KAAK,MAAM,YAAW,GAEjC,KAAK,YAAW,CAAE;UAChB;QACD;MACF;;AAzPL,IAAAuC,MAAA,qBAAqB,SAC1B,YACA,aAA0B;AAE1B,aACE,YAAY,aAAa,aAAa,IAAI,YAAY,eAAe;IAEzE;AAsPF,WAACA;IAvQiC,uBAAS;;ACvB3C,IAAM,6BAA6B;AA6DnC,IAAA;;EAAA,SAAA,QAAA;AAAkC,cAAoBE,OAAA,MAAA;AACpD,aAAAA,MAAY,OAAgB;AAC1B,UAAA,QAAA,OAAK,KAAA,MAAC,KAAK,KAAE;AAGf,YAAA,YAAY,cAAA,CAAA,GAAI,MAAM,MAAK,MAAM,cAAc,GAAG,IAAA,EAAA,IAAI,WAAA;AACpD,mBAAA,yBAAS;MAAT,CAA2B;AAG7B,YAAU,aAAG,SAAC,MAAU;AACtB,eAAA,cAAc,MAAM;UAClB,SAAS,MAAK,MAAM;UACpB,SAAS,MAAK,MAAM;UACpB,cAAc,MAAK,MAAM;UACzB,cAAc,MAAK,MAAM;UACzB,YAAY,MAAK,MAAM;SACxB;MAND;AAQF,YAAU,aAAG,SAAC,MAAU;AACtB,eAAA,cAAc,MAAM;UAClB,cAAc,MAAK,MAAM;SAC1B;MAFD;AAIF,YAAA,gBAAgB,WAAM;AAAA,YAAA1C;AAAA,gBAAAA,MAAA,MAAK,MAAM,mBAAa,QAAAA,QAAA,SAAAA,MAAI,MAAK,MAAM;MAAY;AAEzE,YAAqB,wBAAG,SAAC,UAAgB;AACvC,YAAM,kBAAkB,WAAA;;AACtB,WAAA,MAAAA,MAAA,MAAK,UAAU,QAAQ,OAAC,QAAAA,QAAA,SAAA,SAAAA,IAAE,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,MAAK;QAC1C;AAEA,eAAO,sBAAsB,eAAe;MAC9C;AAEA,YAAA,kBAAkB,SAChB,KACA,OAEuC;AAEvC,YAAI,MAAK,MAAM,YAAY;AACzB,gBAAK,MAAM,WAAW,KAAK,KAAK;;MAEpC;AAEA,YAAA,uBAAuB,SAAC,SAAiB8B,UAAa;;AAC9C,YAAA,KAA2B,MAAK,OAA9B,OAAI,GAAA,MAAE,iBAAc,GAAA;AAC5B,YAAI,SAAS,UAAa,mBAAmB,QAAW;AACtD;;AAGM,YAAA,cAAgB,eAAe,MAAM,cAAc,EAAC;AAE5D,YAAI,MAAK,WAAWA,QAAO,KAAK,MAAK,WAAWA,QAAO,GAAG;AACxD;;AAEF,SAAA,MAAA9B,MAAA,MAAK,OAAM,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG8B,QAAO;AAEpC,YAAI,UAAU,cAAc,GAAG;AAC7B,gBAAK,sBAAsB,kBAAkB,cAAc,QAAQ;mBAC1D,UAAU,eAAe,gBAAgB;AAClD,gBAAK,sBACH,KAAK,IAAI,kBAAkB,UAAU,YAAY,CAAC;;AAE/C,WAAA,MAAA,KAAA,MAAK,UAAU,UAAU,WAAW,OAAG,QAAA,OAAA,SAAA,SAAA,GAAA,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,MAAK;MAC9D;AAEA,YAAA,YAAY,SAAC,GAAS,OAAgB;AAAA,eAAAnB,WAAU,GAAG,KAAK;MAAC;AAEzD,YAAA,gBAAgB,SAAC,GAAS;AAAK,eAAA,MAAM,QAAQ,QAAO,CAAE;MAAC;AAEvD,YAAY,eAAG,SAAC,GAAS;AACvB,eAAA,MAAK,MAAM,aACX,MAAK,MAAM,WACXN,YAAW,QAAQ,QAAO,GAAI,CAAC,GAAG,MAAK,MAAM,SAAS;MAFtD;AAIF,YAAU,aAAG,SAAC,GAAS;AACrB,eAAA,MAAK,MAAM,aACX,MAAK,MAAM,WACXA,YAAW,QAAQ,QAAO,GAAI,CAAC,GAAG,MAAK,MAAM,OAAO;MAFpD;AAIF,YAAS,YAAG,SAAC,GAAS;AACpB,eAAA,cAAc,GAAG,MAAK,MAAM,WAAW,MAAK,MAAM,OAAO;MAAzD;AAEF,YAAkB,qBAAG,SAAC,GAAS;AACvB,YAAAL,MACJ,MAAK,OADC,eAAYA,IAAA,cAAE,aAAUA,IAAA,YAAE,eAAYA,IAAA,cAAE,YAASA,IAAA,WAAE,UAAOA,IAAA;AAGlE,YACE,EAAE,gBAAgB,cAAc,iBAChC,CAAC,MAAK,cAAa,GACnB;AACA,iBAAO;;AAET,YAAI,gBAAgB,SAAS;AAC3B,iBAAO,cAAc,GAAG,MAAK,cAAa,GAAI,OAAO;;AAEvD,YAAI,cAAc,WAAW;AAC3B,iBAAO,cAAc,GAAG,WAAW,MAAK,cAAa,CAAE;;AAEzD,YAAI,gBAAgB,aAAa,CAAC,SAAS;AACzC,iBAAO,cAAc,GAAG,WAAW,MAAK,cAAa,CAAE;;AAEzD,eAAO;MACT;AAEA,YAAqB,wBAAG,SAAC,GAAS;;AAChC,YAAI,CAAC,MAAK,mBAAmB,CAAC,GAAG;AAC/B,iBAAO;;AAGH,YAAA,KAA8B,MAAK,OAAjC,YAAS,GAAA,WAAE,eAAY,GAAA;AAC/B,YAAM,QAAQ,QAAQ,QAAO,GAAI,CAAC;AAElC,YAAI,cAAc;AAChB,iBAAOK,YAAW,QAAOL,MAAA,MAAK,cAAa,OAAE,QAAAA,QAAA,SAAAA,MAAI,IAAI;;AAEvD,eAAOK,YAAW,OAAO,cAAS,QAAT,cAAS,SAAT,YAAa,IAAI;MAC5C;AAEA,YAAmB,sBAAG,SAAC,GAAS;;AAC9B,YAAI,CAAC,MAAK,mBAAmB,CAAC,GAAG;AAC/B,iBAAO;;AAGH,YAAA,KAAwC,MAAK,OAA3C,UAAO,GAAA,SAAE,aAAU,GAAA,YAAE,eAAY,GAAA;AACzC,YAAM,QAAQ,QAAQ,QAAO,GAAI,CAAC;AAElC,YAAI,cAAc,cAAc;AAC9B,iBAAOA,YAAW,QAAOL,MAAA,MAAK,cAAa,OAAE,QAAAA,QAAA,SAAAA,MAAI,IAAI;;AAEvD,eAAOK,YAAW,OAAO,YAAO,QAAP,YAAO,SAAP,UAAW,IAAI;MAC1C;AAEA,YAAkB,qBAAG,SAAC,GAAS;AAC7B,YACE,MAAK,MAAM,SAAS,UACpB,MAAK,MAAM,YAAY,QACvB,MAAK,MAAM,gBAAgB,MAC3B;AACA;;AAGI,YAAAL,MACJ,MAAK,OADC,UAAOA,IAAA,SAAE,UAAOA,IAAA,SAAE,eAAYA,IAAA,cAAE,eAAYA,IAAA,cAAE,aAAUA,IAAA;AAGhE,YAAM,OAAO,eAAe,QAAQ,MAAK,MAAM,MAAM,CAAC,CAAC;AACvD,YAAM,cACH,WAAW,WAAW,gBAAgB,gBAAgB,eACvD,eAAe,GAAG,MAAK,KAAK;AAE9B,eACE,CAAC,MAAK,MAAM,8BACZ,CAAC,MAAK,MAAM,UACZ,CAACW,WAAU,MAAM,eAAe,MAAK,MAAM,QAAQ,CAAC,KACpDA,WAAU,MAAM,eAAe,MAAK,MAAM,YAAY,CAAC,KACvD,CAAC;MAEL;AAEA,YAAA,cAAc,SACZ,OAGA,GAAS;AAED,YAAA,OAAS,MAAK,MAAK;AAC3B,YAAI,SAAS,QAAW;AACtB;;AAEF,cAAK,gBAAgB,eAAe,QAAQ,MAAM,CAAC,CAAC,GAAG,KAAK;MAC9D;AAEA,YAAA,gBAAgB,SAAC,OAA4C,GAAS;;AAC5D,YAAA,MAAQ,MAAK;AACf,YAAA,KAA4C,MAAK,OAA/C,OAAI,GAAA,MAAE,iBAAc,GAAA,gBAAE,kBAAe,GAAA;AAE7C,YAAI,QAAQ,QAAQ,KAAK;AAEvB,gBAAM,eAAc;;AAGtB,YAAI,CAAC,MAAK,MAAM,4BAA4B;AAC1C,kBAAQ,KAAG;YACT,KAAK,QAAQ;AACX,kBAAI,MAAK,MAAM,YAAY,MAAM;AAC/B;;AAEF,oBAAK,YAAY,OAAO,CAAC;AACzB,eAAA,MAAAX,MAAA,MAAK,OAAM,qBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAA,MAAK,MAAM,QAAQ;AAChD;YACF,KAAK,QAAQ;AACX,kBAAI,MAAK,MAAM,gBAAgB,MAAM;AACnC;;AAEF,oBAAK,qBACH,IAAI,GACJ,SAAS,MAAK,MAAM,cAAc,CAAC,CAAC;AAEtC;YACF,KAAK,QAAQ;AACX,kBAAI,MAAK,MAAM,gBAAgB,MAAM;AACnC;;AAEF,oBAAK,qBACH,IAAI,GACJ,SAAS,MAAK,MAAM,cAAc,CAAC,CAAC;AAEtC;YACF,KAAK,QAAQ,SAAS;AACpB,kBACE,SAAS,UACT,mBAAmB,UACnB,MAAK,MAAM,gBAAgB,MAC3B;AACA;;AAEM,kBAAA,cAAgB,eAAe,MAAM,cAAc,EAAC;AAC5D,kBAAIyB,UAAS;AACb,kBAAI,UAAU,IAAIA;AAElB,kBAAI,UAAU,aAAa;AACzB,oBAAM,iBAAiB,iBAAiBA;AAExC,oBAAI,KAAK,eAAe,IAAI,cAAc,gBAAgB;AACxD,kBAAAA,UAAS;uBACJ;AACL,kBAAAA,WAAU;;AAGZ,0BAAU,IAAIA;;AAGhB,oBAAK,qBACH,SACA,SAAS,MAAK,MAAM,cAAcA,OAAM,CAAC;AAE3C;;YAEF,KAAK,QAAQ,WAAW;AACtB,kBACE,SAAS,UACT,mBAAmB,UACnB,MAAK,MAAM,gBAAgB,MAC3B;AACA;;AAEM,kBAAA,YAAc,eAAe,MAAM,cAAc,EAAC;AAC1D,kBAAIA,UAAS;AACb,kBAAI,UAAU,IAAIA;AAElB,kBAAI,UAAU,WAAW;AACvB,oBAAM,iBAAiB,iBAAiBA;AAExC,oBAAI,KAAK,aAAa,IAAI,YAAY,gBAAgB;AACpD,kBAAAA,UAAS;uBACJ;AACL,kBAAAA,WAAU;;AAGZ,0BAAU,IAAIA;;AAGhB,oBAAK,qBACH,SACA,SAAS,MAAK,MAAM,cAAcA,OAAM,CAAC;AAE3C;;;;AAKN,2BAAmB,gBAAgB,KAAK;MAC1C;AAEA,YAAiB,oBAAG,SAAC,GAAS;AACtB,YAAAzB,MASF,MAAK,OARP,OAAIA,IAAA,MACJ,UAAOA,IAAA,SACP,UAAOA,IAAA,SACP,WAAQA,IAAA,UACR,eAAYA,IAAA,cACZ,eAAYA,IAAA,cACZ,aAAUA,IAAA,YACV,gBAAaA,IAAA;AAGf,eAAO,KACL,+BACA,0BAA0B,OAAA,CAAC,GAC3B,OAAO,kBAAa,QAAb,kBAAa,SAAA,SAAb,cAAgB,QAAQ,MAAM,CAAC,CAAC,IAAI,QAC3C;UACE,yCAAyC,WACrC,MAAM,QAAQ,QAAQ,IACtB;UACJ,0CACG,WAAW,WAAW,gBAAgB,gBAAgB,eACvD,eAAe,GAAG,MAAK,KAAK;UAC9B,kDACE,MAAK,mBAAmB,CAAC;UAC3B,4CAA4C,MAAK,aAAa,CAAC;UAC/D,0CAA0C,MAAK,WAAW,CAAC;UAC3D,yCAAyC,MAAK,UAAU,CAAC;UACzD,mDACE,MAAK,mBAAmB,CAAC;UAC3B,sDACE,MAAK,sBAAsB,CAAC;UAC9B,oDACE,MAAK,oBAAoB,CAAC;UAC5B,sCAAsC,MAAK,cAAc,CAAC;QAC3D,CAAA;MAEL;AAEA,YAAe,kBAAG,SAAC,GAAS;AAC1B,YACE,MAAK,MAAM,8BACX,MAAK,MAAM,gBAAgB,MAC3B;AACA,iBAAO;;AAET,YAAM,cAAc,QAAQ,MAAK,MAAM,YAAY;AACnD,YAAM,4BAA4B,eAAe,GAAG,MAAK,KAAK;AAE9D,eAAO,MAAM,eAAe,CAAC,4BAA4B,MAAM;MACjE;AAEA,YAAc,iBAAG,SAAC,GAAS;AACzB,eAAO,MAAK,MAAM,oBAAoB,MAAK,MAAM,kBAAkB,CAAC,IAAI;MAC1E;;;AAEA,IAAA0C,MAAA,UAAA,SAAA,WAAA;AAAA,UAyEC,QAAA;AAxEC,UAAM,YAAY,CAAA;AACZ,UAAA1C,MACJ,KAAK,OADC,OAAIA,IAAA,MAAE,iBAAcA,IAAA,gBAAE,mBAAgBA,IAAA,kBAAE,mBAAgBA,IAAA;AAEhE,UAAI,SAAS,QAAW;AACtB,eAAO;;AAEH,UAAA,KAA6B,eAAe,MAAM,cAAc,GAA9D,cAAW,GAAA,aAAE,YAAS,GAAA;6BAErB2C,IAAC;AACR,kBAAU,KACR,cAAA1C,QACE,cAAA,OAAA,EAAA,KAAK,OAAK,UAAU0C,KAAI,WAAW,GACnC,SAAS,SAAC,OAAK;AACb,gBAAK,YAAY,OAAOA,EAAC;QAC3B,GACA,WAAW,SAAC,OAAK;AACf,cAAI,eAAe,KAAK,GAAG;AACzB,kBAAM,eAAc;AACpB,kBAAM,MAAM,QAAQ;;AAGtB,gBAAK,cAAc,OAAOA,EAAC;WAE7B,UAAU,OAAO,OAAK,gBAAgBA,EAAC,CAAC,GACxC,WAAW,OAAK,kBAAkBA,EAAC,GACnC,cACE,CAAC,OAAK,MAAM,kBACR,SAAC,OAAK;AAAK,iBAAA,iBAAiB,OAAOA,EAAC;QAAC,IACrC,QAEN,gBACE,OAAK,MAAM,kBACP,SAAC,OAAK;AAAK,iBAAA,iBAAiB,OAAOA,EAAC;QAAC,IACrC,QAEN,cACE,CAAC,OAAK,MAAM,kBACR,SAAC,OAAK;AAAK,iBAAA,iBAAiB,OAAOA,EAAC;QAAC,IACrC,QAEN,gBACE,OAAK,MAAM,kBACP,SAAC,OAAK;AAAK,iBAAA,iBAAiB,OAAOA,EAAC;QAAC,IACrC,QAEN,KAAKA,IACS,gBAAA,OAAK,cAAcA,EAAC,IAAI,SAAS,OAAS,GAEvD,OAAK,eAAeA,EAAC,CAAC,CACnB;;;AAzCV,eAAS,IAAI,aAAa,KAAK,WAAW,KAAG;gBAApC,CAAC;MA2CT;AAED,aACE,cAAA1C,QAAA;QAAA;QAAA,EAAK,WAAU,yBAAwB;QACrC,cAAAA,QACE,cAAA,OAAA,EAAA,WAAU,kCACV,cACE,CAAC,KAAK,MAAM,kBACR,KAAK,MAAM,qBACX,QAEN,gBACE,KAAK,MAAM,kBACP,KAAK,MAAM,qBACX,OAAS,GAGd,SAAS;MACN;;AAId,WAACyC;EAAD,EApZkC,uBAAS;;AC7E3C,SAAS,cACP,MACA,UACA,SACA,SAAc;AAEd,MAAM,OAAiB,CAAA;AACvB,WAAS,IAAI,GAAG,IAAI,IAAI,WAAW,GAAG,KAAK;AACzC,QAAM,UAAU,OAAO,WAAW;AAClC,QAAI,YAAY;AAEhB,QAAI,SAAS;AACX,kBAAY,QAAQ,OAAO,KAAK;;AAGlC,QAAI,WAAW,WAAW;AACxB,kBAAY,QAAQ,OAAO,KAAK;;AAGlC,QAAI,WAAW;AACb,WAAK,KAAK,OAAO;;;AAIrB,SAAO;AACT;AAgBA,IAAA;;EAAA,SAAA,QAAA;AAAiD,cAGhDE,sBAAA,MAAA;AACC,aAAAA,qBAAY,OAA+B;AACzC,UAAA,QAAA,OAAK,KAAA,MAAC,KAAK,KAAE;AAuCf,YAAA,gBAAgB,WAAA;AACd,YAAM,eAAe,MAAK,MAAM;AAChC,YAAM,UAAU,MAAK,MAAM,UAAU,IAAI,SAAC,MAAI;AAAK,iBACjD,cAAA3C,QAAA;YAAA;YAAA,EACE,WACE,iBAAiB,OACb,+EACA,iCAEN,KAAK,MACL,SAAS,MAAK,SAAS,KAAK,OAAM,IAAI,GACvB,iBAAA,iBAAiB,OAAO,SAAS,OAAS;YAExD,iBAAiB,OAChB,cAAAA,QAAM,cAAA,QAAA,EAAA,WAAU,0CAAyC,GAAA,GAAA,IAEzD;YAED;UAAI;QAhB0C,CAkBlD;AAED,YAAM,UAAU,MAAK,MAAM,UAAU,QAAQ,MAAK,MAAM,OAAO,IAAI;AACnE,YAAM,UAAU,MAAK,MAAM,UAAU,QAAQ,MAAK,MAAM,OAAO,IAAI;AAEnE,YAAI,CAAC,WAAW,CAAC,MAAK,MAAM,UAAU,KAAK,SAAC,MAAS;AAAA,iBAAA,SAAS;QAAT,CAAgB,GAAG;AACtE,kBAAQ,QACN,cAAAA,QACE;YAAA;YAAA,EAAA,WAAU,iCACV,KAAK,YACL,SAAS,MAAK,eAAc;YAE5B,cAAAA,QAAA,cAAA,KAAA,EAAG,WAAU,gHAA+G,CAAG;UAAA,CAC3H;;AAIV,YAAI,CAAC,WAAW,CAAC,MAAK,MAAM,UAAU,KAAK,SAAC,MAAS;AAAA,iBAAA,SAAS;QAAT,CAAgB,GAAG;AACtE,kBAAQ,KACN,cAAAA,QACE;YAAA;YAAA,EAAA,WAAU,iCACV,KAAK,YACL,SAAS,MAAK,eAAc;YAE5B,cAAAA,QAAA,cAAA,KAAA,EAAG,WAAU,gHAA+G,CAAG;UAAA,CAC3H;;AAIV,eAAO;MACT;AAEA,YAAQ,WAAG,SAAC,MAAY;AACtB,cAAK,MAAM,SAAS,IAAI;MAC1B;AAEA,YAAA,qBAAqB,WAAA;AACnB,cAAK,MAAM,SAAQ;MACrB;AAEA,YAAU,aAAG,SAAC,QAAc;AAC1B,YAAM,QAAQ,MAAK,MAAM,UAAU,IAAI,SAAU,MAAI;AACnD,iBAAO,OAAO;QAChB,CAAC;AAED,cAAK,SAAS;UACZ,WAAW;QACZ,CAAA;MACH;AAEA,YAAA,iBAAiB,WAAA;AACf,eAAO,MAAK,WAAW,CAAC;MAC1B;AAEA,YAAA,iBAAiB,WAAA;AACf,eAAO,MAAK,WAAW,EAAE;MAC3B;AAlHU,UAAA,yBAAmD,MAAK,wBAAhC,yBAA2B,MAAK;AAChE,UAAM,WACJ,2BAA2B,yBAAyB,KAAK;AAE3D,YAAK,QAAQ;QACX,WAAW,cACT,MAAK,MAAM,MACX,UACA,MAAK,MAAM,SACX,MAAK,MAAM,OAAO;;AAGtB,YAAK,kBAAc,yBAAS;;;AAG9B,IAAA2C,qBAAA,UAAA,oBAAA,WAAA;AACE,UAAM,kBAAkB,KAAK,YAAY;AAEzC,UAAI,iBAAiB;AAEnB,YAAM,0BAA0B,gBAAgB,WAC5C,MAAM,KAAK,gBAAgB,QAAQ,IACnC;AACJ,YAAM,uBAAuB,0BACzB,wBAAwB,KAAK,SAAC,SAAY;AAAA,iBAAA,QAAQ;QAAY,CAAA,IAC9D;AAEJ,wBAAgB,YACd,wBAAwB,gCAAgC,cACpD,qBAAqB,aACpB,qBAAqB,eAAe,gBAAgB,gBACnD,KACD,gBAAgB,eAAe,gBAAgB,gBAAgB;;;AAoF1E,IAAAA,qBAAA,UAAA,SAAA,WAAA;AACE,UAAM,gBAAgB,KAAK;QACzB,mCAAmC;QACnC,+CACE,KAAK,MAAM;MACd,CAAA;AAED,aACE,cAAA3C,QAAC,cAAA,qBACC,EAAA,WAAW,eACX,cAAc,KAAK,aACnB,gBAAgB,KAAK,mBAAkB,GAEtC,KAAK,cAAa,CAAE;;AAI7B,WAAC2C;EAAD,EA3IiD,uBAAS;;ACzB1D,IAAA;;EAAA,SAAA,QAAA;AAA0C,cAGzCC,eAAA,MAAA;AAHD,aAAAA,gBAAA;;AAIE,YAAA,QAA2B;QACzB,iBAAiB;;AAGnB,YAAA,sBAAsB,WAAA;AACpB,YAAM,UAAkB,MAAK,MAAM,UAC/B,QAAQ,MAAK,MAAM,OAAO,IAC1B;AACJ,YAAM,UAAkB,MAAK,MAAM,UAC/B,QAAQ,MAAK,MAAM,OAAO,IAC1B;AAEJ,YAAM,UAAgC,CAAA;AACtC,iBAAS,IAAI,SAAS,KAAK,SAAS,KAAK;AACvC,kBAAQ,KACN,cAAA5C,QAAA,cAAA,UAAA,EAAQ,KAAK,GAAG,OAAO,EAAC,GACrB,CAAC,CACK;;AAGb,eAAO;MACT;AAEA,YAAc,iBAAG,SAAC,OAA2C;AAC3D,cAAK,SAAS,SAAS,MAAM,OAAO,KAAK,CAAC;MAC5C;AAEA,YAAA,mBAAmB,WAAA;AAA0B,eAC3C,cAAAA,QACE,cAAA,UAAA,EAAA,OAAO,MAAK,MAAM,MAClB,WAAU,iCACV,UAAU,MAAK,eAAc,GAE5B,MAAK,oBAAmB,CAAE;MAE9B;AAED,YAAA,iBAAiB,SAAC,SAAgB;AAAyB,eACzD,cAAAA,QAAA;UAAA;UAAA,EACE,KAAI,QACJ,OAAO,EAAE,YAAY,UAAU,YAAY,SAAQ,GACnD,WAAU,oCACV,SAAS,SAAC,OAAuC;AAC/C,mBAAA,MAAK,eAAe,KAAK;YAAC;UAG5B,cAAAA,QAAM,cAAA,QAAA,EAAA,WAAU,+CAA8C,CAAG;UACjE,cAAAA,QAAA,cAAA,QAAA,EAAM,WAAU,kDAAiD,GAC9D,MAAK,MAAM,IAAI;QACX;MAEV;AAED,YAAA,iBAAiB,WAAA;AAA0B,eACzC,cAAAA,QAAC,cAAA,qBACCyB,QAAA,EAAA,KAAI,WAAU,GACV,MAAK,OAAK,EACd,UAAU,MAAK,UACf,UAAU,MAAK,eAAc,CAAA,CAAA;MAEhC;AAED,YAAA,mBAAmB,WAAA;AACT,YAAA,kBAAoB,MAAK,MAAK;AACtC,YAAM,SAAS,CAAC,MAAK,eAAe,CAAC,eAAe,CAAC;AACrD,YAAI,iBAAiB;AACnB,iBAAO,QAAQ,MAAK,eAAc,CAAE;;AAEtC,eAAO;MACT;AAEA,YAAQ,WAAG,SAAC,MAAY;AACtB,cAAK,eAAc;AACnB,YAAI,SAAS,MAAK,MAAM;AAAM;AAC9B,cAAK,MAAM,SAAS,IAAI;MAC1B;AAEA,YAAc,iBAAG,SAAC,OAAwC;AACxD,cAAK,SACH;UACE,iBAAiB,CAAC,MAAK,MAAM;WAE/B,WAAA;AACE,cAAI,MAAK,MAAM,oBAAoB;AACjC,kBAAK,iBAAiB,MAAK,MAAM,MAAM,KAAK;;QAEhD,CAAC;MAEL;AAEA,YAAA,mBAAmB,SACjB,MACA,OAAwC;;AAExC,SAAA1B,MAAA,MAAK,cAAQ,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,OAAG,MAAM,KAAK;AAC3B,cAAK,QAAO;MACd;AAEA,YAAA,WAAW,SAAC,MAAY,OAAwC;;AAC9D,SAAA,MAAAA,MAAA,MAAK,OAAM,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,MAAM,KAAK;MACnC;AAEA,YAAA,UAAU,WAAA;;AACR,SAAA,MAAAA,MAAA,MAAK,OAAM,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,IAAI;MAC3B;;;AAEA,IAAA6C,cAAA,UAAA,SAAA,WAAA;AACE,UAAI;AACJ,cAAQ,KAAK,MAAM,cAAY;QAC7B,KAAK;AACH,6BAAmB,KAAK,iBAAgB;AACxC;QACF,KAAK;AACH,6BAAmB,KAAK,iBAAgB;AACxC;;AAGJ,aACE,cAAA5C,QAAA,cAAA,OAAA,EACE,WAAW,wFAAA,OAAwF,KAAK,MAAM,YAAY,EAAE,GAE3H,gBAAgB;;AAIzB,WAAC4C;EAAD,EAjI0C,uBAAS;;ACsDnD,IAAM,4BAA4B;EAChC;EACA;EACA;;AAGF,IAAM,mBAAmB,SAAC,SAAuB;AAC/C,MAAM,cAAc,QAAQ,aAAa,IAAI,MAAM,KAAK;AACxD,SAAO,0BAA0B,KAC/B,SAAC,eAAa;AAAK,WAAA,WAAW,QAAQ,aAAa,KAAK;EAArC,CAAsC;AAE7D;AAmIA,IAAA;;EAAA,SAAA,QAAA;AAAsC,cAAuCC,WAAA,MAAA;AAc3E,aAAAA,UAAY,OAAoB;AAC9B,UAAA,QAAA,OAAK,KAAA,MAAC,KAAK,KAAE;AAoDf,YAAc,iBAAoC;AAIlD,YAAkB,qBAAG,SAAC,OAAiB;AACrC,cAAK,MAAM,eAAe,KAAK;MACjC;AAEA,YAAA,qBAAqB,WAAA;AACnB,eAAO,MAAK,aAAa;MAC3B;AAEA,YAAmB,sBAAG,SAAC,OAAuC;;AAC5D,YAAI,iBAAiB,MAAM,MAAM,GAAG;AAClC,WAAA,MAAA9C,MAAA,MAAK,OAAM,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,KAAK;;MAEtC;AAEA,YAAA,gBAAgB,WAAA;AACR,YAAAA,MAAyC,MAAK,OAA5C,eAAYA,IAAA,cAAE,WAAQA,IAAA,UAAE,aAAUA,IAAA;AAC1C,YAAM,UAAU,oBAAoB,MAAK,KAAK;AAC9C,YAAM,UAAU,oBAAoB,MAAK,KAAK;AAC9C,YAAM,UAAU,QAAO;AACvB,YAAM,cAAc,cAAc,YAAY;AAC9C,YAAI,aAAa;AACf,iBAAO;eACF;AACL,cAAI,WAAW,SAAS,SAAS,OAAO,GAAG;AACzC,mBAAO;qBACE,WAAW,QAAQ,SAAS,OAAO,GAAG;AAC/C,mBAAO;;;AAGX,eAAO;MACT;AAEA,YAAA,gBAAgB,WAAA;AACd,cAAK,SACH,SAACA,KAAQ;AAAN,cAAA,OAAIA,IAAA;AAAO,iBAAC;YACb,MAAM,UAAU,MAAM,CAAC;;QADX,GAGd,WAAM;AAAA,iBAAA,MAAK,kBAAkB,MAAK,MAAM,IAAI;QAAtC,CAAuC;MAEjD;AAEA,YAAA,gBAAgB,WAAA;AACd,cAAK,SACH,SAACA,KAAQ;AAAN,cAAA,OAAIA,IAAA;AAAO,iBAAC;YACb,MAAM,UAAU,MAAM,CAAC;;QADX,GAGd,WAAM;AAAA,iBAAA,MAAK,kBAAkB,MAAK,MAAM,IAAI;QAAtC,CAAuC;MAEjD;AAEA,YAAA,iBAAiB,SACf,KACA,OAGA,iBAAwB;AAExB,cAAK,MAAM,SAAS,KAAK,OAAO,eAAe;AAC/C,cAAK,MAAM,mBAAmB,MAAK,MAAM,gBAAgB,GAAG;MAC9D;AAEA,YAAmB,sBAAG,SAAC,KAAS;AAC9B,cAAK,SAAS,EAAE,eAAe,IAAG,CAAE;AACpC,cAAK,MAAM,mBAAmB,MAAK,MAAM,gBAAgB,GAAG;MAC9D;AAEA,YAAA,wBAAwB,WAAA;AACtB,cAAK,SAAS,EAAE,eAAe,OAAS,CAAE;AAC1C,cAAK,MAAM,qBAAqB,MAAK,MAAM,kBAAiB;MAC9D;AAEA,YAAA,uBAAuB,SACrB,OACA,MAAY;AAEZ,cAAK,SAAS,EAAE,eAAe,QAAQ,QAAO,GAAI,IAAI,EAAC,CAAE;AACzD,SAAC,CAAC,MAAK,MAAM,oBAAoB,MAAK,MAAM,iBAAiB,OAAO,IAAI;MAC1E;AAEA,YAAA,uBAAuB,SACrB,OACA,MAAY;AAEZ,SAAC,CAAC,MAAK,MAAM,oBAAoB,MAAK,MAAM,iBAAiB,OAAO,IAAI;MAC1E;AAEA,YAAgB,mBAAG,SAAC,MAAU;;AAC5B,SAAA,MAAAA,MAAA,MAAK,OAAM,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,IAAI;AAC9B,cAAK,SAAS,EAAE,yBAAyB,KAAI,CAAE;AAC/C,YAAI,MAAK,MAAM,oBAAoB;AACjC,gBAAK,MAAM,SAAS,IAAI;AACxB,WAAA,MAAA,KAAA,MAAK,OAAM,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,IAAI;;AAG3B,cAAK,MAAM,mBAAmB,MAAK,MAAM,gBAAgB,IAAI;MAC/D;AAEA,YAAkC,qCAAG,SAAC,MAAU;AAC9C,YAAI,CAAC,cAAc,MAAM,MAAK,KAAK,GAAG;AACpC,iBAAO;;AAGT,YAAM+C,gBAAe,gBAAgB,IAAI;AACzC,YAAMC,cAAa,cAAc,IAAI;AAErC,YAAM,YAAY,iBAAiBA,aAAYD,aAAY;AAE3D,YAAI,kBAAkB;AAEtB,iBAAS,SAAS,GAAG,UAAU,WAAW,UAAU;AAClD,cAAM,iBAAiB,QAAQA,eAAc,MAAM;AAEnD,cAAI,CAAC,cAAc,gBAAgB,MAAK,KAAK,GAAG;AAC9C,8BAAkB;AAClB;;;AAIJ,eAAO;MACT;AAEA,YAAiB,oBAAG,SAAC,MAAU;;AAC7B,YAAM,2BACJ/C,MAAA,MAAK,mCAAmC,IAAI,OAAK,QAAAA,QAAA,SAAAA,MAAA;AAEnD,cAAK,wBAAwB,uBAAuB;AACpD,YAAI,MAAK,MAAM,oBAAoB;AACjC,gBAAK,MAAM,SAAS,uBAAuB;AAC3C,WAAA,MAAA,KAAA,MAAK,OAAM,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAG,IAAI;;AAG3B,cAAK,MAAM,mBACT,MAAK,MAAM,gBAAgB,uBAAuB;MACtD;AAEA,YAAuB,0BAAG,SAAC,MAAU;;AACnC,SAAA,MAAAA,MAAA,MAAK,OAAM,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,IAAI;AAC/B,cAAK,SAAS,EAAE,yBAAyB,KAAI,CAAE;MACjD;AAEA,YAAqB,wBAAG,SAAC,MAAU;AACjC,cAAK,iBAAiB,IAAI;AAC1B,cAAK,kBAAkB,IAAI;MAC7B;AAEA,YAAU,aAAG,SAAC,MAAY;AACxB,cAAK,SACH,SAACA,KAAQ;AAAN,cAAA,OAAIA,IAAA;AAAO,iBAAC;YACb,MAAM,QAAQ,MAAM,OAAO,IAAI,CAAC;;QADpB,GAGd,WAAM;AAAA,iBAAA,MAAK,iBAAiB,MAAK,MAAM,IAAI;QAArC,CAAsC;MAEhD;AAEA,YAAW,cAAG,SAAC,OAAa;AAC1B,cAAK,SACH,SAACA,KAAQ;AAAN,cAAA,OAAIA,IAAA;AAAO,iBAAC;YACb,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC;;QADtB,GAGd,WAAM;AAAA,iBAAA,MAAK,kBAAkB,MAAK,MAAM,IAAI;QAAtC,CAAuC;MAEjD;AAEA,YAAe,kBAAG,SAAC,WAAe;AAChC,cAAK,SACH,SAACA,KAAQ;AAAN,cAAA,OAAIA,IAAA;AAAO,iBAAC;YACb,MAAM,QAAQ,SAAS,MAAM,SAAS,SAAS,CAAC,GAAG,QAAQ,SAAS,CAAC;;QADzD,GAGd,WAAM;AAAA,iBAAA,MAAK,sBAAsB,MAAK,MAAM,IAAI;QAA1C,CAA2C;MAErD;AAEA,YAAM,SAAG,SAAC,MAA4B;AAA5B,YAAA,SAAA,QAAA;AAAA,iBAAa,MAAK,MAAM;QAAI;AACpC,YAAMuB,eAAc,eAClB,MACA,MAAK,MAAM,QACX,MAAK,MAAM,gBAAgB;AAG7B,YAAM,WAAiC,CAAA;AACvC,YAAI,MAAK,MAAM,iBAAiB;AAC9B,mBAAS,KACP,cAAAtB,QAAA,cAAA,OAAA,EAAK,KAAI,KAAI,WAAU,6BAA4B,GAChD,MAAK,MAAM,aAAa,GAAG,CACxB;;AAGV,eAAO,SAAS,OACd,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,SAACwB,SAAM;AAC/B,cAAM,MAAM,QAAQF,cAAaE,OAAM;AACvC,cAAM,cAAc,MAAK,cAAc,KAAK,MAAK,MAAM,MAAM;AAE7D,cAAM,mBAAmB,MAAK,MAAM,mBAChC,MAAK,MAAM,iBAAiB,GAAG,IAC/B;AAEJ,iBACE,cAAAxB,QAAA,cAAA,OAAA,EACE,KAAKwB,SAAM,cACC,WAAW,KAAK,QAAQ,MAAK,MAAM,MAAM,GACrD,WAAW,KAAK,8BAA8B,gBAAgB,EAAC,GAE9D,WAAW;SAGjB,CAAC;MAEN;AAEA,YAAA,gBAAgB,SAAC,KAAW,QAAe;AACzC,YAAI,MAAK,MAAM,eAAe;AAC5B,iBAAO,4BAA4B,KAAK,MAAK,MAAM,eAAe,MAAM;;AAE1E,eAAO,MAAK,MAAM,mBACd,wBAAwB,KAAK,MAAM,IACnC,sBAAsB,KAAK,MAAM;MACvC;AAEA,YAAA,eAAe,WAAA;AACb,cAAK,SACH,SAACzB,KAAQ;;AAAN,cAAA,OAAIA,IAAA;AAAO,iBAAC;YACb,MAAM,SACJ,MACA,MAAK,MAAM,kBACN,KAAA,MAAK,MAAM,oBAAc,QAAA,OAAA,SAAA,KACxB8C,UAAS,aAAa,iBACxB,CAAC;UAER;QAAC,GACF,WAAM;AAAA,iBAAA,MAAK,iBAAiB,MAAK,MAAM,IAAI;QAArC,CAAsC;MAEhD;AAEA,YAAA,qBAAqB,WAAA;AACnB,cAAK,SAAS,EAAE,eAAe,OAAS,CAAE;MAC5C;AAEA,YAAA,uBAAuB,WAAA;;AACrB,YAAI,MAAK,MAAM,oBAAoB;AACjC;;AAGF,YAAM,eACJ9C,MAAA,MAAK,MAAM,iBAAW,QAAAA,QAAA,SAAAA,MAAI8C,UAAS,aAAa;AAClD,YAAM,mBAAmB,MAAK,MAAM,qBAChC,cAAc,IACd;AACJ,YAAM,mBAAkB,KAAA,MAAK,MAAM,qBAAmB,QAAA,OAAA,SAAA,KAAA;AACtD,YAAM,gBAAgB,UAAU,MAAK,MAAM,MAAM,eAAe;AAEhE,YAAI;AACJ,gBAAQ,MAAI;UACV,KAAK,MAAK,MAAM;AACd,kCAAsB,mBAAmB,MAAK,MAAM,MAAM,MAAK,KAAK;AACpE;UACF,KAAK,MAAK,MAAM;AACd,kCAAsB,oBAAoB,MAAK,MAAM,MAAM,MAAK,KAAK;AACrE;UACF,KAAK,MAAK,MAAM;AACd,kCAAsB,sBACpB,MAAK,MAAM,MACX,MAAK,KAAK;AAEZ;UACF;AACE,kCAAsB,oBAAoB,eAAe,MAAK,KAAK;AACnE;;AAGJ,YACG,GACC,KAAA,MAAK,MAAM,8BAAwB,QAAA,OAAA,SAAA,KACnCA,UAAS,aAAa,6BAEtB,CAAC,MAAK,MAAM,+BACZ,uBACF,MAAK,MAAM,oBACX;AACA;;AAGF,YAAM,cAAc;UAClB;UACA;;AAGF,YAAM,UAAU;UACd;UACA;;AAGF,YAAI,eACF,MAAK;AAEP,YACE,MAAK,MAAM,uBACX,MAAK,MAAM,yBACX,MAAK,MAAM,gBACX;AACA,yBAAe,MAAK;;AAGtB,YAAI,uBAAuB,MAAK,MAAM,6BAA6B;AACjE,kBAAQ,KAAK,kDAAkD;AAC/D,yBAAe;;AAGjB,YAAM,YACJ,MAAK,MAAM,uBACX,MAAK,MAAM,yBACX,MAAK,MAAM;AAEP,YAAA,KAGF,MAAK,OAFP,KAAyE,GAAA,0BAAzE,2BAAwB,OAAA,SAAGA,UAAS,aAAa,2BAAwB,IACzE,KAAuE,GAAA,yBAAvE,0BAAuB,OAAA,SAAGA,UAAS,aAAa,0BAAuB;AAGnE,YAAA,KAOF,MAAK,OANP,KAAA,GAAA,wBAAA,yBAAyB,OAAA,SAAA,OAAO,6BAA6B,WACzD,2BACA,mBAAgB,IACpB,KAAA,GAAA,uBAAA,wBAAwB,OAAA,SAAA,OAAO,4BAA4B,WACvD,0BACA,kBAAe;AAGrB,eACE,cAAA7C,QAAA;UAAA;UAAA,EACE,MAAK,UACL,WAAW,QAAQ,KAAK,GAAG,GAC3B,SAAS,cACT,WAAW,MAAK,MAAM,iBAAe,cACzB,YAAY,wBAAwB,uBAAsB;UAEtE,cAAAA,QAAM,cAAA,QAAA,EAAA,WAAW,YAAY,KAAK,GAAG,EAAC,GACnC,YAAY,0BAA0B,wBAAwB;QAC1D;MAGb;AAEA,YAAA,eAAe,WAAA;AACb,cAAK,SACH,SAACD,KAAQ;;AAAN,cAAA,OAAIA,IAAA;AAAO,iBAAC;YACb,MAAM,SACJ,MACA,MAAK,MAAM,kBACN,KAAA,MAAK,MAAM,oBAAc,QAAA,OAAA,SAAA,KACxB8C,UAAS,aAAa,iBACxB,CAAC;UAER;QAAC,GACF,WAAM;AAAA,iBAAA,MAAK,iBAAiB,MAAK,MAAM,IAAI;QAArC,CAAsC;MAEhD;AAEA,YAAA,mBAAmB,WAAA;;AACjB,YAAI,MAAK,MAAM,oBAAoB;AACjC;;AAGF,YAAI;AACJ,gBAAQ,MAAI;UACV,KAAK,MAAK,MAAM;AACd,kCAAsB,kBAAkB,MAAK,MAAM,MAAM,MAAK,KAAK;AACnE;UACF,KAAK,MAAK,MAAM;AACd,kCAAsB,mBAAmB,MAAK,MAAM,MAAM,MAAK,KAAK;AACpE;UACF,KAAK,MAAK,MAAM;AACd,kCAAsB,qBAAqB,MAAK,MAAM,MAAM,MAAK,KAAK;AACtE;UACF;AACE,kCAAsB,mBAAmB,MAAK,MAAM,MAAM,MAAK,KAAK;AACpE;;AAGJ,YACG,GACC9C,MAAA,MAAK,MAAM,8BAAwB,QAAAA,QAAA,SAAAA,MACnC8C,UAAS,aAAa,6BAEtB,CAAC,MAAK,MAAM,+BACZ,uBACF,MAAK,MAAM,oBACX;AACA;;AAGF,YAAM,UAAoB;UACxB;UACA;;AAEF,YAAM,cAAc;UAClB;UACA;;AAEF,YAAI,MAAK,MAAM,gBAAgB;AAC7B,kBAAQ,KAAK,+CAA+C;;AAE9D,YAAI,MAAK,MAAM,aAAa;AAC1B,kBAAQ,KAAK,uDAAuD;;AAGtE,YAAI,eACF,MAAK;AAEP,YACE,MAAK,MAAM,uBACX,MAAK,MAAM,yBACX,MAAK,MAAM,gBACX;AACA,yBAAe,MAAK;;AAGtB,YAAI,uBAAuB,MAAK,MAAM,6BAA6B;AACjE,kBAAQ,KAAK,8CAA8C;AAC3D,yBAAe;;AAGjB,YAAM,YACJ,MAAK,MAAM,uBACX,MAAK,MAAM,yBACX,MAAK,MAAM;AAEP,YAAA,KAGF,MAAK,OAFP,KAAiE,GAAA,sBAAjE,uBAAoB,OAAA,SAAGA,UAAS,aAAa,uBAAoB,IACjE,KAA+D,GAAA,qBAA/D,sBAAmB,OAAA,SAAGA,UAAS,aAAa,sBAAmB;AAE3D,YAAA,KAOF,MAAK,OANP,KAAA,GAAA,oBAAA,qBAAqB,OAAA,SAAA,OAAO,yBAAyB,WACjD,uBACA,eAAY,IAChB,KAAA,GAAA,mBAAA,oBAAoB,OAAA,SAAA,OAAO,wBAAwB,WAC/C,sBACA,cAAW;AAGjB,eACE,cAAA7C,QAAA;UAAA;UAAA,EACE,MAAK,UACL,WAAW,QAAQ,KAAK,GAAG,GAC3B,SAAS,cACT,WAAW,MAAK,MAAM,iBAAe,cACzB,YAAY,oBAAoB,mBAAkB;UAE9D,cAAAA,QAAM,cAAA,QAAA,EAAA,WAAW,YAAY,KAAK,GAAG,EAAC,GACnC,YAAY,sBAAsB,oBAAoB;QAClD;MAGb;AAEA,YAAkB,qBAAG,SAAC,MAA4B;AAA5B,YAAA,SAAA,QAAA;AAAA,iBAAa,MAAK,MAAM;QAAI;AAChD,YAAM,UAAU,CAAC,iCAAiC;AAElD,YAAI,MAAK,MAAM,kBAAkB;AAC/B,kBAAQ,KAAK,kDAAkD;;AAEjE,YAAI,MAAK,MAAM,mBAAmB;AAChC,kBAAQ,KAAK,mDAAmD;;AAElE,YAAI,MAAK,MAAM,uBAAuB;AACpC,kBAAQ,KAAK,uDAAuD;;AAEtE,eACE,cAAAA,QAAA,cAAA,MAAA,EAAI,WAAW,QAAQ,KAAK,GAAG,EAAC,GAC7B,WAAW,MAAM,MAAK,MAAM,YAAY,MAAK,MAAM,MAAM,CAAC;MAGjE;AAEA,YAAkB,qBAAG,SACnB,cAA6B;AAA7B,YAAA,iBAAA,QAAA;AAAA,yBAA6B;QAAA;AAE7B,YAAI,CAAC,MAAK,MAAM,oBAAoB,cAAc;AAChD;;AAEF,eACE,cAAAA,QAAC,cAAA,cACKyB,QAAA,CAAA,GAAAoB,UAAS,cACT,MAAK,OACT,EAAA,MAAM,MAAK,MAAM,MACjB,UAAU,MAAK,YACf,MAAM,QAAQ,MAAK,MAAM,IAAI,EAAC,CAAA,CAAA;MAGpC;AAEA,YAAmB,sBAAG,SACpB,cAA6B;AAA7B,YAAA,iBAAA,QAAA;AAAA,yBAA6B;QAAA;AAE7B,YAAI,CAAC,MAAK,MAAM,qBAAqB,cAAc;AACjD;;AAEF,eACE,cAAA7C,QAAA,cAAC,eAAayB,QAAA,CAAA,GACRoB,UAAS,cACT,MAAK,OAAK,EACd,OAAO,SAAS,MAAK,MAAM,IAAI,GAC/B,UAAU,MAAK,YAAW,CAAA,CAAA;MAGhC;AAEA,YAAuB,0BAAG,SACxB,cAA6B;AAA7B,YAAA,iBAAA,QAAA;AAAA,yBAA6B;QAAA;AAE7B,YAAI,CAAC,MAAK,MAAM,yBAAyB,cAAc;AACrD;;AAEF,eACE,cAAA7C,QAAC,cAAA,mBACKyB,QAAA,CAAA,GAAAoB,UAAS,cACT,MAAK,OACT,EAAA,MAAM,MAAK,MAAM,MACjB,UAAU,MAAK,gBAAe,CAC9B,CAAA;MAEN;AAEA,YAAsB,yBAAG,SAAC,OAAuC;AAC/D,cAAK,MAAM,SAAS,gBAAe,GAAI,KAAK;AAC5C,cAAK,MAAM,mBAAmB,MAAK,MAAM,gBAAgB,gBAAe,CAAE;MAC5E;AAEA,YAAA,oBAAoB,WAAA;AAClB,YAAI,CAAC,MAAK,MAAM,eAAe,MAAK,MAAM,oBAAoB;AAC5D;;AAEF,eACE,cAAA7C,QACE,cAAA,OAAA,EAAA,WAAU,kCACV,SAAS,MAAK,uBAAsB,GAEnC,MAAK,MAAM,WAAW;MAG7B;AAEA,YAAmB,sBAAG,SAACD,KAAgD;YAA9C,YAASA,IAAA,WAAE,IAACA,IAAA;AAAuC,eAC1E,cAAAC,QAAA;UAAA;UAAA,EACE,WAAW,4BAAA,OACT,MAAK,MAAM,iBACP,8CACA,EAAE,EACN;UAED,MAAK,mBAAmB,SAAS;UAClC,cAAAA,QAAA;YAAA;YAAA,EACE,WAAW,0EAA0E,OAAA,MAAK,MAAM,YAAY,GAC5G,SAAS,MAAK,oBAAmB;YAEhC,MAAK,oBAAoB,MAAM,CAAC;YAChC,MAAK,wBAAwB,MAAM,CAAC;YACpC,MAAK,mBAAmB,MAAM,CAAC;UAAC;UAEnC,cAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,8BAA6B,GACzC,MAAK,OAAO,SAAS,CAAC;QACnB;MAnBkE;AAuB5E,YAAkB,qBAAG,SAAC,YAA0C;;AACtD,YAAA,YAAiB,WAAU,WAAhB,IAAM,WAAU;AAEnC,YACG,MAAK,MAAM,kBAAkB,CAAC,MAAK,MAAM,kBAC1C,MAAK,MAAM,oBACX;AACA,iBAAO;;AAGT,YAAM,0BAA0B,oBAC9B,MAAK,MAAM,MACX,MAAK,KAAK;AAGZ,YAAM,0BAA0B,mBAC9B,MAAK,MAAM,MACX,MAAK,KAAK;AAGZ,YAAM,yBAAyB,mBAC7B,MAAK,MAAM,MACX,MAAK,KAAK;AAGZ,YAAM,yBAAyB,kBAC7B,MAAK,MAAM,MACX,MAAK,KAAK;AAGZ,YAAM,eACJ,CAAC,MAAK,MAAM,uBACZ,CAAC,MAAK,MAAM,yBACZ,CAAC,MAAK,MAAM;AAEd,eACE,cAAAA,QAAA;UAAA;UAAA,EACE,WAAU,6DACV,SAAS,MAAK,MAAM,gBAAe;WAElC,MAAAD,MAAA,MAAK,OAAM,wBAAkB,QAAA,OAAA,SAAA,0CACzB,MAAK,KAAK,GAAA,EACb,mBAAmB,GACnB,WACA,aAAa,MAAK,aAClB,YAAY,MAAK,YACjB,eAAe,MAAK,eACpB,eAAe,MAAK,eACpB,cAAc,MAAK,cACnB,cAAc,MAAK,cACnB,yBACA,yBACA,wBACA,uBAAsB,CACtB,CAAA;UACD,gBACC,cAAAC,QAAA,cAAA,OAAA,EAAK,WAAU,8BAA6B,GACzC,MAAK,OAAO,SAAS,CAAC;QAE1B;MAGP;AAEA,YAAgB,mBAAG,SAACD,KAInB;AAHC,YAAA,YAASA,IAAA;AAIH,YAAA,KAGF,MAAK,OAFP,iBAAc,GAAA,gBACd,KAAA,GAAA,gBAAA,iBAAc,OAAA,SAAG8C,UAAS,aAAa,iBAAc;AAEjD,YAAA,KAA6B,eACjC,WACA,cAAc,GAFR,cAAW,GAAA,aAAE,YAAS,GAAA;AAI9B,eACE,cAAA7C,QAAK,cAAA,OAAA,EAAA,WAAU,wDAAuD,GACnE,iBAAiB,GAAG,OAAA,aAAW,KAAA,EAAA,OAAM,SAAS,IAAK,QAAQ,SAAS,CAAC;MAG5E;AAEA,YAAY,eAAG,SAACD,KAMf;AALC,YAAA,YAASA,IAAA,WACT,KAAAA,IAAA,GAAA,IAAC,OAAA,SAAG,IAAC;AAKL,YAAM,aAAa,EAAE,WAAW,EAAC;AACjC,gBAAQ,MAAI;UACV,KAAK,MAAK,MAAM,uBAAuB;AACrC,mBAAO,MAAK,mBAAmB,UAAU;UAC3C,MAAK,MAAK,MAAM,uBACd,MAAK,MAAM,yBACX,MAAK,MAAM;AACX,mBAAO,MAAK,iBAAiB,UAAU;UACzC;AACE,mBAAO,MAAK,oBAAoB,UAAU;;MAEhD;AAEA,YAAA,eAAe,WAAA;;AACb,YAAI,MAAK,MAAM,sBAAsB,MAAK,MAAM,gBAAgB;AAC9D;;AAGF,YAAM,YAAkC,CAAA;AACxC,YAAM,eACJA,MAAA,MAAK,MAAM,iBAAW,QAAAA,QAAA,SAAAA,MAAI8C,UAAS,aAAa;AAClD,YAAM,mBAAmB,MAAK,MAAM,qBAChC,cAAc,IACd;AACJ,YAAM,gBACJ,MAAK,MAAM,uBAAuB,MAAK,MAAM,wBACzC,SAAS,MAAK,MAAM,MAAM,gBAAgB,IAC1C,UAAU,MAAK,MAAM,MAAM,gBAAgB;AACjD,YAAM,mBAAkB,KAAA,MAAK,MAAM,qBAAmB,QAAA,OAAA,SAAA,KAAA;AACtD,iBAAS,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AACpC,cAAM,cAAc,IAAI,kBAAkB;AAC1C,cAAM,YACJ,MAAK,MAAM,uBAAuB,MAAK,MAAM,wBACzC,SAAS,eAAe,WAAW,IACnC,UAAU,eAAe,WAAW;AAC1C,cAAM,WAAW,SAAS,OAAA,CAAC;AAC3B,cAAM,6BAA6B,IAAI,cAAc;AACrD,cAAM,+BAA+B,IAAI;AACzC,oBAAU,KACR,cAAA7C,QACE;YAAA;YAAA,EAAA,KAAK,UACL,KAAK,SAAC,KAAG;AACP,oBAAK,iBAAiB,QAAG,QAAH,QAAA,SAAA,MAAO;eAE/B,WAAU,oCAAmC;YAE5C,MAAK,aAAa,EAAE,WAAW,EAAC,CAAE;YACnC,cAAAA,QAAA,cAAC,OACKyB,QAAA,CAAA,GAAAoB,UAAS,cACT,MAAK,OAAK,EACd,iBAAiB,MAAK,MAAM,sBAC5B,KAAK,WACL,YAAY,MAAK,gBACjB,iBAAiB,MAAK,MAAM,oBAC5B,sBAAsB,MAAK,MAAM,iBACjC,iBAAiB,MAAK,qBACtB,cAAc,MAAK,uBACnB,gBAAgB,GAChB,eAAe,MAAK,MAAM,eAC1B,4BACA,6BAA0D,CAC1D,CAAA;UAAA,CACE;;AAGV,eAAO;MACT;AAEA,YAAA,cAAc,WAAA;AACZ,YAAI,MAAK,MAAM,oBAAoB;AACjC;;AAEF,YAAI,MAAK,MAAM,gBAAgB;AAC7B,iBACE,cAAA7C,QAAA;YAAA;YAAA,EAAK,WAAU,oCAAmC;YAC/C,MAAK,aAAa,EAAE,WAAW,MAAK,MAAM,KAAI,CAAE;YACjD,cAAAA,QAAC,cAAA,MACKyB,QAAA,CAAA,GAAAoB,UAAS,cACT,MAAK,OAAK,EACd,eAAe,MAAK,MAAM,eAC1B,MAAM,MAAK,MAAM,MACjB,YAAY,MAAK,gBACjB,oBAAoB,MAAK,oBACzB,kBAAkB,MAAK,sBACvB,kBAAkB,MAAK,qBAAoB,CAAA,CAAA;UAC3C;;AAIR;MACF;AAEA,YAAA,oBAAoB,WAAA;AAClB,YACE,MAAK,MAAM,mBACV,MAAK,MAAM,kBAAkB,MAAK,MAAM,qBACzC;AACA,iBACE,cAAA7C,QAAA,cAAC,MAAIyB,QAAA,CAAA,GACCoB,UAAS,cACT,MAAK,OACT,EAAA,UAAU,MAAK,MAAM,cACrB,QAAQ,MAAK,MAAM,YACnB,WAAW,MAAK,MAAM,eACtB,UAAU,MAAK,MAAM,eAAc,CACnC,CAAA;;AAGN;MACF;AAEA,YAAA,yBAAyB,WAAA;AACvB,YAAM,OAAO,MAAK,MAAM,WACpB,IAAI,KAAK,MAAK,MAAM,QAAQ,IAC5B;AACJ,YAAM,YAAY,QAAQ3C,SAAQ,IAAI,KAAK,QAAQ,MAAK,MAAM,QAAQ;AACtE,YAAM,aAAa,YACf,GAAG,OAAA,QAAQ,KAAK,SAAQ,CAAE,GAAC,GAAA,EAAA,OAAI,QAAQ,KAAK,WAAU,CAAE,CAAC,IACzD;AACJ,YAAI,MAAK,MAAM,eAAe;AAC5B,iBACE,cAAAF,QAAA,cAAC,WAASyB,QAAA,CAAA,GACJoB,UAAS,cACT,MAAK,OAAK,EACd,MAAM,MACN,YACA,UAAU,MAAK,MAAM,aAAY,CAAA,CAAA;;AAIvC;MACF;AAEA,YAAA,uBAAuB,WAAA;;AACf,YAAA,KAA6B,eACjC,MAAK,MAAM,OACX9C,MAAA,MAAK,MAAM,oBAAc,QAAAA,QAAA,SAAAA,MAAI8C,UAAS,aAAa,cAAc,GAF3D,cAAW,GAAA,aAAE,YAAS,GAAA;AAI9B,YAAI;AAEJ,YAAI,MAAK,MAAM,gBAAgB;AAC7B,4BAAkB,GAAG,OAAA,aAAiB,KAAA,EAAA,OAAA,SAAS;mBAE/C,MAAK,MAAM,uBACX,MAAK,MAAM,uBACX;AACA,4BAAkB,QAAQ,MAAK,MAAM,IAAI;eACpC;AACL,4BAAkB,GAAA,OAAG,iBACnB,SAAS,MAAK,MAAM,IAAI,GACxB,MAAK,MAAM,MAAM,GAClB,GAAA,EAAA,OAAI,QAAQ,MAAK,MAAM,IAAI,CAAC;;AAG/B,eACE,cAAA7C,QAAA,cAAA,QAAA,EACE,MAAK,SACK,aAAA,UACV,WAAU,8BAA6B,GAEtC,MAAK,MAAM,2BAA2B,eAAe;MAG5D;AAEA,YAAA,iBAAiB,WAAA;AACf,YAAI,MAAK,MAAM,UAAU;AACvB,iBACE,cAAAA,QAAA,cAAA,OAAA,EAAK,WAAU,uCAAsC,GAClD,MAAK,MAAM,QAAQ;;AAI1B;MACF;AAl3BE,YAAK,mBAAe,yBAAS;AAE7B,YAAK,QAAQ;QACX,MAAM,MAAK,cAAa;QACxB,eAAe;QACf,gBAAgB;QAChB,yBAAyB;;;;AAtB7B,WAAA,eAAW6C,WAAY,gBAAA;MAAvB,KAAA,WAAA;AACE,eAAO;UACL,aAAa;UACb,0BAA0B;UAC1B,aAAa;UACb,yBAAyB;UACzB,qBAAqB;UACrB,0BAA0B;UAC1B,sBAAsB;UACtB,gBAAgB;;;;;IAEnB,CAAA;AAeD,IAAAA,UAAA,UAAA,oBAAA,WAAA;AAAA,UAUC,QAAA;AALC,UAAI,KAAK,MAAM,gBAAgB;AAC7B,aAAK,uBAAwB,WAAA;AAC3B,gBAAK,SAAS,EAAE,gBAAgB,MAAK,eAAc,CAAE;UACtD;;;AAIL,IAAAA,UAAkB,UAAA,qBAAlB,SAAmB,WAAwB;AAA3C,UAwBC,QAAA;AAvBC,UACE,KAAK,MAAM,iBACV,CAACnC,WAAU,KAAK,MAAM,cAAc,UAAU,YAAY,KACzD,KAAK,MAAM,oBAAoB,UAAU,kBAC3C;AACA,YAAM,oBAAkB,CAACJ,aACvB,KAAK,MAAM,MACX,KAAK,MAAM,YAAY;AAEzB,aAAK,SACH;UACE,MAAM,KAAK,MAAM;QAClB,GACD,WAAA;AAAM,iBAAA,qBAAmB,MAAK,wBAAwB,MAAK,MAAM,IAAI;QAA/D,CAAgE;iBAGxE,KAAK,MAAM,cACX,CAACI,WAAU,KAAK,MAAM,YAAY,UAAU,UAAU,GACtD;AACA,aAAK,SAAS;UACZ,MAAM,KAAK,MAAM;QAClB,CAAA;;;AAw0BL,IAAAmC,UAAA,UAAA,SAAA,WAAA;AACE,UAAM,YAAY,KAAK,MAAM,aAAa;AAC1C,aACE,cAAA7C,QAAA;QAAC;QAAmB,EAClB,gBAAgB,KAAK,oBACrB,OAAO,EAAE,SAAS,WAAU,GAC5B,cAAc,KAAK,cACnB,aAAa,KAAK,MAAM,wBAAuB;QAE/C,cAAAA,QAAA;UAAC;UAAS,EACR,WAAW,KAAK,oBAAoB,KAAK,MAAM,WAAW;YACxD,+BAA+B,KAAK,MAAM;WAC3C,GACD,UAAU,KAAK,MAAM,kBAAkB,KAAK,MAAM,eAClD,oBAAoB,KAAK,MAAM,mBAAkB;UAEhD,KAAK,qBAAoB;UACzB,KAAK,qBAAoB;UACzB,KAAK,iBAAgB;UACrB,KAAK,aAAY;UACjB,KAAK,YAAW;UAChB,KAAK,kBAAiB;UACtB,KAAK,kBAAiB;UACtB,KAAK,uBAAsB;UAC3B,KAAK,eAAc;QAAE;MACZ;;AAIpB,WAAC6C;EAAD,EAl6BsC,uBAAS;;AC/L/C,IAAM,eAA4C,SAAC9C,KAI/B;MAHlB,OAAIA,IAAA,MACJ,KAAAA,IAAA,WAAA,YAAS,OAAA,SAAG,KAAE,IACd,UAAOA,IAAA;AAEP,MAAM,eAAe;AAErB,MAAI,OAAO,SAAS,UAAU;AAC5B,WACE,cAAAC,QACE,cAAA,KAAA,EAAA,WAAW,GAAG,OAAA,cAAY,GAAA,EAAA,OAAI,MAAI,GAAA,EAAA,OAAI,SAAS,GAAE,eACrC,QACZ,QAAgB,CAAA;;AAKtB,MAAI,cAAAA,QAAM,eAAe,IAAI,GAAG;AAE9B,WAAO,cAAAA,QAAM,aAAa,MAA4B;MACpD,WAAW,GAAA,OAAG,KAAK,MAAM,aAAa,IAAE,GAAA,EAAA,OAAI,cAAY,GAAA,EAAA,OAAI,SAAS;MACrE,SAAS,SAAC,OAAuB;AAC/B,YAAI,OAAO,KAAK,MAAM,YAAY,YAAY;AAC5C,eAAK,MAAM,QAAQ,KAAK;;AAG1B,YAAI,OAAO,YAAY,YAAY;AACjC,kBAAQ,KAAK;;;IAGlB,CAAA;;AAIH,SACE,cAAAA,QAAA;IAAA;IAAA,EACE,WAAW,GAAA,OAAG,cAAY,GAAA,EAAA,OAAI,SAAS,GACvC,OAAM,8BACN,SAAQ,eACR,QAAgB;IAEhB,cAAAA,QAAA,cAAA,QAAA,EAAM,GAAE,8NAA6N,CAAG;EAAA;AAG9O;AClDA,IAAA;;EAAA,SAAA,QAAA;AAAqB,cAAsBgD,SAAA,MAAA;AACzC,aAAAA,QAAY,OAAkB;AAC5B,UAAA,QAAA,OAAK,KAAA,MAAC,KAAK,KAAE;AAuBP,YAAU,aAAuB;AAtBvC,YAAK,KAAK,SAAS,cAAc,KAAK;;;AAGxC,IAAAA,QAAA,UAAA,oBAAA,WAAA;AACE,WAAK,cAAc,KAAK,MAAM,cAAc,UAAU,eACpD,KAAK,MAAM,QAAQ;AAErB,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa,SAAS,cAAc,KAAK;AAC9C,aAAK,WAAW,aAAa,MAAM,KAAK,MAAM,QAAQ;AACtD,SAAC,KAAK,MAAM,cAAc,SAAS,MAAM,YAAY,KAAK,UAAU;;AAEtE,WAAK,WAAW,YAAY,KAAK,EAAE;;AAGrC,IAAAA,QAAA,UAAA,uBAAA,WAAA;AACE,UAAI,KAAK,YAAY;AACnB,aAAK,WAAW,YAAY,KAAK,EAAE;;;AAOvC,IAAAA,QAAA,UAAA,SAAA,WAAA;AACE,aAAO,kBAAAC,QAAS,aAAa,KAAK,MAAM,UAAU,KAAK,EAAE;;AAE7D,WAACD;EAAD,EA9BqB,uBAAS;;ACZ9B,IAAM,4BACJ;AACF,IAAM,kBAAkB,SACtB,MAKqB;AAErB,MAAI,gBAAgB,mBAAmB;AACrC,WAAO,KAAK,aAAa;;AAG3B,SAAO,CAAC,KAAK,YAAY,KAAK,aAAa;AAC7C;AAqBA,IAAA;;EAAA,SAAA,QAAA;AAAqC,cAAuBE,UAAA,MAAA;AAK1D,aAAAA,SAAY,OAAmB;AAC7B,UAAA,QAAA,OAAK,KAAA,MAAC,KAAK,KAAE;AAef,YAAA,iBAAiB,WAAA;;AACf,eAAA,MAAM,UAAU,MACb,MACCnD,MAAA,MAAK,WAAW,aAAO,QAAAA,QAAA,SAAA,SAAAA,IAAE,iBAAiB,yBAAyB,GACnE,GACA,EAAE,EAEH,OAAO,eAAe;;AAE3B,YAAA,mBAAmB,WAAA;AACjB,YAAM,cAAc,MAAK,eAAc;AACvC,uBACE,YAAY,SAAS,KACrB,YAAY,YAAY,SAAS,CAAC,EAAE,MAAK;MAC7C;AAEA,YAAA,iBAAiB,WAAA;AACf,YAAM,cAAc,MAAK,eAAc;AACvC,uBAAe,YAAY,SAAS,KAAK,YAAY,CAAC,EAAE,MAAK;MAC/D;AAhCE,YAAK,iBAAa,yBAAS;;;AAkC7B,IAAAmD,SAAA,UAAA,SAAA,WAAA;;AACE,UAAI,GAAEnD,MAAA,KAAK,MAAM,mBAAiB,QAAAA,QAAA,SAAAA,MAAAmD,SAAQ,aAAa,gBAAgB;AACrE,eAAO,KAAK,MAAM;;AAEpB,aACE,cAAAlD,QAAK;QAAA;QAAA,EAAA,WAAU,8BAA6B,KAAK,KAAK,WAAU;QAC9D,cAAAA,QAAA,cAAA,OAAA,EACE,WAAU,qCACV,UAAU,GACV,SAAS,KAAK,iBAAgB,CAC9B;QACD,KAAK,MAAM;QACZ,cAAAA,QAAA,cAAA,OAAA,EACE,WAAU,mCACV,UAAU,GACV,SAAS,KAAK,eAAc,CAC5B;MAAA;;AAzDD,IAAAkD,SAAA,eAAe;MACpB,eAAe;IAChB;AA2DH,WAACA;IA9DoC,uBAAS;;ACFtB,SAAA,aACtBC,YAAiC;AAGjC,MAAM,eAA4B,SAAC,OAAK;;AACtC,QAAM,aACJ,OAAO,MAAM,eAAe,YAAY,MAAM,aAAa;AAC7D,QAAM,eAAyC,sBAAO,IAAI;AAC1D,QAAM,gBAAgBC,aAAW,QAAA,EAC/B,MAAM,CAAC,YACP,sBAAsB,YACtB,WAAW,MAAM,iBACjB,YAAU,cAAA;MACRC,MAAK,EAAE,SAAS,GAAE,CAAE;MACpB7B,QAAO,EAAE;MACT8B,OAAM,EAAE,SAAS,SAAQ,CAAE;IACxB,IAACvD,MAAA,MAAM,qBAAmB,QAAAA,QAAA,SAAAA,MAAA,CAAA,GAAG,IAAA,EAAA,GAE/B,MAAM,WAAW,CAAA;AAGtB,QAAM,iBAAiB0B,QAClBA,QAAA,CAAA,GAAA,KAAK,GAAA,EACR,YACA,aAAW,QAAA,QAAA,CAAA,GAAO,aAAa,GAAA,EAAE,SAAQ,CAAA,EAAA,CAAA;AAG3C,WAAO,cAAAzB,QAAC,cAAAmD,YAAc1B,QAAA,CAAA,GAAA,cAAc,CAAA;EACtC;AAEA,SAAO;AACT;AC3CA,IAAA;;EAAA,SAAA,QAAA;AAAqC,cAA+B8B,kBAAA,MAAA;AAApE,aAAAA,mBAAA;;;AACE,WAAA,eAAWA,kBAAY,gBAAA;MAAvB,KAAA,WAAA;AACE,eAAO;UACL,YAAY;;;;;IAEf,CAAA;AAED,IAAAA,iBAAA,UAAA,SAAA,WAAA;AACQ,UAAAxD,MAYF,KAAK,OAXP,YAASA,IAAA,WACT,mBAAgBA,IAAA,kBAChB,KAAoDA,IAAA,YAApD,aAAa,OAAA,SAAAwD,iBAAgB,aAAa,aAAU,IACpD,kBAAexD,IAAA,iBACf,kBAAeA,IAAA,iBACf,gBAAaA,IAAA,eACb,kBAAeA,IAAA,iBACf,WAAQA,IAAA,UACR,aAAUA,IAAA,YACV,cAAWA,IAAA,aACX,YAASA,IAAA;AAGX,UAAI,SAAyC;AAE7C,UAAI,CAAC,YAAY;AACf,YAAM,UAAU,KAAK,2BAA2B,SAAS;AACzD,iBACE,cAAAC,QAAA;UAAC;UAAQ,EAAA,cAA4B;UACnC,cAAAA,QACE;YAAA;YAAA,EAAA,KAAK,YAAY,KAAK,aACtB,OAAO,YAAY,gBACnB,WAAW,SACK,kBAAA,YAAY,WAC5B,WAAW,gBAAe;YAEzB;YACA,aACC,cAAAA,QAAC,cAAA,eAAa,EACZ,KAAK,YAAY,UACjB,SAAS,YAAY,SACrB,MAAK,gBACL,aAAa,GACb,QAAQ,GACR,OAAO,IACP,OAAO,EAAE,WAAW,mBAAkB,GACtC,WAAU,6BAA4B,CAAA;UAEzC;QACG;;AAKZ,UAAI,KAAK,MAAM,iBAAiB;AAC9B,qBAAS,6BAAc,KAAK,MAAM,iBAAiB,CAAA,GAAI,MAAM;;AAG/D,UAAI,YAAY,CAAC,YAAY;AAC3B,iBACE,cAAAA,QAAA,cAAC,QAAM,EAAC,UAAoB,WAAsB,GAC/C,MAAM;;AAKb,UAAM,iBAAiB,KAAK,4BAA4B,gBAAgB;AAExE,aACE,cAAAA,QAAA;QAAA,cAAAA,QAAA;QAAA;QACE,cAAAA,QAAA,cAAA,OAAA,EAAK,KAAK,YAAY,KAAK,cAAc,WAAW,eAAc,GAC/D,eAAe;QAEjB;MAAM;;AAIf,WAACuD;EAAD,EA5EqC,uBAAS;;AA8E9C,IAAA,oBAAe,aAAmC,eAAe;AC9CjE,IAAM,0BAA0B;AAKhC,SAAS,uBACP,OACA,OAAmB;AAEnB,MAAI,SAAS,OAAO;AAClB,WACE,SAAS,KAAK,MAAM,SAAS,KAAK,KAAK,QAAQ,KAAK,MAAM,QAAQ,KAAK;;AAI3E,SAAO,UAAU;AACnB;AAKA,IAAM,cAAc;AA0KpB,IAAA;;EAAA,SAAA,QAAA;AAAwC,cAGvCC,aAAA,MAAA;AAkDC,aAAAA,YAAY,OAAsB;AAChC,UAAA,QAAA,OAAK,KAAA,MAAC,KAAK,KAAE;AAiEf,YAAQ,WAAoB;AAE5B,YAAK,QAAuB;AAE5B,YAAA,kBAAkB,WAAA;AAChB,eAAA,MAAK,MAAM,aACP,MAAK,MAAM,aACX,MAAK,MAAM,cAAc,MAAK,MAAM,YAClC,MAAK,MAAM,YACX,MAAK,MAAM,gBAAgB,MAAK,MAAM,UACpC,MAAK,MAAM,UACX,QAAO;MANf;AASF,YAAA,iBAAiB,WAAA;;AACf,gBAAAzD,MAAA,MAAK,MAAM,cAAU,QAAAA,QAAA,SAAA,SAAAA,IAAA,OAAsB,SAAC,aAAa,SAAO;AAC9D,cAAM,OAAO,IAAI,KAAK,QAAQ,IAAI;AAClC,cAAI,CAACG,SAAQ,IAAI,GAAG;AAClB,mBAAO;;AAGT,iBAAA,cAAA,cAAA,CAAA,GAAW,aAAkB,IAAA,GAAA,CAAAuB,QAAAA,QAAA,CAAA,GAAA,OAAO,GAAE,EAAA,KAAI,CAAI,CAAA,GAAA,KAAA;WAC7C,CAAA,CAAE;;AAEP,YAAA,mBAAmB,WAAA;;AACjB,YAAM,sBAAsB,MAAK,gBAAe;AAChD,YAAM,UAAU,oBAAoB,MAAK,KAAK;AAC9C,YAAM,UAAU,oBAAoB,MAAK,KAAK;AAC9C,YAAM,sBACJ,WAAW,SAAS,qBAAqB,cAAc,OAAO,CAAC,IAC3D,UACA,WAAW,QAAQ,qBAAqB,YAAY,OAAO,CAAC,IAC1D,UACA;AACR,eAAO;UACL,MAAM,MAAK,MAAM,aAAa;UAC9B,cAAc;UACd,YAAY;UACZ,eACE1B,MAAC,MAAK,MAAM,eACR,MAAK,MAAM,YACX,MAAK,MAAM,cAAS,QAAAA,QAAA,SAAAA,MAAI;;;UAG9B,gBAAgB,oBAAoB,MAAK,MAAM,cAAc;UAC7D,SAAS;;;UAGT,sBAAsB;UACtB,yBAAyB;UACzB,WAAW;;MAEf;AAEA,YAAA,oBAAoB,WAAA;AAClB,cAAK,SAAQ0B,QAAAA,QAAA,CAAA,GACR,MAAK,KAAK,GAAA,EACb,WAAW,MAAK,CAAA,CAAA;MAEpB;AAEA,YAAA,kBAAkB,WAAA;AAChB,cAAK,SAAQA,QAAAA,QAAA,CAAA,GACR,MAAK,KAAK,GAAA,EACb,WAAW,KAAI,CAAA,CAAA;MAEnB;AAEA,YAAA,mCAAmC,WAAA;AACjC,YAAI,SAAS,oBAAoB,UAAU;AACzC;;AAGF,cAAK,gBAAe;MACtB;AAEA,YAAA,2BAA2B,WAAA;AACzB,YAAI,MAAK,qBAAqB;AAC5B,uBAAa,MAAK,mBAAmB;;MAEzC;AAEA,YAAA,YAAY,WAAA;AACV,mBAAW,WAAA;;AACT,WAAA,MAAA1B,MAAA,MAAK,WAAO,QAAAA,QAAA,SAAA,SAAAA,IAAA,WAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAA,EAAE,eAAe,KAAI,CAAE;WAC1C,CAAC;MACN;AAEA,YAAA,WAAW,WAAA;AACT,mBAAW,WAAA;;AACT,WAAA,MAAAA,MAAA,MAAK,WAAO,QAAAA,QAAA,SAAA,SAAAA,IAAA,UAAI,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,GAAA;WACf,CAAC;MACN;AAEA,YAAA,WAAW,WAAA;AACT,cAAK,UAAS;MAChB;AAEA,YAAA,UAAU,WAAA;AACR,cAAK,SAAQ;AACb,cAAK,iBAAgB;MACvB;AAEA,YAAA,UAAU,SAAC,MAAe,aAA4B;AAA5B,YAAA,gBAAA,QAAA;AAAA,wBAA4B;QAAA;AACpD,cAAK,SACH;UACE;UACA,cACE,QAAQ,MAAK,MAAM,OACf,MAAK,MAAM,eACX,MAAK,iBAAgB,EAAG;UAC9B,qBAAqB;WAEvB,WAAA;AACE,cAAI,CAAC,MAAM;AACT,kBAAK,SACH,SAAC,MAAqB;AAAK,qBAAC;gBAC1B,SAAS,cAAc,KAAK,UAAU;;YACtC,GACF,WAAA;AACE,eAAC,eAAe,MAAK,QAAO;AAE5B,oBAAK,SAAS,EAAE,YAAY,KAAI,CAAE;YACpC,CAAC;;QAGP,CAAC;MAEL;AACA,YAAA,UAAU,WAAA;AAAe,eAAA,OAAO,MAAK,MAAM,YAAY;MAAC;AAExD,YAAA,iBAAiB,WAAA;AACf,eAAA,MAAK,MAAM,SAAS,SAChB,MAAK,MAAM,QAAQ,CAAC,MAAK,MAAM,YAAY,CAAC,MAAK,MAAM,WACvD,MAAK,MAAM;MAFf;AAIF,YAAW,cAAG,SAAC,OAAoC;;AACjD,YAAM,gBAAgB,MAAK,MAAM;AACjC,YAAM,gBAAgB,gBAAgB,MAAK,MAAM,OAAO;AAExD,YAAI,eAAe;AACjB,gBAAK,kBAAiB;;AAGxB,YAAI,CAAC,MAAK,MAAM,gBAAgB,eAAe;AAC7C,WAAA,MAAAA,MAAA,MAAK,OAAM,aAAO,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,KAAK;AAC1B,cAAI,CAAC,MAAK,MAAM,sBAAsB,CAAC,MAAK,MAAM,UAAU;AAC1D,kBAAK,QAAQ,IAAI;;;AAGrB,cAAK,SAAS,EAAE,SAAS,KAAI,CAAE;MACjC;AAEA,YAAA,uBAAuB,WAAA;AAErB,YAAI,MAAK,qBAAqB;AAC5B,gBAAK,yBAAwB;;AAM/B,cAAK,SAAS,EAAE,cAAc,KAAI,GAAI,WAAA;AACpC,gBAAK,sBAAsB,WAAW,WAAA;AACpC,kBAAK,SAAQ;AACb,kBAAK,SAAS,EAAE,cAAc,MAAK,CAAE;UACvC,CAAC;QACH,CAAC;MACH;AAEA,YAAA,mBAAmB,WAAA;AACjB,qBAAa,MAAK,iBAAiB;AACnC,cAAK,oBAAoB;MAC3B;AAEA,YAAA,kBAAkB,WAAA;AAChB,cAAK,iBAAgB;AACrB,cAAK,oBAAoB,WAAW,WAAA;AAAM,iBAAA,MAAK,SAAQ;QAAb,GAAiB,CAAC;MAC9D;AAEA,YAAA,sBAAsB,WAAA;AACpB,cAAK,iBAAgB;MACvB;AAEA,YAAU,aAAG,SAAC,OAAoC;;AAChD,YAAI,CAAC,MAAK,MAAM,QAAQ,MAAK,MAAM,cAAc,MAAK,MAAM,eAAe;AACzE,WAAA,MAAAA,MAAA,MAAK,OAAM,YAAM,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,KAAK;;AAG3B,cAAK,SAAS,EAAE,SAAS,MAAK,CAAE;MAClC;AAEA,YAA0B,6BAAG,SAAC,OAAiB;;AAC7C,YAAI,CAAC,MAAK,MAAM,QAAQ;AACtB,gBAAK,QAAQ,KAAK;;AAEpB,SAAA,MAAAA,MAAA,MAAK,OAAM,oBAAc,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,KAAK;AACjC,YAAI,MAAK,MAAM,YAAY;AACzB,gBAAM,eAAc;;MAExB;AAGA,YAAA,eAAe,WAAA;;AACb,YAAgE,UAAA,CAAA;iBAAA,KAAA,GAAhE,KAAgE,UAAA,QAAhE,MAAgE;AAAhE,kBAAgE,EAAA,IAAA,UAAA,EAAA;;AAEhE,YAAM,QAAQ,QAAQ,CAAC;AACvB,YAAI,MAAK,MAAM,aAAa;AAC1B,gBAAK,MAAM,YAAY,MAAM,OAAM,OAAO;AAC1C,cACE,CAAC,SACD,OAAO,MAAM,uBAAuB,cACpC,MAAM,mBAAkB,GACxB;AACA;;;AAIJ,cAAK,SAAS;UACZ,aACE,UAAK,QAAL,UAAA,SAAA,SAAA,MAAO,mBAAkB,mBAAmB,MAAM,OAAO,QAAQ;UACnE,qBAAqB;QACtB,CAAA;AAEK,YAAA,KAMF,MAAK,OALP,KAA+C,GAAA,YAA/C,aAAU,OAAA,SAAGyD,YAAW,aAAa,aAAU,IAC/C,KAAA,GAAA,eAAA,gBAAa,OAAA,SAAGA,YAAW,aAAa,gBAAa,IACrD,eAAY,GAAA,cACZ,YAAS,GAAA,WACT,UAAO,GAAA;AAGT,YAAM,SACJ,UAAK,QAAL,UAAA,SAAA,SAAA,MAAO,mBAAkB,mBAAmB,MAAM,OAAO,QAAQ;AAEnE,YAAI,cAAc;AACV,cAAA,KAAyB,MAC5B,MAAM,KAAK,CAAC,EACZ,IAAI,SAAC,KAAG;AAAK,mBAAA,IAAI,KAAI;UAAR,CAAU,GAFnB,aAAU,GAAA,CAAA,GAAE,WAAQ,GAAA,CAAA;AAG3B,cAAM,eAAe,UACnB,eAAA,QAAA,eAAA,SAAA,aAAc,IACd,YACA,MAAK,MAAM,QACX,aAAa;AAEf,cAAM,aAAa,UACjB,aAAA,QAAA,aAAA,SAAA,WAAY,IACZ,YACA,MAAK,MAAM,QACX,aAAa;AAEf,cAAM,gBAAe,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,QAAO,QAAO,iBAAY,QAAZ,iBAAY,SAAA,SAAZ,aAAc,QAAO;AACnE,cAAM,cAAa,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,QAAO,QAAO,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,QAAO;AAE7D,cAAI,CAAC,gBAAgB,CAAC,YAAY;AAChC;;AAGF,cAAI,gBAAgB,cAAc,cAAc,MAAK,KAAK,GAAG;AAC3D;;AAEF,cAAI,cAAc,cAAc,YAAY,MAAK,KAAK,GAAG;AACvD;;AAGF,WAAA,MAAAzD,MAAA,MAAK,OAAM,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,CAAC,cAAc,UAAU,GAAG,KAAK;eAClD;AAEL,cAAI,OAAO,UACT,OACA,YACA,MAAK,MAAM,QACX,eACA,MAAK,MAAM,OAAO;AAIpB,cACE,MAAK,MAAM,sBACX,MAAK,MAAM,YACX,QACA,CAACW,WAAU,MAAM,MAAK,MAAM,QAAQ,GACpC;AACA,mBAAO,IAAI,MAAK,MAAM,UAAU;cAC9B,OAAO,SAAS,IAAI;cACpB,SAAS,WAAW,IAAI;cACxB,SAAS,WAAW,IAAI;YACzB,CAAA;;AAIH,cAAI,QAAQ,CAAC,OAAO;AAClB,kBAAK,YAAY,MAAM,OAAO,IAAI;;;MAGxC;AAEA,YAAA,eAAe,SACb,MACA,OACA,iBAAwB;AAExB,YAAI,MAAK,MAAM,uBAAuB,CAAC,MAAK,MAAM,gBAAgB;AAGhE,gBAAK,qBAAoB;;AAE3B,YAAI,MAAK,MAAM,aAAa;AAC1B,gBAAK,MAAM,YAAY,KAAK;;AAE9B,cAAK,YAAY,MAAM,OAAO,OAAO,eAAe;AACpD,YAAI,MAAK,MAAM,gBAAgB;AAC7B,gBAAK,SAAS,EAAE,yBAAyB,KAAI,CAAE;;AAEjD,YAAI,CAAC,MAAK,MAAM,uBAAuB,MAAK,MAAM,gBAAgB;AAChE,gBAAK,gBAAgB,IAAI;mBAChB,CAAC,MAAK,MAAM,QAAQ;AAC7B,cAAI,CAAC,MAAK,MAAM,cAAc;AAC5B,kBAAK,QAAQ,KAAK;;AAGd,cAAAX,MAAyB,MAAK,OAA5B,YAASA,IAAA,WAAE,UAAOA,IAAA;AAE1B,cACE,aACA,CAAC,YACA,MAAK,MAAM,aAAa,CAAC,aAAa,MAAM,SAAS,IACtD;AACA,kBAAK,QAAQ,KAAK;;;MAGxB;AAGA,YAAW,cAAG,SACZ,MACA,OACA,WACA,iBAAwB;;AAExB,YAAI,cAAc;AAGlB,YAAI,MAAK,MAAM,gBAAgB;AAC7B,cACE,gBAAgB,QAChB,eAAe,QAAQ,WAAW,GAAG,MAAK,KAAK,GAC/C;AACA;;mBAEO,MAAK,MAAM,qBAAqB;AACzC,cAAI,gBAAgB,QAAQ,gBAAgB,aAAa,MAAK,KAAK,GAAG;AACpE;;eAEG;AACL,cAAI,gBAAgB,QAAQ,cAAc,aAAa,MAAK,KAAK,GAAG;AAClE;;;AAIE,YAAA,KASF,MAAK,OARP,WAAQ,GAAA,UACR,eAAY,GAAA,cACZ,YAAS,GAAA,WACT,UAAO,GAAA,SACP,kBAAe,GAAA,iBACf,gBAAa,GAAA,eACb,UAAO,GAAA,SACP,YAAS,GAAA;AAGX,YACE,CAACa,SAAQ,MAAK,MAAM,UAAU,WAAW,KACzC,MAAK,MAAM,gBACX,gBACA,iBACA;AACA,cAAI,gBAAgB,MAAM;AAExB,gBACE,MAAK,MAAM,aACV,CAAC,aACC,CAAC,MAAK,MAAM,kBACX,CAAC,MAAK,MAAM,sBACZ,CAAC,MAAK,MAAM,gBAChB;AACA,4BAAc,QAAQ,aAAa;gBACjC,MAAM,SAAS,MAAK,MAAM,QAAQ;gBAClC,QAAQ,WAAW,MAAK,MAAM,QAAQ;gBACtC,QAAQ,WAAW,MAAK,MAAM,QAAQ;cACvC,CAAA;;AAIH,gBACE,CAAC,cACA,MAAK,MAAM,kBAAkB,MAAK,MAAM,qBACzC;AACA,kBAAI,SAAS;AACX,8BAAc,QAAQ,aAAa;kBACjC,MAAM,QAAQ,SAAQ;kBACtB,QAAQ,QAAQ,WAAU;kBAC1B,QAAQ,QAAQ,WAAU;gBAC3B,CAAA;;;AAIL,gBAAI,CAAC,MAAK,MAAM,QAAQ;AACtB,oBAAK,SAAS;gBACZ,cAAc;cACf,CAAA;;AAEH,gBAAI,CAAC,MAAK,MAAM,oBAAoB;AAClC,oBAAK,SAAS,EAAE,gBAAgC,CAAE;;;AAItD,cAAI,cAAc;AAChB,gBAAM,WAAW,CAAC,aAAa,CAAC;AAChC,gBAAM,gBAAgB,aAAa,CAAC;AACpC,gBAAM,gBAAgB,aAAa;AACnC,gBAAI,UAAU;AACZ,2BAAA,QAAA,aAAA,SAAA,SAAA,SAAW,CAAC,aAAa,IAAI,GAAG,KAAK;uBAC5B,eAAe;AACxB,kBAAI,gBAAgB,MAAM;AACxB,6BAAA,QAAA,aAAA,SAAA,SAAA,SAAW,CAAC,MAAM,IAAI,GAAG,KAAK;yBACrB,aAAa,aAAa,SAAS,GAAG;AAC/C,oBAAI,WAAW;AACb,+BAAA,QAAA,aAAA,SAAA,SAAA,SAAW,CAAC,aAAa,SAAS,GAAG,KAAK;uBACrC;AACL,+BAAA,QAAA,aAAA,SAAA,SAAA,SAAW,CAAC,aAAa,IAAI,GAAG,KAAK;;qBAElC;AACL,6BAAA,QAAA,aAAA,SAAA,SAAA,SAAW,CAAC,WAAW,WAAW,GAAG,KAAK;;;AAG9C,gBAAI,eAAe;AACjB,2BAAA,QAAA,aAAA,SAAA,SAAA,SAAW,CAAC,aAAa,IAAI,GAAG,KAAK;;qBAE9B,iBAAiB;AAC1B,gBAAI,gBAAgB,MAAM;AACxB,kBAAI,EAAC,kBAAA,QAAA,kBAAA,SAAA,SAAA,cAAe,SAAQ;AAC1B,6BAAQ,QAAR,aAAQ,SAAA,SAAR,SAAW,CAAC,WAAW,GAAG,KAAK;qBAC1B;AACL,oBAAM,+BAA+B,cAAc,KACjD,SAAC,cAAY;AAAK,yBAAAF,WAAU,cAAc,WAAW;gBAAnC,CAAoC;AAGxD,oBAAI,8BAA8B;AAChC,sBAAM,YAAY,cAAc,OAC9B,SAAC,cAAiB;AAAA,2BAAA,CAACA,WAAU,cAAc,WAAW;kBAApC,CAAqC;AAGzD,+BAAQ,QAAR,aAAA,SAAA,SAAA,SAAW,WAAW,KAAK;uBACtB;AACL,+BAAQ,QAAR,aAAQ,SAAA,SAAR,SAAQ,cAAA,cAAA,CAAA,GAAO,eAAa,IAAA,GAAA,CAAE,WAAW,GAAA,KAAA,GAAG,KAAK;;;;iBAIlD;AACL,yBAAQ,QAAR,aAAA,SAAA,SAAA,SAAW,aAAa,KAAK;;;AAIjC,YAAI,CAAC,WAAW;AACd,WAAA,MAAAX,MAAA,MAAK,OAAM,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,aAAa,KAAK;AACxC,gBAAK,SAAS,EAAE,YAAY,KAAI,CAAE;;MAEtC;AAGA,YAAe,kBAAG,SAAC,MAAkB;AACnC,YAAM,aAAa,OAAO,MAAK,MAAM,OAAO;AAC5C,YAAM,aAAa,OAAO,MAAK,MAAM,OAAO;AAC5C,YAAI,uBAAuB;AAC3B,YAAI,MAAM;AACR,cAAM,iBAAiB,cAAc,IAAI;AACzC,cAAI,cAAc,YAAY;AAE5B,mCAAuB,aACrB,MACA,MAAK,MAAM,SACX,MAAK,MAAM,OAAO;qBAEX,YAAY;AACrB,gBAAM,oBAAoB,cAAc,MAAK,MAAM,OAAO;AAC1D,mCACE,QAAQ,MAAM,iBAAiB,KAC/Ba,SAAQ,gBAAgB,iBAAiB;qBAClC,YAAY;AACrB,gBAAM,kBAAkB,YAAY,MAAK,MAAM,OAAO;AACtD,mCACE,SAAS,MAAM,eAAe,KAC9BA,SAAQ,gBAAgB,eAAe;;;AAG7C,YAAI,sBAAsB;AACxB,gBAAK,SAAS;YACZ,cAAc;UACf,CAAA;;MAEL;AAEA,YAAA,iBAAiB,WAAA;AACf,cAAK,QAAQ,CAAC,MAAK,MAAM,IAAI;MAC/B;AAEA,YAAgB,mBAAG,SAAC,MAAU;;AAC5B,YAAI,MAAK,MAAM,gBAAgB,MAAK,MAAM,iBAAiB;AACzD;;AAGF,YAAM,WAAW,MAAK,MAAM,WACxB,MAAK,MAAM,WACX,MAAK,gBAAe;AACxB,YAAM,cAAc,MAAK,MAAM,WAC3B,OACA,QAAQ,UAAU;UAChB,MAAM,SAAS,IAAI;UACnB,QAAQ,WAAW,IAAI;QACxB,CAAA;AAEL,cAAK,SAAS;UACZ,cAAc;QACf,CAAA;AAED,SAAA,MAAAb,MAAA,MAAK,OAAM,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,WAAW;AACjC,YAAI,MAAK,MAAM,uBAAuB,CAAC,MAAK,MAAM,eAAe;AAC/D,gBAAK,qBAAoB;AACzB,gBAAK,QAAQ,KAAK;;AAEpB,YAAI,MAAK,MAAM,eAAe;AAC5B,gBAAK,QAAQ,IAAI;;AAEnB,YAAI,MAAK,MAAM,sBAAsB,MAAK,MAAM,gBAAgB;AAC9D,gBAAK,SAAS,EAAE,yBAAyB,KAAI,CAAE;;AAEjD,cAAK,SAAS,EAAE,YAAY,KAAI,CAAE;MACpC;AAEA,YAAA,eAAe,WAAA;;AACb,YAAI,CAAC,MAAK,MAAM,YAAY,CAAC,MAAK,MAAM,UAAU;AAChD,gBAAK,QAAQ,IAAI;;AAGnB,SAAA,MAAAA,MAAA,MAAK,OAAM,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,GAAA;MACzB;AAEA,YAAc,iBAAG,SAAC,OAAuC;;AACvD,SAAA,MAAAA,MAAA,MAAK,OAAM,eAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,KAAK;AAC5B,YAAM,WAAW,MAAM;AAEvB,YACE,CAAC,MAAK,MAAM,QACZ,CAAC,MAAK,MAAM,UACZ,CAAC,MAAK,MAAM,oBACZ;AACA,cACE,aAAa,QAAQ,aACrB,aAAa,QAAQ,WACrB,aAAa,QAAQ,OACrB;AACA,aAAA,KAAA,MAAK,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,KAAA;;AAEnB;;AAIF,YAAI,MAAK,MAAM,MAAM;AACnB,cAAI,aAAa,QAAQ,aAAa,aAAa,QAAQ,SAAS;AAClE,kBAAM,eAAc;AACpB,gBAAM,iBAAiB,MAAK,MAAM,qBAC9B,oDACA,MAAK,MAAM,kBAAkB,MAAK,MAAM,kBACtC,iDACA,MAAK,MAAM,2BACT,MAAK,MAAM,sBACX,gDACA;AACR,gBAAM,iBACJ,KAAA,MAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,aAAa,oBAAmB,WAC/C,MAAK,SAAS,aAAa,QAAQ,cAAc,cAAc;AACjE,oCAAwB,eACtB,aAAa,MAAM,EAAE,eAAe,KAAI,CAAE;AAE5C;;AAGF,cAAM,OAAO,QAAQ,MAAK,MAAM,YAAY;AAC5C,cAAI,aAAa,QAAQ,OAAO;AAC9B,kBAAM,eAAc;AACnB,kBAAM,OAA4B,KAAI;AACvC,gBACE,MAAK,QAAO,KACZ,MAAK,MAAM,wBAAwB,+BACnC;AACA,oBAAK,aAAa,MAAM,KAAK;AAC7B,eAAC,MAAK,MAAM,uBAAuB,MAAK,gBAAgB,IAAI;mBACvD;AACL,oBAAK,QAAQ,KAAK;;qBAEX,aAAa,QAAQ,QAAQ;AACtC,kBAAM,eAAc;AACnB,kBAAM,OAA4B,KAAI;AACvC,kBAAK,qBAAoB;AACzB,kBAAK,QAAQ,KAAK;qBACT,aAAa,QAAQ,KAAK;AACnC,kBAAK,QAAQ,KAAK;;AAGpB,cAAI,CAAC,MAAK,QAAO,GAAI;AACnB,aAAA,MAAA,KAAA,MAAK,OAAM,kBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAA,EAAE,MAAM,GAAG,KAAK,YAAW,CAAE;;;MAG7D;AAEA,YAAe,kBAAG,SAAC,OAA0C;AAC3D,YAAM,WAAW,MAAM;AACvB,YAAI,aAAa,QAAQ,QAAQ;AAC/B,gBAAM,eAAc;AACpB,gBAAK,SACH;YACE,cAAc;aAEhB,WAAA;AACE,kBAAK,QAAQ,KAAK;AAClB,uBAAW,WAAA;AACT,oBAAK,SAAQ;AACb,oBAAK,SAAS,EAAE,cAAc,MAAK,CAAE;YACvC,CAAC;UACH,CAAC;;MAGP;AAGA,YAAY,eAAG,SAAC,OAA0C;;AAClD,YAAA,KAUF,MAAK,OATP,UAAO,GAAA,SACP,UAAO,GAAA,SACP,6BAA0B,GAAA,4BAC1B,iBAAc,GAAA,gBACd,sBAAmB,GAAA,qBACnB,SAAM,GAAA,QACN,mBAAgB,GAAA,kBAChB,qBAAkB,GAAA,oBAClB0D,UAAM,GAAA;AAER,SAAA,MAAA1D,MAAA,MAAK,OAAM,eAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,KAAG,KAAK;AAC5B,YAAI;AAA4B;AAChC,YAAM,WAAW,MAAM;AACvB,YAAM,mBAAmB,MAAM;AAE/B,YAAM,OAAO,QAAQ,MAAK,MAAM,YAAY;AAE5C,YAAM,mBAAmB,SAAC+B,WAAmB,MAAU;AACrD,cAAI,oBAAoB;AACxB,kBAAQA,WAAQ;YACd,KAAK,QAAQ;AACX,kCAAoB,iBAChB,SAAS,MAAM,CAAC,IAChB,QAAQ,MAAM,CAAC;AACnB;YACF,KAAK,QAAQ;AACX,kCAAoB,iBAChB,SAAS,MAAM,CAAC,IAChB,QAAQ,MAAM,CAAC;AACnB;YACF,KAAK,QAAQ;AACX,kCAAoB,SAAS,MAAM,CAAC;AACpC;YACF,KAAK,QAAQ;AACX,kCAAoB,SAAS,MAAM,CAAC;AACpC;YACF,KAAK,QAAQ;AACX,kCAAoB,mBAChB,SAAS,MAAM,CAAC,IAChB,UAAU,MAAM,CAAC;AACrB;YACF,KAAK,QAAQ;AACX,kCAAoB,mBAChB,SAAS,MAAM,CAAC,IAChB,UAAU,MAAM,CAAC;AACrB;YACF,KAAK,QAAQ;AACX,kCAAoB,eAAe,MAAM,QAAQ,gBAAgB;AACjE;YACF,KAAK,QAAQ;AACX,kCAAoB,aAAa,IAAI;AACrC;;AAEJ,iBAAO;QACT;AAEA,YAAM,aAAa,SAACA,WAAmB,MAAU;AAC/C,cAAM,iBAAiB;AACvB,cAAI,eAAeA;AACnB,cAAI,iBAAiB;AACrB,cAAI,aAAa;AACjB,cAAI4B,gBAAe,iBAAiB5B,WAAU,IAAI;AAElD,iBAAO,CAAC,gBAAgB;AACtB,gBAAI,cAAc,gBAAgB;AAChC,cAAA4B,gBAAe;AACf;;AAGF,gBAAI,WAAWA,gBAAe,SAAS;AACrC,6BAAe,QAAQ;AACvB,cAAAA,gBAAe,cAAc,SAAS,MAAK,KAAK,IAC5C,iBAAiB,cAAcA,aAAY,IAC3C;;AAIN,gBAAI,WAAWA,gBAAe,SAAS;AACrC,6BAAe,QAAQ;AACvB,cAAAA,gBAAe,cAAc,SAAS,MAAK,KAAK,IAC5C,iBAAiB,cAAcA,aAAY,IAC3C;;AAGN,gBAAI,cAAcA,eAAc,MAAK,KAAK,GAAG;AAE3C,kBACE,iBAAiB,QAAQ,UACzB,iBAAiB,QAAQ,MACzB;AACA,+BAAe,QAAQ;;AAIzB,kBACE,iBAAiB,QAAQ,YACzB,iBAAiB,QAAQ,KACzB;AACA,+BAAe,QAAQ;;AAEzB,cAAAA,gBAAe,iBAAiB,cAAcA,aAAY;mBACrD;AACL,+BAAiB;;AAEnB;;AAGF,iBAAOA;QACT;AAEA,YAAI,aAAa,QAAQ,OAAO;AAC9B,gBAAM,eAAc;AACpB,gBAAK,aAAa,MAAM,KAAK;AAC7B,WAAC,uBAAuB,MAAK,gBAAgB,IAAI;AACjD;mBACS,aAAa,QAAQ,QAAQ;AACtC,gBAAM,eAAc;AAEpB,gBAAK,QAAQ,KAAK;AAClB,cAAI,CAAC,MAAK,QAAO,GAAI;AACnB,aAAA,MAAA,KAAA,MAAK,OAAM,kBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAA,EAAE,MAAM,GAAG,KAAK,YAAW,CAAE;;AAEzD;;AAGF,YAAI,eAAe;AACnB,gBAAQ,UAAQ;UACd,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;UACb,KAAK,QAAQ;AACX,2BAAe,WAAW,UAAU,IAAI;AACxC;;AAEJ,YAAI,CAAC,cAAc;AACjB,WAAA,MAAA,KAAA,MAAK,OAAM,kBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAA,EAAE,MAAM,GAAG,KAAK,YAAW,CAAE;AACvD;;AAEF,cAAM,eAAc;AACpB,cAAK,SAAS,EAAE,qBAAqB,8BAA6B,CAAE;AACpE,YAAI,oBAAoB;AACtB,gBAAK,YAAY,YAAY;;AAE/B,cAAK,gBAAgB,YAAY;AAEjC,YAAID,SAAQ;AACV,cAAM,YAAY,SAAS,IAAI;AAC/B,cAAM,WAAW,SAAS,YAAY;AACtC,cAAM,WAAW,QAAQ,IAAI;AAC7B,cAAM,UAAU,QAAQ,YAAY;AAEpC,cAAI,cAAc,YAAY,aAAa,SAAS;AAElD,kBAAK,SAAS,EAAE,sBAAsB,KAAI,CAAE;iBACvC;AAEL,kBAAK,SAAS,EAAE,sBAAsB,MAAK,CAAE;;;MAGnD;AAIA,YAAe,kBAAG,SAAC,OAA0C;AAC3D,YAAM,WAAW,MAAM;AACvB,YAAI,aAAa,QAAQ,QAAQ;AAC/B,gBAAM,eAAc;AACpB,gBAAK,qBAAoB;;MAE7B;AAEA,YAAY,eAAG,SAAC,OAA2C;AACzD,YAAI,OAAO;AACT,cAAI,MAAM,gBAAgB;AACxB,kBAAM,eAAc;;;AAIxB,cAAK,qBAAoB;AAEnB,YAAA1D,MAA6B,MAAK,OAAhC,eAAYA,IAAA,cAAE,WAAQA,IAAA;AAC9B,YAAI,cAAc;AAChB,uBAAA,QAAA,aAAA,SAAA,SAAA,SAAW,CAAC,MAAM,IAAI,GAAG,KAAK;eACzB;AACL,uBAAQ,QAAR,aAAA,SAAA,SAAA,SAAW,MAAM,KAAK;;AAGxB,cAAK,SAAS,EAAE,YAAY,KAAI,CAAE;MACpC;AAEA,YAAA,QAAQ,WAAA;AACN,cAAK,aAAY;MACnB;AAEA,YAAQ,WAAG,SAAC,OAAY;AACtB,YACE,OAAO,MAAK,MAAM,kBAAkB,aACpC,MAAK,MAAM,eACX;AACA,cACE,MAAM,WAAW,YACjB,MAAM,WAAW,SAAS,mBAC1B,MAAM,WAAW,SAAS,MAC1B;AACA,kBAAK,QAAQ,KAAK;;mBAEX,OAAO,MAAK,MAAM,kBAAkB,YAAY;AACzD,cAAI,MAAK,MAAM,cAAc,KAAK,GAAG;AACnC,kBAAK,QAAQ,KAAK;;;MAGxB;AAEA,YAAA,iBAAiB,WAAA;;AACf,YAAI,CAAC,MAAK,MAAM,UAAU,CAAC,MAAK,eAAc,GAAI;AAChD,iBAAO;;AAET,eACE,cAAAC,QAAC,cAAA,UACCyB,QAAA,EAAA,uBAAuB,QACvB,KAAK,SAAC,MAAI;AACR,gBAAK,WAAW;QAClB,EAAC,GACG,MAAK,OACL,MAAK,OACT,EAAA,SAAS,MAAK,SACd,aACE1B,MAAA,MAAK,MAAM,wBAAkB,QAAAA,QAAA,SAAAA,MAC7ByD,YAAW,aAAa,oBAE1B,UAAU,MAAK,cACf,gBAAgB,MAAK,4BACrB,UAAU,eAAe,MAAK,eAAc,CAAE,GAC9C,yBACA,iBAAiB,MAAK,qBACtB,cAAc,MAAK,kBACnB,WAAW,MAAK,MAAM,mBACtB,WAAW,MAAK,MAAM,mBACtB,iBAAiB,MAAK,MAAM,WAC5B,oBAAoB,MAAK,cACzB,iBAAiB,MAAK,iBACtB,eACE,KAAA,MAAK,MAAM,kBAAY,QAAA,OAAA,SAAA,KAAIA,YAAW,aAAa,aAAY,CAAA,GAGhE,MAAK,MAAM,QAAQ;MAG1B;AAEA,YAAA,uBAAuB,WAAA;AACf,YAAAzD,MACJ,MAAK,OADC,KAAAA,IAAA,YAAA,aAAU,OAAA,SAAGyD,YAAW,aAAa,aAAU,IAAE,SAAMzD,IAAA;AAE/D,YAAM,iBACJ,MAAK,MAAM,iBAAiB,MAAK,MAAM;AACzC,YAAM,iBAAiB,iBAAiB,UAAU;AAClD,YAAI;AAEJ,YAAI,MAAK,MAAM,cAAc;AAC3B,4BAAkB,wBAAA,OAAwB,eACxC,MAAK,MAAM,WACX;YACE,YAAY;YACZ;UACD,CAAA,GAED,IAAA,EAAA,OAAA,MAAK,MAAM,UACP,eACA,eAAe,MAAK,MAAM,SAAS;YACjC,YAAY;YACZ;WACD,IACD,EAAE;eAEH;AACL,cAAI,MAAK,MAAM,oBAAoB;AACjC,8BAAkB,kBAAkB,OAAA,eAClC,MAAK,MAAM,UACX,EAAE,YAAY,OAAM,CAAE,CACvB;qBACQ,MAAK,MAAM,gBAAgB;AACpC,8BAAkB,kBAAA,OAAkB,eAClC,MAAK,MAAM,UACX,EAAE,YAAY,QAAQ,OAAM,CAAE,CAC/B;qBACQ,MAAK,MAAM,qBAAqB;AACzC,8BAAkB,mBAAA,OAAmB,eACnC,MAAK,MAAM,UACX,EAAE,YAAY,aAAa,OAAM,CAAE,CACpC;qBACQ,MAAK,MAAM,uBAAuB;AAC3C,8BAAkB,qBAAA,OAAqB,eACrC,MAAK,MAAM,UACX;cACE,YAAY;cACZ;YACD,CAAA,CACF;iBACI;AACL,8BAAkB,kBAAA,OAAkB,eAClC,MAAK,MAAM,UACX;cACE,YAAY;cACZ;YACD,CAAA,CACF;;;AAIL,eACE,cAAAC,QAAA,cAAA,QAAA,EACE,MAAK,SACK,aAAA,UACV,WAAU,8BAA6B,GAEtC,eAAe;MAGtB;AAEA,YAAA,kBAAkB,WAAA;;;AAChB,YAAM,YAAY,KAAK,MAAK,MAAM,YAASD,MAAA,CAAA,GACzCA,IAAC,uBAAuB,IAAG,MAAK,MAAM;AAGxC,YAAM,cAAc,MAAK,MAAM,eAAe,cAAAC,QAAO,cAAA,SAAA,EAAA,MAAK,OAAM,CAAA;AAChE,YAAM,iBAAiB,MAAK,MAAM,kBAAkB;AAC9C,YAAA,KACJ,MAAK,OADC,KAAA,GAAA,YAAA,aAAU,OAAA,SAAGwD,YAAW,aAAa,aAAU,IAAE,SAAM,GAAA;AAE/D,YAAM,aACJ,OAAO,MAAK,MAAM,UAAU,WACxB,MAAK,MAAM,QACX,OAAO,MAAK,MAAM,eAAe,WAC/B,MAAK,MAAM,aACX,MAAK,MAAM,eACT,oBAAoB,MAAK,MAAM,WAAW,MAAK,MAAM,SAAS;UAC5D;UACA;SACD,IACD,MAAK,MAAM,kBACT,yBAAwB,KAAA,MAAK,MAAM,mBAAiB,QAAA,OAAA,SAAA,KAAA,CAAA,GAAI;UACtD;UACA;SACD,IACD,eAAe,MAAK,MAAM,UAAU;UAClC;UACA;QACD,CAAA;AAEb,mBAAO,4BAAa,cAAW,KAAA,CAAA,GAC7B,GAAC,cAAc,IAAG,SAAC,OAAyB;AAC1C,gBAAK,QAAQ;WAEf,GAAA,QAAO,YACP,GAAM,SAAE,MAAK,YACb,GAAQ,WAAE,MAAK,cACf,GAAO,UAAE,MAAK,cACd,GAAO,UAAE,MAAK,aACd,GAAS,YAAE,MAAK,gBAChB,GAAA,KAAI,MAAK,MAAM,IACf,GAAA,OAAM,MAAK,MAAM,MACjB,GAAA,OAAM,MAAK,MAAM,MACjB,GAAA,YAAW,MAAK,MAAM,WACtB,GAAA,cAAa,MAAK,MAAM,iBACxB,GAAA,WAAU,MAAK,MAAM,UACrB,GAAA,eAAc,MAAK,MAAM,cACzB,GAAS,YAAE,KAAK,YAAY,MAAM,WAAW,SAAS,GACtD,GAAA,QAAO,MAAK,MAAM,OAClB,GAAA,WAAU,MAAK,MAAM,UACrB,GAAA,WAAU,MAAK,MAAM,UACrB,GAAA,WAAU,MAAK,MAAM,UACrB,GAAA,kBAAA,IAAoB,MAAK,MAAM,iBAC/B,GAAA,cAAA,IAAgB,MAAK,MAAM,aAC3B,GAAA,iBAAA,IAAmB,MAAK,MAAM,gBAC9B,GAAA,eAAA,IAAiB,MAAK,MAAM;MAEhC;AAEA,YAAA,oBAAoB,WAAA;AACZ,YAAAzD,MAUF,MAAK,OATP,cAAWA,IAAA,aACX,WAAQA,IAAA,UACR,WAAQA,IAAA,UACR,YAASA,IAAA,WACT,UAAOA,IAAA,SACP,mBAAgBA,IAAA,kBAChB,KAAAA,IAAA,sBAAA,uBAAoB,OAAA,SAAG,KAAE,IACzB,KAAwBA,IAAA,gBAAxB,iBAAiB,OAAA,SAAA,UAAO,IACxB,gBAAaA,IAAA;AAEf,YACE,gBACC,YAAY,QACX,aAAa,QACb,WAAW,SACX,kBAAa,QAAb,kBAAA,SAAA,SAAA,cAAe,UACjB;AACA,iBACE,cAAAC,QACE,cAAA,UAAA,EAAA,MAAK,UACL,WAAW,KACT,gCACA,sBACA,EAAE,0CAA0C,SAAQ,CAAE,GAExD,UAAkB,cACN,gBACZ,SAAS,MAAK,cACd,OAAO,kBACP,UAAU,GAAE,CAAA;eAGX;AACL,iBAAO;;MAEX;AAjmCE,YAAK,QAAQ,MAAK,iBAAgB;AAClC,YAAK,sBAAsB;;;AApD7B,WAAA,eAAWwD,aAAY,gBAAA;MAAvB,KAAA,WAAA;AACE,eAAO;UACL,cAAc;UACd,YAAY;UACZ,oBAAoB;UACpB,UAAU;UACV,4BAA4B;UAC5B,cAAc;UACd,oBAAoB;UACpB,aAAa;UACb,UAAU;UACV,YAAY;UACZ,4BAA4B;UAC5B,qBAAqB;UACrB,gBAAgB;UAChB,eAAe;UACf,oBAAoB;UACpB,qBAAqB;UACrB,yBAAyB;UACzB,8BAA8B;UAC9B,+BAA+B;UAC/B,gBAAgB;UAChB,uBAAuB;UACvB,gBAAgB;UAChB,eAAe;UACf,WAAW;UACX,eAAe;UACf,aAAa;UACb,wBAAwB;UACxB,0BAA0B;UAC1B,oBAAoB;UACpB,sBAAsB;UACtB,uBAAuB;UACvB,yBAAyB;UACzB,mBAAmB;UACnB,qBAAqB;UACrB,gBAAgB;UAChB,eAAe;UACf,gBAAgB;UAChB,oBAAoB;UACpB,iBAAiB;UACjB,kBAAkB;UAClB,iBAAiB;UACjB,kBAAkB;UAClB,2BAA2B;UAC3B,iBAAiB;;;;;IAEpB,CAAA;AAQD,IAAAA,YAAA,UAAA,oBAAA,WAAA;AACE,aAAO,iBAAiB,UAAU,KAAK,UAAU,IAAI;AACrD,eAAS,iBACP,oBACA,KAAK,gCAAgC;;AAIzC,IAAAA,YAAA,UAAA,qBAAA,SACE,WACA,WAA0B;;AAE1B,UACE,UAAU,UACV,uBAAuB,UAAU,UAAU,KAAK,MAAM,QAAQ,GAC9D;AACA,aAAK,gBAAgB,KAAK,MAAM,QAAQ;;AAE1C,UACE,KAAK,MAAM,oBAAoB,UAC/B,UAAU,gBAAgB,KAAK,MAAM,aACrC;AACA,aAAK,SAAS,EAAE,iBAAiB,EAAC,CAAE;;AAEtC,UAAI,UAAU,mBAAmB,KAAK,MAAM,gBAAgB;AAC1D,aAAK,SAAS;UACZ,gBAAgB,oBAAoB,KAAK,MAAM,cAAc;QAC9D,CAAA;;AAEH,UACE,CAAC,UAAU,WACX,CAAC5C,SAAQ,UAAU,UAAU,KAAK,MAAM,QAAQ,GAChD;AACA,aAAK,SAAS,EAAE,YAAY,KAAI,CAAE;;AAGpC,UAAI,UAAU,SAAS,KAAK,MAAM,MAAM;AACtC,YAAI,UAAU,SAAS,SAAS,KAAK,MAAM,SAAS,MAAM;AACxD,WAAA,MAAAb,MAAA,KAAK,OAAM,oBAAc,QAAA,OAAA,SAAA,SAAA,GAAA,KAAAA,GAAA;;AAG3B,YAAI,UAAU,SAAS,QAAQ,KAAK,MAAM,SAAS,OAAO;AACxD,WAAA,MAAA,KAAA,KAAK,OAAM,qBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;;;;AAKhC,IAAAyD,YAAA,UAAA,uBAAA,WAAA;AACE,WAAK,yBAAwB;AAC7B,aAAO,oBAAoB,UAAU,KAAK,UAAU,IAAI;AACxD,eAAS,oBACP,oBACA,KAAK,gCAAgC;;AA2iCzC,IAAAA,YAAA,UAAA,uBAAA,WAAA;AACQ,UAAAzD,MAMF,KAAK,OALP,WAAQA,IAAA,UACR,OAAIA,IAAA,MACJ,wBAAqBA,IAAA,uBACrB,wBAAqBA,IAAA,uBACrB,4BAAyBA,IAAA;AAEnB,UAAA,OAAS,KAAK,MAAK;AAE3B,UAAI,uBAAuB;AACzB,gBAAQ,KACN,oFAAoF;;AAIxF,aACE,cAAAC,QAAA;QAAA;QAAA,EACE,WAAW,oCAAA,OACT,WAAW,0CAA0C,EAAE,EACvD;QAED,YACC,cAAAA,QAAA,cAAC,cAAYyB,QAAA,EACX,MACA,WAAW,KACT,uBACA,CAAC,yBAAyB,uBAC1B,QAAQ,wCAAwC,EACjD,GACI,4BACD;UACE,SAAS,KAAK;QACf,IACD,IAAI,CAAC;QAGZ,KAAK,MAAM,2BAA2B,KAAK,qBAAoB;QAC/D,KAAK,gBAAe;QACpB,KAAK,kBAAiB;MAAE;;AAK/B,IAAA+B,YAAA,UAAA,SAAA,WAAA;AACE,UAAM,WAAW,KAAK,eAAc;AAEpC,UAAI,KAAK,MAAM;AAAQ,eAAO;AAE9B,UAAI,KAAK,MAAM,YAAY;AACzB,YAAI,kBAAkB,KAAK,MAAM,OAC/B,cAAAxD,QAAC;UAAA;UAAO,EAAC,eAAe,KAAK,MAAM,cAAa;UAC9C,cAAAA,QACE,cAAA,OAAA,EAAA,WAAU,4BACV,UAAU,IACV,WAAW,KAAK,gBAAe,GAE9B,QAAQ;QACL,IAEN;AAEJ,YAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,UAAU;AAC1C,4BACE,cAAAA,QAAC,cAAA,QAAM,QAAA,EAAC,UAAU,KAAK,MAAM,SAAQ,GAAM,KAAK,KAAK,GAClD,eAAe;;AAKtB,eACE,cAAAA,QAAA;UAAA;UAAA;UACG,KAAK,qBAAoB;UACzB;QAAe;;AAKtB,aACE,cAAAA,QAAA,cAACuD,mBAAe9B,QAAA,CAAA,GACV,KAAK,OACT,EAAA,WAAW,KAAK,MAAM,iBACtB,YAAY,CAAC,KAAK,eAAc,GAChC,iBAAiB,KAAK,qBAAoB,GAC1C,iBAAiB,UACjB,iBAAiB,KAAK,iBACtB,WAAW,KAAK,MAAM,gBAAe,CACrC,CAAA;;AAGR,WAAC+B;EAAD,EApvCwC,uBAAS;;AAsvCjD,IAAM,6BAA6B;AACnC,IAAM,gCAAgC;", "names": ["React", "getComputedStyle", "min", "max", "candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "_element$getRootNode", "call", "ownerDocument", "focusableCandidateSelector", "candidateSelectors", "concat", "join", "index", "offset", "ReactDOM", "platform", "min", "max", "offset", "platform", "placements", "sides", "side", "placement", "overflow", "platform", "getComputedStyle", "max", "min", "cleanup", "offset", "flip", "arrow", "computePosition", "React", "import_react", "index", "platform", "computePosition", "data", "arrow", "offset", "flip", "arrow", "set", "index", "ARROW_LEFT", "ARROW_RIGHT", "ARROW_UP", "ARROW_DOWN", "Composite", "index", "CompositeItem", "SafeReact", "React", "FloatingArrow", "arrow", "shift", "isRTL", "getComputedStyle", "FocusGuard", "Visually<PERSON><PERSON><PERSON><PERSON><PERSON>iss", "FloatingOverlay", "open", "useFloating", "node", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "__extends", "TypeError", "String", "__", "constructor", "create", "__assign", "assign", "t", "s", "i", "n", "arguments", "length", "apply", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "ar", "slice", "concat", "_a", "React", "KeyType", "<PERSON><PERSON><PERSON><PERSON>", "isValidDate", "isSameYear", "dfIsSameYear", "isSameMonth", "dfIsSameMonth", "isSameQuarter", "dfIsSameQuarter", "isSameDay", "dfIsSameDay", "isEqual", "dfIsEqual", "min", "max", "index", "startOfDay", "InputTime", "Day", "WeekNumber", "Week", "startOfWeek", "endOfWeek", "offset", "__assign", "Month", "preSelection", "selected", "newDate", "eventKey", "month", "newCalculatedDate", "newCalculatedMonth", "_b", "MonthDropdownOptions", "MonthDropdown", "MonthYearDropdownOptions", "MonthYearDropdown", "Time", "format", "Year", "y", "YearDropdownOptions", "YearDropdown", "Calendar", "startOfMonth", "endOfMonth", "Portal", "ReactDOM", "TabLoop", "Component", "useFloating", "flip", "arrow", "PopperComponent", "DatePicker", "inline", "newSelection"]}