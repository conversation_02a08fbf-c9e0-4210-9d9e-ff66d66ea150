import { render, screen } from '@testing-library/react';
import PolarAreaChart from '../../../src/components/Charts/PolarAreaChart';
import { PolarArea } from 'react-chartjs-2';

jest.mock('react-chartjs-2', () => ({
    PolarArea: jest.fn(() => <div data-testid="polar-area-chart" />)
}));

describe('PolarAreaChart Component', () => {
    const mockData = {
        labels: ['Red', 'Blue', 'Yellow'],
        datasets: [{
            label: 'My First Dataset',
            data: [11, 16, 7],
            backgroundColor: ['rgb(255, 99, 132)', 'rgb(54, 162, 235)', 'rgb(255, 205, 86)'],
        }],
    };

    const mockOptions = {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: 'Polar Area Chart',
            },
        },
    };

    it('should render without crashing', () => {
        render(<PolarAreaChart data={mockData} options={mockOptions} />);
        expect(screen.getByTestId('polar-area-chart')).toBeInTheDocument();
    });

    it('should pass the correct data and options to PolarArea', () => {
        render(<PolarAreaChart data={mockData} options={mockOptions} />);
        expect(PolarArea).toHaveBeenCalledWith(
            expect.objectContaining({
                data: mockData,
                options: mockOptions,
            }),
            {}
        );
    });
});
