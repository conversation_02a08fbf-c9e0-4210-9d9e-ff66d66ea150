import { Button, Grid, Modal, Typography } from "@mui/material";
import axiosInstance from "../../../../axios";
import ModalContainer from "../../../../components/ModalContainer";

const DeleteRoleModal = ({ deleteRole, setDeleteRole, setDeleting, onSuccess }) => {
    const handleClose = () => {
        setDeleteRole();
    };

    const onDelete = () => {
        handleClose();
        setDeleting(deleteRole.role_id);
        axiosInstance
            .delete("/roles/" + deleteRole._id, { meta: { showSnackbar: true } })
            .then(() => onSuccess && onSuccess())
            .catch(console.error)
            .finally(() => setDeleting());
    };

    return (
        <Modal open={!!deleteRole} onClose={handleClose}>
            <ModalContainer title={"Delete Role"} onClose={handleClose}>
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: "auto" }}>
                    <Grid>
                        <Typography>Are you sure you want to delete role &quot;{deleteRole?.role_name}&quot;?</Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid>
                            <Button variant="contained" onClick={handleClose} className="btn-cancel">
                                Cancel
                            </Button>
                        </Grid>
                        <Grid>
                            <Button variant="contained" color="error" onClick={onDelete} sx={{ textTransform: "none", padding: "10px 24px" }}>
                                Delete
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default DeleteRoleModal;
