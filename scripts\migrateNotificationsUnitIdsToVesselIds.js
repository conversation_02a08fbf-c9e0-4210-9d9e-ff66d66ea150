require("dotenv").config();
const mongoose = require("mongoose");
const NotificationAlert = require("../models/NotificationAlert");
const NotificationSummary = require("../models/NotificationSummary");
const Vessel = require("../models/Vessel");

const dryRun = process.argv.includes("--dry-run");

// Progress bar utilities
function createProgressBar(current, total, barLength = 40) {
    const percentage = Math.round((current / total) * 100);
    const filledLength = Math.round((current / total) * barLength);
    const bar = "█".repeat(filledLength) + "░".repeat(barLength - filledLength);
    return `[${bar}] ${percentage}% (${current}/${total})`;
}

function clearLine() {
    process.stdout.write("\r\x1b[K");
}

function writeProgress(message) {
    clearLine();
    process.stdout.write(message);
}

function writeLine(message) {
    clearLine();
    process.stdout.write(message + "\n");
}

function convertUnitIdsToVesselIds(unitIds, unitToVesselMap) {
    if (!Array.isArray(unitIds)) return [];

    const vesselIds = [];
    const unmappedUnits = [];

    unitIds.forEach((unitId) => {
        if (typeof unitId === "string") {
            if (unitId === "all") {
                // Keep 'all' as is for special case
                vesselIds.push("all");
            } else {
                const vesselId = unitToVesselMap.get(unitId);
                if (vesselId) {
                    vesselIds.push(new mongoose.Types.ObjectId(vesselId));
                } else {
                    unmappedUnits.push(unitId);
                }
            }
        }
    });

    if (unmappedUnits.length > 0) {
        writeLine(`⚠️  Could not map unit_ids to vessels: ${unmappedUnits.join(", ")}`);
    }

    return vesselIds;
}

async function migrateNotificationAlerts(unitToVesselMap) {
    writeLine("\n🔔 Migrating notification alerts...");
    const alerts = await NotificationAlert.find({});
    let updated = 0;

    for (let i = 0; i < alerts.length; i++) {
        const alert = alerts[i];
        const progress = createProgressBar(i + 1, alerts.length);
        writeProgress(`🔔 Processing notification alerts... ${progress}`);

        try {
            // Convert unit_ids to vessel_ids
            const vesselIds = convertUnitIdsToVesselIds(alert.unit_id, unitToVesselMap);

            // Only update if we have vessel_ids to add
            if (vesselIds.length > 0) {
                if (!dryRun) {
                    await NotificationAlert.updateOne({ _id: alert._id }, { $set: { vessel_ids: vesselIds } });
                }
                updated++;
            }
        } catch (error) {
            writeLine(`❌ Error migrating notification alert ${alert._id}: ${error.message}`);
        }
    }

    writeLine(`✅ Notification alerts migration completed: ${updated} updated`);
    return updated;
}

async function migrateNotificationSummaries(unitToVesselMap) {
    writeLine("\n📊 Migrating notification summaries...");
    const summaries = await NotificationSummary.find({});
    let updated = 0;

    for (let i = 0; i < summaries.length; i++) {
        const summary = summaries[i];
        const progress = createProgressBar(i + 1, summaries.length);
        writeProgress(`📊 Processing notification summaries... ${progress}`);

        try {
            // Convert unit_ids to vessel_ids
            const vesselIds = convertUnitIdsToVesselIds(summary.unit_id, unitToVesselMap);

            // Only update if we have vessel_ids to add
            if (vesselIds.length > 0) {
                if (!dryRun) {
                    await NotificationSummary.updateOne({ _id: summary._id }, { $set: { vessel_ids: vesselIds } });
                }
                updated++;
            }
        } catch (error) {
            writeLine(`❌ Error migrating notification summary ${summary._id}: ${error.message}`);
        }
    }

    writeLine(`✅ Notification summaries migration completed: ${updated} updated`);
    return updated;
}

async function migrateNotificationsUnitIdsToVesselIds() {
    try {
        writeLine("🚀 Starting migration from unit_ids to vessel_ids in notifications...");
        if (dryRun) {
            writeLine("🔍 DRY RUN MODE - No changes will be made");
        }

        // Create mapping from unit_id to vessel ObjectId
        writeProgress("📋 Creating unit_id to vessel_id mapping...");
        const vessels = await Vessel.find({ unit_id: { $ne: null, $exists: true } }, { _id: 1, unit_id: 1, name: 1 });

        const unitToVesselMap = new Map();
        vessels.forEach((vessel) => {
            unitToVesselMap.set(vessel.unit_id, vessel._id);
        });

        writeLine(`✅ Created mapping for ${unitToVesselMap.size} vessels`);

        // Run migrations for each collection
        const alertsUpdated = await migrateNotificationAlerts(unitToVesselMap);
        const summariesUpdated = await migrateNotificationSummaries(unitToVesselMap);

        const totalUpdated = alertsUpdated + summariesUpdated;

        // Final summary
        writeLine("\n📊 Migration Summary:");
        writeLine("=".repeat(50));
        writeLine(`🔔 Notification Alerts: ${alertsUpdated} updated`);
        writeLine(`📊 Notification Summaries: ${summariesUpdated} updated`);
        writeLine(`📈 Total Records: ${totalUpdated} updated`);
        writeLine("=".repeat(50));

        if (dryRun) {
            writeLine("🔍 DRY RUN COMPLETED - No actual changes were made");
        } else {
            writeLine("✅ MIGRATION COMPLETED SUCCESSFULLY!");
        }
    } catch (error) {
        writeLine(`❌ Migration failed: ${error.message}`);
        process.exit(1);
    } finally {
        process.exit(0);
    }
}

migrateNotificationsUnitIdsToVesselIds();
