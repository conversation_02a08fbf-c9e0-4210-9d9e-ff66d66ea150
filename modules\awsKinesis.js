const AWS = require("aws-sdk");
const { KinesisVideoClient, GetDataEndpointCommand } = require("@aws-sdk/client-kinesis-video");
const { KinesisVideoArchivedMediaClient, GetImagesCommand, ListFragmentsCommand } = require("@aws-sdk/client-kinesis-video-archived-media");
const { <PERSON>uffer } = require("node:buffer");
const Vessel = require("../models/Vessel");
const { getCloudfrontSignedUrl } = require("./awsS3");

const credentials = {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
};

// const preprocessStreamTags = (tags) => {
//     if (!tags) {
//         return {
//             Name: "Unregistered",
//             Thumbnail: null,
//         };
//     }
//     if (!tags.Name) {
//         tags.Name = tags.name || "Unregistered";
//     }
//     if (!tags.Thumbnail) {
//         tags.Thumbnail = tags.thumbnail || null;
//     }
//     return tags;
// };

const handleClientLimitExceededException = async (err, retryFunction, ...args) => {
    const retryAfter = parseInt(err?.retryDelay, 10) || 2;
    console.warn(
        "[awsKinesis.handleClientLimitExceededException] Account level limit exceeded on number of requests for this API, waiting",
        retryAfter,
        "seconds and retrying",
    );
    await new Promise((resolve) => setTimeout(resolve, retryAfter * 1000));
    console.log("[awsKinesis.handleClientLimitExceededException] Retrying...");
    return await retryFunction.apply(this, args);
};

// const cachedTags = {};
// const cachedTagsResetInterval = 3600000;
// const cachedTagsResetInterval = 10000;

async function getStreamTags({ streamName }) {
    // try {
    //     const kinesisVideo = new AWS.KinesisVideo({
    //         region,
    //         credentials,
    //     });

    //     let tagsResponse;

    //     const cachedStreamTags = cachedTags[streamName];
    //     if (cachedStreamTags) {
    //         tagsResponse = cachedStreamTags.data;
    //         // reset cache every hour
    //         const isExpired = Date.now() - cachedStreamTags.timestamp > cachedTagsResetInterval;
    //         if (isExpired) {
    //             delete cachedTags[streamName];
    //         }
    //     } else {
    //         console.log("no cache for getStreamTags for stream", streamName, ", fetching from aws");
    //         tagsResponse = await kinesisVideo.listTagsForStream({ StreamName: streamName }).promise();

    //         cachedTags[streamName] = {
    //             data: tagsResponse,
    //             timestamp: Date.now(),
    //         };
    //     }

    //     return preprocessStreamTags(tagsResponse?.Tags);
    // } catch (err) {
    //     if (err.code === "ClientLimitExceededException") {
    //         return await handleClientLimitExceededException(err, getStreamTags, { streamName });
    //     }
    //     throw err;
    // }

    // new code for backwards compatibility
    const vessel = await Vessel.findOne({ unit_id: streamName });

    const Name = vessel?.name || "Unregistered";
    const Thumbnail = vessel?.thumbnail_compressed_s3_key || null;
    const ThumbnailUrl = Thumbnail ? getCloudfrontSignedUrl({ fileName: Thumbnail }) : null;

    const tags = {
        Name,
        Thumbnail: ThumbnailUrl,
    };
    return tags;
}

const cachedStreamsList = {};
const cachedStreamsListResetInterval = 3600000;
// const cachedStreamsListResetInterval = 10000;

async function getStreamsList({ region }) {
    try {
        const kinesisVideo = new AWS.KinesisVideo({
            region,
            credentials,
        });

        let streamsList;

        if (cachedStreamsList[region]) {
            streamsList = cachedStreamsList[region].data;
            // reset cache every hour
            const isExpired = Date.now() - cachedStreamsList[region].timestamp > cachedStreamsListResetInterval;
            if (isExpired) {
                delete cachedStreamsList[region];
            }
        } else {
            console.log("no cache for getStreamsList for region", region, ", fetching from aws");

            let allStreams = [];
            let nextToken;

            do {
                const listStreamsResponse = await kinesisVideo.listStreams({ NextToken: nextToken }).promise();
                const streams = listStreamsResponse.StreamInfoList;
                allStreams = allStreams.concat(streams);
                nextToken = listStreamsResponse.NextToken;
            } while (nextToken);

            streamsList = allStreams;

            cachedStreamsList[region] = {
                data: allStreams,
                timestamp: Date.now(),
            };
        }

        return streamsList;
    } catch (err) {
        if (err.code === "ClientLimitExceededException") {
            return await handleClientLimitExceededException(err, getStreamsList, { region });
        }
        throw err;
    }
}

const cachedStreamLiveStatus = {};
const cachedStreamLiveStatusResetInterval = 120000;
// const cachedStreamLiveStatusResetInterval = 10000;

async function getStreamLiveStatus({ streamName, region }) {
    let isLive = false;

    try {
        if (cachedStreamLiveStatus[streamName]) {
            isLive = cachedStreamLiveStatus[streamName].data;
            // reset cache every 2 minutes
            const isExpired = Date.now() - cachedStreamLiveStatus[streamName].timestamp > cachedStreamLiveStatusResetInterval;
            if (isExpired) {
                delete cachedStreamLiveStatus[streamName];
            }
            return isLive;
        }

        console.log("getStreamLiveStatus for stream", streamName, ", region", region, ", fetching from aws");

        const kinesisVideo = new AWS.KinesisVideo({
            region,
            credentials,
        });

        const getDataEndpointParams = {
            APIName: "GET_DASH_STREAMING_SESSION_URL",
            StreamName: streamName,
        };

        const dataEndpoint = await kinesisVideo.getDataEndpoint(getDataEndpointParams).promise();

        const kinesisVideoArchivedMedia = new AWS.KinesisVideoArchivedMedia({
            endpoint: dataEndpoint.DataEndpoint,
            region,
            credentials,
        });

        const dashUrlResponse = await kinesisVideoArchivedMedia
            .getDASHStreamingSessionURL({
                StreamName: streamName,
                PlaybackMode: "LIVE",
                Expires: 43200,
                MaxManifestFragmentResults: 5000,
                DASHFragmentSelector: {},
            })
            .promise();

        if (dashUrlResponse.DASHStreamingSessionURL) {
            isLive = true;
        } else {
            isLive = false;
        }

        cachedStreamLiveStatus[streamName] = {
            data: isLive,
            timestamp: Date.now(),
        };

        return isLive;
    } catch (error) {
        if (error.code === "ResourceNotFoundException") {
            isLive = false;

            cachedStreamLiveStatus[streamName] = {
                data: isLive,
                timestamp: Date.now(),
            };

            return isLive;
        }

        // if (error.code === "ClientLimitExceededException") {
        //     console.warn("[getStreamLiveStatus] Account level limit exceeded on number of requests for this API, waiting", error.retryDelay, "seconds and retrying");
        //     await new Promise((resolve) => setTimeout(resolve, error.retryDelay * 1000));
        //     console.log("[getStreamLiveStatus] Retrying...");
        //     return await getStreamLiveStatus({ streamName, region });
        // }

        if (error.code === "ClientLimitExceededException") {
            return await handleClientLimitExceededException(error, getStreamLiveStatus, { streamName, region });
        }

        console.error("[getStreamLiveStatus] Error", error);
        throw error;
    }
}

async function listStreams({ region }) {
    try {
        const allStreams = await getStreamsList({ region });

        const streamsWithTags = await Promise.all(
            allStreams.map(async (stream) => {
                const streamName = stream.StreamName;
                const IsLive = await getStreamLiveStatus({ streamName, region });
                const Tags = await getStreamTags({ streamName });
                // const tagsResponse = {
                //     Tags: {
                //         Name: "Unregistered",
                //         Thumbnail: null,
                //     },
                // };
                return {
                    ...stream,
                    Tags,
                    IsLive,
                    Region: region,
                };
            }),
        );

        return streamsWithTags;
    } catch (err) {
        // if (err.code === "ClientLimitExceededException") {
        //     const retryAfter = parseInt(err.retryDelay, 10);
        //     console.warn("[awsKinesis.listStreams] Account level limit exceeded on number of requests for this API, waiting", retryAfter, "seconds and retrying");
        //     await new Promise((resolve) => setTimeout(resolve, retryAfter * 1000));
        //     console.log("[awsKinesis.listStreams] Retrying...");
        //     return await listStreams({ region });
        // }

        if (err.code !== "ResourceNotFoundException") {
            console.error("[awsKinesis.listStreams] Unexpected error", err);
        }

        throw err;
    }
}

// async function listStreamsInfo({ region }) {
//     const kinesisVideo = new AWS.KinesisVideo({
//         region,
//         credentials
//     });

//     let allStreams = [];
//     let nextToken;
//     try {
//         do {
//             const listStreamsResponse = await kinesisVideo.listStreams({ NextToken: nextToken }).promise();
//             const streams = listStreamsResponse.StreamInfoList;
//             allStreams = allStreams.concat(streams);
//             nextToken = listStreamsResponse.NextToken;
//         } while (nextToken);

//         const streamsWithTags = await Promise.all(allStreams.map(async (stream) => {
//             const streamName = stream.StreamName;
//             const tagsResponse = await kinesisVideo.listTagsForStream({ StreamName: streamName }).promise();
//             return {
//                 unit_id: streamName,
//                 name: tagsResponse.Tags.Name || tagsResponse.Tags.name || null,
//                 thumbnail: tagsResponse.Tags.Thumbnail || tagsResponse.Tags.thumbnail || null,
//                 is_live:
//             };
//         }));

//         return streamsWithTags;
//     } catch (error) {
//         console.error(error);
//         throw error;
//     }
// }

const cachedDataEndpoints = {};

const getDataEndPoint = async ({ APIName, StreamName, region }) => {
    try {
        // setTimeout(() => {
        //     console.log('adding invalid data endpoints for testing');
        //     cachedDataEndpoints[`${APIName}-${StreamName}`] = {
        //         DataEndpoint: 'https://b-8a2e830eeeeee.kinesisvideo.ap-southeast-1.amazonaws.com',
        //     };
        // }, 5000);

        if (cachedDataEndpoints[`${APIName}-${StreamName}`]) {
            return cachedDataEndpoints[`${APIName}-${StreamName}`];
        }

        const kinesisVideo = new AWS.KinesisVideo({
            region,
            credentials,
        });
        const res = await kinesisVideo.getDataEndpoint({ APIName, StreamName }).promise();
        cachedDataEndpoints[`${APIName}-${StreamName}`] = res;

        return res;
    } catch (error) {
        if (error.code === "ClientLimitExceededException" || error.name === "ClientLimitExceededException") {
            return await handleClientLimitExceededException(error, getDataEndPoint, { APIName, StreamName, region });
        }
        throw error;
    }
};

const clearCachedDataEndpoints = ({ APIName, StreamName }) => {
    delete cachedDataEndpoints[`${APIName}-${StreamName}`];
};

async function getDashStreamingSessionURL_V2({ streamName, region, streamMode = "LIVE", startTimestamp, totalDuration }) {
    let kinesisVideoArchivedMedia;
    try {
        const dets = Date.now();
        const dataEndpoint = await getDataEndPoint({ APIName: "GET_DASH_STREAMING_SESSION_URL", StreamName: streamName, region });
        console.log("[dashStreamingSessionURL] getDataEndPoint time taken", Date.now() - dets, "ms");

        kinesisVideoArchivedMedia = new AWS.KinesisVideoArchivedMedia({
            endpoint: dataEndpoint.DataEndpoint,
            region,
            credentials,
        });

        const getDashStreamingUrl = async ({ streamName, streamMode, startTimestamp }) => {
            try {
                return await kinesisVideoArchivedMedia
                    .getDASHStreamingSessionURL({
                        StreamName: streamName,
                        PlaybackMode: streamMode,
                        Expires: 43200,
                        MaxManifestFragmentResults: 5000,
                        DASHFragmentSelector:
                            streamMode === "ON_DEMAND"
                                ? {
                                      FragmentSelectorType: "PRODUCER_TIMESTAMP",
                                      TimestampRange: {
                                          StartTimestamp: new Date(startTimestamp),
                                          EndTimestamp: new Date(new Date(startTimestamp).getTime() + 24 * 60 * 60 * 1000),
                                      },
                                  }
                                : {},
                    })
                    .promise();
            } catch (error) {
                console.log("Kinesis: getDashStreamingUrl", error);
                if (error.code === "ClientLimitExceededException" || error.name === "ClientLimitExceededException") {
                    return await handleClientLimitExceededException(error, getDashStreamingUrl, { streamName, streamMode, startTimestamp });
                }
                throw error;
            }
        };

        const dts = Date.now();
        const dashUrlResponse = await getDashStreamingUrl({ streamName, streamMode, startTimestamp });
        console.log("[dashStreamingSessionURL] getDashStreamingUrl time taken", Date.now() - dts, "ms");

        return {
            url: dashUrlResponse.DASHStreamingSessionURL,
        };
    } catch (error) {
        console.log("Kinesis: getDashStreamingSessionURL_V2", error.code, error.name, error);

        if (error.code === "ResourceNotFoundException" && streamMode === "LIVE") {
            throw new Error("No streams found in the specified timestamp range.");
        }

        if (error.code === "UnknownEndpoint") {
            clearCachedDataEndpoints({ APIName: "GET_DASH_STREAMING_SESSION_URL", StreamName: streamName });
            return await getDashStreamingSessionURL_V2({ streamName, region, streamMode, startTimestamp, totalDuration });
        }

        if (streamMode === "ON_DEMAND" && (error.code === "ResourceNotFoundException" || error.code === "InvalidArgumentException")) {
            try {
                // const startFragment = new Date(Date.now() - minutes * 60 * 1000);
                const startFragment = new Date(startTimestamp).getTime();
                const latestFragment = await getLatestFragment({
                    streamName,
                    region,
                    startTime: startFragment, // Adjust start time
                    endTime: new Date(startFragment + totalDuration * 60 * 1000).getTime(), // Current time
                });

                if (!latestFragment) {
                    throw new Error("No streams found in the specified timestamp range.");
                }

                // const dataEndpoint = await handleRateLimit(() =>
                //     kinesisVideo
                //         .getDataEndpoint({
                //             APIName: "GET_DASH_STREAMING_SESSION_URL",
                //             StreamName: streamName,
                //         })
                //         .promise(),
                // );

                // const kinesisVideoArchivedMedia = new AWS.KinesisVideoArchivedMedia({
                //     endpoint: dataEndpoint.DataEndpoint,
                //     region,
                //     credentials,
                // });

                const startTime = new Date(latestFragment.ServerTimestamp);
                const timeAgo = 24 * 60 * 60 * 1000; // 24 hours
                const endTime = new Date(startTime.getTime() + timeAgo);
                const requestedDate = new Date(startTimestamp);
                const diffMilliseconds = startTime - requestedDate;
                const diffMinutes = Math.floor(diffMilliseconds / (1000 * 60));

                // const data = {
                //     requestedDate: new Date(Date.now() - minutes * 60 * 1000),
                //     availableVideo: latestFragment.ServerTimestamp,
                //     statTime: startTime,
                //     requestMinutes: minutes,
                //     availableMinutes: adjustedMinutes,
                //     diffMinutes: diffMinutes,
                // };
                // const dashUrlResponse = await handleRateLimit(() =>
                //     kinesisVideoArchivedMedia
                //         .getDASHStreamingSessionURL({
                //             StreamName: streamName,
                //             PlaybackMode: "ON_DEMAND",
                //             Expires: 43200,
                //             MaxManifestFragmentResults: 5000,
                //             DASHFragmentSelector: {
                //                 FragmentSelectorType: "SERVER_TIMESTAMP",
                //                 TimestampRange: {
                //                     StartTimestamp: startTime,
                //                     EndTimestamp: endTime,
                //                 },
                //             },
                //         })
                //         .promise(),
                // );
                const getDashStreamingSessionUrl = async ({ streamName, startTime, endTime }) => {
                    try {
                        return await kinesisVideoArchivedMedia
                            .getDASHStreamingSessionURL({
                                StreamName: streamName,
                                PlaybackMode: "ON_DEMAND",
                                Expires: 43200,
                                MaxManifestFragmentResults: 5000,
                                DASHFragmentSelector: {
                                    FragmentSelectorType: "PRODUCER_TIMESTAMP",
                                    TimestampRange: {
                                        StartTimestamp: startTime,
                                        EndTimestamp: endTime,
                                    },
                                },
                            })
                            .promise();
                    } catch (error) {
                        console.log("Kinesis: getDashStreamingSessionUrl", error);
                        if (error.code === "ClientLimitExceededException" || error.name === "ClientLimitExceededException") {
                            return await handleClientLimitExceededException(error, getDashStreamingSessionUrl, { streamName, startTime, endTime });
                        }
                        throw error;
                    }
                };
                const dashUrlResponse = await getDashStreamingSessionUrl({ streamName, startTime, endTime });

                // console.log("DASH URL Response (from latest fragment):", dashUrlResponse);
                return {
                    url: dashUrlResponse.DASHStreamingSessionURL,
                    minutes: diffMinutes,
                };
            } catch (fragmentError) {
                // console.log("Before Fragment 2 ", fragmentError.name, fragmentError.code, fragmentError);
                // if (fragmentError.name === "ClientLimitExceededException") {
                //     throw new Error("Rate limit exceeded. Please try again shortly.");
                // }
                console.error("Kinesis Error fetching the latest fragment:", fragmentError);
                throw fragmentError;
            }
        } else {
            console.error("Kinesis Unexpected error in awsKinesis.getDashStreamingSessionURL", error.code, error.name, error);
            // throw new Error("The video stream is currently unavailable. Please try again later.");
            throw error;
        }
    }
}

async function getDashStreamingSessionURL({ streamName, region, streamMode = "LIVE", minutes = 60 }) {
    const kinesisVideo = new AWS.KinesisVideo({
        region,
        credentials,
    });

    try {
        const getDataEndpointParams = {
            APIName: "GET_DASH_STREAMING_SESSION_URL",
            StreamName: streamName,
        };

        const dataEndpoint = await kinesisVideo.getDataEndpoint(getDataEndpointParams).promise();

        const kinesisVideoArchivedMedia = new AWS.KinesisVideoArchivedMedia({
            endpoint: dataEndpoint.DataEndpoint,
            region,
            credentials,
        });

        const dashUrlResponse = await kinesisVideoArchivedMedia
            .getDASHStreamingSessionURL({
                StreamName: streamName,
                PlaybackMode: streamMode,
                Expires: 43200,
                MaxManifestFragmentResults: 5000,
                DASHFragmentSelector:
                    streamMode === "ON_DEMAND"
                        ? {
                              FragmentSelectorType: "SERVER_TIMESTAMP",
                              TimestampRange: {
                                  StartTimestamp: new Date(Date.now() - minutes * 60 * 1000), // minutes ago
                                  EndTimestamp: minutes <= 1440 ? new Date() : new Date(Date.now() - minutes * 60 * 1000 + 60 * 60 * 1000),
                              },
                          }
                        : {},
            })
            .promise();
        return dashUrlResponse.DASHStreamingSessionURL;
    } catch (error) {
        if (error.code === "ResourceNotFoundException") {
            console.warn("Stream not found. Attempting to fetch the latest fragment...");
            try {
                const latestFragment = await getLatestFragment({
                    streamName,
                    region,
                    startTime: Date.now() - minutes * 60 * 1000, // Adjust start time
                    endTime: Date.now(), // Current time
                });

                if (latestFragment) {
                    console.log("Latest fragment found:", latestFragment);
                    const getDataEndpointParams = {
                        APIName: "GET_DASH_STREAMING_SESSION_URL",
                        StreamName: streamName,
                    };

                    const dataEndpoint = await kinesisVideo.getDataEndpoint(getDataEndpointParams).promise();
                    const kinesisVideoArchivedMedia = new AWS.KinesisVideoArchivedMedia({
                        endpoint: dataEndpoint.DataEndpoint,
                        region,
                        credentials,
                    });

                    const startTime = new Date(latestFragment.ServerTimestamp);
                    const timeAgo = minutes <= 1440 ? new Date() : 24 * 60 * 60 * 1000;
                    const endTime = new Date(startTime.getTime() + timeAgo); // Adjust end time

                    const dashUrlResponse = await kinesisVideoArchivedMedia
                        .getDASHStreamingSessionURL({
                            StreamName: streamName,
                            PlaybackMode: "ON_DEMAND",
                            Expires: 43200,
                            MaxManifestFragmentResults: 5000,
                            DASHFragmentSelector: {
                                FragmentSelectorType: "SERVER_TIMESTAMP",
                                TimestampRange: {
                                    StartTimestamp: startTime,
                                    EndTimestamp: endTime,
                                },
                            },
                        })
                        .promise();
                    console.log("DASH URL Response (from latest fragment):", dashUrlResponse);
                    return dashUrlResponse.DASHStreamingSessionURL;
                } else {
                    throw new Error("No streams found in the specified timestamp range.");
                }
            } catch (fragmentError) {
                console.error("Error fetching the latest fragment:", fragmentError);
                throw fragmentError;
            }
        } else {
            console.error("Unexpected error in awsKinesis.getDashStreamingSessionURL", error);
            throw error;
        }
    }
}

async function getClip(streamName, region, startTime, endTime) {
    const kinesisVideo = new AWS.KinesisVideo({
        region,
        credentials,
    });

    try {
        const getDataEndpointParams = {
            APIName: "GET_CLIP",
            StreamName: streamName,
        };

        const dataEndpoint = await kinesisVideo.getDataEndpoint(getDataEndpointParams).promise();

        const kinesisVideoArchivedMedia = new AWS.KinesisVideoArchivedMedia({
            endpoint: dataEndpoint.DataEndpoint,
            region,
            credentials,
        });

        const getClipParams = {
            StreamName: streamName,
            ClipFragmentSelector: {
                FragmentSelectorType: "PRODUCER_TIMESTAMP",
                TimestampRange: {
                    StartTimestamp: new Date(startTime), // "2023-10-27T10:01:00Z"
                    EndTimestamp: new Date(endTime),
                },
            },
        };

        const clipResponse = await kinesisVideoArchivedMedia.getClip(getClipParams).promise();
        console.log("GetClip successful. ContentType:", clipResponse.ContentType);

        return clipResponse;
    } catch (err) {
        console.error("Error:", err);
        if (err.code === "ResourceNotFoundException") {
            console.error("The stream was not found.");
        } else if (err.code === "InvalidArgumentException") {
            console.error("Invalid input parameters.");
        } else if (err.code === "NotAuthorizedException") {
            console.error("Not authorized to access the stream.");
        } else if (err.code === "ClientLimitExceededException") {
            console.error("Client limit exceeded.");
        } else {
            console.error("An unexpected error occurred.");
        }
        throw err;
    }
}

async function getScreenshot(streamName, region, targetTimestamp, imageFormat = "jpg") {
    const kvsClient = new KinesisVideoClient({ region });

    try {
        const dataEndpointResponse = await kvsClient.send(
            new GetDataEndpointCommand({
                StreamName: streamName,
                APIName: "GET_IMAGES",
            }),
        );

        if (!dataEndpointResponse.DataEndpoint) {
            throw new Error("Could not retrieve data endpoint for GetImages.");
        }
        const dataEndpoint = dataEndpointResponse.DataEndpoint;
        const archivedMediaClient = new KinesisVideoArchivedMediaClient({
            region,
            endpoint: dataEndpoint,
        });

        const startTimestamp = new Date(targetTimestamp - (1 * 1000));
        const endTimestamp = new Date(targetTimestamp + (5 * 1000));

        const getImagesParams = {
            StreamName: streamName,
            ImageSelectorType: "PRODUCER_TIMESTAMP",
            SamplingInterval: 200,
            Format: imageFormat, // JPEG or PNG
            StartTimestamp: startTimestamp,
            EndTimestamp: endTimestamp,
            MaxResults: 1, // We only want one image
            FormatConfig: {
                JPEGQuality: "100",
            },
        };

        let attempts = 0;

        while (true) {
            try {
                const getImagesResponse = await archivedMediaClient.send(new GetImagesCommand(getImagesParams));
                if (!getImagesResponse.Images || getImagesResponse.Images.length === 0) {
                    const newError = new Error("Sorry, image not found for the selected timestamp. Please try again.");
                    newError.name = "ScreenshotNotFoundError";
                    throw newError;
                }

                const imageInfo = getImagesResponse.Images[0];

                console.log(imageInfo.TimeStamp);

                if (imageInfo.Error) {
                    throw new Error(`GetImages API returned an error for the image: ${imageInfo.Error} (${imageInfo.ErrorCode})`);
                }

                if (!imageInfo.ImageContent) {
                    throw new Error("GetImages response received, but image content was empty.");
                }

                return Buffer.from(imageInfo.ImageContent, "base64");
            } catch (error) {
                if (error.name === "ResourceNotFoundException") {
                    if ((attempts += 1) >= 3) {
                        throw error;
                    }
                } else {
                    throw error;
                }
            }
        }
    } catch (error) {
        console.error("Error capturing Kinesis Video screenshot:", error);
        throw error;
    }
}

async function getHlsStreamingSessionURL({ streamName, region, streamMode = "LIVE", minutes = 60 }) {
    console.log(`streamMode ${streamMode}`);
    const kinesisVideo = new AWS.KinesisVideo({
        region,
        credentials,
    });

    try {
        const getDataEndpointParams = {
            APIName: "GET_HLS_STREAMING_SESSION_URL",
            StreamName: streamName,
        };

        const dataEndpoint = await kinesisVideo.getDataEndpoint(getDataEndpointParams).promise();

        const kinesisVideoArchivedMedia = new AWS.KinesisVideoArchivedMedia({
            endpoint: dataEndpoint.DataEndpoint,
            region,
            credentials,
        });

        const hlsUrlResponse = await kinesisVideoArchivedMedia
            .getHLSStreamingSessionURL({
                StreamName: streamName,
                PlaybackMode: streamMode,
                Expires: 43200,
                MaxMediaPlaylistFragmentResults: 5000,
                HLSFragmentSelector:
                    streamMode === "ON_DEMAND"
                        ? {
                              FragmentSelectorType: "SERVER_TIMESTAMP",
                              TimestampRange: {
                                  StartTimestamp: new Date(Date.now() - minutes * 60 * 1000), // minutes ago
                                  EndTimestamp: minutes <= 1440 ? new Date() : new Date(Date.now() - minutes * 60 * 1000 + 60 * 60 * 1000),
                              },
                          }
                        : {},
            })
            .promise();

        return hlsUrlResponse.HLSStreamingSessionURL;
    } catch (error) {
        console.error(error);
        throw error;
    }
}

async function getLatestFragment({ streamName, region, startTime, endTime }) {
    const kinesisVideo = new AWS.KinesisVideo({
        region,
        credentials,
    });

    try {
        const getDataEndpointParams = {
            APIName: "LIST_FRAGMENTS",
            StreamName: streamName,
        };
        const getDataEndPoint = async ({ getDataEndpointParams }) => {
            try {
                return await kinesisVideo.getDataEndpoint(getDataEndpointParams).promise();
            } catch (error) {
                console.log("Kinesis: Fragment GetDataPoint", error);
                if (error.code === "ClientLimitExceededException" || error.name === "ClientLimitExceededException") {
                    return await handleClientLimitExceededException(error, getDataEndPoint, { getDataEndpointParams });
                }
                throw error;
            }
        };

        const dataEndpoint = await getDataEndPoint({ getDataEndpointParams });

        // const dataEndpoint = await handleRateLimit(() => kinesisVideo.getDataEndpoint(getDataEndpointParams).promise());

        const kinesisVideoArchivedMedia = new KinesisVideoArchivedMediaClient({
            endpoint: dataEndpoint.DataEndpoint,
            region,
            credentials,
        });

        const listFragmentsParams = {
            StreamName: streamName,
            FragmentSelector: {
                FragmentSelectorType: "PRODUCER_TIMESTAMP",
                TimestampRange: {
                    StartTimestamp: new Date(startTime),
                    EndTimestamp: new Date(endTime),
                },
            },
        };

        const getListFragments = async ({ listFragmentsParams }) => {
            try {
                return await kinesisVideoArchivedMedia.send(new ListFragmentsCommand(listFragmentsParams));
            } catch (error) {
                console.log("Kinesis: Fragment getListFragments", error);
                if (error.code === "ClientLimitExceededException" || error.name === "ClientLimitExceededException") {
                    return await handleClientLimitExceededException(error, getListFragments, { listFragmentsParams });
                }
                throw error;
            }
        };

        const listFragmentsResponse = await getListFragments({ listFragmentsParams });

        // const listFragmentsResponse = await kinesisVideoArchivedMedia.send(new ListFragmentsCommand(listFragmentsParams));

        if (listFragmentsResponse.Fragments && listFragmentsResponse.Fragments.length > 0) {
            const latestFragment = listFragmentsResponse.Fragments[listFragmentsResponse.Fragments.length - 1];
            console.log("Latest Fragment:", latestFragment);
            return latestFragment;
        } else {
            console.warn("Kinesis: Fragment Can not get any fragment from the given dutation");
            return null;
        }
    } catch (error) {
        console.error("Kinesis: Fragment  getLatestFragment:", error.code, error.name, error);
        if (error.name === "ClientLimitExceededException" || error.code === "ClientLimitExceededException") {
            // throw new Error("You have reached the limit. Please try again later.");
            const res = await handleClientLimitExceededException({ retryDelay: 4 }, getLatestFragment, { streamName, region, startTime, endTime });
            // console.log("AWS in catch", res);
            return res;
        } else {
            throw error;
        }
    }
}

module.exports = {
    listStreams,
    // listStreamsInfo,
    getDashStreamingSessionURL,
    getDashStreamingSessionURL_V2,
    getClip,
    getScreenshot,
    getHlsStreamingSessionURL,
    getStreamTags,
    getLatestFragment,
};
