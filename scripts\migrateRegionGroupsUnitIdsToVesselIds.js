require("dotenv").config();
const RegionGroup = require("../models/RegionGroup");
const Vessel = require("../models/Vessel");

const dryRun = process.argv.includes("--dry-run");

// Progress bar utilities
function createProgressBar(current, total, barLength = 40) {
    const percentage = Math.round((current / total) * 100);
    const filledLength = Math.round((current / total) * barLength);
    const bar = "█".repeat(filledLength) + "░".repeat(barLength - filledLength);
    return `[${bar}] ${percentage}% (${current}/${total})`;
}

function clearLine() {
    process.stdout.write("\r\x1b[K");
}

function writeProgress(message) {
    clearLine();
    process.stdout.write(message);
}

function writeLine(message) {
    clearLine();
    process.stdout.write(message + "\n");
}

function convertUnitIdsToVesselIds(unitIds, unitToVesselMap) {
    if (!Array.isArray(unitIds)) return [];

    const vesselIds = [];
    const unmappedUnits = [];

    unitIds.forEach((unitId) => {
        if (typeof unitId === "string") {
            const vesselId = unitToVesselMap.get(unitId);
            if (vesselId) {
                vesselIds.push(vesselId);
            } else {
                unmappedUnits.push(unitId);
            }
        }
    });

    if (unmappedUnits.length > 0) {
        writeLine(`⚠️  Could not map unit_ids to vessels: ${unmappedUnits.join(", ")}`);
    }

    return vesselIds;
}

async function migrateRegionGroups(unitToVesselMap) {
    writeLine("\n🌍 Migrating region groups...");
    const regionGroups = await RegionGroup.find({});
    let updated = 0;

    for (let i = 0; i < regionGroups.length; i++) {
        const regionGroup = regionGroups[i];
        const progress = createProgressBar(i + 1, regionGroups.length);
        writeProgress(`🌍 Processing region groups... ${progress}`);

        try {
            // Convert unit_ids to vessel_ids
            const vesselIds = convertUnitIdsToVesselIds(regionGroup.unit_ids, unitToVesselMap);

            // Only update if we have vessel_ids to add or if unit_ids exist but vessel_ids is empty
            if (
                vesselIds.length > 0 ||
                (regionGroup.unit_ids && regionGroup.unit_ids.length > 0 && (!regionGroup.vessel_ids || regionGroup.vessel_ids.length === 0))
            ) {
                if (!dryRun) {
                    await RegionGroup.updateOne({ _id: regionGroup._id }, { $set: { vessel_ids: vesselIds } });
                }
                updated++;
            }
        } catch (error) {
            writeLine(`❌ Error migrating region group ${regionGroup._id}: ${error.message}`);
        }
    }

    writeLine(`✅ Region groups migration completed: ${updated} updated`);
    return updated;
}

async function migrateRegionGroupsUnitIdsToVesselIds() {
    try {
        writeLine("🚀 Starting migration from unit_ids to vessel_ids in region groups...");
        if (dryRun) {
            writeLine("🔍 DRY RUN MODE - No changes will be made");
        }

        // Create mapping from unit_id to vessel ObjectId
        writeProgress("📋 Creating unit_id to vessel_id mapping...");
        const vessels = await Vessel.find({ unit_id: { $ne: null, $exists: true } }, { _id: 1, unit_id: 1, name: 1 });

        const unitToVesselMap = new Map();
        vessels.forEach((vessel) => {
            unitToVesselMap.set(vessel.unit_id, vessel._id);
        });

        writeLine(`✅ Created mapping for ${unitToVesselMap.size} vessels`);

        // Run migration for region groups
        const regionGroupsUpdated = await migrateRegionGroups(unitToVesselMap);

        // Final summary
        writeLine("\n📊 Migration Summary:");
        writeLine("=".repeat(50));
        writeLine(`🌍 Region Groups: ${regionGroupsUpdated} updated`);
        writeLine(`📈 Total Records: ${regionGroupsUpdated} updated`);
        writeLine("=".repeat(50));

        if (dryRun) {
            writeLine("🔍 DRY RUN COMPLETED - No actual changes were made");
        } else {
            writeLine("✅ MIGRATION COMPLETED SUCCESSFULLY!");
        }
    } catch (error) {
        writeLine(`❌ Migration failed: ${error.message}`);
        process.exit(1);
    } finally {
        process.exit(0);
    }
}

migrateRegionGroupsUnitIdsToVesselIds();
