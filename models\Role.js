const mongoose = require("mongoose");
const Permission = require("./Permission");
const ioEmitter = require("../modules/ioEmitter");
const db = require("../modules/db");

const roleSchema = new mongoose.Schema({
    role_id: { type: Number, required: true },
    role_name: { type: String, required: true, unique: true },
    denied_permissions: { type: Array, required: true },
    deletable: { type: Boolean, required: true, default: true },
    editable: { type: Boolean, required: true, default: true },
    hierarchy_number: { type: Number, required: true },
    created_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
    },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});

// Pre-save hook to auto-increment role_id
roleSchema.pre("save", async function (next) {
    if (this.isNew) {
        // Only increment for new documents
        try {
            // Find the document with the highest `role_id` and increment by 1
            const latest = await this.constructor.findOne().sort({ role_id: -1 });
            this.role_id = latest ? latest.role_id + 1 : 1;
            this.hierarchy_number = this.role_id;
            this.denied_permissions = await Permission.find().then((result) => result.map((p) => p.permission_id));
            next();
        } catch (err) {
            next(err);
        }
    } else {
        next();
    }
});

roleSchema.post("save", emitChangedEvent);

roleSchema.post("findOneAndDelete", emitChangedEvent);

function emitChangedEvent(role) {
    ioEmitter.emit("notifyAll", { name: `roles/changed`, data: role.toObject() });
}

const Role = db.qm.model("Role", roleSchema);

module.exports = Role;
module.exports.emitChangedEvent = process.env.NODE_ENV === "test" && emitChangedEvent;
