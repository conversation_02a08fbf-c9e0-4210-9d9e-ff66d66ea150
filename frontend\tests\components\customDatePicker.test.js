import { render, screen } from '@testing-library/react';
import CustomDatePicker from '../../src/components/CustomDatePicker';
import theme from '../../src/theme';
import { ThemeProvider } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';

jest.mock('@mui/x-date-pickers', () => ({
    DatePicker: jest.fn(),
}));

describe('CustomDatePicker', () => {
    const mockOnChange = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should render DatePicker with inputRef', () => {
        DatePicker.mockImplementation(({ value, onChange, sx, onOpen, onClose, inputRef }) => {
            onOpen();
            onClose();
            inputRef.current = { addEventListener: jest.fn(), removeEventListener: jest.fn() };
            return (<input
                value={value}
                onChange={e => onChange(e.target.value)}
                style={sx}
                aria-label="custom-datetime-picker"
            />)
        });

        render(
            <ThemeProvider theme={theme}>
                <CustomDatePicker onChange={mockOnChange} />
            </ThemeProvider>
        );

        expect(DatePicker).toHaveBeenCalled();
    });

    it('should render DatePicker with correct theme color in sx', () => {
        DatePicker.mockImplementation(({ value, onChange, sx, onOpen, onClose }) => {
            onOpen();
            onClose();
            return (<input
                value={value}
                onChange={e => onChange(e.target.value)}
                style={sx}
                aria-label="custom-datetime-picker"
            />)
        });

        render(
            <ThemeProvider theme={theme}>
                <CustomDatePicker onChange={mockOnChange} iconPosition={'start'} />
            </ThemeProvider>
        );

        expect(DatePicker).toHaveBeenCalled();
    });
});
