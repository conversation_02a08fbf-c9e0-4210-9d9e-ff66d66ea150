{"name": "quartermaster-frontend", "private": true, "version": "2.12.4", "type": "module", "scripts": {"start": "vite", "dev": "vite", "dev:clean": "vite --force", "build": "vite build", "analyze": "vite build --mode analyze", "lint": "eslint \"src/**/*.{js,jsx}\" --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint \"src/**/*.{js,jsx}\" --fix", "preview": "vite preview", "test": "jest --coverage --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier --config .prettierrc --ignore-path .prettierignore --write \"**/*.{js,jsx,ts,tsx,css}\""}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@googlemaps/markerclusterer": "^2.5.3", "@mui/icons-material": "^7.1.0", "@mui/material": "7.1", "@mui/x-data-grid": "^7.15.0", "@mui/x-date-pickers": "^7.13.0", "@react-google-maps/api": "^2.19.3", "axios": "^1.7.2", "chart.js": "^4.4.5", "chartjs-plugin-annotation": "^3.1.0", "country-flag-icons": "^1.5.18", "dashjs": "^5.0.0", "date-fns": "^3.6.0", "dayjs": "^1.11.12", "dotenv": "^16.4.5", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "formik": "^2.4.6", "geodesy": "^2.4.0", "hls.js": "^1.5.17", "idb": "^8.0.0", "jwt-decode": "^4.0.0", "mgrs": "^2.1.0", "notistack": "^3.0.1", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-datepicker": "^7.6.0", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-joyride": "^2.9.2", "react-mosaic-component": "^6.1.1", "react-router-dom": "^6.24.1", "react-window": "^1.8.11", "socket.io-client": "^4.7.5", "video.js": "^8.16.1", "yup": "^1.6.1"}, "devDependencies": {"@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.25.9", "@eslint/js": "^9.18.0", "@testing-library/jest-dom": "^6.6.2", "@testing-library/react": "^16.0.1", "@types/d3": "^7.4.3", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.18.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-svg-transformer": "^1.0.0", "prettier": "3.5.3", "rollup-plugin-visualizer": "^5.12.0", "terser": "^5.39.0", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1"}}