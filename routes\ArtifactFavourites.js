const express = require("express");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const isAuthenticated = require("../middlewares/auth");
const { validateData } = require("../middlewares/validator");
const { body } = require("express-validator");
const mongoose = require("mongoose");
const { addFavouriteArtifact, deleteFavouriteArtifact, getUserFavouriteArtifacts } = require("../services/ArtifactFavourites.service");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_FAVOURITE_ARTIFACT),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                body("artifact_id")
                    .isString()
                    .withMessage("artifact_id must be a string")
                    .notEmpty()
                    .withMessage("artifact_id must not be an empty string")
                    .custom((value) => mongoose.Types.ObjectId.isValid(value))
                    .withMessage("artifact_id must be a valid object id"),
            ],
            req,
            res,
            next,
        ),
    addFavouriteArtifact,
);

// there is currently no usecase for this endpoint
// router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_ALL_FAVOURITE_ARTIFACTS), isAuthenticated, getAllFavouriteArtifacts);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_USER_FAVOURITE_ARTIFACTS), isAuthenticated, getUserFavouriteArtifacts);

router.delete(
    "/",
    assignEndpointId.bind(this, endpointIds.DELETE_FAVOURITE_ARTIFACT),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                body("artifact_id")
                    .isString()
                    .withMessage("artifact_id must be a string")
                    .notEmpty()
                    .withMessage("artifact_id must not be an empty string")
                    .custom((value) => mongoose.Types.ObjectId.isValid(value))
                    .withMessage("artifact_id must be a valid object id"),
            ],
            req,
            res,
            next,
        ),
    deleteFavouriteArtifact,
);

module.exports = router;
