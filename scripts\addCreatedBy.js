// scripts/addCreatedBy.js

require("dotenv").config(); // if you keep your URI in .env

const User = require("../models/User");
const Role = require("../models/Role");
const { default: mongoose } = require("mongoose");
const ApiKey = require("../models/ApiKey");

async function runMigration() {
    try {
        const id = "66942a54a7f848634a00990a"; // <PERSON><PERSON><PERSON>'s id

        // 1. Patch all users missing `created_by`
        const userResult = await User.updateMany({ created_by: { $exists: false } }, { $set: { created_by: mongoose.Types.ObjectId(id) } });
        console.log(`Users patched: ${userResult.modifiedCount}`);

        // 2. Patch all roles missing `created_by`
        const roleResult = await Role.updateMany({ created_by: { $exists: false } }, { $set: { created_by: mongoose.Types.ObjectId(id) } });
        console.log(`Roles patched: ${roleResult.modifiedCount}`);

        // 2. Patch all roles missing `created_by`
        const apiKeyResult = await ApiKey.updateMany({ created_by: { $exists: false } }, { $set: { created_by: mongoose.Types.ObjectId(id) } });
        console.log(`API Key patched: ${apiKeyResult.modifiedCount}`);

        console.log("Migration complete.");
    } catch (err) {
        console.error(err);
        process.exit(1);
    }
}

runMigration();
