const express = require('express');
const Permission = require('../models/Permission');
const { validateError } = require('../utils/functions');
const isAuthenticated = require('../middlewares/auth');
const { default: rateLimit } = require('express-rate-limit');
const assignEndpointId = require('../middlewares/assignEndpointId');
const { endpointIds } = require('../utils/endpointIds');
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: 'Too many requests from this IP' },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use('/', apiLimiter);

router.get('/',
    assignEndpointId.bind(this, endpointIds.FETCH_PERMISSIONS),
    isAuthenticated,
    async (req, res) => {
        try {
            const permissions = await Permission.find()
            res.json(permissions);
        } catch (err) {
            validateError(err, res)
        }
    }
);

module.exports = router;