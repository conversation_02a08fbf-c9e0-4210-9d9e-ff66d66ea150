import { useSnackbar } from "notistack";

/**
 * Hook to use a toaster utility for displaying notifications.
 *
 * @typedef {object} ToasterOptions
 * @property {boolean} [hideIconVariant=true] - Whether to hide the variant icon.
 * @property {boolean} [preventDuplicate=true] - Whether to prevent duplicate messages.
 * @property {string} [variant] - Variant type (e.g., 'default', 'error', 'success', 'warning', 'info').
 * @property {number} [autoHideDuration] - Duration in milliseconds to auto-hide the snackbar.
 * @property {Object} [anchorOrigin] - Position of the snackbar.
 */

/**
 * Hook to provide a toaster utility for showing notifications.
 *
 * @return {Function} The toaster function for displaying notifications.
 * @example
 * const toaster = useToaster();
 * toaster("This is a success message!", { variant: "success" });
 */
export const useToaster = () => {
    const { enqueueSnackbar } = useSnackbar();

    /**
     * Displays a notification using the provided message and options.
     *
     * @param {string} message - The message to display in the notification.
     * @param {ToasterOptions} [options={}] - Configuration options for the notification.
     */
    const toaster = (message, options = {}) => {
        if (!message) {
            console.warn(
                "useToaster called without a message. Example usage: toaster('Message goes here', { variant: 'success' })"
            );
            return;
        }

        const defaultOptions = {
            hideIconVariant: true,
            preventDuplicate: true,
        };

        const updatedOptions = { ...defaultOptions, ...options };

        enqueueSnackbar(message, updatedOptions);
    };

    return toaster;
};