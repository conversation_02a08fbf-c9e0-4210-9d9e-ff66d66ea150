{"version": 3, "sources": ["../../chartjs-plugin-annotation/dist/chartjs-plugin-annotation.esm.js"], "sourcesContent": ["/*!\n* chartjs-plugin-annotation v3.1.0\n* https://www.chartjs.org/chartjs-plugin-annotation/index\n * (c) 2024 chartjs-plugin-annotation Contributors\n * Released under the MIT License\n */\nimport { Element, DoughnutController, defaults, Animations, Chart } from 'chart.js';\nimport { distanceBetweenPoints, toRadians, isObject, valueOrDefault, defined, isFunction, callback, isArray, toFont, addRoundedRectPath, toTRBLCorners, QUARTER_PI, PI, HALF_PI, TWO_THIRDS_PI, TAU, isNumber, RAD_PER_DEG, toPadding, isFinite, getAngleFromPoint, toDegrees, clipArea, unclipArea } from 'chart.js/helpers';\n\n/**\n * @typedef { import(\"chart.js\").ChartEvent } ChartEvent\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst interaction = {\n  modes: {\n    /**\n     * Point mode returns all elements that hit test based on the event position\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    point(visibleElements, event) {\n      return filterElements(visibleElements, event, {intersect: true});\n    },\n\n    /**\n     * Nearest mode returns the element closest to the event position\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found (only 1 element)\n     */\n    nearest(visibleElements, event, options) {\n      return getNearestItem(visibleElements, event, options);\n    },\n    /**\n     * x mode returns the elements that hit-test at the current x coordinate\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    x(visibleElements, event, options) {\n      return filterElements(visibleElements, event, {intersect: options.intersect, axis: 'x'});\n    },\n\n    /**\n     * y mode returns the elements that hit-test at the current y coordinate\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    y(visibleElements, event, options) {\n      return filterElements(visibleElements, event, {intersect: options.intersect, axis: 'y'});\n    }\n  }\n};\n\n/**\n * Returns all elements that hit test based on the event position\n * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n * @param {ChartEvent} event - the event we are find things at\n * @param {Object} options - interaction options to use\n * @return {AnnotationElement[]} - elements that are found\n */\nfunction getElements(visibleElements, event, options) {\n  const mode = interaction.modes[options.mode] || interaction.modes.nearest;\n  return mode(visibleElements, event, options);\n}\n\nfunction inRangeByAxis(element, event, axis) {\n  if (axis !== 'x' && axis !== 'y') {\n    return element.inRange(event.x, event.y, 'x', true) || element.inRange(event.x, event.y, 'y', true);\n  }\n  return element.inRange(event.x, event.y, axis, true);\n}\n\nfunction getPointByAxis(event, center, axis) {\n  if (axis === 'x') {\n    return {x: event.x, y: center.y};\n  } else if (axis === 'y') {\n    return {x: center.x, y: event.y};\n  }\n  return center;\n}\n\nfunction filterElements(visibleElements, event, options) {\n  return visibleElements.filter((element) => options.intersect ? element.inRange(event.x, event.y) : inRangeByAxis(element, event, options.axis));\n}\n\nfunction getNearestItem(visibleElements, event, options) {\n  let minDistance = Number.POSITIVE_INFINITY;\n\n  return filterElements(visibleElements, event, options)\n    .reduce((nearestItems, element) => {\n      const center = element.getCenterPoint();\n      const evenPoint = getPointByAxis(event, center, options.axis);\n      const distance = distanceBetweenPoints(event, evenPoint);\n      if (distance < minDistance) {\n        nearestItems = [element];\n        minDistance = distance;\n      } else if (distance === minDistance) {\n        // Can have multiple items at the same distance in which case we sort by size\n        nearestItems.push(element);\n      }\n\n      return nearestItems;\n    }, [])\n    .sort((a, b) => a._index - b._index)\n    .slice(0, 1); // return only the top item;\n}\n\n/**\n * @typedef {import('chart.js').Point} Point\n */\n\n/**\n * Rotate a `point` relative to `center` point by `angle`\n * @param {Point} point - the point to rotate\n * @param {Point} center - center point for rotation\n * @param {number} angle - angle for rotation, in radians\n * @returns {Point} rotated point\n */\nfunction rotated(point, center, angle) {\n  const cos = Math.cos(angle);\n  const sin = Math.sin(angle);\n  const cx = center.x;\n  const cy = center.y;\n\n  return {\n    x: cx + cos * (point.x - cx) - sin * (point.y - cy),\n    y: cy + sin * (point.x - cx) + cos * (point.y - cy)\n  };\n}\n\nconst isOlderPart = (act, req) => req > act || (act.length > req.length && act.slice(0, req.length) === req);\n\n/**\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('chart.js').InteractionAxis } InteractionAxis\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst EPSILON = 0.001;\nconst clamp = (x, from, to) => Math.min(to, Math.max(from, x));\n\n/**\n * @param {{value: number, start: number, end: number}} limit\n * @param {number} hitSize\n * @returns {boolean}\n */\nconst inLimit = (limit, hitSize) => limit.value >= limit.start - hitSize && limit.value <= limit.end + hitSize;\n\n/**\n * @param {Object} obj\n * @param {number} from\n * @param {number} to\n * @returns {Object}\n */\nfunction clampAll(obj, from, to) {\n  for (const key of Object.keys(obj)) {\n    obj[key] = clamp(obj[key], from, to);\n  }\n  return obj;\n}\n\n/**\n * @param {Point} point\n * @param {Point} center\n * @param {number} radius\n * @param {number} hitSize\n * @returns {boolean}\n */\nfunction inPointRange(point, center, radius, hitSize) {\n  if (!point || !center || radius <= 0) {\n    return false;\n  }\n  return (Math.pow(point.x - center.x, 2) + Math.pow(point.y - center.y, 2)) <= Math.pow(radius + hitSize, 2);\n}\n\n/**\n * @param {Point} point\n * @param {{x: number, y: number, x2: number, y2: number}} rect\n * @param {InteractionAxis} axis\n * @param {{borderWidth: number, hitTolerance: number}} hitsize\n * @returns {boolean}\n */\nfunction inBoxRange(point, {x, y, x2, y2}, axis, {borderWidth, hitTolerance}) {\n  const hitSize = (borderWidth + hitTolerance) / 2;\n  const inRangeX = point.x >= x - hitSize - EPSILON && point.x <= x2 + hitSize + EPSILON;\n  const inRangeY = point.y >= y - hitSize - EPSILON && point.y <= y2 + hitSize + EPSILON;\n  if (axis === 'x') {\n    return inRangeX;\n  } else if (axis === 'y') {\n    return inRangeY;\n  }\n  return inRangeX && inRangeY;\n}\n\n/**\n * @param {Point} point\n * @param {rect: {x: number, y: number, x2: number, y2: number}, center: {x: number, y: number}} element\n * @param {InteractionAxis} axis\n * @param {{rotation: number, borderWidth: number, hitTolerance: number}}\n * @returns {boolean}\n */\nfunction inLabelRange(point, {rect, center}, axis, {rotation, borderWidth, hitTolerance}) {\n  const rotPoint = rotated(point, center, toRadians(-rotation));\n  return inBoxRange(rotPoint, rect, axis, {borderWidth, hitTolerance});\n}\n\n/**\n * @param {AnnotationElement} element\n * @param {boolean} useFinalPosition\n * @returns {Point}\n */\nfunction getElementCenterPoint(element, useFinalPosition) {\n  const {centerX, centerY} = element.getProps(['centerX', 'centerY'], useFinalPosition);\n  return {x: centerX, y: centerY};\n}\n\n/**\n * @param {string} pkg\n * @param {string} min\n * @param {string} ver\n * @param {boolean} [strict=true]\n * @returns {boolean}\n */\nfunction requireVersion(pkg, min, ver, strict = true) {\n  const parts = ver.split('.');\n  let i = 0;\n  for (const req of min.split('.')) {\n    const act = parts[i++];\n    if (parseInt(req, 10) < parseInt(act, 10)) {\n      break;\n    }\n    if (isOlderPart(act, req)) {\n      if (strict) {\n        throw new Error(`${pkg} v${ver} is not supported. v${min} or newer is required.`);\n      } else {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nconst isPercentString = (s) => typeof s === 'string' && s.endsWith('%');\nconst toPercent = (s) => parseFloat(s) / 100;\nconst toPositivePercent = (s) => clamp(toPercent(s), 0, 1);\n\nconst boxAppering = (x, y) => ({x, y, x2: x, y2: y, width: 0, height: 0});\nconst defaultInitAnimation = {\n  box: (properties) => boxAppering(properties.centerX, properties.centerY),\n  doughnutLabel: (properties) => boxAppering(properties.centerX, properties.centerY),\n  ellipse: (properties) => ({centerX: properties.centerX, centerY: properties.centerX, radius: 0, width: 0, height: 0}),\n  label: (properties) => boxAppering(properties.centerX, properties.centerY),\n  line: (properties) => boxAppering(properties.x, properties.y),\n  point: (properties) => ({centerX: properties.centerX, centerY: properties.centerY, radius: 0, width: 0, height: 0}),\n  polygon: (properties) => boxAppering(properties.centerX, properties.centerY)\n};\n\n/**\n * @typedef { import('chart.js').FontSpec } FontSpec\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('chart.js').Padding } Padding\n * @typedef { import('../../types/element').AnnotationBoxModel } AnnotationBoxModel\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n * @typedef { import('../../types/options').AnnotationPointCoordinates } AnnotationPointCoordinates\n * @typedef { import('../../types/label').CoreLabelOptions } CoreLabelOptions\n * @typedef { import('../../types/label').LabelPositionObject } LabelPositionObject\n */\n\n/**\n * @param {number} size\n * @param {number|string} position\n * @returns {number}\n */\nfunction getRelativePosition(size, position) {\n  if (position === 'start') {\n    return 0;\n  }\n  if (position === 'end') {\n    return size;\n  }\n  if (isPercentString(position)) {\n    return toPositivePercent(position) * size;\n  }\n  return size / 2;\n}\n\n/**\n * @param {number} size\n * @param {number|string} value\n * @param {boolean} [positivePercent=true]\n * @returns {number}\n */\nfunction getSize(size, value, positivePercent = true) {\n  if (typeof value === 'number') {\n    return value;\n  } else if (isPercentString(value)) {\n    return (positivePercent ? toPositivePercent(value) : toPercent(value)) * size;\n  }\n  return size;\n}\n\n/**\n * @param {{x: number, width: number}} size\n * @param {CoreLabelOptions} options\n * @returns {number}\n */\nfunction calculateTextAlignment(size, options) {\n  const {x, width} = size;\n  const textAlign = options.textAlign;\n  if (textAlign === 'center') {\n    return x + width / 2;\n  } else if (textAlign === 'end' || textAlign === 'right') {\n    return x + width;\n  }\n  return x;\n}\n\n/**\n * @param {Point} point\n * @param {{height: number, width: number}} labelSize\n * @param {{borderWidth: number, position: {LabelPositionObject|string}, xAdjust: number, yAdjust: number}} options\n * @param {Padding|undefined} padding\n * @returns {{x: number, y: number, x2: number, y2: number, height: number, width: number, centerX: number, centerY: number}}\n */\nfunction measureLabelRectangle(point, labelSize, {borderWidth, position, xAdjust, yAdjust}, padding) {\n  const hasPadding = isObject(padding);\n  const width = labelSize.width + (hasPadding ? padding.width : 0) + borderWidth;\n  const height = labelSize.height + (hasPadding ? padding.height : 0) + borderWidth;\n  const positionObj = toPosition(position);\n  const x = calculateLabelPosition$1(point.x, width, xAdjust, positionObj.x);\n  const y = calculateLabelPosition$1(point.y, height, yAdjust, positionObj.y);\n\n  return {\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n    width,\n    height,\n    centerX: x + width / 2,\n    centerY: y + height / 2\n  };\n}\n\n/**\n * @param {LabelPositionObject|string} value\n * @param {string|number} defaultValue\n * @returns {LabelPositionObject}\n */\nfunction toPosition(value, defaultValue = 'center') {\n  if (isObject(value)) {\n    return {\n      x: valueOrDefault(value.x, defaultValue),\n      y: valueOrDefault(value.y, defaultValue),\n    };\n  }\n  value = valueOrDefault(value, defaultValue);\n  return {\n    x: value,\n    y: value\n  };\n}\n\n/**\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n * @returns {boolean}\n */\nconst shouldFit = (options, fitRatio) => options && options.autoFit && fitRatio < 1;\n\n/**\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n * @returns {FontSpec[]}\n */\nfunction toFonts(options, fitRatio) {\n  const optFont = options.font;\n  const fonts = isArray(optFont) ? optFont : [optFont];\n  if (shouldFit(options, fitRatio)) {\n    return fonts.map(function(f) {\n      const font = toFont(f);\n      font.size = Math.floor(f.size * fitRatio);\n      font.lineHeight = f.lineHeight;\n      return toFont(font);\n    });\n  }\n  return fonts.map(f => toFont(f));\n}\n\n/**\n * @param {AnnotationPointCoordinates} options\n * @returns {boolean}\n */\nfunction isBoundToPoint(options) {\n  return options && (defined(options.xValue) || defined(options.yValue));\n}\n\nfunction calculateLabelPosition$1(start, size, adjust = 0, position) {\n  return start - getRelativePosition(size, position) + adjust;\n}\n\n/**\n * @param {Chart} chart\n * @param {AnnotationBoxModel} properties\n * @param {CoreAnnotationOptions} options\n * @returns {AnnotationElement}\n */\nfunction initAnimationProperties(chart, properties, options) {\n  const initAnim = options.init;\n  if (!initAnim) {\n    return;\n  } else if (initAnim === true) {\n    return applyDefault(properties, options);\n  }\n  return execCallback(chart, properties, options);\n}\n\n/**\n * @param {Object} options\n * @param {Array} hooks\n * @param {Object} hooksContainer\n * @returns {boolean}\n */\nfunction loadHooks(options, hooks, hooksContainer) {\n  let activated = false;\n  hooks.forEach(hook => {\n    if (isFunction(options[hook])) {\n      activated = true;\n      hooksContainer[hook] = options[hook];\n    } else if (defined(hooksContainer[hook])) {\n      delete hooksContainer[hook];\n    }\n  });\n  return activated;\n}\n\nfunction applyDefault(properties, options) {\n  const type = options.type || 'line';\n  return defaultInitAnimation[type](properties);\n}\n\nfunction execCallback(chart, properties, options) {\n  const result = callback(options.init, [{chart, properties, options}]);\n  if (result === true) {\n    return applyDefault(properties, options);\n  } else if (isObject(result)) {\n    return result;\n  }\n}\n\nconst widthCache = new Map();\nconst notRadius = (radius) => isNaN(radius) || radius <= 0;\nconst fontsKey = (fonts) => fonts.reduce(function(prev, item) {\n  prev += item.string;\n  return prev;\n}, '');\n\n/**\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('../../types/label').CoreLabelOptions } CoreLabelOptions\n * @typedef { import('../../types/options').PointAnnotationOptions } PointAnnotationOptions\n */\n\n/**\n * Determine if content is an image or a canvas.\n * @param {*} content\n * @returns boolean|undefined\n * @todo move this function to chart.js helpers\n */\nfunction isImageOrCanvas(content) {\n  if (content && typeof content === 'object') {\n    const type = content.toString();\n    return (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]');\n  }\n}\n\n/**\n * Set the translation on the canvas if the rotation must be applied.\n * @param {CanvasRenderingContext2D} ctx - chart canvas context\n * @param {Point} point - the point of translation\n * @param {number} rotation - rotation (in degrees) to apply\n */\nfunction translate(ctx, {x, y}, rotation) {\n  if (rotation) {\n    ctx.translate(x, y);\n    ctx.rotate(toRadians(rotation));\n    ctx.translate(-x, -y);\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {Object} options\n * @returns {boolean|undefined}\n */\nfunction setBorderStyle(ctx, options) {\n  if (options && options.borderWidth) {\n    ctx.lineCap = options.borderCapStyle || 'butt';\n    ctx.setLineDash(options.borderDash);\n    ctx.lineDashOffset = options.borderDashOffset;\n    ctx.lineJoin = options.borderJoinStyle || 'miter';\n    ctx.lineWidth = options.borderWidth;\n    ctx.strokeStyle = options.borderColor;\n    return true;\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {Object} options\n */\nfunction setShadowStyle(ctx, options) {\n  ctx.shadowColor = options.backgroundShadowColor;\n  ctx.shadowBlur = options.shadowBlur;\n  ctx.shadowOffsetX = options.shadowOffsetX;\n  ctx.shadowOffsetY = options.shadowOffsetY;\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {CoreLabelOptions} options\n * @returns {{width: number, height: number}}\n */\nfunction measureLabelSize(ctx, options) {\n  const content = options.content;\n  if (isImageOrCanvas(content)) {\n    const size = {\n      width: getSize(content.width, options.width),\n      height: getSize(content.height, options.height)\n    };\n    return size;\n  }\n  const fonts = toFonts(options);\n  const strokeWidth = options.textStrokeWidth;\n  const lines = isArray(content) ? content : [content];\n  const mapKey = lines.join() + fontsKey(fonts) + strokeWidth + (ctx._measureText ? '-spriting' : '');\n  if (!widthCache.has(mapKey)) {\n    widthCache.set(mapKey, calculateLabelSize(ctx, lines, fonts, strokeWidth));\n  }\n  return widthCache.get(mapKey);\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{x: number, y: number, width: number, height: number}} rect\n * @param {Object} options\n */\nfunction drawBox(ctx, rect, options) {\n  const {x, y, width, height} = rect;\n  ctx.save();\n  setShadowStyle(ctx, options);\n  const stroke = setBorderStyle(ctx, options);\n  ctx.fillStyle = options.backgroundColor;\n  ctx.beginPath();\n  addRoundedRectPath(ctx, {\n    x, y, w: width, h: height,\n    radius: clampAll(toTRBLCorners(options.borderRadius), 0, Math.min(width, height) / 2)\n  });\n  ctx.closePath();\n  ctx.fill();\n  if (stroke) {\n    ctx.shadowColor = options.borderShadowColor;\n    ctx.stroke();\n  }\n  ctx.restore();\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{x: number, y: number, width: number, height: number}} rect\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n */\nfunction drawLabel(ctx, rect, options, fitRatio) {\n  const content = options.content;\n  if (isImageOrCanvas(content)) {\n    ctx.save();\n    ctx.globalAlpha = getOpacity(options.opacity, content.style.opacity);\n    ctx.drawImage(content, rect.x, rect.y, rect.width, rect.height);\n    ctx.restore();\n    return;\n  }\n  const labels = isArray(content) ? content : [content];\n  const fonts = toFonts(options, fitRatio);\n  const optColor = options.color;\n  const colors = isArray(optColor) ? optColor : [optColor];\n  const x = calculateTextAlignment(rect, options);\n  const y = rect.y + options.textStrokeWidth / 2;\n  ctx.save();\n  ctx.textBaseline = 'middle';\n  ctx.textAlign = options.textAlign;\n  if (setTextStrokeStyle(ctx, options)) {\n    applyLabelDecoration(ctx, {x, y}, labels, fonts);\n  }\n  applyLabelContent(ctx, {x, y}, labels, {fonts, colors});\n  ctx.restore();\n}\n\nfunction setTextStrokeStyle(ctx, options) {\n  if (options.textStrokeWidth > 0) {\n    // https://stackoverflow.com/questions/13627111/drawing-text-with-an-outer-stroke-with-html5s-canvas\n    ctx.lineJoin = 'round';\n    ctx.miterLimit = 2;\n    ctx.lineWidth = options.textStrokeWidth;\n    ctx.strokeStyle = options.textStrokeColor;\n    return true;\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{radius: number, options: PointAnnotationOptions}} element\n * @param {number} x\n * @param {number} y\n */\nfunction drawPoint(ctx, element, x, y) {\n  const {radius, options} = element;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n\n  if (isImageOrCanvas(style)) {\n    ctx.save();\n    ctx.translate(x, y);\n    ctx.rotate(rad);\n    ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n    ctx.restore();\n    return;\n  }\n  if (notRadius(radius)) {\n    return;\n  }\n  drawPointStyle(ctx, {x, y, radius, rotation, style, rad});\n}\n\nfunction drawPointStyle(ctx, {x, y, radius, rotation, style, rad}) {\n  let xOffset, yOffset, size, cornerRadius;\n  ctx.beginPath();\n\n  switch (style) {\n  // Default includes circle\n  default:\n    ctx.arc(x, y, radius, 0, TAU);\n    ctx.closePath();\n    break;\n  case 'triangle':\n    ctx.moveTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    ctx.closePath();\n    break;\n  case 'rectRounded':\n    // NOTE: the rounded rect implementation changed to use `arc` instead of\n    // `quadraticCurveTo` since it generates better results when rect is\n    // almost a circle. 0.516 (instead of 0.5) produces results with visually\n    // closer proportion to the previous impl and it is inscribed in the\n    // circle with `radius`. For more details, see the following PRs:\n    // https://github.com/chartjs/Chart.js/issues/5597\n    // https://github.com/chartjs/Chart.js/issues/5858\n    cornerRadius = radius * 0.516;\n    size = radius - cornerRadius;\n    xOffset = Math.cos(rad + QUARTER_PI) * size;\n    yOffset = Math.sin(rad + QUARTER_PI) * size;\n    ctx.arc(x - xOffset, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n    ctx.arc(x + yOffset, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n    ctx.arc(x + xOffset, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n    ctx.arc(x - yOffset, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n    ctx.closePath();\n    break;\n  case 'rect':\n    if (!rotation) {\n      size = Math.SQRT1_2 * radius;\n      ctx.rect(x - size, y - size, 2 * size, 2 * size);\n      break;\n    }\n    rad += QUARTER_PI;\n    /* falls through */\n  case 'rectRot':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    ctx.closePath();\n    break;\n  case 'crossRot':\n    rad += QUARTER_PI;\n    /* falls through */\n  case 'cross':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'star':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    rad += QUARTER_PI;\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'line':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    break;\n  case 'dash':\n    ctx.moveTo(x, y);\n    ctx.lineTo(x + Math.cos(rad) * radius, y + Math.sin(rad) * radius);\n    break;\n  }\n\n  ctx.fill();\n}\n\nfunction calculateLabelSize(ctx, lines, fonts, strokeWidth) {\n  ctx.save();\n  const count = lines.length;\n  let width = 0;\n  let height = strokeWidth;\n  for (let i = 0; i < count; i++) {\n    const font = fonts[Math.min(i, fonts.length - 1)];\n    ctx.font = font.string;\n    const text = lines[i];\n    width = Math.max(width, ctx.measureText(text).width + strokeWidth);\n    height += font.lineHeight;\n  }\n  ctx.restore();\n  return {width, height};\n}\n\nfunction applyLabelDecoration(ctx, {x, y}, labels, fonts) {\n  ctx.beginPath();\n  let lhs = 0;\n  labels.forEach(function(l, i) {\n    const f = fonts[Math.min(i, fonts.length - 1)];\n    const lh = f.lineHeight;\n    ctx.font = f.string;\n    ctx.strokeText(l, x, y + lh / 2 + lhs);\n    lhs += lh;\n  });\n  ctx.stroke();\n}\n\nfunction applyLabelContent(ctx, {x, y}, labels, {fonts, colors}) {\n  let lhs = 0;\n  labels.forEach(function(l, i) {\n    const c = colors[Math.min(i, colors.length - 1)];\n    const f = fonts[Math.min(i, fonts.length - 1)];\n    const lh = f.lineHeight;\n    ctx.beginPath();\n    ctx.font = f.string;\n    ctx.fillStyle = c;\n    ctx.fillText(l, x, y + lh / 2 + lhs);\n    lhs += lh;\n    ctx.fill();\n  });\n}\n\nfunction getOpacity(value, elementValue) {\n  const opacity = isNumber(value) ? value : elementValue;\n  return isNumber(opacity) ? clamp(opacity, 0, 1) : 1;\n}\n\nconst positions = ['left', 'bottom', 'top', 'right'];\n\n/**\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\n/**\n * Drawa the callout component for labels.\n * @param {CanvasRenderingContext2D} ctx - chart canvas context\n * @param {AnnotationElement} element - the label element\n */\nfunction drawCallout(ctx, element) {\n  const {pointX, pointY, options} = element;\n  const callout = options.callout;\n  const calloutPosition = callout && callout.display && resolveCalloutPosition(element, callout);\n  if (!calloutPosition || isPointInRange(element, callout, calloutPosition)) {\n    return;\n  }\n\n  ctx.save();\n  ctx.beginPath();\n  const stroke = setBorderStyle(ctx, callout);\n  if (!stroke) {\n    return ctx.restore();\n  }\n  const {separatorStart, separatorEnd} = getCalloutSeparatorCoord(element, calloutPosition);\n  const {sideStart, sideEnd} = getCalloutSideCoord(element, calloutPosition, separatorStart);\n  if (callout.margin > 0 || options.borderWidth === 0) {\n    ctx.moveTo(separatorStart.x, separatorStart.y);\n    ctx.lineTo(separatorEnd.x, separatorEnd.y);\n  }\n  ctx.moveTo(sideStart.x, sideStart.y);\n  ctx.lineTo(sideEnd.x, sideEnd.y);\n  const rotatedPoint = rotated({x: pointX, y: pointY}, element.getCenterPoint(), toRadians(-element.rotation));\n  ctx.lineTo(rotatedPoint.x, rotatedPoint.y);\n  ctx.stroke();\n  ctx.restore();\n}\n\nfunction getCalloutSeparatorCoord(element, position) {\n  const {x, y, x2, y2} = element;\n  const adjust = getCalloutSeparatorAdjust(element, position);\n  let separatorStart, separatorEnd;\n  if (position === 'left' || position === 'right') {\n    separatorStart = {x: x + adjust, y};\n    separatorEnd = {x: separatorStart.x, y: y2};\n  } else {\n    //  position 'top' or 'bottom'\n    separatorStart = {x, y: y + adjust};\n    separatorEnd = {x: x2, y: separatorStart.y};\n  }\n  return {separatorStart, separatorEnd};\n}\n\nfunction getCalloutSeparatorAdjust(element, position) {\n  const {width, height, options} = element;\n  const adjust = options.callout.margin + options.borderWidth / 2;\n  if (position === 'right') {\n    return width + adjust;\n  } else if (position === 'bottom') {\n    return height + adjust;\n  }\n  return -adjust;\n}\n\nfunction getCalloutSideCoord(element, position, separatorStart) {\n  const {y, width, height, options} = element;\n  const start = options.callout.start;\n  const side = getCalloutSideAdjust(position, options.callout);\n  let sideStart, sideEnd;\n  if (position === 'left' || position === 'right') {\n    sideStart = {x: separatorStart.x, y: y + getSize(height, start)};\n    sideEnd = {x: sideStart.x + side, y: sideStart.y};\n  } else {\n    //  position 'top' or 'bottom'\n    sideStart = {x: separatorStart.x + getSize(width, start), y: separatorStart.y};\n    sideEnd = {x: sideStart.x, y: sideStart.y + side};\n  }\n  return {sideStart, sideEnd};\n}\n\nfunction getCalloutSideAdjust(position, options) {\n  const side = options.side;\n  if (position === 'left' || position === 'top') {\n    return -side;\n  }\n  return side;\n}\n\nfunction resolveCalloutPosition(element, options) {\n  const position = options.position;\n  if (positions.includes(position)) {\n    return position;\n  }\n  return resolveCalloutAutoPosition(element, options);\n}\n\nfunction resolveCalloutAutoPosition(element, options) {\n  const {x, y, x2, y2, width, height, pointX, pointY, centerX, centerY, rotation} = element;\n  const center = {x: centerX, y: centerY};\n  const start = options.start;\n  const xAdjust = getSize(width, start);\n  const yAdjust = getSize(height, start);\n  const xPoints = [x, x + xAdjust, x + xAdjust, x2];\n  const yPoints = [y + yAdjust, y2, y, y2];\n  const result = [];\n  for (let index = 0; index < 4; index++) {\n    const rotatedPoint = rotated({x: xPoints[index], y: yPoints[index]}, center, toRadians(rotation));\n    result.push({\n      position: positions[index],\n      distance: distanceBetweenPoints(rotatedPoint, {x: pointX, y: pointY})\n    });\n  }\n  return result.sort((a, b) => a.distance - b.distance)[0].position;\n}\n\nfunction isPointInRange(element, callout, position) {\n  const {pointX, pointY} = element;\n  const margin = callout.margin;\n  let x = pointX;\n  let y = pointY;\n  if (position === 'left') {\n    x += margin;\n  } else if (position === 'right') {\n    x -= margin;\n  } else if (position === 'top') {\n    y += margin;\n  } else if (position === 'bottom') {\n    y -= margin;\n  }\n  return element.inRange(x, y);\n}\n\nconst limitedLineScale = {\n  xScaleID: {min: 'xMin', max: 'xMax', start: 'left', end: 'right', startProp: 'x', endProp: 'x2'},\n  yScaleID: {min: 'yMin', max: 'yMax', start: 'bottom', end: 'top', startProp: 'y', endProp: 'y2'}\n};\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").Scale } Scale\n * @typedef { import(\"chart.js\").Point } Point\n * @typedef { import('../../types/element').AnnotationBoxModel } AnnotationBoxModel\n * @typedef { import('../../types/options').CoreAnnotationOptions } CoreAnnotationOptions\n * @typedef { import('../../types/options').LineAnnotationOptions } LineAnnotationOptions\n * @typedef { import('../../types/options').PointAnnotationOptions } PointAnnotationOptions\n * @typedef { import('../../types/options').PolygonAnnotationOptions } PolygonAnnotationOptions\n */\n\n/**\n * @param {Scale} scale\n * @param {number|string} value\n * @param {number} fallback\n * @returns {number}\n */\nfunction scaleValue(scale, value, fallback) {\n  value = typeof value === 'number' ? value : scale.parse(value);\n  return isFinite(value) ? scale.getPixelForValue(value) : fallback;\n}\n\n/**\n * Search the scale defined in chartjs by the axis related to the annotation options key.\n * @param {{ [key: string]: Scale }} scales\n * @param {CoreAnnotationOptions} options\n * @param {string} key\n * @returns {string}\n */\nfunction retrieveScaleID(scales, options, key) {\n  const scaleID = options[key];\n  if (scaleID || key === 'scaleID') {\n    return scaleID;\n  }\n  const axis = key.charAt(0);\n  const axes = Object.values(scales).filter((scale) => scale.axis && scale.axis === axis);\n  if (axes.length) {\n    return axes[0].id;\n  }\n  return axis;\n}\n\n/**\n * @param {Scale} scale\n * @param {{min: number, max: number, start: number, end: number}} options\n * @returns {{start: number, end: number}|undefined}\n */\nfunction getDimensionByScale(scale, options) {\n  if (scale) {\n    const reverse = scale.options.reverse;\n    const start = scaleValue(scale, options.min, reverse ? options.end : options.start);\n    const end = scaleValue(scale, options.max, reverse ? options.start : options.end);\n    return {\n      start,\n      end\n    };\n  }\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @returns {Point}\n */\nfunction getChartPoint(chart, options) {\n  const {chartArea, scales} = chart;\n  const xScale = scales[retrieveScaleID(scales, options, 'xScaleID')];\n  const yScale = scales[retrieveScaleID(scales, options, 'yScaleID')];\n  let x = chartArea.width / 2;\n  let y = chartArea.height / 2;\n\n  if (xScale) {\n    x = scaleValue(xScale, options.xValue, xScale.left + xScale.width / 2);\n  }\n\n  if (yScale) {\n    y = scaleValue(yScale, options.yValue, yScale.top + yScale.height / 2);\n  }\n  return {x, y};\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolveBoxProperties(chart, options) {\n  const scales = chart.scales;\n  const xScale = scales[retrieveScaleID(scales, options, 'xScaleID')];\n  const yScale = scales[retrieveScaleID(scales, options, 'yScaleID')];\n\n  if (!xScale && !yScale) {\n    return {};\n  }\n\n  let {left: x, right: x2} = xScale || chart.chartArea;\n  let {top: y, bottom: y2} = yScale || chart.chartArea;\n  const xDim = getChartDimensionByScale(xScale, {min: options.xMin, max: options.xMax, start: x, end: x2});\n  x = xDim.start;\n  x2 = xDim.end;\n  const yDim = getChartDimensionByScale(yScale, {min: options.yMin, max: options.yMax, start: y2, end: y});\n  y = yDim.start;\n  y2 = yDim.end;\n\n  return {\n    x,\n    y,\n    x2,\n    y2,\n    width: x2 - x,\n    height: y2 - y,\n    centerX: x + (x2 - x) / 2,\n    centerY: y + (y2 - y) / 2\n  };\n}\n\n/**\n * @param {Chart} chart\n * @param {PointAnnotationOptions|PolygonAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolvePointProperties(chart, options) {\n  if (!isBoundToPoint(options)) {\n    const box = resolveBoxProperties(chart, options);\n    let radius = options.radius;\n    if (!radius || isNaN(radius)) {\n      radius = Math.min(box.width, box.height) / 2;\n      options.radius = radius;\n    }\n    const size = radius * 2;\n    const adjustCenterX = box.centerX + options.xAdjust;\n    const adjustCenterY = box.centerY + options.yAdjust;\n    return {\n      x: adjustCenterX - radius,\n      y: adjustCenterY - radius,\n      x2: adjustCenterX + radius,\n      y2: adjustCenterY + radius,\n      centerX: adjustCenterX,\n      centerY: adjustCenterY,\n      width: size,\n      height: size,\n      radius\n    };\n  }\n  return getChartCircle(chart, options);\n}\n/**\n * @param {Chart} chart\n * @param {LineAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolveLineProperties(chart, options) {\n  const {scales, chartArea} = chart;\n  const scale = scales[options.scaleID];\n  const area = {x: chartArea.left, y: chartArea.top, x2: chartArea.right, y2: chartArea.bottom};\n\n  if (scale) {\n    resolveFullLineProperties(scale, area, options);\n  } else {\n    resolveLimitedLineProperties(scales, area, options);\n  }\n  return area;\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @param {boolean} [centerBased=false]\n * @returns {AnnotationBoxModel}\n */\nfunction resolveBoxAndLabelProperties(chart, options) {\n  const properties = resolveBoxProperties(chart, options);\n  properties.initProperties = initAnimationProperties(chart, properties, options);\n  properties.elements = [{\n    type: 'label',\n    optionScope: 'label',\n    properties: resolveLabelElementProperties$1(chart, properties, options),\n    initProperties: properties.initProperties\n  }];\n  return properties;\n}\n\nfunction getChartCircle(chart, options) {\n  const point = getChartPoint(chart, options);\n  const size = options.radius * 2;\n  return {\n    x: point.x - options.radius + options.xAdjust,\n    y: point.y - options.radius + options.yAdjust,\n    x2: point.x + options.radius + options.xAdjust,\n    y2: point.y + options.radius + options.yAdjust,\n    centerX: point.x + options.xAdjust,\n    centerY: point.y + options.yAdjust,\n    radius: options.radius,\n    width: size,\n    height: size\n  };\n}\n\nfunction getChartDimensionByScale(scale, options) {\n  const result = getDimensionByScale(scale, options) || options;\n  return {\n    start: Math.min(result.start, result.end),\n    end: Math.max(result.start, result.end)\n  };\n}\n\nfunction resolveFullLineProperties(scale, area, options) {\n  const min = scaleValue(scale, options.value, NaN);\n  const max = scaleValue(scale, options.endValue, min);\n  if (scale.isHorizontal()) {\n    area.x = min;\n    area.x2 = max;\n  } else {\n    area.y = min;\n    area.y2 = max;\n  }\n}\n\nfunction resolveLimitedLineProperties(scales, area, options) {\n  for (const scaleId of Object.keys(limitedLineScale)) {\n    const scale = scales[retrieveScaleID(scales, options, scaleId)];\n    if (scale) {\n      const {min, max, start, end, startProp, endProp} = limitedLineScale[scaleId];\n      const dim = getDimensionByScale(scale, {min: options[min], max: options[max], start: scale[start], end: scale[end]});\n      area[startProp] = dim.start;\n      area[endProp] = dim.end;\n    }\n  }\n}\n\nfunction calculateX({properties, options}, labelSize, position, padding) {\n  const {x: start, x2: end, width: size} = properties;\n  return calculatePosition({start, end, size, borderWidth: options.borderWidth}, {\n    position: position.x,\n    padding: {start: padding.left, end: padding.right},\n    adjust: options.label.xAdjust,\n    size: labelSize.width\n  });\n}\n\nfunction calculateY({properties, options}, labelSize, position, padding) {\n  const {y: start, y2: end, height: size} = properties;\n  return calculatePosition({start, end, size, borderWidth: options.borderWidth}, {\n    position: position.y,\n    padding: {start: padding.top, end: padding.bottom},\n    adjust: options.label.yAdjust,\n    size: labelSize.height\n  });\n}\n\nfunction calculatePosition(boxOpts, labelOpts) {\n  const {start, end, borderWidth} = boxOpts;\n  const {position, padding: {start: padStart, end: padEnd}, adjust} = labelOpts;\n  const availableSize = end - borderWidth - start - padStart - padEnd - labelOpts.size;\n  return start + borderWidth / 2 + adjust + getRelativePosition(availableSize, position);\n}\n\nfunction resolveLabelElementProperties$1(chart, properties, options) {\n  const label = options.label;\n  label.backgroundColor = 'transparent';\n  label.callout.display = false;\n  const position = toPosition(label.position);\n  const padding = toPadding(label.padding);\n  const labelSize = measureLabelSize(chart.ctx, label);\n  const x = calculateX({properties, options}, labelSize, position, padding);\n  const y = calculateY({properties, options}, labelSize, position, padding);\n  const width = labelSize.width + padding.width;\n  const height = labelSize.height + padding.height;\n  return {\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n    width,\n    height,\n    centerX: x + width / 2,\n    centerY: y + height / 2,\n    rotation: label.rotation\n  };\n\n}\n\nconst moveHooks = ['enter', 'leave'];\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n */\n\nconst eventHooks = moveHooks.concat('click');\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n */\nfunction updateListeners(chart, state, options) {\n  state.listened = loadHooks(options, eventHooks, state.listeners);\n  state.moveListened = false;\n\n  moveHooks.forEach(hook => {\n    if (isFunction(options[hook])) {\n      state.moveListened = true;\n    }\n  });\n\n  if (!state.listened || !state.moveListened) {\n    state.annotations.forEach(scope => {\n      if (!state.listened && isFunction(scope.click)) {\n        state.listened = true;\n      }\n      if (!state.moveListened) {\n        moveHooks.forEach(hook => {\n          if (isFunction(scope[hook])) {\n            state.listened = true;\n            state.moveListened = true;\n          }\n        });\n      }\n    });\n  }\n}\n\n/**\n * @param {Object} state\n * @param {ChartEvent} event\n * @param {AnnotationPluginOptions} options\n * @return {boolean|undefined}\n */\nfunction handleEvent(state, event, options) {\n  if (state.listened) {\n    switch (event.type) {\n    case 'mousemove':\n    case 'mouseout':\n      return handleMoveEvents(state, event, options);\n    case 'click':\n      return handleClickEvents(state, event, options);\n    }\n  }\n}\n\nfunction handleMoveEvents(state, event, options) {\n  if (!state.moveListened) {\n    return;\n  }\n\n  let elements;\n\n  if (event.type === 'mousemove') {\n    elements = getElements(state.visibleElements, event, options.interaction);\n  } else {\n    elements = [];\n  }\n\n  const previous = state.hovered;\n  state.hovered = elements;\n\n  const context = {state, event};\n  let changed = dispatchMoveEvents(context, 'leave', previous, elements);\n  return dispatchMoveEvents(context, 'enter', elements, previous) || changed;\n}\n\nfunction dispatchMoveEvents({state, event}, hook, elements, checkElements) {\n  let changed;\n  for (const element of elements) {\n    if (checkElements.indexOf(element) < 0) {\n      changed = dispatchEvent(element.options[hook] || state.listeners[hook], element, event) || changed;\n    }\n  }\n  return changed;\n}\n\nfunction handleClickEvents(state, event, options) {\n  const listeners = state.listeners;\n  const elements = getElements(state.visibleElements, event, options.interaction);\n  let changed;\n  for (const element of elements) {\n    changed = dispatchEvent(element.options.click || listeners.click, element, event) || changed;\n  }\n  return changed;\n}\n\nfunction dispatchEvent(handler, element, event) {\n  return callback(handler, [element.$context, event]) === true;\n}\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst elementHooks = ['afterDraw', 'beforeDraw'];\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n */\nfunction updateHooks(chart, state, options) {\n  const visibleElements = state.visibleElements;\n  state.hooked = loadHooks(options, elementHooks, state.hooks);\n\n  if (!state.hooked) {\n    visibleElements.forEach(scope => {\n      if (!state.hooked) {\n        elementHooks.forEach(hook => {\n          if (isFunction(scope.options[hook])) {\n            state.hooked = true;\n          }\n        });\n      }\n    });\n  }\n}\n\n/**\n * @param {Object} state\n * @param {AnnotationElement} element\n * @param {string} hook\n */\nfunction invokeHook(state, element, hook) {\n  if (state.hooked) {\n    const callbackHook = element.options[hook] || state.hooks[hook];\n    return callback(callbackHook, [element.$context]);\n  }\n}\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").Scale } Scale\n * @typedef { import('../../types/options').CoreAnnotationOptions } CoreAnnotationOptions\n */\n\n/**\n * @param {Chart} chart\n * @param {Scale} scale\n * @param {CoreAnnotationOptions[]} annotations\n */\nfunction adjustScaleRange(chart, scale, annotations) {\n  const range = getScaleLimits(chart.scales, scale, annotations);\n  let changed = changeScaleLimit(scale, range, 'min', 'suggestedMin');\n  changed = changeScaleLimit(scale, range, 'max', 'suggestedMax') || changed;\n  if (changed && isFunction(scale.handleTickRangeOptions)) {\n    scale.handleTickRangeOptions();\n  }\n}\n\n/**\n * @param {CoreAnnotationOptions[]} annotations\n * @param {{ [key: string]: Scale }} scales\n */\nfunction verifyScaleOptions(annotations, scales) {\n  for (const annotation of annotations) {\n    verifyScaleIDs(annotation, scales);\n  }\n}\n\nfunction changeScaleLimit(scale, range, limit, suggestedLimit) {\n  if (isFinite(range[limit]) && !scaleLimitDefined(scale.options, limit, suggestedLimit)) {\n    const changed = scale[limit] !== range[limit];\n    scale[limit] = range[limit];\n    return changed;\n  }\n}\n\nfunction scaleLimitDefined(scaleOptions, limit, suggestedLimit) {\n  return defined(scaleOptions[limit]) || defined(scaleOptions[suggestedLimit]);\n}\n\nfunction verifyScaleIDs(annotation, scales) {\n  for (const key of ['scaleID', 'xScaleID', 'yScaleID']) {\n    const scaleID = retrieveScaleID(scales, annotation, key);\n    if (scaleID && !scales[scaleID] && verifyProperties(annotation, key)) {\n      console.warn(`No scale found with id '${scaleID}' for annotation '${annotation.id}'`);\n    }\n  }\n}\n\nfunction verifyProperties(annotation, key) {\n  if (key === 'scaleID') {\n    return true;\n  }\n  const axis = key.charAt(0);\n  for (const prop of ['Min', 'Max', 'Value']) {\n    if (defined(annotation[axis + prop])) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction getScaleLimits(scales, scale, annotations) {\n  const axis = scale.axis;\n  const scaleID = scale.id;\n  const scaleIDOption = axis + 'ScaleID';\n  const limits = {\n    min: valueOrDefault(scale.min, Number.NEGATIVE_INFINITY),\n    max: valueOrDefault(scale.max, Number.POSITIVE_INFINITY)\n  };\n  for (const annotation of annotations) {\n    if (annotation.scaleID === scaleID) {\n      updateLimits(annotation, scale, ['value', 'endValue'], limits);\n    } else if (retrieveScaleID(scales, annotation, scaleIDOption) === scaleID) {\n      updateLimits(annotation, scale, [axis + 'Min', axis + 'Max', axis + 'Value'], limits);\n    }\n  }\n  return limits;\n}\n\nfunction updateLimits(annotation, scale, props, limits) {\n  for (const prop of props) {\n    const raw = annotation[prop];\n    if (defined(raw)) {\n      const value = scale.parse(raw);\n      limits.min = Math.min(limits.min, value);\n      limits.max = Math.max(limits.max, value);\n    }\n  }\n}\n\nclass BoxAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const {x, y} = rotated({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), toRadians(-this.options.rotation));\n    return inBoxRange({x, y}, this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), axis, this.options);\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.options.rotation);\n    drawBox(ctx, this, this.options);\n    ctx.restore();\n  }\n\n  get label() {\n    return this.elements && this.elements[0];\n  }\n\n  resolveElementProperties(chart, options) {\n    return resolveBoxAndLabelProperties(chart, options);\n  }\n}\n\nBoxAnnotation.id = 'boxAnnotation';\n\nBoxAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderRadius: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  init: undefined,\n  hitTolerance: 0,\n  label: {\n    backgroundColor: 'transparent',\n    borderWidth: 0,\n    callout: {\n      display: false\n    },\n    color: 'black',\n    content: null,\n    display: false,\n    drawTime: undefined,\n    font: {\n      family: undefined,\n      lineHeight: undefined,\n      size: undefined,\n      style: undefined,\n      weight: 'bold'\n    },\n    height: undefined,\n    hitTolerance: undefined,\n    opacity: undefined,\n    padding: 6,\n    position: 'center',\n    rotation: undefined,\n    textAlign: 'start',\n    textStrokeColor: undefined,\n    textStrokeWidth: 0,\n    width: undefined,\n    xAdjust: 0,\n    yAdjust: 0,\n    z: undefined\n  },\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\n\nBoxAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nBoxAnnotation.descriptors = {\n  label: {\n    _fallback: true\n  }\n};\n\nclass DoughnutLabelAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    return inLabelRange(\n      {x: mouseX, y: mouseY},\n      {rect: this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), center: this.getCenterPoint(useFinalPosition)},\n      axis,\n      {rotation: this.rotation, borderWidth: 0, hitTolerance: this.options.hitTolerance}\n    );\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const options = this.options;\n    if (!options.display || !options.content) {\n      return;\n    }\n    drawBackground(ctx, this);\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.rotation);\n    drawLabel(ctx, this, options, this._fitRatio);\n    ctx.restore();\n  }\n\n  resolveElementProperties(chart, options) {\n    const meta = getDatasetMeta(chart, options);\n    if (!meta) {\n      return {};\n    }\n    const {controllerMeta, point, radius} = getControllerMeta(chart, options, meta);\n    let labelSize = measureLabelSize(chart.ctx, options);\n    const _fitRatio = getFitRatio(labelSize, radius);\n    if (shouldFit(options, _fitRatio)) {\n      labelSize = {width: labelSize.width * _fitRatio, height: labelSize.height * _fitRatio};\n    }\n    const {position, xAdjust, yAdjust} = options;\n    const boxSize = measureLabelRectangle(point, labelSize, {borderWidth: 0, position, xAdjust, yAdjust});\n    return {\n      initProperties: initAnimationProperties(chart, boxSize, options),\n      ...boxSize,\n      ...controllerMeta,\n      rotation: options.rotation,\n      _fitRatio\n    };\n  }\n}\n\nDoughnutLabelAnnotation.id = 'doughnutLabelAnnotation';\n\nDoughnutLabelAnnotation.defaults = {\n  autoFit: true,\n  autoHide: true,\n  backgroundColor: 'transparent',\n  backgroundShadowColor: 'transparent',\n  borderColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderShadowColor: 'transparent',\n  borderWidth: 0,\n  color: 'black',\n  content: null,\n  display: true,\n  font: {\n    family: undefined,\n    lineHeight: undefined,\n    size: undefined,\n    style: undefined,\n    weight: undefined\n  },\n  height: undefined,\n  hitTolerance: 0,\n  init: undefined,\n  opacity: undefined,\n  position: 'center',\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  spacing: 1,\n  textAlign: 'center',\n  textStrokeColor: undefined,\n  textStrokeWidth: 0,\n  width: undefined,\n  xAdjust: 0,\n  yAdjust: 0\n};\n\nDoughnutLabelAnnotation.defaultRoutes = {\n};\n\nfunction getDatasetMeta(chart, options) {\n  return chart.getSortedVisibleDatasetMetas().reduce(function(result, value) {\n    const controller = value.controller;\n    if (controller instanceof DoughnutController &&\n      isControllerVisible(chart, options, value.data) &&\n      (!result || controller.innerRadius < result.controller.innerRadius) &&\n      controller.options.circumference >= 90) {\n      return value;\n    }\n    return result;\n  }, undefined);\n}\n\nfunction isControllerVisible(chart, options, elements) {\n  if (!options.autoHide) {\n    return true;\n  }\n  for (let i = 0; i < elements.length; i++) {\n    if (!elements[i].hidden && chart.getDataVisibility(i)) {\n      return true;\n    }\n  }\n}\n\nfunction getControllerMeta({chartArea}, options, meta) {\n  const {left, top, right, bottom} = chartArea;\n  const {innerRadius, offsetX, offsetY} = meta.controller;\n  const x = (left + right) / 2 + offsetX;\n  const y = (top + bottom) / 2 + offsetY;\n  const square = {\n    left: Math.max(x - innerRadius, left),\n    right: Math.min(x + innerRadius, right),\n    top: Math.max(y - innerRadius, top),\n    bottom: Math.min(y + innerRadius, bottom)\n  };\n  const point = {\n    x: (square.left + square.right) / 2,\n    y: (square.top + square.bottom) / 2\n  };\n  const space = options.spacing + options.borderWidth / 2;\n  const _radius = innerRadius - space;\n  const _counterclockwise = point.y > y;\n  const side = _counterclockwise ? top + space : bottom - space;\n  const angles = getAngles(side, x, y, _radius);\n  const controllerMeta = {\n    _centerX: x,\n    _centerY: y,\n    _radius,\n    _counterclockwise,\n    ...angles\n  };\n  return {\n    controllerMeta,\n    point,\n    radius: Math.min(innerRadius, Math.min(square.right - square.left, square.bottom - square.top) / 2)\n  };\n}\n\nfunction getFitRatio({width, height}, radius) {\n  const hypo = Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2));\n  return (radius * 2) / hypo;\n}\n\nfunction getAngles(y, centerX, centerY, radius) {\n  const yk2 = Math.pow(centerY - y, 2);\n  const r2 = Math.pow(radius, 2);\n  const b = centerX * -2;\n  const c = Math.pow(centerX, 2) + yk2 - r2;\n  const delta = Math.pow(b, 2) - (4 * c);\n  if (delta <= 0) {\n    return {\n      _startAngle: 0,\n      _endAngle: TAU\n    };\n  }\n  const start = (-b - Math.sqrt(delta)) / 2;\n  const end = (-b + Math.sqrt(delta)) / 2;\n  return {\n    _startAngle: getAngleFromPoint({x: centerX, y: centerY}, {x: start, y}).angle,\n    _endAngle: getAngleFromPoint({x: centerX, y: centerY}, {x: end, y}).angle\n  };\n}\n\nfunction drawBackground(ctx, element) {\n  const {_centerX, _centerY, _radius, _startAngle, _endAngle, _counterclockwise, options} = element;\n  ctx.save();\n  const stroke = setBorderStyle(ctx, options);\n  ctx.fillStyle = options.backgroundColor;\n  ctx.beginPath();\n  ctx.arc(_centerX, _centerY, _radius, _startAngle, _endAngle, _counterclockwise);\n  ctx.closePath();\n  ctx.fill();\n  if (stroke) {\n    ctx.stroke();\n  }\n  ctx.restore();\n}\n\nclass LabelAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    return inLabelRange(\n      {x: mouseX, y: mouseY},\n      {rect: this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), center: this.getCenterPoint(useFinalPosition)},\n      axis,\n      {rotation: this.rotation, borderWidth: this.options.borderWidth, hitTolerance: this.options.hitTolerance}\n    );\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const options = this.options;\n    const visible = !defined(this._visible) || this._visible;\n    if (!options.display || !options.content || !visible) {\n      return;\n    }\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.rotation);\n    drawCallout(ctx, this);\n    drawBox(ctx, this, options);\n    drawLabel(ctx, getLabelSize(this), options);\n    ctx.restore();\n  }\n\n  resolveElementProperties(chart, options) {\n    let point;\n    if (!isBoundToPoint(options)) {\n      const {centerX, centerY} = resolveBoxProperties(chart, options);\n      point = {x: centerX, y: centerY};\n    } else {\n      point = getChartPoint(chart, options);\n    }\n    const padding = toPadding(options.padding);\n    const labelSize = measureLabelSize(chart.ctx, options);\n    const boxSize = measureLabelRectangle(point, labelSize, options, padding);\n    return {\n      initProperties: initAnimationProperties(chart, boxSize, options),\n      pointX: point.x,\n      pointY: point.y,\n      ...boxSize,\n      rotation: options.rotation\n    };\n  }\n}\n\nLabelAnnotation.id = 'labelAnnotation';\n\nLabelAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundColor: 'transparent',\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderRadius: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 0,\n  callout: {\n    borderCapStyle: 'butt',\n    borderColor: undefined,\n    borderDash: [],\n    borderDashOffset: 0,\n    borderJoinStyle: 'miter',\n    borderWidth: 1,\n    display: false,\n    margin: 5,\n    position: 'auto',\n    side: 5,\n    start: '50%',\n  },\n  color: 'black',\n  content: null,\n  display: true,\n  font: {\n    family: undefined,\n    lineHeight: undefined,\n    size: undefined,\n    style: undefined,\n    weight: undefined\n  },\n  height: undefined,\n  hitTolerance: 0,\n  init: undefined,\n  opacity: undefined,\n  padding: 6,\n  position: 'center',\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  textAlign: 'center',\n  textStrokeColor: undefined,\n  textStrokeWidth: 0,\n  width: undefined,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\n\nLabelAnnotation.defaultRoutes = {\n  borderColor: 'color'\n};\n\nfunction getLabelSize({x, y, width, height, options}) {\n  const hBorderWidth = options.borderWidth / 2;\n  const padding = toPadding(options.padding);\n  return {\n    x: x + padding.left + hBorderWidth,\n    y: y + padding.top + hBorderWidth,\n    width: width - padding.left - padding.right - options.borderWidth,\n    height: height - padding.top - padding.bottom - options.borderWidth\n  };\n}\n\nconst pointInLine = (p1, p2, t) => ({x: p1.x + t * (p2.x - p1.x), y: p1.y + t * (p2.y - p1.y)});\nconst interpolateX = (y, p1, p2) => pointInLine(p1, p2, Math.abs((y - p1.y) / (p2.y - p1.y))).x;\nconst interpolateY = (x, p1, p2) => pointInLine(p1, p2, Math.abs((x - p1.x) / (p2.x - p1.x))).y;\nconst sqr = v => v * v;\nconst rangeLimit = (mouseX, mouseY, {x, y, x2, y2}, axis) => axis === 'y' ? {start: Math.min(y, y2), end: Math.max(y, y2), value: mouseY} : {start: Math.min(x, x2), end: Math.max(x, x2), value: mouseX};\n// http://www.independent-software.com/determining-coordinates-on-a-html-canvas-bezier-curve.html\nconst coordInCurve = (start, cp, end, t) => (1 - t) * (1 - t) * start + 2 * (1 - t) * t * cp + t * t * end;\nconst pointInCurve = (start, cp, end, t) => ({x: coordInCurve(start.x, cp.x, end.x, t), y: coordInCurve(start.y, cp.y, end.y, t)});\nconst coordAngleInCurve = (start, cp, end, t) => 2 * (1 - t) * (cp - start) + 2 * t * (end - cp);\nconst angleInCurve = (start, cp, end, t) => -Math.atan2(coordAngleInCurve(start.x, cp.x, end.x, t), coordAngleInCurve(start.y, cp.y, end.y, t)) + 0.5 * PI;\n\nclass LineAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      const point = {mouseX, mouseY};\n      const {path, ctx} = this;\n      if (path) {\n        setBorderStyle(ctx, this.options);\n        ctx.lineWidth += this.options.hitTolerance;\n        const {chart} = this.$context;\n        const mx = mouseX * chart.currentDevicePixelRatio;\n        const my = mouseY * chart.currentDevicePixelRatio;\n        const result = ctx.isPointInStroke(path, mx, my) || isOnLabel(this, point, useFinalPosition);\n        ctx.restore();\n        return result;\n      }\n      const epsilon = sqr(hitSize);\n      return intersects(this, point, epsilon, useFinalPosition) || isOnLabel(this, point, useFinalPosition);\n    }\n    return inAxisRange(this, {mouseX, mouseY}, axis, {hitSize, useFinalPosition});\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const {x, y, x2, y2, cp, options} = this;\n\n    ctx.save();\n    if (!setBorderStyle(ctx, options)) {\n      // no border width, then line is not drawn\n      return ctx.restore();\n    }\n    setShadowStyle(ctx, options);\n\n    const length = Math.sqrt(Math.pow(x2 - x, 2) + Math.pow(y2 - y, 2));\n    if (options.curve && cp) {\n      drawCurve(ctx, this, cp, length);\n      return ctx.restore();\n    }\n    const {startOpts, endOpts, startAdjust, endAdjust} = getArrowHeads(this);\n    const angle = Math.atan2(y2 - y, x2 - x);\n    ctx.translate(x, y);\n    ctx.rotate(angle);\n    ctx.beginPath();\n    ctx.moveTo(0 + startAdjust, 0);\n    ctx.lineTo(length - endAdjust, 0);\n    ctx.shadowColor = options.borderShadowColor;\n    ctx.stroke();\n    drawArrowHead(ctx, 0, startAdjust, startOpts);\n    drawArrowHead(ctx, length, -endAdjust, endOpts);\n    ctx.restore();\n  }\n\n  get label() {\n    return this.elements && this.elements[0];\n  }\n\n  resolveElementProperties(chart, options) {\n    const area = resolveLineProperties(chart, options);\n    const {x, y, x2, y2} = area;\n    const inside = isLineInArea(area, chart.chartArea);\n    const properties = inside\n      ? limitLineToArea({x, y}, {x: x2, y: y2}, chart.chartArea)\n      : {x, y, x2, y2, width: Math.abs(x2 - x), height: Math.abs(y2 - y)};\n    properties.centerX = (x2 + x) / 2;\n    properties.centerY = (y2 + y) / 2;\n    properties.initProperties = initAnimationProperties(chart, properties, options);\n    if (options.curve) {\n      const p1 = {x: properties.x, y: properties.y};\n      const p2 = {x: properties.x2, y: properties.y2};\n      properties.cp = getControlPoint(properties, options, distanceBetweenPoints(p1, p2));\n    }\n    const labelProperties = resolveLabelElementProperties(chart, properties, options.label);\n    // additonal prop to manage zoom/pan\n    labelProperties._visible = inside;\n\n    properties.elements = [{\n      type: 'label',\n      optionScope: 'label',\n      properties: labelProperties,\n      initProperties: properties.initProperties\n    }];\n    return properties;\n  }\n}\n\nLineAnnotation.id = 'lineAnnotation';\n\nconst arrowHeadsDefaults = {\n  backgroundColor: undefined,\n  backgroundShadowColor: undefined,\n  borderColor: undefined,\n  borderDash: undefined,\n  borderDashOffset: undefined,\n  borderShadowColor: undefined,\n  borderWidth: undefined,\n  display: undefined,\n  fill: undefined,\n  length: undefined,\n  shadowBlur: undefined,\n  shadowOffsetX: undefined,\n  shadowOffsetY: undefined,\n  width: undefined\n};\n\nLineAnnotation.defaults = {\n  adjustScaleRange: true,\n  arrowHeads: {\n    display: false,\n    end: Object.assign({}, arrowHeadsDefaults),\n    fill: false,\n    length: 12,\n    start: Object.assign({}, arrowHeadsDefaults),\n    width: 6\n  },\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 2,\n  curve: false,\n  controlPoint: {\n    y: '-50%'\n  },\n  display: true,\n  endValue: undefined,\n  init: undefined,\n  hitTolerance: 0,\n  label: {\n    backgroundColor: 'rgba(0,0,0,0.8)',\n    backgroundShadowColor: 'transparent',\n    borderCapStyle: 'butt',\n    borderColor: 'black',\n    borderDash: [],\n    borderDashOffset: 0,\n    borderJoinStyle: 'miter',\n    borderRadius: 6,\n    borderShadowColor: 'transparent',\n    borderWidth: 0,\n    callout: Object.assign({}, LabelAnnotation.defaults.callout),\n    color: '#fff',\n    content: null,\n    display: false,\n    drawTime: undefined,\n    font: {\n      family: undefined,\n      lineHeight: undefined,\n      size: undefined,\n      style: undefined,\n      weight: 'bold'\n    },\n    height: undefined,\n    hitTolerance: undefined,\n    opacity: undefined,\n    padding: 6,\n    position: 'center',\n    rotation: 0,\n    shadowBlur: 0,\n    shadowOffsetX: 0,\n    shadowOffsetY: 0,\n    textAlign: 'center',\n    textStrokeColor: undefined,\n    textStrokeWidth: 0,\n    width: undefined,\n    xAdjust: 0,\n    yAdjust: 0,\n    z: undefined\n  },\n  scaleID: undefined,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  value: undefined,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\n\nLineAnnotation.descriptors = {\n  arrowHeads: {\n    start: {\n      _fallback: true\n    },\n    end: {\n      _fallback: true\n    },\n    _fallback: true\n  }\n};\n\nLineAnnotation.defaultRoutes = {\n  borderColor: 'color'\n};\n\nfunction inAxisRange(element, {mouseX, mouseY}, axis, {hitSize, useFinalPosition}) {\n  const limit = rangeLimit(mouseX, mouseY, element.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), axis);\n  return inLimit(limit, hitSize) || isOnLabel(element, {mouseX, mouseY}, useFinalPosition, axis);\n}\n\nfunction isLineInArea({x, y, x2, y2}, {top, right, bottom, left}) {\n  return !(\n    (x < left && x2 < left) ||\n    (x > right && x2 > right) ||\n    (y < top && y2 < top) ||\n    (y > bottom && y2 > bottom)\n  );\n}\n\nfunction limitPointToArea({x, y}, p2, {top, right, bottom, left}) {\n  if (x < left) {\n    y = interpolateY(left, {x, y}, p2);\n    x = left;\n  }\n  if (x > right) {\n    y = interpolateY(right, {x, y}, p2);\n    x = right;\n  }\n  if (y < top) {\n    x = interpolateX(top, {x, y}, p2);\n    y = top;\n  }\n  if (y > bottom) {\n    x = interpolateX(bottom, {x, y}, p2);\n    y = bottom;\n  }\n  return {x, y};\n}\n\nfunction limitLineToArea(p1, p2, area) {\n  const {x, y} = limitPointToArea(p1, p2, area);\n  const {x: x2, y: y2} = limitPointToArea(p2, p1, area);\n  return {x, y, x2, y2, width: Math.abs(x2 - x), height: Math.abs(y2 - y)};\n}\n\nfunction intersects(element, {mouseX, mouseY}, epsilon = EPSILON, useFinalPosition) {\n  // Adapted from https://stackoverflow.com/a/6853926/25507\n  const {x: x1, y: y1, x2, y2} = element.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition);\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  const lenSq = sqr(dx) + sqr(dy);\n  const t = lenSq === 0 ? -1 : ((mouseX - x1) * dx + (mouseY - y1) * dy) / lenSq;\n\n  let xx, yy;\n  if (t < 0) {\n    xx = x1;\n    yy = y1;\n  } else if (t > 1) {\n    xx = x2;\n    yy = y2;\n  } else {\n    xx = x1 + t * dx;\n    yy = y1 + t * dy;\n  }\n  return (sqr(mouseX - xx) + sqr(mouseY - yy)) <= epsilon;\n}\n\nfunction isOnLabel(element, {mouseX, mouseY}, useFinalPosition, axis) {\n  const label = element.label;\n  return label.options.display && label.inRange(mouseX, mouseY, axis, useFinalPosition);\n}\n\nfunction resolveLabelElementProperties(chart, properties, options) {\n  const borderWidth = options.borderWidth;\n  const padding = toPadding(options.padding);\n  const textSize = measureLabelSize(chart.ctx, options);\n  const width = textSize.width + padding.width + borderWidth;\n  const height = textSize.height + padding.height + borderWidth;\n  return calculateLabelPosition(properties, options, {width, height, padding}, chart.chartArea);\n}\n\nfunction calculateAutoRotation(properties) {\n  const {x, y, x2, y2} = properties;\n  const rotation = Math.atan2(y2 - y, x2 - x);\n  // Flip the rotation if it goes > PI/2 or < -PI/2, so label stays upright\n  return rotation > PI / 2 ? rotation - PI : rotation < PI / -2 ? rotation + PI : rotation;\n}\n\nfunction calculateLabelPosition(properties, label, sizes, chartArea) {\n  const {width, height, padding} = sizes;\n  const {xAdjust, yAdjust} = label;\n  const p1 = {x: properties.x, y: properties.y};\n  const p2 = {x: properties.x2, y: properties.y2};\n  const rotation = label.rotation === 'auto' ? calculateAutoRotation(properties) : toRadians(label.rotation);\n  const size = rotatedSize(width, height, rotation);\n  const t = calculateT(properties, label, {labelSize: size, padding}, chartArea);\n  const pt = properties.cp ? pointInCurve(p1, properties.cp, p2, t) : pointInLine(p1, p2, t);\n  const xCoordinateSizes = {size: size.w, min: chartArea.left, max: chartArea.right, padding: padding.left};\n  const yCoordinateSizes = {size: size.h, min: chartArea.top, max: chartArea.bottom, padding: padding.top};\n  const centerX = adjustLabelCoordinate(pt.x, xCoordinateSizes) + xAdjust;\n  const centerY = adjustLabelCoordinate(pt.y, yCoordinateSizes) + yAdjust;\n  return {\n    x: centerX - (width / 2),\n    y: centerY - (height / 2),\n    x2: centerX + (width / 2),\n    y2: centerY + (height / 2),\n    centerX,\n    centerY,\n    pointX: pt.x,\n    pointY: pt.y,\n    width,\n    height,\n    rotation: toDegrees(rotation)\n  };\n}\n\nfunction rotatedSize(width, height, rotation) {\n  const cos = Math.cos(rotation);\n  const sin = Math.sin(rotation);\n  return {\n    w: Math.abs(width * cos) + Math.abs(height * sin),\n    h: Math.abs(width * sin) + Math.abs(height * cos)\n  };\n}\n\nfunction calculateT(properties, label, sizes, chartArea) {\n  let t;\n  const space = spaceAround(properties, chartArea);\n  if (label.position === 'start') {\n    t = calculateTAdjust({w: properties.x2 - properties.x, h: properties.y2 - properties.y}, sizes, label, space);\n  } else if (label.position === 'end') {\n    t = 1 - calculateTAdjust({w: properties.x - properties.x2, h: properties.y - properties.y2}, sizes, label, space);\n  } else {\n    t = getRelativePosition(1, label.position);\n  }\n  return t;\n}\n\nfunction calculateTAdjust(lineSize, sizes, label, space) {\n  const {labelSize, padding} = sizes;\n  const lineW = lineSize.w * space.dx;\n  const lineH = lineSize.h * space.dy;\n  const x = (lineW > 0) && ((labelSize.w / 2 + padding.left - space.x) / lineW);\n  const y = (lineH > 0) && ((labelSize.h / 2 + padding.top - space.y) / lineH);\n  return clamp(Math.max(x, y), 0, 0.25);\n}\n\nfunction spaceAround(properties, chartArea) {\n  const {x, x2, y, y2} = properties;\n  const t = Math.min(y, y2) - chartArea.top;\n  const l = Math.min(x, x2) - chartArea.left;\n  const b = chartArea.bottom - Math.max(y, y2);\n  const r = chartArea.right - Math.max(x, x2);\n  return {\n    x: Math.min(l, r),\n    y: Math.min(t, b),\n    dx: l <= r ? 1 : -1,\n    dy: t <= b ? 1 : -1\n  };\n}\n\nfunction adjustLabelCoordinate(coordinate, labelSizes) {\n  const {size, min, max, padding} = labelSizes;\n  const halfSize = size / 2;\n  if (size > max - min) {\n    // if it does not fit, display as much as possible\n    return (max + min) / 2;\n  }\n  if (min >= (coordinate - padding - halfSize)) {\n    coordinate = min + padding + halfSize;\n  }\n  if (max <= (coordinate + padding + halfSize)) {\n    coordinate = max - padding - halfSize;\n  }\n  return coordinate;\n}\n\nfunction getArrowHeads(line) {\n  const options = line.options;\n  const arrowStartOpts = options.arrowHeads && options.arrowHeads.start;\n  const arrowEndOpts = options.arrowHeads && options.arrowHeads.end;\n  return {\n    startOpts: arrowStartOpts,\n    endOpts: arrowEndOpts,\n    startAdjust: getLineAdjust(line, arrowStartOpts),\n    endAdjust: getLineAdjust(line, arrowEndOpts)\n  };\n}\n\nfunction getLineAdjust(line, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return 0;\n  }\n  const {length, width} = arrowOpts;\n  const adjust = line.options.borderWidth / 2;\n  const p1 = {x: length, y: width + adjust};\n  const p2 = {x: 0, y: adjust};\n  return Math.abs(interpolateX(0, p1, p2));\n}\n\nfunction drawArrowHead(ctx, offset, adjust, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return;\n  }\n  const {length, width, fill, backgroundColor, borderColor} = arrowOpts;\n  const arrowOffsetX = Math.abs(offset - length) + adjust;\n  ctx.beginPath();\n  setShadowStyle(ctx, arrowOpts);\n  setBorderStyle(ctx, arrowOpts);\n  ctx.moveTo(arrowOffsetX, -width);\n  ctx.lineTo(offset + adjust, 0);\n  ctx.lineTo(arrowOffsetX, width);\n  if (fill === true) {\n    ctx.fillStyle = backgroundColor || borderColor;\n    ctx.closePath();\n    ctx.fill();\n    ctx.shadowColor = 'transparent';\n  } else {\n    ctx.shadowColor = arrowOpts.borderShadowColor;\n  }\n  ctx.stroke();\n}\n\nfunction getControlPoint(properties, options, distance) {\n  const {x, y, x2, y2, centerX, centerY} = properties;\n  const angle = Math.atan2(y2 - y, x2 - x);\n  const cp = toPosition(options.controlPoint, 0);\n  const point = {\n    x: centerX + getSize(distance, cp.x, false),\n    y: centerY + getSize(distance, cp.y, false)\n  };\n  return rotated(point, {x: centerX, y: centerY}, angle);\n}\n\nfunction drawArrowHeadOnCurve(ctx, {x, y}, {angle, adjust}, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return;\n  }\n  ctx.save();\n  ctx.translate(x, y);\n  ctx.rotate(angle);\n  drawArrowHead(ctx, 0, -adjust, arrowOpts);\n  ctx.restore();\n}\n\nfunction drawCurve(ctx, element, cp, length) {\n  const {x, y, x2, y2, options} = element;\n  const {startOpts, endOpts, startAdjust, endAdjust} = getArrowHeads(element);\n  const p1 = {x, y};\n  const p2 = {x: x2, y: y2};\n  const startAngle = angleInCurve(p1, cp, p2, 0);\n  const endAngle = angleInCurve(p1, cp, p2, 1) - PI;\n  const ps = pointInCurve(p1, cp, p2, startAdjust / length);\n  const pe = pointInCurve(p1, cp, p2, 1 - endAdjust / length);\n\n  const path = new Path2D();\n  ctx.beginPath();\n  path.moveTo(ps.x, ps.y);\n  path.quadraticCurveTo(cp.x, cp.y, pe.x, pe.y);\n  ctx.shadowColor = options.borderShadowColor;\n  ctx.stroke(path);\n  element.path = path;\n  element.ctx = ctx;\n  drawArrowHeadOnCurve(ctx, ps, {angle: startAngle, adjust: startAdjust}, startOpts);\n  drawArrowHeadOnCurve(ctx, pe, {angle: endAngle, adjust: endAdjust}, endOpts);\n}\n\nclass EllipseAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const rotation = this.options.rotation;\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      return pointInEllipse({x: mouseX, y: mouseY}, this.getProps(['width', 'height', 'centerX', 'centerY'], useFinalPosition), rotation, hitSize);\n    }\n    const {x, y, x2, y2} = this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition);\n    const limit = axis === 'y' ? {start: y, end: y2} : {start: x, end: x2};\n    const rotatedPoint = rotated({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), toRadians(-rotation));\n    return rotatedPoint[axis] >= limit.start - hitSize - EPSILON && rotatedPoint[axis] <= limit.end + hitSize + EPSILON;\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const {width, height, centerX, centerY, options} = this;\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), options.rotation);\n    setShadowStyle(ctx, this.options);\n    ctx.beginPath();\n    ctx.fillStyle = options.backgroundColor;\n    const stroke = setBorderStyle(ctx, options);\n    ctx.ellipse(centerX, centerY, height / 2, width / 2, PI / 2, 0, 2 * PI);\n    ctx.fill();\n    if (stroke) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n  }\n\n  get label() {\n    return this.elements && this.elements[0];\n  }\n\n  resolveElementProperties(chart, options) {\n    return resolveBoxAndLabelProperties(chart, options);\n  }\n\n}\n\nEllipseAnnotation.id = 'ellipseAnnotation';\n\nEllipseAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  label: Object.assign({}, BoxAnnotation.defaults.label),\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\n\nEllipseAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nEllipseAnnotation.descriptors = {\n  label: {\n    _fallback: true\n  }\n};\n\nfunction pointInEllipse(p, ellipse, rotation, hitSize) {\n  const {width, height, centerX, centerY} = ellipse;\n  const xRadius = width / 2;\n  const yRadius = height / 2;\n\n  if (xRadius <= 0 || yRadius <= 0) {\n    return false;\n  }\n  // https://stackoverflow.com/questions/7946187/point-and-ellipse-rotated-position-test-algorithm\n  const angle = toRadians(rotation || 0);\n  const cosAngle = Math.cos(angle);\n  const sinAngle = Math.sin(angle);\n  const a = Math.pow(cosAngle * (p.x - centerX) + sinAngle * (p.y - centerY), 2);\n  const b = Math.pow(sinAngle * (p.x - centerX) - cosAngle * (p.y - centerY), 2);\n  return (a / Math.pow(xRadius + hitSize, 2)) + (b / Math.pow(yRadius + hitSize, 2)) <= 1.0001;\n}\n\nclass PointAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const {x, y, x2, y2, width} = this.getProps(['x', 'y', 'x2', 'y2', 'width'], useFinalPosition);\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      return inPointRange({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), width / 2, hitSize);\n    }\n    const limit = axis === 'y' ? {start: y, end: y2, value: mouseY} : {start: x, end: x2, value: mouseX};\n    return inLimit(limit, hitSize);\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const options = this.options;\n    const borderWidth = options.borderWidth;\n    if (options.radius < 0.1) {\n      return;\n    }\n    ctx.save();\n    ctx.fillStyle = options.backgroundColor;\n    setShadowStyle(ctx, options);\n    const stroke = setBorderStyle(ctx, options);\n    drawPoint(ctx, this, this.centerX, this.centerY);\n    if (stroke && !isImageOrCanvas(options.pointStyle)) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n    options.borderWidth = borderWidth;\n  }\n\n  resolveElementProperties(chart, options) {\n    const properties = resolvePointProperties(chart, options);\n    properties.initProperties = initAnimationProperties(chart, properties, options);\n    return properties;\n  }\n}\n\nPointAnnotation.id = 'pointAnnotation';\n\nPointAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  pointStyle: 'circle',\n  radius: 10,\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\n\nPointAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nclass PolygonAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    if (axis !== 'x' && axis !== 'y') {\n      return this.options.radius >= 0.1 && this.elements.length > 1 && pointIsInPolygon(this.elements, mouseX, mouseY, useFinalPosition);\n    }\n    const rotatedPoint = rotated({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), toRadians(-this.options.rotation));\n    const axisPoints = this.elements.map((point) => axis === 'y' ? point.bY : point.bX);\n    const start = Math.min(...axisPoints);\n    const end = Math.max(...axisPoints);\n    return rotatedPoint[axis] >= start && rotatedPoint[axis] <= end;\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const {elements, options} = this;\n    ctx.save();\n    ctx.beginPath();\n    ctx.fillStyle = options.backgroundColor;\n    setShadowStyle(ctx, options);\n    const stroke = setBorderStyle(ctx, options);\n    let first = true;\n    for (const el of elements) {\n      if (first) {\n        ctx.moveTo(el.x, el.y);\n        first = false;\n      } else {\n        ctx.lineTo(el.x, el.y);\n      }\n    }\n    ctx.closePath();\n    ctx.fill();\n    // If no border, don't draw it\n    if (stroke) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n  }\n\n  resolveElementProperties(chart, options) {\n    const properties = resolvePointProperties(chart, options);\n    const {sides, rotation} = options;\n    const elements = [];\n    const angle = (2 * PI) / sides;\n    let rad = rotation * RAD_PER_DEG;\n    for (let i = 0; i < sides; i++, rad += angle) {\n      const elProps = buildPointElement(properties, options, rad);\n      elProps.initProperties = initAnimationProperties(chart, properties, options);\n      elements.push(elProps);\n    }\n    properties.elements = elements;\n    return properties;\n  }\n}\n\nPolygonAnnotation.id = 'polygonAnnotation';\n\nPolygonAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  point: {\n    radius: 0\n  },\n  radius: 10,\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  sides: 3,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\n\nPolygonAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nfunction buildPointElement({centerX, centerY}, {radius, borderWidth, hitTolerance}, rad) {\n  const hitSize = (borderWidth + hitTolerance) / 2;\n  const sin = Math.sin(rad);\n  const cos = Math.cos(rad);\n  const point = {x: centerX + sin * radius, y: centerY - cos * radius};\n  return {\n    type: 'point',\n    optionScope: 'point',\n    properties: {\n      x: point.x,\n      y: point.y,\n      centerX: point.x,\n      centerY: point.y,\n      bX: centerX + sin * (radius + hitSize),\n      bY: centerY - cos * (radius + hitSize)\n    }\n  };\n}\n\nfunction pointIsInPolygon(points, x, y, useFinalPosition) {\n  let isInside = false;\n  let A = points[points.length - 1].getProps(['bX', 'bY'], useFinalPosition);\n  for (const point of points) {\n    const B = point.getProps(['bX', 'bY'], useFinalPosition);\n    if ((B.bY > y) !== (A.bY > y) && x < (A.bX - B.bX) * (y - B.bY) / (A.bY - B.bY) + B.bX) {\n      isInside = !isInside;\n    }\n    A = B;\n  }\n  return isInside;\n}\n\nconst annotationTypes = {\n  box: BoxAnnotation,\n  doughnutLabel: DoughnutLabelAnnotation,\n  ellipse: EllipseAnnotation,\n  label: LabelAnnotation,\n  line: LineAnnotation,\n  point: PointAnnotation,\n  polygon: PolygonAnnotation\n};\n\n/**\n * Register fallback for annotation elements\n * For example lineAnnotation options would be looked through:\n * - the annotation object (options.plugins.annotation.annotations[id])\n * - element options (options.elements.lineAnnotation)\n * - element defaults (defaults.elements.lineAnnotation)\n * - annotation plugin defaults (defaults.plugins.annotation, this is what we are registering here)\n */\nObject.keys(annotationTypes).forEach(key => {\n  defaults.describe(`elements.${annotationTypes[key].id}`, {\n    _fallback: 'plugins.annotation.common'\n  });\n});\n\nconst directUpdater = {\n  update: Object.assign\n};\n\nconst hooks$1 = eventHooks.concat(elementHooks);\nconst resolve = (value, optDefs) => isObject(optDefs) ? resolveObj(value, optDefs) : value;\n\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").UpdateMode } UpdateMode\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n */\n\n/**\n * @param {string} prop\n * @returns {boolean}\n */\nconst isIndexable = (prop) => prop === 'color' || prop === 'font';\n\n/**\n * Resolve the annotation type, checking if is supported.\n * @param {string} [type=line] - annotation type\n * @returns {string} resolved annotation type\n */\nfunction resolveType(type = 'line') {\n  if (annotationTypes[type]) {\n    return type;\n  }\n  console.warn(`Unknown annotation type: '${type}', defaulting to 'line'`);\n  return 'line';\n}\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n * @param {UpdateMode} mode\n */\nfunction updateElements(chart, state, options, mode) {\n  const animations = resolveAnimations(chart, options.animations, mode);\n\n  const annotations = state.annotations;\n  const elements = resyncElements(state.elements, annotations);\n\n  for (let i = 0; i < annotations.length; i++) {\n    const annotationOptions = annotations[i];\n    const element = getOrCreateElement(elements, i, annotationOptions.type);\n    const resolver = annotationOptions.setContext(getContext(chart, element, elements, annotationOptions));\n    const properties = element.resolveElementProperties(chart, resolver);\n\n    properties.skip = toSkip(properties);\n\n    if ('elements' in properties) {\n      updateSubElements(element, properties.elements, resolver, animations);\n      // Remove the sub-element definitions from properties, so the actual elements\n      // are not overwritten by their definitions\n      delete properties.elements;\n    }\n\n    if (!defined(element.x)) {\n      // If the element is newly created, assing the properties directly - to\n      // make them readily awailable to any scriptable options. If we do not do this,\n      // the properties retruned by `resolveElementProperties` are available only\n      // after options resolution.\n      Object.assign(element, properties);\n    }\n\n    Object.assign(element, properties.initProperties);\n    properties.options = resolveAnnotationOptions(resolver);\n\n    animations.update(element, properties);\n  }\n}\n\nfunction toSkip(properties) {\n  return isNaN(properties.x) || isNaN(properties.y);\n}\n\nfunction resolveAnimations(chart, animOpts, mode) {\n  if (mode === 'reset' || mode === 'none' || mode === 'resize') {\n    return directUpdater;\n  }\n  return new Animations(chart, animOpts);\n}\n\nfunction updateSubElements(mainElement, elements, resolver, animations) {\n  const subElements = mainElement.elements || (mainElement.elements = []);\n  subElements.length = elements.length;\n  for (let i = 0; i < elements.length; i++) {\n    const definition = elements[i];\n    const properties = definition.properties;\n    const subElement = getOrCreateElement(subElements, i, definition.type, definition.initProperties);\n    const subResolver = resolver[definition.optionScope].override(definition);\n    properties.options = resolveAnnotationOptions(subResolver);\n    animations.update(subElement, properties);\n  }\n}\n\nfunction getOrCreateElement(elements, index, type, initProperties) {\n  const elementClass = annotationTypes[resolveType(type)];\n  let element = elements[index];\n  if (!element || !(element instanceof elementClass)) {\n    element = elements[index] = new elementClass();\n    Object.assign(element, initProperties);\n  }\n  return element;\n}\n\nfunction resolveAnnotationOptions(resolver) {\n  const elementClass = annotationTypes[resolveType(resolver.type)];\n  const result = {};\n  result.id = resolver.id;\n  result.type = resolver.type;\n  result.drawTime = resolver.drawTime;\n  Object.assign(result,\n    resolveObj(resolver, elementClass.defaults),\n    resolveObj(resolver, elementClass.defaultRoutes));\n  for (const hook of hooks$1) {\n    result[hook] = resolver[hook];\n  }\n  return result;\n}\n\nfunction resolveObj(resolver, defs) {\n  const result = {};\n  for (const prop of Object.keys(defs)) {\n    const optDefs = defs[prop];\n    const value = resolver[prop];\n    if (isIndexable(prop) && isArray(value)) {\n      result[prop] = value.map((item) => resolve(item, optDefs));\n    } else {\n      result[prop] = resolve(value, optDefs);\n    }\n  }\n  return result;\n}\n\nfunction getContext(chart, element, elements, annotation) {\n  return element.$context || (element.$context = Object.assign(Object.create(chart.getContext()), {\n    element,\n    get elements() {\n      return elements.filter((el) => el && el.options);\n    },\n    id: annotation.id,\n    type: 'annotation'\n  }));\n}\n\nfunction resyncElements(elements, annotations) {\n  const count = annotations.length;\n  const start = elements.length;\n\n  if (start < count) {\n    const add = count - start;\n    elements.splice(start, 0, ...new Array(add));\n  } else if (start > count) {\n    elements.splice(count, start - count);\n  }\n  return elements;\n}\n\nvar version = \"3.1.0\";\n\nconst chartStates = new Map();\nconst isNotDoughnutLabel = annotation => annotation.type !== 'doughnutLabel';\nconst hooks = eventHooks.concat(elementHooks);\n\nvar annotation = {\n  id: 'annotation',\n\n  version,\n\n  beforeRegister() {\n    requireVersion('chart.js', '4.0', Chart.version);\n  },\n\n  afterRegister() {\n    Chart.register(annotationTypes);\n  },\n\n  afterUnregister() {\n    Chart.unregister(annotationTypes);\n  },\n\n  beforeInit(chart) {\n    chartStates.set(chart, {\n      annotations: [],\n      elements: [],\n      visibleElements: [],\n      listeners: {},\n      listened: false,\n      moveListened: false,\n      hooks: {},\n      hooked: false,\n      hovered: []\n    });\n  },\n\n  beforeUpdate(chart, args, options) {\n    const state = chartStates.get(chart);\n    const annotations = state.annotations = [];\n\n    let annotationOptions = options.annotations;\n    if (isObject(annotationOptions)) {\n      Object.keys(annotationOptions).forEach(key => {\n        const value = annotationOptions[key];\n        if (isObject(value)) {\n          value.id = key;\n          annotations.push(value);\n        }\n      });\n    } else if (isArray(annotationOptions)) {\n      annotations.push(...annotationOptions);\n    }\n    verifyScaleOptions(annotations.filter(isNotDoughnutLabel), chart.scales);\n  },\n\n  afterDataLimits(chart, args) {\n    const state = chartStates.get(chart);\n    adjustScaleRange(chart, args.scale, state.annotations.filter(isNotDoughnutLabel).filter(a => a.display && a.adjustScaleRange));\n  },\n\n  afterUpdate(chart, args, options) {\n    const state = chartStates.get(chart);\n    updateListeners(chart, state, options);\n    updateElements(chart, state, options, args.mode);\n    state.visibleElements = state.elements.filter(el => !el.skip && el.options.display);\n    updateHooks(chart, state, options);\n  },\n\n  beforeDatasetsDraw(chart, _args, options) {\n    draw(chart, 'beforeDatasetsDraw', options.clip);\n  },\n\n  afterDatasetsDraw(chart, _args, options) {\n    draw(chart, 'afterDatasetsDraw', options.clip);\n  },\n\n  beforeDatasetDraw(chart, _args, options) {\n    draw(chart, _args.index, options.clip);\n  },\n\n  beforeDraw(chart, _args, options) {\n    draw(chart, 'beforeDraw', options.clip);\n  },\n\n  afterDraw(chart, _args, options) {\n    draw(chart, 'afterDraw', options.clip);\n  },\n\n  beforeEvent(chart, args, options) {\n    const state = chartStates.get(chart);\n    if (handleEvent(state, args.event, options)) {\n      args.changed = true;\n    }\n  },\n\n  afterDestroy(chart) {\n    chartStates.delete(chart);\n  },\n\n  getAnnotations(chart) {\n    const state = chartStates.get(chart);\n    return state ? state.elements : [];\n  },\n\n  // only for testing\n  _getAnnotationElementsAtEventForMode(visibleElements, event, options) {\n    return getElements(visibleElements, event, options);\n  },\n\n  defaults: {\n    animations: {\n      numbers: {\n        properties: ['x', 'y', 'x2', 'y2', 'width', 'height', 'centerX', 'centerY', 'pointX', 'pointY', 'radius'],\n        type: 'number'\n      },\n      colors: {\n        properties: ['backgroundColor', 'borderColor'],\n        type: 'color'\n      }\n    },\n    clip: true,\n    interaction: {\n      mode: undefined,\n      axis: undefined,\n      intersect: undefined\n    },\n    common: {\n      drawTime: 'afterDatasetsDraw',\n      init: false,\n      label: {\n      }\n    }\n  },\n\n  descriptors: {\n    _indexable: false,\n    _scriptable: (prop) => !hooks.includes(prop) && prop !== 'init',\n    annotations: {\n      _allKeys: false,\n      _fallback: (prop, opts) => `elements.${annotationTypes[resolveType(opts.type)].id}`\n    },\n    interaction: {\n      _fallback: true\n    },\n    common: {\n      label: {\n        _indexable: isIndexable,\n        _fallback: true\n      },\n      _indexable: isIndexable\n    }\n  },\n\n  additionalOptionScopes: ['']\n};\n\nfunction draw(chart, caller, clip) {\n  const {ctx, chartArea} = chart;\n  const state = chartStates.get(chart);\n\n  if (clip) {\n    clipArea(ctx, chartArea);\n  }\n\n  const drawableElements = getDrawableElements(state.visibleElements, caller).sort((a, b) => a.element.options.z - b.element.options.z);\n  for (const item of drawableElements) {\n    drawElement(ctx, chartArea, state, item);\n  }\n\n  if (clip) {\n    unclipArea(ctx);\n  }\n}\n\nfunction getDrawableElements(elements, caller) {\n  const drawableElements = [];\n  for (const el of elements) {\n    if (el.options.drawTime === caller) {\n      drawableElements.push({element: el, main: true});\n    }\n    if (el.elements && el.elements.length) {\n      for (const sub of el.elements) {\n        if (sub.options.display && sub.options.drawTime === caller) {\n          drawableElements.push({element: sub});\n        }\n      }\n    }\n  }\n  return drawableElements;\n}\n\nfunction drawElement(ctx, chartArea, state, item) {\n  const el = item.element;\n  if (item.main) {\n    invokeHook(state, el, 'beforeDraw');\n    el.draw(ctx, chartArea);\n    invokeHook(state, el, 'afterDraw');\n  } else {\n    el.draw(ctx, chartArea);\n  }\n}\n\nexport { annotation as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,IAAM,cAAc;AAAA,EAClB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOL,MAAM,iBAAiB,OAAO;AAC5B,aAAO,eAAe,iBAAiB,OAAO,EAAC,WAAW,KAAI,CAAC;AAAA,IACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,QAAQ,iBAAiB,OAAO,SAAS;AACvC,aAAO,eAAe,iBAAiB,OAAO,OAAO;AAAA,IACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,EAAE,iBAAiB,OAAO,SAAS;AACjC,aAAO,eAAe,iBAAiB,OAAO,EAAC,WAAW,QAAQ,WAAW,MAAM,IAAG,CAAC;AAAA,IACzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,EAAE,iBAAiB,OAAO,SAAS;AACjC,aAAO,eAAe,iBAAiB,OAAO,EAAC,WAAW,QAAQ,WAAW,MAAM,IAAG,CAAC;AAAA,IACzF;AAAA,EACF;AACF;AASA,SAAS,YAAY,iBAAiB,OAAO,SAAS;AACpD,QAAM,OAAO,YAAY,MAAM,QAAQ,IAAI,KAAK,YAAY,MAAM;AAClE,SAAO,KAAK,iBAAiB,OAAO,OAAO;AAC7C;AAEA,SAAS,cAAc,SAAS,OAAO,MAAM;AAC3C,MAAI,SAAS,OAAO,SAAS,KAAK;AAChC,WAAO,QAAQ,QAAQ,MAAM,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG,MAAM,GAAG,KAAK,IAAI;AAAA,EACpG;AACA,SAAO,QAAQ,QAAQ,MAAM,GAAG,MAAM,GAAG,MAAM,IAAI;AACrD;AAEA,SAAS,eAAe,OAAO,QAAQ,MAAM;AAC3C,MAAI,SAAS,KAAK;AAChB,WAAO,EAAC,GAAG,MAAM,GAAG,GAAG,OAAO,EAAC;AAAA,EACjC,WAAW,SAAS,KAAK;AACvB,WAAO,EAAC,GAAG,OAAO,GAAG,GAAG,MAAM,EAAC;AAAA,EACjC;AACA,SAAO;AACT;AAEA,SAAS,eAAe,iBAAiB,OAAO,SAAS;AACvD,SAAO,gBAAgB,OAAO,CAAC,YAAY,QAAQ,YAAY,QAAQ,QAAQ,MAAM,GAAG,MAAM,CAAC,IAAI,cAAc,SAAS,OAAO,QAAQ,IAAI,CAAC;AAChJ;AAEA,SAAS,eAAe,iBAAiB,OAAO,SAAS;AACvD,MAAI,cAAc,OAAO;AAEzB,SAAO,eAAe,iBAAiB,OAAO,OAAO,EAClD,OAAO,CAAC,cAAc,YAAY;AACjC,UAAM,SAAS,QAAQ,eAAe;AACtC,UAAM,YAAY,eAAe,OAAO,QAAQ,QAAQ,IAAI;AAC5D,UAAM,WAAW,sBAAsB,OAAO,SAAS;AACvD,QAAI,WAAW,aAAa;AAC1B,qBAAe,CAAC,OAAO;AACvB,oBAAc;AAAA,IAChB,WAAW,aAAa,aAAa;AAEnC,mBAAa,KAAK,OAAO;AAAA,IAC3B;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC,EACJ,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,EAClC,MAAM,GAAG,CAAC;AACf;AAaA,SAAS,QAAQ,OAAO,QAAQ,OAAO;AACrC,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,QAAM,MAAM,KAAK,IAAI,KAAK;AAC1B,QAAM,KAAK,OAAO;AAClB,QAAM,KAAK,OAAO;AAElB,SAAO;AAAA,IACL,GAAG,KAAK,OAAO,MAAM,IAAI,MAAM,OAAO,MAAM,IAAI;AAAA,IAChD,GAAG,KAAK,OAAO,MAAM,IAAI,MAAM,OAAO,MAAM,IAAI;AAAA,EAClD;AACF;AAEA,IAAM,cAAc,CAAC,KAAK,QAAQ,MAAM,OAAQ,IAAI,SAAS,IAAI,UAAU,IAAI,MAAM,GAAG,IAAI,MAAM,MAAM;AAQxG,IAAM,UAAU;AAChB,IAAM,QAAQ,CAAC,GAAG,MAAM,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,MAAM,CAAC,CAAC;AAO7D,IAAM,UAAU,CAAC,OAAO,YAAY,MAAM,SAAS,MAAM,QAAQ,WAAW,MAAM,SAAS,MAAM,MAAM;AAQvG,SAAS,SAAS,KAAK,MAAM,IAAI;AAC/B,aAAW,OAAO,OAAO,KAAK,GAAG,GAAG;AAClC,QAAI,GAAG,IAAI,MAAM,IAAI,GAAG,GAAG,MAAM,EAAE;AAAA,EACrC;AACA,SAAO;AACT;AASA,SAAS,aAAa,OAAO,QAAQ,QAAQ,SAAS;AACpD,MAAI,CAAC,SAAS,CAAC,UAAU,UAAU,GAAG;AACpC,WAAO;AAAA,EACT;AACA,SAAQ,KAAK,IAAI,MAAM,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,IAAI,MAAM,IAAI,OAAO,GAAG,CAAC,KAAM,KAAK,IAAI,SAAS,SAAS,CAAC;AAC5G;AASA,SAAS,WAAW,OAAO,EAAC,GAAG,GAAG,IAAI,GAAE,GAAG,MAAM,EAAC,aAAa,aAAY,GAAG;AAC5E,QAAM,WAAW,cAAc,gBAAgB;AAC/C,QAAM,WAAW,MAAM,KAAK,IAAI,UAAU,WAAW,MAAM,KAAK,KAAK,UAAU;AAC/E,QAAM,WAAW,MAAM,KAAK,IAAI,UAAU,WAAW,MAAM,KAAK,KAAK,UAAU;AAC/E,MAAI,SAAS,KAAK;AAChB,WAAO;AAAA,EACT,WAAW,SAAS,KAAK;AACvB,WAAO;AAAA,EACT;AACA,SAAO,YAAY;AACrB;AASA,SAAS,aAAa,OAAO,EAAC,MAAM,OAAM,GAAG,MAAM,EAAC,UAAU,aAAa,aAAY,GAAG;AACxF,QAAM,WAAW,QAAQ,OAAO,QAAQ,UAAU,CAAC,QAAQ,CAAC;AAC5D,SAAO,WAAW,UAAU,MAAM,MAAM,EAAC,aAAa,aAAY,CAAC;AACrE;AAOA,SAAS,sBAAsB,SAAS,kBAAkB;AACxD,QAAM,EAAC,SAAS,QAAO,IAAI,QAAQ,SAAS,CAAC,WAAW,SAAS,GAAG,gBAAgB;AACpF,SAAO,EAAC,GAAG,SAAS,GAAG,QAAO;AAChC;AASA,SAAS,eAAe,KAAK,KAAK,KAAK,SAAS,MAAM;AACpD,QAAM,QAAQ,IAAI,MAAM,GAAG;AAC3B,MAAI,IAAI;AACR,aAAW,OAAO,IAAI,MAAM,GAAG,GAAG;AAChC,UAAM,MAAM,MAAM,GAAG;AACrB,QAAI,SAAS,KAAK,EAAE,IAAI,SAAS,KAAK,EAAE,GAAG;AACzC;AAAA,IACF;AACA,QAAI,YAAY,KAAK,GAAG,GAAG;AACzB,UAAI,QAAQ;AACV,cAAM,IAAI,MAAM,GAAG,GAAG,KAAK,GAAG,uBAAuB,GAAG,wBAAwB;AAAA,MAClF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,kBAAkB,CAAC,MAAM,OAAO,MAAM,YAAY,EAAE,SAAS,GAAG;AACtE,IAAM,YAAY,CAAC,MAAM,WAAW,CAAC,IAAI;AACzC,IAAM,oBAAoB,CAAC,MAAM,MAAM,UAAU,CAAC,GAAG,GAAG,CAAC;AAEzD,IAAM,cAAc,CAAC,GAAG,OAAO,EAAC,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,QAAQ,EAAC;AACvE,IAAM,uBAAuB;AAAA,EAC3B,KAAK,CAAC,eAAe,YAAY,WAAW,SAAS,WAAW,OAAO;AAAA,EACvE,eAAe,CAAC,eAAe,YAAY,WAAW,SAAS,WAAW,OAAO;AAAA,EACjF,SAAS,CAAC,gBAAgB,EAAC,SAAS,WAAW,SAAS,SAAS,WAAW,SAAS,QAAQ,GAAG,OAAO,GAAG,QAAQ,EAAC;AAAA,EACnH,OAAO,CAAC,eAAe,YAAY,WAAW,SAAS,WAAW,OAAO;AAAA,EACzE,MAAM,CAAC,eAAe,YAAY,WAAW,GAAG,WAAW,CAAC;AAAA,EAC5D,OAAO,CAAC,gBAAgB,EAAC,SAAS,WAAW,SAAS,SAAS,WAAW,SAAS,QAAQ,GAAG,OAAO,GAAG,QAAQ,EAAC;AAAA,EACjH,SAAS,CAAC,eAAe,YAAY,WAAW,SAAS,WAAW,OAAO;AAC7E;AAkBA,SAASA,qBAAoB,MAAM,UAAU;AAC3C,MAAI,aAAa,SAAS;AACxB,WAAO;AAAA,EACT;AACA,MAAI,aAAa,OAAO;AACtB,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,QAAQ,GAAG;AAC7B,WAAO,kBAAkB,QAAQ,IAAI;AAAA,EACvC;AACA,SAAO,OAAO;AAChB;AAQA,SAAS,QAAQ,MAAM,OAAO,kBAAkB,MAAM;AACpD,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT,WAAW,gBAAgB,KAAK,GAAG;AACjC,YAAQ,kBAAkB,kBAAkB,KAAK,IAAI,UAAU,KAAK,KAAK;AAAA,EAC3E;AACA,SAAO;AACT;AAOA,SAAS,uBAAuB,MAAM,SAAS;AAC7C,QAAM,EAAC,GAAG,MAAK,IAAI;AACnB,QAAM,YAAY,QAAQ;AAC1B,MAAI,cAAc,UAAU;AAC1B,WAAO,IAAI,QAAQ;AAAA,EACrB,WAAW,cAAc,SAAS,cAAc,SAAS;AACvD,WAAO,IAAI;AAAA,EACb;AACA,SAAO;AACT;AASA,SAAS,sBAAsB,OAAO,WAAW,EAAC,aAAa,UAAU,SAAS,QAAO,GAAG,SAAS;AACnG,QAAM,aAAa,SAAS,OAAO;AACnC,QAAM,QAAQ,UAAU,SAAS,aAAa,QAAQ,QAAQ,KAAK;AACnE,QAAM,SAAS,UAAU,UAAU,aAAa,QAAQ,SAAS,KAAK;AACtE,QAAM,cAAc,WAAW,QAAQ;AACvC,QAAM,IAAI,yBAAyB,MAAM,GAAG,OAAO,SAAS,YAAY,CAAC;AACzE,QAAM,IAAI,yBAAyB,MAAM,GAAG,QAAQ,SAAS,YAAY,CAAC;AAE1E,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,IAAI,IAAI;AAAA,IACR,IAAI,IAAI;AAAA,IACR;AAAA,IACA;AAAA,IACA,SAAS,IAAI,QAAQ;AAAA,IACrB,SAAS,IAAI,SAAS;AAAA,EACxB;AACF;AAOA,SAAS,WAAW,OAAO,eAAe,UAAU;AAClD,MAAI,SAAS,KAAK,GAAG;AACnB,WAAO;AAAA,MACL,GAAG,eAAe,MAAM,GAAG,YAAY;AAAA,MACvC,GAAG,eAAe,MAAM,GAAG,YAAY;AAAA,IACzC;AAAA,EACF;AACA,UAAQ,eAAe,OAAO,YAAY;AAC1C,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACF;AAOA,IAAM,YAAY,CAAC,SAAS,aAAa,WAAW,QAAQ,WAAW,WAAW;AAOlF,SAAS,QAAQ,SAAS,UAAU;AAClC,QAAM,UAAU,QAAQ;AACxB,QAAM,QAAQ,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AACnD,MAAI,UAAU,SAAS,QAAQ,GAAG;AAChC,WAAO,MAAM,IAAI,SAAS,GAAG;AAC3B,YAAM,OAAO,OAAO,CAAC;AACrB,WAAK,OAAO,KAAK,MAAM,EAAE,OAAO,QAAQ;AACxC,WAAK,aAAa,EAAE;AACpB,aAAO,OAAO,IAAI;AAAA,IACpB,CAAC;AAAA,EACH;AACA,SAAO,MAAM,IAAI,OAAK,OAAO,CAAC,CAAC;AACjC;AAMA,SAAS,eAAe,SAAS;AAC/B,SAAO,YAAY,QAAQ,QAAQ,MAAM,KAAK,QAAQ,QAAQ,MAAM;AACtE;AAEA,SAAS,yBAAyB,OAAO,MAAM,SAAS,GAAG,UAAU;AACnE,SAAO,QAAQA,qBAAoB,MAAM,QAAQ,IAAI;AACvD;AAQA,SAAS,wBAAwB,OAAO,YAAY,SAAS;AAC3D,QAAM,WAAW,QAAQ;AACzB,MAAI,CAAC,UAAU;AACb;AAAA,EACF,WAAW,aAAa,MAAM;AAC5B,WAAO,aAAa,YAAY,OAAO;AAAA,EACzC;AACA,SAAO,aAAa,OAAO,YAAY,OAAO;AAChD;AAQA,SAAS,UAAU,SAASC,QAAO,gBAAgB;AACjD,MAAI,YAAY;AAChB,EAAAA,OAAM,QAAQ,UAAQ;AACpB,QAAI,WAAW,QAAQ,IAAI,CAAC,GAAG;AAC7B,kBAAY;AACZ,qBAAe,IAAI,IAAI,QAAQ,IAAI;AAAA,IACrC,WAAW,QAAQ,eAAe,IAAI,CAAC,GAAG;AACxC,aAAO,eAAe,IAAI;AAAA,IAC5B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,aAAa,YAAY,SAAS;AACzC,QAAM,OAAO,QAAQ,QAAQ;AAC7B,SAAO,qBAAqB,IAAI,EAAE,UAAU;AAC9C;AAEA,SAAS,aAAa,OAAO,YAAY,SAAS;AAChD,QAAM,SAAS,SAAS,QAAQ,MAAM,CAAC,EAAC,OAAO,YAAY,QAAO,CAAC,CAAC;AACpE,MAAI,WAAW,MAAM;AACnB,WAAO,aAAa,YAAY,OAAO;AAAA,EACzC,WAAW,SAAS,MAAM,GAAG;AAC3B,WAAO;AAAA,EACT;AACF;AAEA,IAAM,aAAa,oBAAI,IAAI;AAC3B,IAAM,YAAY,CAAC,WAAW,MAAM,MAAM,KAAK,UAAU;AACzD,IAAM,WAAW,CAAC,UAAU,MAAM,OAAO,SAAS,MAAM,MAAM;AAC5D,UAAQ,KAAK;AACb,SAAO;AACT,GAAG,EAAE;AAcL,SAAS,gBAAgB,SAAS;AAChC,MAAI,WAAW,OAAO,YAAY,UAAU;AAC1C,UAAM,OAAO,QAAQ,SAAS;AAC9B,WAAQ,SAAS,+BAA+B,SAAS;AAAA,EAC3D;AACF;AAQA,SAAS,UAAU,KAAK,EAAC,GAAG,EAAC,GAAG,UAAU;AACxC,MAAI,UAAU;AACZ,QAAI,UAAU,GAAG,CAAC;AAClB,QAAI,OAAO,UAAU,QAAQ,CAAC;AAC9B,QAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,EACtB;AACF;AAOA,SAAS,eAAe,KAAK,SAAS;AACpC,MAAI,WAAW,QAAQ,aAAa;AAClC,QAAI,UAAU,QAAQ,kBAAkB;AACxC,QAAI,YAAY,QAAQ,UAAU;AAClC,QAAI,iBAAiB,QAAQ;AAC7B,QAAI,WAAW,QAAQ,mBAAmB;AAC1C,QAAI,YAAY,QAAQ;AACxB,QAAI,cAAc,QAAQ;AAC1B,WAAO;AAAA,EACT;AACF;AAMA,SAAS,eAAe,KAAK,SAAS;AACpC,MAAI,cAAc,QAAQ;AAC1B,MAAI,aAAa,QAAQ;AACzB,MAAI,gBAAgB,QAAQ;AAC5B,MAAI,gBAAgB,QAAQ;AAC9B;AAOA,SAAS,iBAAiB,KAAK,SAAS;AACtC,QAAM,UAAU,QAAQ;AACxB,MAAI,gBAAgB,OAAO,GAAG;AAC5B,UAAM,OAAO;AAAA,MACX,OAAO,QAAQ,QAAQ,OAAO,QAAQ,KAAK;AAAA,MAC3C,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,IAChD;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,QAAQ,OAAO;AAC7B,QAAM,cAAc,QAAQ;AAC5B,QAAM,QAAQ,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AACnD,QAAM,SAAS,MAAM,KAAK,IAAI,SAAS,KAAK,IAAI,eAAe,IAAI,eAAe,cAAc;AAChG,MAAI,CAAC,WAAW,IAAI,MAAM,GAAG;AAC3B,eAAW,IAAI,QAAQ,mBAAmB,KAAK,OAAO,OAAO,WAAW,CAAC;AAAA,EAC3E;AACA,SAAO,WAAW,IAAI,MAAM;AAC9B;AAOA,SAAS,QAAQ,KAAK,MAAM,SAAS;AACnC,QAAM,EAAC,GAAG,GAAG,OAAO,OAAM,IAAI;AAC9B,MAAI,KAAK;AACT,iBAAe,KAAK,OAAO;AAC3B,QAAM,SAAS,eAAe,KAAK,OAAO;AAC1C,MAAI,YAAY,QAAQ;AACxB,MAAI,UAAU;AACd,qBAAmB,KAAK;AAAA,IACtB;AAAA,IAAG;AAAA,IAAG,GAAG;AAAA,IAAO,GAAG;AAAA,IACnB,QAAQ,SAAS,cAAc,QAAQ,YAAY,GAAG,GAAG,KAAK,IAAI,OAAO,MAAM,IAAI,CAAC;AAAA,EACtF,CAAC;AACD,MAAI,UAAU;AACd,MAAI,KAAK;AACT,MAAI,QAAQ;AACV,QAAI,cAAc,QAAQ;AAC1B,QAAI,OAAO;AAAA,EACb;AACA,MAAI,QAAQ;AACd;AAQA,SAAS,UAAU,KAAK,MAAM,SAAS,UAAU;AAC/C,QAAM,UAAU,QAAQ;AACxB,MAAI,gBAAgB,OAAO,GAAG;AAC5B,QAAI,KAAK;AACT,QAAI,cAAc,WAAW,QAAQ,SAAS,QAAQ,MAAM,OAAO;AACnE,QAAI,UAAU,SAAS,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAC9D,QAAI,QAAQ;AACZ;AAAA,EACF;AACA,QAAM,SAAS,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AACpD,QAAM,QAAQ,QAAQ,SAAS,QAAQ;AACvC,QAAM,WAAW,QAAQ;AACzB,QAAM,SAAS,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AACvD,QAAM,IAAI,uBAAuB,MAAM,OAAO;AAC9C,QAAM,IAAI,KAAK,IAAI,QAAQ,kBAAkB;AAC7C,MAAI,KAAK;AACT,MAAI,eAAe;AACnB,MAAI,YAAY,QAAQ;AACxB,MAAI,mBAAmB,KAAK,OAAO,GAAG;AACpC,yBAAqB,KAAK,EAAC,GAAG,EAAC,GAAG,QAAQ,KAAK;AAAA,EACjD;AACA,oBAAkB,KAAK,EAAC,GAAG,EAAC,GAAG,QAAQ,EAAC,OAAO,OAAM,CAAC;AACtD,MAAI,QAAQ;AACd;AAEA,SAAS,mBAAmB,KAAK,SAAS;AACxC,MAAI,QAAQ,kBAAkB,GAAG;AAE/B,QAAI,WAAW;AACf,QAAI,aAAa;AACjB,QAAI,YAAY,QAAQ;AACxB,QAAI,cAAc,QAAQ;AAC1B,WAAO;AAAA,EACT;AACF;AAQA,SAASC,WAAU,KAAK,SAAS,GAAG,GAAG;AACrC,QAAM,EAAC,QAAQ,QAAO,IAAI;AAC1B,QAAM,QAAQ,QAAQ;AACtB,QAAM,WAAW,QAAQ;AACzB,MAAI,OAAO,YAAY,KAAK;AAE5B,MAAI,gBAAgB,KAAK,GAAG;AAC1B,QAAI,KAAK;AACT,QAAI,UAAU,GAAG,CAAC;AAClB,QAAI,OAAO,GAAG;AACd,QAAI,UAAU,OAAO,CAAC,MAAM,QAAQ,GAAG,CAAC,MAAM,SAAS,GAAG,MAAM,OAAO,MAAM,MAAM;AACnF,QAAI,QAAQ;AACZ;AAAA,EACF;AACA,MAAI,UAAU,MAAM,GAAG;AACrB;AAAA,EACF;AACA,iBAAe,KAAK,EAAC,GAAG,GAAG,QAAQ,UAAU,OAAO,IAAG,CAAC;AAC1D;AAEA,SAAS,eAAe,KAAK,EAAC,GAAG,GAAG,QAAQ,UAAU,OAAO,IAAG,GAAG;AACjE,MAAI,SAAS,SAAS,MAAM;AAC5B,MAAI,UAAU;AAEd,UAAQ,OAAO;AAAA;AAAA,IAEf;AACE,UAAI,IAAI,GAAG,GAAG,QAAQ,GAAG,GAAG;AAC5B,UAAI,UAAU;AACd;AAAA,IACF,KAAK;AACH,UAAI,OAAO,IAAI,KAAK,IAAI,GAAG,IAAI,QAAQ,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM;AACjE,aAAO;AACP,UAAI,OAAO,IAAI,KAAK,IAAI,GAAG,IAAI,QAAQ,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM;AACjE,aAAO;AACP,UAAI,OAAO,IAAI,KAAK,IAAI,GAAG,IAAI,QAAQ,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM;AACjE,UAAI,UAAU;AACd;AAAA,IACF,KAAK;AAQH,qBAAe,SAAS;AACxB,aAAO,SAAS;AAChB,gBAAU,KAAK,IAAI,MAAM,UAAU,IAAI;AACvC,gBAAU,KAAK,IAAI,MAAM,UAAU,IAAI;AACvC,UAAI,IAAI,IAAI,SAAS,IAAI,SAAS,cAAc,MAAM,IAAI,MAAM,OAAO;AACvE,UAAI,IAAI,IAAI,SAAS,IAAI,SAAS,cAAc,MAAM,SAAS,GAAG;AAClE,UAAI,IAAI,IAAI,SAAS,IAAI,SAAS,cAAc,KAAK,MAAM,OAAO;AAClE,UAAI,IAAI,IAAI,SAAS,IAAI,SAAS,cAAc,MAAM,SAAS,MAAM,EAAE;AACvE,UAAI,UAAU;AACd;AAAA,IACF,KAAK;AACH,UAAI,CAAC,UAAU;AACb,eAAO,KAAK,UAAU;AACtB,YAAI,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI,IAAI;AAC/C;AAAA,MACF;AACA,aAAO;AAAA;AAAA,IAET,KAAK;AACH,gBAAU,KAAK,IAAI,GAAG,IAAI;AAC1B,gBAAU,KAAK,IAAI,GAAG,IAAI;AAC1B,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,UAAU;AACd;AAAA,IACF,KAAK;AACH,aAAO;AAAA;AAAA,IAET,KAAK;AACH,gBAAU,KAAK,IAAI,GAAG,IAAI;AAC1B,gBAAU,KAAK,IAAI,GAAG,IAAI;AAC1B,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC;AAAA,IACF,KAAK;AACH,gBAAU,KAAK,IAAI,GAAG,IAAI;AAC1B,gBAAU,KAAK,IAAI,GAAG,IAAI;AAC1B,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,aAAO;AACP,gBAAU,KAAK,IAAI,GAAG,IAAI;AAC1B,gBAAU,KAAK,IAAI,GAAG,IAAI;AAC1B,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC;AAAA,IACF,KAAK;AACH,gBAAU,KAAK,IAAI,GAAG,IAAI;AAC1B,gBAAU,KAAK,IAAI,GAAG,IAAI;AAC1B,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC,UAAI,OAAO,IAAI,SAAS,IAAI,OAAO;AACnC;AAAA,IACF,KAAK;AACH,UAAI,OAAO,GAAG,CAAC;AACf,UAAI,OAAO,IAAI,KAAK,IAAI,GAAG,IAAI,QAAQ,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM;AACjE;AAAA,EACF;AAEA,MAAI,KAAK;AACX;AAEA,SAAS,mBAAmB,KAAK,OAAO,OAAO,aAAa;AAC1D,MAAI,KAAK;AACT,QAAM,QAAQ,MAAM;AACpB,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,UAAM,OAAO,MAAM,KAAK,IAAI,GAAG,MAAM,SAAS,CAAC,CAAC;AAChD,QAAI,OAAO,KAAK;AAChB,UAAM,OAAO,MAAM,CAAC;AACpB,YAAQ,KAAK,IAAI,OAAO,IAAI,YAAY,IAAI,EAAE,QAAQ,WAAW;AACjE,cAAU,KAAK;AAAA,EACjB;AACA,MAAI,QAAQ;AACZ,SAAO,EAAC,OAAO,OAAM;AACvB;AAEA,SAAS,qBAAqB,KAAK,EAAC,GAAG,EAAC,GAAG,QAAQ,OAAO;AACxD,MAAI,UAAU;AACd,MAAI,MAAM;AACV,SAAO,QAAQ,SAAS,GAAG,GAAG;AAC5B,UAAM,IAAI,MAAM,KAAK,IAAI,GAAG,MAAM,SAAS,CAAC,CAAC;AAC7C,UAAM,KAAK,EAAE;AACb,QAAI,OAAO,EAAE;AACb,QAAI,WAAW,GAAG,GAAG,IAAI,KAAK,IAAI,GAAG;AACrC,WAAO;AAAA,EACT,CAAC;AACD,MAAI,OAAO;AACb;AAEA,SAAS,kBAAkB,KAAK,EAAC,GAAG,EAAC,GAAG,QAAQ,EAAC,OAAO,OAAM,GAAG;AAC/D,MAAI,MAAM;AACV,SAAO,QAAQ,SAAS,GAAG,GAAG;AAC5B,UAAM,IAAI,OAAO,KAAK,IAAI,GAAG,OAAO,SAAS,CAAC,CAAC;AAC/C,UAAM,IAAI,MAAM,KAAK,IAAI,GAAG,MAAM,SAAS,CAAC,CAAC;AAC7C,UAAM,KAAK,EAAE;AACb,QAAI,UAAU;AACd,QAAI,OAAO,EAAE;AACb,QAAI,YAAY;AAChB,QAAI,SAAS,GAAG,GAAG,IAAI,KAAK,IAAI,GAAG;AACnC,WAAO;AACP,QAAI,KAAK;AAAA,EACX,CAAC;AACH;AAEA,SAAS,WAAW,OAAO,cAAc;AACvC,QAAM,UAAU,SAAS,KAAK,IAAI,QAAQ;AAC1C,SAAO,SAAS,OAAO,IAAI,MAAM,SAAS,GAAG,CAAC,IAAI;AACpD;AAEA,IAAM,YAAY,CAAC,QAAQ,UAAU,OAAO,OAAO;AAWnD,SAAS,YAAY,KAAK,SAAS;AACjC,QAAM,EAAC,QAAQ,QAAQ,QAAO,IAAI;AAClC,QAAM,UAAU,QAAQ;AACxB,QAAM,kBAAkB,WAAW,QAAQ,WAAW,uBAAuB,SAAS,OAAO;AAC7F,MAAI,CAAC,mBAAmB,eAAe,SAAS,SAAS,eAAe,GAAG;AACzE;AAAA,EACF;AAEA,MAAI,KAAK;AACT,MAAI,UAAU;AACd,QAAM,SAAS,eAAe,KAAK,OAAO;AAC1C,MAAI,CAAC,QAAQ;AACX,WAAO,IAAI,QAAQ;AAAA,EACrB;AACA,QAAM,EAAC,gBAAgB,aAAY,IAAI,yBAAyB,SAAS,eAAe;AACxF,QAAM,EAAC,WAAW,QAAO,IAAI,oBAAoB,SAAS,iBAAiB,cAAc;AACzF,MAAI,QAAQ,SAAS,KAAK,QAAQ,gBAAgB,GAAG;AACnD,QAAI,OAAO,eAAe,GAAG,eAAe,CAAC;AAC7C,QAAI,OAAO,aAAa,GAAG,aAAa,CAAC;AAAA,EAC3C;AACA,MAAI,OAAO,UAAU,GAAG,UAAU,CAAC;AACnC,MAAI,OAAO,QAAQ,GAAG,QAAQ,CAAC;AAC/B,QAAM,eAAe,QAAQ,EAAC,GAAG,QAAQ,GAAG,OAAM,GAAG,QAAQ,eAAe,GAAG,UAAU,CAAC,QAAQ,QAAQ,CAAC;AAC3G,MAAI,OAAO,aAAa,GAAG,aAAa,CAAC;AACzC,MAAI,OAAO;AACX,MAAI,QAAQ;AACd;AAEA,SAAS,yBAAyB,SAAS,UAAU;AACnD,QAAM,EAAC,GAAG,GAAG,IAAI,GAAE,IAAI;AACvB,QAAM,SAAS,0BAA0B,SAAS,QAAQ;AAC1D,MAAI,gBAAgB;AACpB,MAAI,aAAa,UAAU,aAAa,SAAS;AAC/C,qBAAiB,EAAC,GAAG,IAAI,QAAQ,EAAC;AAClC,mBAAe,EAAC,GAAG,eAAe,GAAG,GAAG,GAAE;AAAA,EAC5C,OAAO;AAEL,qBAAiB,EAAC,GAAG,GAAG,IAAI,OAAM;AAClC,mBAAe,EAAC,GAAG,IAAI,GAAG,eAAe,EAAC;AAAA,EAC5C;AACA,SAAO,EAAC,gBAAgB,aAAY;AACtC;AAEA,SAAS,0BAA0B,SAAS,UAAU;AACpD,QAAM,EAAC,OAAO,QAAQ,QAAO,IAAI;AACjC,QAAM,SAAS,QAAQ,QAAQ,SAAS,QAAQ,cAAc;AAC9D,MAAI,aAAa,SAAS;AACxB,WAAO,QAAQ;AAAA,EACjB,WAAW,aAAa,UAAU;AAChC,WAAO,SAAS;AAAA,EAClB;AACA,SAAO,CAAC;AACV;AAEA,SAAS,oBAAoB,SAAS,UAAU,gBAAgB;AAC9D,QAAM,EAAC,GAAG,OAAO,QAAQ,QAAO,IAAI;AACpC,QAAM,QAAQ,QAAQ,QAAQ;AAC9B,QAAM,OAAO,qBAAqB,UAAU,QAAQ,OAAO;AAC3D,MAAI,WAAW;AACf,MAAI,aAAa,UAAU,aAAa,SAAS;AAC/C,gBAAY,EAAC,GAAG,eAAe,GAAG,GAAG,IAAI,QAAQ,QAAQ,KAAK,EAAC;AAC/D,cAAU,EAAC,GAAG,UAAU,IAAI,MAAM,GAAG,UAAU,EAAC;AAAA,EAClD,OAAO;AAEL,gBAAY,EAAC,GAAG,eAAe,IAAI,QAAQ,OAAO,KAAK,GAAG,GAAG,eAAe,EAAC;AAC7E,cAAU,EAAC,GAAG,UAAU,GAAG,GAAG,UAAU,IAAI,KAAI;AAAA,EAClD;AACA,SAAO,EAAC,WAAW,QAAO;AAC5B;AAEA,SAAS,qBAAqB,UAAU,SAAS;AAC/C,QAAM,OAAO,QAAQ;AACrB,MAAI,aAAa,UAAU,aAAa,OAAO;AAC7C,WAAO,CAAC;AAAA,EACV;AACA,SAAO;AACT;AAEA,SAAS,uBAAuB,SAAS,SAAS;AAChD,QAAM,WAAW,QAAQ;AACzB,MAAI,UAAU,SAAS,QAAQ,GAAG;AAChC,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,SAAS,OAAO;AACpD;AAEA,SAAS,2BAA2B,SAAS,SAAS;AACpD,QAAM,EAAC,GAAG,GAAG,IAAI,IAAI,OAAO,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAQ,IAAI;AAClF,QAAM,SAAS,EAAC,GAAG,SAAS,GAAG,QAAO;AACtC,QAAM,QAAQ,QAAQ;AACtB,QAAM,UAAU,QAAQ,OAAO,KAAK;AACpC,QAAM,UAAU,QAAQ,QAAQ,KAAK;AACrC,QAAM,UAAU,CAAC,GAAG,IAAI,SAAS,IAAI,SAAS,EAAE;AAChD,QAAM,UAAU,CAAC,IAAI,SAAS,IAAI,GAAG,EAAE;AACvC,QAAM,SAAS,CAAC;AAChB,WAAS,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACtC,UAAM,eAAe,QAAQ,EAAC,GAAG,QAAQ,KAAK,GAAG,GAAG,QAAQ,KAAK,EAAC,GAAG,QAAQ,UAAU,QAAQ,CAAC;AAChG,WAAO,KAAK;AAAA,MACV,UAAU,UAAU,KAAK;AAAA,MACzB,UAAU,sBAAsB,cAAc,EAAC,GAAG,QAAQ,GAAG,OAAM,CAAC;AAAA,IACtE,CAAC;AAAA,EACH;AACA,SAAO,OAAO,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,EAAE;AAC3D;AAEA,SAAS,eAAe,SAAS,SAAS,UAAU;AAClD,QAAM,EAAC,QAAQ,OAAM,IAAI;AACzB,QAAM,SAAS,QAAQ;AACvB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,aAAa,QAAQ;AACvB,SAAK;AAAA,EACP,WAAW,aAAa,SAAS;AAC/B,SAAK;AAAA,EACP,WAAW,aAAa,OAAO;AAC7B,SAAK;AAAA,EACP,WAAW,aAAa,UAAU;AAChC,SAAK;AAAA,EACP;AACA,SAAO,QAAQ,QAAQ,GAAG,CAAC;AAC7B;AAEA,IAAM,mBAAmB;AAAA,EACvB,UAAU,EAAC,KAAK,QAAQ,KAAK,QAAQ,OAAO,QAAQ,KAAK,SAAS,WAAW,KAAK,SAAS,KAAI;AAAA,EAC/F,UAAU,EAAC,KAAK,QAAQ,KAAK,QAAQ,OAAO,UAAU,KAAK,OAAO,WAAW,KAAK,SAAS,KAAI;AACjG;AAmBA,SAAS,WAAW,OAAO,OAAO,UAAU;AAC1C,UAAQ,OAAO,UAAU,WAAW,QAAQ,MAAM,MAAM,KAAK;AAC7D,SAAO,eAAS,KAAK,IAAI,MAAM,iBAAiB,KAAK,IAAI;AAC3D;AASA,SAAS,gBAAgB,QAAQ,SAAS,KAAK;AAC7C,QAAM,UAAU,QAAQ,GAAG;AAC3B,MAAI,WAAW,QAAQ,WAAW;AAChC,WAAO;AAAA,EACT;AACA,QAAM,OAAO,IAAI,OAAO,CAAC;AACzB,QAAM,OAAO,OAAO,OAAO,MAAM,EAAE,OAAO,CAAC,UAAU,MAAM,QAAQ,MAAM,SAAS,IAAI;AACtF,MAAI,KAAK,QAAQ;AACf,WAAO,KAAK,CAAC,EAAE;AAAA,EACjB;AACA,SAAO;AACT;AAOA,SAAS,oBAAoB,OAAO,SAAS;AAC3C,MAAI,OAAO;AACT,UAAM,UAAU,MAAM,QAAQ;AAC9B,UAAM,QAAQ,WAAW,OAAO,QAAQ,KAAK,UAAU,QAAQ,MAAM,QAAQ,KAAK;AAClF,UAAM,MAAM,WAAW,OAAO,QAAQ,KAAK,UAAU,QAAQ,QAAQ,QAAQ,GAAG;AAChF,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAOA,SAAS,cAAc,OAAO,SAAS;AACrC,QAAM,EAAC,WAAW,OAAM,IAAI;AAC5B,QAAM,SAAS,OAAO,gBAAgB,QAAQ,SAAS,UAAU,CAAC;AAClE,QAAM,SAAS,OAAO,gBAAgB,QAAQ,SAAS,UAAU,CAAC;AAClE,MAAI,IAAI,UAAU,QAAQ;AAC1B,MAAI,IAAI,UAAU,SAAS;AAE3B,MAAI,QAAQ;AACV,QAAI,WAAW,QAAQ,QAAQ,QAAQ,OAAO,OAAO,OAAO,QAAQ,CAAC;AAAA,EACvE;AAEA,MAAI,QAAQ;AACV,QAAI,WAAW,QAAQ,QAAQ,QAAQ,OAAO,MAAM,OAAO,SAAS,CAAC;AAAA,EACvE;AACA,SAAO,EAAC,GAAG,EAAC;AACd;AAOA,SAAS,qBAAqB,OAAO,SAAS;AAC5C,QAAM,SAAS,MAAM;AACrB,QAAM,SAAS,OAAO,gBAAgB,QAAQ,SAAS,UAAU,CAAC;AAClE,QAAM,SAAS,OAAO,gBAAgB,QAAQ,SAAS,UAAU,CAAC;AAElE,MAAI,CAAC,UAAU,CAAC,QAAQ;AACtB,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,EAAC,MAAM,GAAG,OAAO,GAAE,IAAI,UAAU,MAAM;AAC3C,MAAI,EAAC,KAAK,GAAG,QAAQ,GAAE,IAAI,UAAU,MAAM;AAC3C,QAAM,OAAO,yBAAyB,QAAQ,EAAC,KAAK,QAAQ,MAAM,KAAK,QAAQ,MAAM,OAAO,GAAG,KAAK,GAAE,CAAC;AACvG,MAAI,KAAK;AACT,OAAK,KAAK;AACV,QAAM,OAAO,yBAAyB,QAAQ,EAAC,KAAK,QAAQ,MAAM,KAAK,QAAQ,MAAM,OAAO,IAAI,KAAK,EAAC,CAAC;AACvG,MAAI,KAAK;AACT,OAAK,KAAK;AAEV,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,IACb,SAAS,KAAK,KAAK,KAAK;AAAA,IACxB,SAAS,KAAK,KAAK,KAAK;AAAA,EAC1B;AACF;AAOA,SAAS,uBAAuB,OAAO,SAAS;AAC9C,MAAI,CAAC,eAAe,OAAO,GAAG;AAC5B,UAAM,MAAM,qBAAqB,OAAO,OAAO;AAC/C,QAAI,SAAS,QAAQ;AACrB,QAAI,CAAC,UAAU,MAAM,MAAM,GAAG;AAC5B,eAAS,KAAK,IAAI,IAAI,OAAO,IAAI,MAAM,IAAI;AAC3C,cAAQ,SAAS;AAAA,IACnB;AACA,UAAM,OAAO,SAAS;AACtB,UAAM,gBAAgB,IAAI,UAAU,QAAQ;AAC5C,UAAM,gBAAgB,IAAI,UAAU,QAAQ;AAC5C,WAAO;AAAA,MACL,GAAG,gBAAgB;AAAA,MACnB,GAAG,gBAAgB;AAAA,MACnB,IAAI,gBAAgB;AAAA,MACpB,IAAI,gBAAgB;AAAA,MACpB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,MACP,QAAQ;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO,eAAe,OAAO,OAAO;AACtC;AAMA,SAAS,sBAAsB,OAAO,SAAS;AAC7C,QAAM,EAAC,QAAQ,UAAS,IAAI;AAC5B,QAAM,QAAQ,OAAO,QAAQ,OAAO;AACpC,QAAM,OAAO,EAAC,GAAG,UAAU,MAAM,GAAG,UAAU,KAAK,IAAI,UAAU,OAAO,IAAI,UAAU,OAAM;AAE5F,MAAI,OAAO;AACT,8BAA0B,OAAO,MAAM,OAAO;AAAA,EAChD,OAAO;AACL,iCAA6B,QAAQ,MAAM,OAAO;AAAA,EACpD;AACA,SAAO;AACT;AAQA,SAAS,6BAA6B,OAAO,SAAS;AACpD,QAAM,aAAa,qBAAqB,OAAO,OAAO;AACtD,aAAW,iBAAiB,wBAAwB,OAAO,YAAY,OAAO;AAC9E,aAAW,WAAW,CAAC;AAAA,IACrB,MAAM;AAAA,IACN,aAAa;AAAA,IACb,YAAY,gCAAgC,OAAO,YAAY,OAAO;AAAA,IACtE,gBAAgB,WAAW;AAAA,EAC7B,CAAC;AACD,SAAO;AACT;AAEA,SAAS,eAAe,OAAO,SAAS;AACtC,QAAM,QAAQ,cAAc,OAAO,OAAO;AAC1C,QAAM,OAAO,QAAQ,SAAS;AAC9B,SAAO;AAAA,IACL,GAAG,MAAM,IAAI,QAAQ,SAAS,QAAQ;AAAA,IACtC,GAAG,MAAM,IAAI,QAAQ,SAAS,QAAQ;AAAA,IACtC,IAAI,MAAM,IAAI,QAAQ,SAAS,QAAQ;AAAA,IACvC,IAAI,MAAM,IAAI,QAAQ,SAAS,QAAQ;AAAA,IACvC,SAAS,MAAM,IAAI,QAAQ;AAAA,IAC3B,SAAS,MAAM,IAAI,QAAQ;AAAA,IAC3B,QAAQ,QAAQ;AAAA,IAChB,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF;AAEA,SAAS,yBAAyB,OAAO,SAAS;AAChD,QAAM,SAAS,oBAAoB,OAAO,OAAO,KAAK;AACtD,SAAO;AAAA,IACL,OAAO,KAAK,IAAI,OAAO,OAAO,OAAO,GAAG;AAAA,IACxC,KAAK,KAAK,IAAI,OAAO,OAAO,OAAO,GAAG;AAAA,EACxC;AACF;AAEA,SAAS,0BAA0B,OAAO,MAAM,SAAS;AACvD,QAAM,MAAM,WAAW,OAAO,QAAQ,OAAO,GAAG;AAChD,QAAM,MAAM,WAAW,OAAO,QAAQ,UAAU,GAAG;AACnD,MAAI,MAAM,aAAa,GAAG;AACxB,SAAK,IAAI;AACT,SAAK,KAAK;AAAA,EACZ,OAAO;AACL,SAAK,IAAI;AACT,SAAK,KAAK;AAAA,EACZ;AACF;AAEA,SAAS,6BAA6B,QAAQ,MAAM,SAAS;AAC3D,aAAW,WAAW,OAAO,KAAK,gBAAgB,GAAG;AACnD,UAAM,QAAQ,OAAO,gBAAgB,QAAQ,SAAS,OAAO,CAAC;AAC9D,QAAI,OAAO;AACT,YAAM,EAAC,KAAK,KAAK,OAAO,KAAK,WAAW,QAAO,IAAI,iBAAiB,OAAO;AAC3E,YAAM,MAAM,oBAAoB,OAAO,EAAC,KAAK,QAAQ,GAAG,GAAG,KAAK,QAAQ,GAAG,GAAG,OAAO,MAAM,KAAK,GAAG,KAAK,MAAM,GAAG,EAAC,CAAC;AACnH,WAAK,SAAS,IAAI,IAAI;AACtB,WAAK,OAAO,IAAI,IAAI;AAAA,IACtB;AAAA,EACF;AACF;AAEA,SAAS,WAAW,EAAC,YAAY,QAAO,GAAG,WAAW,UAAU,SAAS;AACvE,QAAM,EAAC,GAAG,OAAO,IAAI,KAAK,OAAO,KAAI,IAAI;AACzC,SAAO,kBAAkB,EAAC,OAAO,KAAK,MAAM,aAAa,QAAQ,YAAW,GAAG;AAAA,IAC7E,UAAU,SAAS;AAAA,IACnB,SAAS,EAAC,OAAO,QAAQ,MAAM,KAAK,QAAQ,MAAK;AAAA,IACjD,QAAQ,QAAQ,MAAM;AAAA,IACtB,MAAM,UAAU;AAAA,EAClB,CAAC;AACH;AAEA,SAAS,WAAW,EAAC,YAAY,QAAO,GAAG,WAAW,UAAU,SAAS;AACvE,QAAM,EAAC,GAAG,OAAO,IAAI,KAAK,QAAQ,KAAI,IAAI;AAC1C,SAAO,kBAAkB,EAAC,OAAO,KAAK,MAAM,aAAa,QAAQ,YAAW,GAAG;AAAA,IAC7E,UAAU,SAAS;AAAA,IACnB,SAAS,EAAC,OAAO,QAAQ,KAAK,KAAK,QAAQ,OAAM;AAAA,IACjD,QAAQ,QAAQ,MAAM;AAAA,IACtB,MAAM,UAAU;AAAA,EAClB,CAAC;AACH;AAEA,SAAS,kBAAkB,SAAS,WAAW;AAC7C,QAAM,EAAC,OAAO,KAAK,YAAW,IAAI;AAClC,QAAM,EAAC,UAAU,SAAS,EAAC,OAAO,UAAU,KAAK,OAAM,GAAG,OAAM,IAAI;AACpE,QAAM,gBAAgB,MAAM,cAAc,QAAQ,WAAW,SAAS,UAAU;AAChF,SAAO,QAAQ,cAAc,IAAI,SAASF,qBAAoB,eAAe,QAAQ;AACvF;AAEA,SAAS,gCAAgC,OAAO,YAAY,SAAS;AACnE,QAAM,QAAQ,QAAQ;AACtB,QAAM,kBAAkB;AACxB,QAAM,QAAQ,UAAU;AACxB,QAAM,WAAW,WAAW,MAAM,QAAQ;AAC1C,QAAM,UAAU,UAAU,MAAM,OAAO;AACvC,QAAM,YAAY,iBAAiB,MAAM,KAAK,KAAK;AACnD,QAAM,IAAI,WAAW,EAAC,YAAY,QAAO,GAAG,WAAW,UAAU,OAAO;AACxE,QAAM,IAAI,WAAW,EAAC,YAAY,QAAO,GAAG,WAAW,UAAU,OAAO;AACxE,QAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,QAAM,SAAS,UAAU,SAAS,QAAQ;AAC1C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,IAAI,IAAI;AAAA,IACR,IAAI,IAAI;AAAA,IACR;AAAA,IACA;AAAA,IACA,SAAS,IAAI,QAAQ;AAAA,IACrB,SAAS,IAAI,SAAS;AAAA,IACtB,UAAU,MAAM;AAAA,EAClB;AAEF;AAEA,IAAM,YAAY,CAAC,SAAS,OAAO;AAOnC,IAAM,aAAa,UAAU,OAAO,OAAO;AAO3C,SAAS,gBAAgB,OAAO,OAAO,SAAS;AAC9C,QAAM,WAAW,UAAU,SAAS,YAAY,MAAM,SAAS;AAC/D,QAAM,eAAe;AAErB,YAAU,QAAQ,UAAQ;AACxB,QAAI,WAAW,QAAQ,IAAI,CAAC,GAAG;AAC7B,YAAM,eAAe;AAAA,IACvB;AAAA,EACF,CAAC;AAED,MAAI,CAAC,MAAM,YAAY,CAAC,MAAM,cAAc;AAC1C,UAAM,YAAY,QAAQ,WAAS;AACjC,UAAI,CAAC,MAAM,YAAY,WAAW,MAAM,KAAK,GAAG;AAC9C,cAAM,WAAW;AAAA,MACnB;AACA,UAAI,CAAC,MAAM,cAAc;AACvB,kBAAU,QAAQ,UAAQ;AACxB,cAAI,WAAW,MAAM,IAAI,CAAC,GAAG;AAC3B,kBAAM,WAAW;AACjB,kBAAM,eAAe;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAQA,SAAS,YAAY,OAAO,OAAO,SAAS;AAC1C,MAAI,MAAM,UAAU;AAClB,YAAQ,MAAM,MAAM;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AACH,eAAO,iBAAiB,OAAO,OAAO,OAAO;AAAA,MAC/C,KAAK;AACH,eAAO,kBAAkB,OAAO,OAAO,OAAO;AAAA,IAChD;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,OAAO,OAAO,SAAS;AAC/C,MAAI,CAAC,MAAM,cAAc;AACvB;AAAA,EACF;AAEA,MAAI;AAEJ,MAAI,MAAM,SAAS,aAAa;AAC9B,eAAW,YAAY,MAAM,iBAAiB,OAAO,QAAQ,WAAW;AAAA,EAC1E,OAAO;AACL,eAAW,CAAC;AAAA,EACd;AAEA,QAAM,WAAW,MAAM;AACvB,QAAM,UAAU;AAEhB,QAAM,UAAU,EAAC,OAAO,MAAK;AAC7B,MAAI,UAAU,mBAAmB,SAAS,SAAS,UAAU,QAAQ;AACrE,SAAO,mBAAmB,SAAS,SAAS,UAAU,QAAQ,KAAK;AACrE;AAEA,SAAS,mBAAmB,EAAC,OAAO,MAAK,GAAG,MAAM,UAAU,eAAe;AACzE,MAAI;AACJ,aAAW,WAAW,UAAU;AAC9B,QAAI,cAAc,QAAQ,OAAO,IAAI,GAAG;AACtC,gBAAU,cAAc,QAAQ,QAAQ,IAAI,KAAK,MAAM,UAAU,IAAI,GAAG,SAAS,KAAK,KAAK;AAAA,IAC7F;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,OAAO,OAAO,SAAS;AAChD,QAAM,YAAY,MAAM;AACxB,QAAM,WAAW,YAAY,MAAM,iBAAiB,OAAO,QAAQ,WAAW;AAC9E,MAAI;AACJ,aAAW,WAAW,UAAU;AAC9B,cAAU,cAAc,QAAQ,QAAQ,SAAS,UAAU,OAAO,SAAS,KAAK,KAAK;AAAA,EACvF;AACA,SAAO;AACT;AAEA,SAAS,cAAc,SAAS,SAAS,OAAO;AAC9C,SAAO,SAAS,SAAS,CAAC,QAAQ,UAAU,KAAK,CAAC,MAAM;AAC1D;AAQA,IAAM,eAAe,CAAC,aAAa,YAAY;AAO/C,SAAS,YAAY,OAAO,OAAO,SAAS;AAC1C,QAAM,kBAAkB,MAAM;AAC9B,QAAM,SAAS,UAAU,SAAS,cAAc,MAAM,KAAK;AAE3D,MAAI,CAAC,MAAM,QAAQ;AACjB,oBAAgB,QAAQ,WAAS;AAC/B,UAAI,CAAC,MAAM,QAAQ;AACjB,qBAAa,QAAQ,UAAQ;AAC3B,cAAI,WAAW,MAAM,QAAQ,IAAI,CAAC,GAAG;AACnC,kBAAM,SAAS;AAAA,UACjB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAOA,SAAS,WAAW,OAAO,SAAS,MAAM;AACxC,MAAI,MAAM,QAAQ;AAChB,UAAM,eAAe,QAAQ,QAAQ,IAAI,KAAK,MAAM,MAAM,IAAI;AAC9D,WAAO,SAAS,cAAc,CAAC,QAAQ,QAAQ,CAAC;AAAA,EAClD;AACF;AAaA,SAAS,iBAAiB,OAAO,OAAO,aAAa;AACnD,QAAM,QAAQ,eAAe,MAAM,QAAQ,OAAO,WAAW;AAC7D,MAAI,UAAU,iBAAiB,OAAO,OAAO,OAAO,cAAc;AAClE,YAAU,iBAAiB,OAAO,OAAO,OAAO,cAAc,KAAK;AACnE,MAAI,WAAW,WAAW,MAAM,sBAAsB,GAAG;AACvD,UAAM,uBAAuB;AAAA,EAC/B;AACF;AAMA,SAAS,mBAAmB,aAAa,QAAQ;AAC/C,aAAWG,eAAc,aAAa;AACpC,mBAAeA,aAAY,MAAM;AAAA,EACnC;AACF;AAEA,SAAS,iBAAiB,OAAO,OAAO,OAAO,gBAAgB;AAC7D,MAAI,eAAS,MAAM,KAAK,CAAC,KAAK,CAAC,kBAAkB,MAAM,SAAS,OAAO,cAAc,GAAG;AACtF,UAAM,UAAU,MAAM,KAAK,MAAM,MAAM,KAAK;AAC5C,UAAM,KAAK,IAAI,MAAM,KAAK;AAC1B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAkB,cAAc,OAAO,gBAAgB;AAC9D,SAAO,QAAQ,aAAa,KAAK,CAAC,KAAK,QAAQ,aAAa,cAAc,CAAC;AAC7E;AAEA,SAAS,eAAeA,aAAY,QAAQ;AAC1C,aAAW,OAAO,CAAC,WAAW,YAAY,UAAU,GAAG;AACrD,UAAM,UAAU,gBAAgB,QAAQA,aAAY,GAAG;AACvD,QAAI,WAAW,CAAC,OAAO,OAAO,KAAK,iBAAiBA,aAAY,GAAG,GAAG;AACpE,cAAQ,KAAK,2BAA2B,OAAO,qBAAqBA,YAAW,EAAE,GAAG;AAAA,IACtF;AAAA,EACF;AACF;AAEA,SAAS,iBAAiBA,aAAY,KAAK;AACzC,MAAI,QAAQ,WAAW;AACrB,WAAO;AAAA,EACT;AACA,QAAM,OAAO,IAAI,OAAO,CAAC;AACzB,aAAW,QAAQ,CAAC,OAAO,OAAO,OAAO,GAAG;AAC1C,QAAI,QAAQA,YAAW,OAAO,IAAI,CAAC,GAAG;AACpC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ,OAAO,aAAa;AAClD,QAAM,OAAO,MAAM;AACnB,QAAM,UAAU,MAAM;AACtB,QAAM,gBAAgB,OAAO;AAC7B,QAAM,SAAS;AAAA,IACb,KAAK,eAAe,MAAM,KAAK,OAAO,iBAAiB;AAAA,IACvD,KAAK,eAAe,MAAM,KAAK,OAAO,iBAAiB;AAAA,EACzD;AACA,aAAWA,eAAc,aAAa;AACpC,QAAIA,YAAW,YAAY,SAAS;AAClC,mBAAaA,aAAY,OAAO,CAAC,SAAS,UAAU,GAAG,MAAM;AAAA,IAC/D,WAAW,gBAAgB,QAAQA,aAAY,aAAa,MAAM,SAAS;AACzE,mBAAaA,aAAY,OAAO,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,GAAG,MAAM;AAAA,IACtF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAaA,aAAY,OAAO,OAAO,QAAQ;AACtD,aAAW,QAAQ,OAAO;AACxB,UAAM,MAAMA,YAAW,IAAI;AAC3B,QAAI,QAAQ,GAAG,GAAG;AAChB,YAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,aAAO,MAAM,KAAK,IAAI,OAAO,KAAK,KAAK;AACvC,aAAO,MAAM,KAAK,IAAI,OAAO,KAAK,KAAK;AAAA,IACzC;AAAA,EACF;AACF;AAEA,IAAM,gBAAN,cAA4B,QAAQ;AAAA,EAElC,QAAQ,QAAQ,QAAQ,MAAM,kBAAkB;AAC9C,UAAM,EAAC,GAAG,EAAC,IAAI,QAAQ,EAAC,GAAG,QAAQ,GAAG,OAAM,GAAG,KAAK,eAAe,gBAAgB,GAAG,UAAU,CAAC,KAAK,QAAQ,QAAQ,CAAC;AACvH,WAAO,WAAW,EAAC,GAAG,EAAC,GAAG,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,GAAG,gBAAgB,GAAG,MAAM,KAAK,OAAO;AAAA,EACvG;AAAA,EAEA,eAAe,kBAAkB;AAC/B,WAAO,sBAAsB,MAAM,gBAAgB;AAAA,EACrD;AAAA,EAEA,KAAK,KAAK;AACR,QAAI,KAAK;AACT,cAAU,KAAK,KAAK,eAAe,GAAG,KAAK,QAAQ,QAAQ;AAC3D,YAAQ,KAAK,MAAM,KAAK,OAAO;AAC/B,QAAI,QAAQ;AAAA,EACd;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,YAAY,KAAK,SAAS,CAAC;AAAA,EACzC;AAAA,EAEA,yBAAyB,OAAO,SAAS;AACvC,WAAO,6BAA6B,OAAO,OAAO;AAAA,EACpD;AACF;AAEA,cAAc,KAAK;AAEnB,cAAc,WAAW;AAAA,EACvB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,EAChB,YAAY,CAAC;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,SAAS;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,MACJ,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,GAAG;AAAA,EACL;AAAA,EACA,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,GAAG;AACL;AAEA,cAAc,gBAAgB;AAAA,EAC5B,aAAa;AAAA,EACb,iBAAiB;AACnB;AAEA,cAAc,cAAc;AAAA,EAC1B,OAAO;AAAA,IACL,WAAW;AAAA,EACb;AACF;AAEA,IAAM,0BAAN,cAAsC,QAAQ;AAAA,EAE5C,QAAQ,QAAQ,QAAQ,MAAM,kBAAkB;AAC9C,WAAO;AAAA,MACL,EAAC,GAAG,QAAQ,GAAG,OAAM;AAAA,MACrB,EAAC,MAAM,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,GAAG,gBAAgB,GAAG,QAAQ,KAAK,eAAe,gBAAgB,EAAC;AAAA,MAC7G;AAAA,MACA,EAAC,UAAU,KAAK,UAAU,aAAa,GAAG,cAAc,KAAK,QAAQ,aAAY;AAAA,IACnF;AAAA,EACF;AAAA,EAEA,eAAe,kBAAkB;AAC/B,WAAO,sBAAsB,MAAM,gBAAgB;AAAA,EACrD;AAAA,EAEA,KAAK,KAAK;AACR,UAAM,UAAU,KAAK;AACrB,QAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ,SAAS;AACxC;AAAA,IACF;AACA,mBAAe,KAAK,IAAI;AACxB,QAAI,KAAK;AACT,cAAU,KAAK,KAAK,eAAe,GAAG,KAAK,QAAQ;AACnD,cAAU,KAAK,MAAM,SAAS,KAAK,SAAS;AAC5C,QAAI,QAAQ;AAAA,EACd;AAAA,EAEA,yBAAyB,OAAO,SAAS;AACvC,UAAM,OAAO,eAAe,OAAO,OAAO;AAC1C,QAAI,CAAC,MAAM;AACT,aAAO,CAAC;AAAA,IACV;AACA,UAAM,EAAC,gBAAgB,OAAO,OAAM,IAAI,kBAAkB,OAAO,SAAS,IAAI;AAC9E,QAAI,YAAY,iBAAiB,MAAM,KAAK,OAAO;AACnD,UAAM,YAAY,YAAY,WAAW,MAAM;AAC/C,QAAI,UAAU,SAAS,SAAS,GAAG;AACjC,kBAAY,EAAC,OAAO,UAAU,QAAQ,WAAW,QAAQ,UAAU,SAAS,UAAS;AAAA,IACvF;AACA,UAAM,EAAC,UAAU,SAAS,QAAO,IAAI;AACrC,UAAM,UAAU,sBAAsB,OAAO,WAAW,EAAC,aAAa,GAAG,UAAU,SAAS,QAAO,CAAC;AACpG,WAAO;AAAA,MACL,gBAAgB,wBAAwB,OAAO,SAAS,OAAO;AAAA,MAC/D,GAAG;AAAA,MACH,GAAG;AAAA,MACH,UAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AAEA,wBAAwB,KAAK;AAE7B,wBAAwB,WAAW;AAAA,EACjC,SAAS;AAAA,EACT,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,YAAY,CAAC;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,MAAM;AAAA,IACJ,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,MAAM;AAAA,EACN,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,SAAS;AAAA,EACT,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AACX;AAEA,wBAAwB,gBAAgB,CACxC;AAEA,SAAS,eAAe,OAAO,SAAS;AACtC,SAAO,MAAM,6BAA6B,EAAE,OAAO,SAAS,QAAQ,OAAO;AACzE,UAAM,aAAa,MAAM;AACzB,QAAI,sBAAsB,sBACxB,oBAAoB,OAAO,SAAS,MAAM,IAAI,MAC7C,CAAC,UAAU,WAAW,cAAc,OAAO,WAAW,gBACvD,WAAW,QAAQ,iBAAiB,IAAI;AACxC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,GAAG,MAAS;AACd;AAEA,SAAS,oBAAoB,OAAO,SAAS,UAAU;AACrD,MAAI,CAAC,QAAQ,UAAU;AACrB,WAAO;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,QAAI,CAAC,SAAS,CAAC,EAAE,UAAU,MAAM,kBAAkB,CAAC,GAAG;AACrD,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,EAAC,UAAS,GAAG,SAAS,MAAM;AACrD,QAAM,EAAC,MAAM,KAAK,OAAO,OAAM,IAAI;AACnC,QAAM,EAAC,aAAa,SAAS,QAAO,IAAI,KAAK;AAC7C,QAAM,KAAK,OAAO,SAAS,IAAI;AAC/B,QAAM,KAAK,MAAM,UAAU,IAAI;AAC/B,QAAM,SAAS;AAAA,IACb,MAAM,KAAK,IAAI,IAAI,aAAa,IAAI;AAAA,IACpC,OAAO,KAAK,IAAI,IAAI,aAAa,KAAK;AAAA,IACtC,KAAK,KAAK,IAAI,IAAI,aAAa,GAAG;AAAA,IAClC,QAAQ,KAAK,IAAI,IAAI,aAAa,MAAM;AAAA,EAC1C;AACA,QAAM,QAAQ;AAAA,IACZ,IAAI,OAAO,OAAO,OAAO,SAAS;AAAA,IAClC,IAAI,OAAO,MAAM,OAAO,UAAU;AAAA,EACpC;AACA,QAAM,QAAQ,QAAQ,UAAU,QAAQ,cAAc;AACtD,QAAM,UAAU,cAAc;AAC9B,QAAM,oBAAoB,MAAM,IAAI;AACpC,QAAM,OAAO,oBAAoB,MAAM,QAAQ,SAAS;AACxD,QAAM,SAAS,UAAU,MAAM,GAAG,GAAG,OAAO;AAC5C,QAAM,iBAAiB;AAAA,IACrB,UAAU;AAAA,IACV,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,QAAQ,KAAK,IAAI,aAAa,KAAK,IAAI,OAAO,QAAQ,OAAO,MAAM,OAAO,SAAS,OAAO,GAAG,IAAI,CAAC;AAAA,EACpG;AACF;AAEA,SAAS,YAAY,EAAC,OAAO,OAAM,GAAG,QAAQ;AAC5C,QAAM,OAAO,KAAK,KAAK,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,QAAQ,CAAC,CAAC;AAC/D,SAAQ,SAAS,IAAK;AACxB;AAEA,SAAS,UAAU,GAAG,SAAS,SAAS,QAAQ;AAC9C,QAAM,MAAM,KAAK,IAAI,UAAU,GAAG,CAAC;AACnC,QAAM,KAAK,KAAK,IAAI,QAAQ,CAAC;AAC7B,QAAM,IAAI,UAAU;AACpB,QAAM,IAAI,KAAK,IAAI,SAAS,CAAC,IAAI,MAAM;AACvC,QAAM,QAAQ,KAAK,IAAI,GAAG,CAAC,IAAK,IAAI;AACpC,MAAI,SAAS,GAAG;AACd,WAAO;AAAA,MACL,aAAa;AAAA,MACb,WAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,SAAS,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AACxC,QAAM,OAAO,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AACtC,SAAO;AAAA,IACL,aAAa,kBAAkB,EAAC,GAAG,SAAS,GAAG,QAAO,GAAG,EAAC,GAAG,OAAO,EAAC,CAAC,EAAE;AAAA,IACxE,WAAW,kBAAkB,EAAC,GAAG,SAAS,GAAG,QAAO,GAAG,EAAC,GAAG,KAAK,EAAC,CAAC,EAAE;AAAA,EACtE;AACF;AAEA,SAAS,eAAe,KAAK,SAAS;AACpC,QAAM,EAAC,UAAU,UAAU,SAAS,aAAa,WAAW,mBAAmB,QAAO,IAAI;AAC1F,MAAI,KAAK;AACT,QAAM,SAAS,eAAe,KAAK,OAAO;AAC1C,MAAI,YAAY,QAAQ;AACxB,MAAI,UAAU;AACd,MAAI,IAAI,UAAU,UAAU,SAAS,aAAa,WAAW,iBAAiB;AAC9E,MAAI,UAAU;AACd,MAAI,KAAK;AACT,MAAI,QAAQ;AACV,QAAI,OAAO;AAAA,EACb;AACA,MAAI,QAAQ;AACd;AAEA,IAAM,kBAAN,cAA8B,QAAQ;AAAA,EAEpC,QAAQ,QAAQ,QAAQ,MAAM,kBAAkB;AAC9C,WAAO;AAAA,MACL,EAAC,GAAG,QAAQ,GAAG,OAAM;AAAA,MACrB,EAAC,MAAM,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,GAAG,gBAAgB,GAAG,QAAQ,KAAK,eAAe,gBAAgB,EAAC;AAAA,MAC7G;AAAA,MACA,EAAC,UAAU,KAAK,UAAU,aAAa,KAAK,QAAQ,aAAa,cAAc,KAAK,QAAQ,aAAY;AAAA,IAC1G;AAAA,EACF;AAAA,EAEA,eAAe,kBAAkB;AAC/B,WAAO,sBAAsB,MAAM,gBAAgB;AAAA,EACrD;AAAA,EAEA,KAAK,KAAK;AACR,UAAM,UAAU,KAAK;AACrB,UAAM,UAAU,CAAC,QAAQ,KAAK,QAAQ,KAAK,KAAK;AAChD,QAAI,CAAC,QAAQ,WAAW,CAAC,QAAQ,WAAW,CAAC,SAAS;AACpD;AAAA,IACF;AACA,QAAI,KAAK;AACT,cAAU,KAAK,KAAK,eAAe,GAAG,KAAK,QAAQ;AACnD,gBAAY,KAAK,IAAI;AACrB,YAAQ,KAAK,MAAM,OAAO;AAC1B,cAAU,KAAK,aAAa,IAAI,GAAG,OAAO;AAC1C,QAAI,QAAQ;AAAA,EACd;AAAA,EAEA,yBAAyB,OAAO,SAAS;AACvC,QAAI;AACJ,QAAI,CAAC,eAAe,OAAO,GAAG;AAC5B,YAAM,EAAC,SAAS,QAAO,IAAI,qBAAqB,OAAO,OAAO;AAC9D,cAAQ,EAAC,GAAG,SAAS,GAAG,QAAO;AAAA,IACjC,OAAO;AACL,cAAQ,cAAc,OAAO,OAAO;AAAA,IACtC;AACA,UAAM,UAAU,UAAU,QAAQ,OAAO;AACzC,UAAM,YAAY,iBAAiB,MAAM,KAAK,OAAO;AACrD,UAAM,UAAU,sBAAsB,OAAO,WAAW,SAAS,OAAO;AACxE,WAAO;AAAA,MACL,gBAAgB,wBAAwB,OAAO,SAAS,OAAO;AAAA,MAC/D,QAAQ,MAAM;AAAA,MACd,QAAQ,MAAM;AAAA,MACd,GAAG;AAAA,MACH,UAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AACF;AAEA,gBAAgB,KAAK;AAErB,gBAAgB,WAAW;AAAA,EACzB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,EAChB,YAAY,CAAC;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,SAAS;AAAA,IACP,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,YAAY,CAAC;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,aAAa;AAAA,IACb,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,MAAM;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,MAAM;AAAA,IACJ,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAAA,EACA,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,WAAW;AAAA,EACX,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,GAAG;AACL;AAEA,gBAAgB,gBAAgB;AAAA,EAC9B,aAAa;AACf;AAEA,SAAS,aAAa,EAAC,GAAG,GAAG,OAAO,QAAQ,QAAO,GAAG;AACpD,QAAM,eAAe,QAAQ,cAAc;AAC3C,QAAM,UAAU,UAAU,QAAQ,OAAO;AACzC,SAAO;AAAA,IACL,GAAG,IAAI,QAAQ,OAAO;AAAA,IACtB,GAAG,IAAI,QAAQ,MAAM;AAAA,IACrB,OAAO,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,QAAQ;AAAA,IACtD,QAAQ,SAAS,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC1D;AACF;AAEA,IAAM,cAAc,CAAC,IAAI,IAAI,OAAO,EAAC,GAAG,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG,GAAE;AAC7F,IAAM,eAAe,CAAC,GAAG,IAAI,OAAO,YAAY,IAAI,IAAI,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC,EAAE;AAC9F,IAAM,eAAe,CAAC,GAAG,IAAI,OAAO,YAAY,IAAI,IAAI,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC,EAAE;AAC9F,IAAM,MAAM,OAAK,IAAI;AACrB,IAAM,aAAa,CAAC,QAAQ,QAAQ,EAAC,GAAG,GAAG,IAAI,GAAE,GAAG,SAAS,SAAS,MAAM,EAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,OAAO,OAAM,IAAI,EAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,OAAO,OAAM;AAExM,IAAM,eAAe,CAAC,OAAO,IAAI,KAAK,OAAO,IAAI,MAAM,IAAI,KAAK,QAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI;AACvG,IAAM,eAAe,CAAC,OAAO,IAAI,KAAK,OAAO,EAAC,GAAG,aAAa,MAAM,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,aAAa,MAAM,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,EAAC;AAChI,IAAM,oBAAoB,CAAC,OAAO,IAAI,KAAK,MAAM,KAAK,IAAI,MAAM,KAAK,SAAS,IAAI,KAAK,MAAM;AAC7F,IAAM,eAAe,CAAC,OAAO,IAAI,KAAK,MAAM,CAAC,KAAK,MAAM,kBAAkB,MAAM,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,kBAAkB,MAAM,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,MAAM;AAExJ,IAAM,iBAAN,cAA6B,QAAQ;AAAA,EAEnC,QAAQ,QAAQ,QAAQ,MAAM,kBAAkB;AAC9C,UAAM,WAAW,KAAK,QAAQ,cAAc,KAAK,QAAQ,gBAAgB;AACzE,QAAI,SAAS,OAAO,SAAS,KAAK;AAChC,YAAM,QAAQ,EAAC,QAAQ,OAAM;AAC7B,YAAM,EAAC,MAAM,IAAG,IAAI;AACpB,UAAI,MAAM;AACR,uBAAe,KAAK,KAAK,OAAO;AAChC,YAAI,aAAa,KAAK,QAAQ;AAC9B,cAAM,EAAC,MAAK,IAAI,KAAK;AACrB,cAAM,KAAK,SAAS,MAAM;AAC1B,cAAM,KAAK,SAAS,MAAM;AAC1B,cAAM,SAAS,IAAI,gBAAgB,MAAM,IAAI,EAAE,KAAK,UAAU,MAAM,OAAO,gBAAgB;AAC3F,YAAI,QAAQ;AACZ,eAAO;AAAA,MACT;AACA,YAAM,UAAU,IAAI,OAAO;AAC3B,aAAO,WAAW,MAAM,OAAO,SAAS,gBAAgB,KAAK,UAAU,MAAM,OAAO,gBAAgB;AAAA,IACtG;AACA,WAAO,YAAY,MAAM,EAAC,QAAQ,OAAM,GAAG,MAAM,EAAC,SAAS,iBAAgB,CAAC;AAAA,EAC9E;AAAA,EAEA,eAAe,kBAAkB;AAC/B,WAAO,sBAAsB,MAAM,gBAAgB;AAAA,EACrD;AAAA,EAEA,KAAK,KAAK;AACR,UAAM,EAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAO,IAAI;AAEpC,QAAI,KAAK;AACT,QAAI,CAAC,eAAe,KAAK,OAAO,GAAG;AAEjC,aAAO,IAAI,QAAQ;AAAA,IACrB;AACA,mBAAe,KAAK,OAAO;AAE3B,UAAM,SAAS,KAAK,KAAK,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC,CAAC;AAClE,QAAI,QAAQ,SAAS,IAAI;AACvB,gBAAU,KAAK,MAAM,IAAI,MAAM;AAC/B,aAAO,IAAI,QAAQ;AAAA,IACrB;AACA,UAAM,EAAC,WAAW,SAAS,aAAa,UAAS,IAAI,cAAc,IAAI;AACvE,UAAM,QAAQ,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC;AACvC,QAAI,UAAU,GAAG,CAAC;AAClB,QAAI,OAAO,KAAK;AAChB,QAAI,UAAU;AACd,QAAI,OAAO,IAAI,aAAa,CAAC;AAC7B,QAAI,OAAO,SAAS,WAAW,CAAC;AAChC,QAAI,cAAc,QAAQ;AAC1B,QAAI,OAAO;AACX,kBAAc,KAAK,GAAG,aAAa,SAAS;AAC5C,kBAAc,KAAK,QAAQ,CAAC,WAAW,OAAO;AAC9C,QAAI,QAAQ;AAAA,EACd;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,YAAY,KAAK,SAAS,CAAC;AAAA,EACzC;AAAA,EAEA,yBAAyB,OAAO,SAAS;AACvC,UAAM,OAAO,sBAAsB,OAAO,OAAO;AACjD,UAAM,EAAC,GAAG,GAAG,IAAI,GAAE,IAAI;AACvB,UAAM,SAAS,aAAa,MAAM,MAAM,SAAS;AACjD,UAAM,aAAa,SACf,gBAAgB,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,IAAI,GAAG,GAAE,GAAG,MAAM,SAAS,IACvD,EAAC,GAAG,GAAG,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,CAAC,GAAG,QAAQ,KAAK,IAAI,KAAK,CAAC,EAAC;AACpE,eAAW,WAAW,KAAK,KAAK;AAChC,eAAW,WAAW,KAAK,KAAK;AAChC,eAAW,iBAAiB,wBAAwB,OAAO,YAAY,OAAO;AAC9E,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,EAAC,GAAG,WAAW,GAAG,GAAG,WAAW,EAAC;AAC5C,YAAM,KAAK,EAAC,GAAG,WAAW,IAAI,GAAG,WAAW,GAAE;AAC9C,iBAAW,KAAK,gBAAgB,YAAY,SAAS,sBAAsB,IAAI,EAAE,CAAC;AAAA,IACpF;AACA,UAAM,kBAAkB,8BAA8B,OAAO,YAAY,QAAQ,KAAK;AAEtF,oBAAgB,WAAW;AAE3B,eAAW,WAAW,CAAC;AAAA,MACrB,MAAM;AAAA,MACN,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,gBAAgB,WAAW;AAAA,IAC7B,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAEA,eAAe,KAAK;AAEpB,IAAM,qBAAqB;AAAA,EACzB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,OAAO;AACT;AAEA,eAAe,WAAW;AAAA,EACxB,kBAAkB;AAAA,EAClB,YAAY;AAAA,IACV,SAAS;AAAA,IACT,KAAK,OAAO,OAAO,CAAC,GAAG,kBAAkB;AAAA,IACzC,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB;AAAA,IAC3C,OAAO;AAAA,EACT;AAAA,EACA,YAAY,CAAC;AAAA,EACb,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,OAAO;AAAA,EACP,cAAc;AAAA,IACZ,GAAG;AAAA,EACL;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,YAAY,CAAC;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,aAAa;AAAA,IACb,SAAS,OAAO,OAAO,CAAC,GAAG,gBAAgB,SAAS,OAAO;AAAA,IAC3D,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,MACJ,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,eAAe;AAAA,IACf,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,GAAG;AAAA,EACL;AAAA,EACA,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,GAAG;AACL;AAEA,eAAe,cAAc;AAAA,EAC3B,YAAY;AAAA,IACV,OAAO;AAAA,MACL,WAAW;AAAA,IACb;AAAA,IACA,KAAK;AAAA,MACH,WAAW;AAAA,IACb;AAAA,IACA,WAAW;AAAA,EACb;AACF;AAEA,eAAe,gBAAgB;AAAA,EAC7B,aAAa;AACf;AAEA,SAAS,YAAY,SAAS,EAAC,QAAQ,OAAM,GAAG,MAAM,EAAC,SAAS,iBAAgB,GAAG;AACjF,QAAM,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,GAAG,gBAAgB,GAAG,IAAI;AACzG,SAAO,QAAQ,OAAO,OAAO,KAAK,UAAU,SAAS,EAAC,QAAQ,OAAM,GAAG,kBAAkB,IAAI;AAC/F;AAEA,SAAS,aAAa,EAAC,GAAG,GAAG,IAAI,GAAE,GAAG,EAAC,KAAK,OAAO,QAAQ,KAAI,GAAG;AAChE,SAAO,EACJ,IAAI,QAAQ,KAAK,QACjB,IAAI,SAAS,KAAK,SAClB,IAAI,OAAO,KAAK,OAChB,IAAI,UAAU,KAAK;AAExB;AAEA,SAAS,iBAAiB,EAAC,GAAG,EAAC,GAAG,IAAI,EAAC,KAAK,OAAO,QAAQ,KAAI,GAAG;AAChE,MAAI,IAAI,MAAM;AACZ,QAAI,aAAa,MAAM,EAAC,GAAG,EAAC,GAAG,EAAE;AACjC,QAAI;AAAA,EACN;AACA,MAAI,IAAI,OAAO;AACb,QAAI,aAAa,OAAO,EAAC,GAAG,EAAC,GAAG,EAAE;AAClC,QAAI;AAAA,EACN;AACA,MAAI,IAAI,KAAK;AACX,QAAI,aAAa,KAAK,EAAC,GAAG,EAAC,GAAG,EAAE;AAChC,QAAI;AAAA,EACN;AACA,MAAI,IAAI,QAAQ;AACd,QAAI,aAAa,QAAQ,EAAC,GAAG,EAAC,GAAG,EAAE;AACnC,QAAI;AAAA,EACN;AACA,SAAO,EAAC,GAAG,EAAC;AACd;AAEA,SAAS,gBAAgB,IAAI,IAAI,MAAM;AACrC,QAAM,EAAC,GAAG,EAAC,IAAI,iBAAiB,IAAI,IAAI,IAAI;AAC5C,QAAM,EAAC,GAAG,IAAI,GAAG,GAAE,IAAI,iBAAiB,IAAI,IAAI,IAAI;AACpD,SAAO,EAAC,GAAG,GAAG,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,CAAC,GAAG,QAAQ,KAAK,IAAI,KAAK,CAAC,EAAC;AACzE;AAEA,SAAS,WAAW,SAAS,EAAC,QAAQ,OAAM,GAAG,UAAU,SAAS,kBAAkB;AAElF,QAAM,EAAC,GAAG,IAAI,GAAG,IAAI,IAAI,GAAE,IAAI,QAAQ,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,GAAG,gBAAgB;AACxF,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,QAAQ,IAAI,EAAE,IAAI,IAAI,EAAE;AAC9B,QAAM,IAAI,UAAU,IAAI,OAAO,SAAS,MAAM,MAAM,SAAS,MAAM,MAAM;AAEzE,MAAI,IAAI;AACR,MAAI,IAAI,GAAG;AACT,SAAK;AACL,SAAK;AAAA,EACP,WAAW,IAAI,GAAG;AAChB,SAAK;AACL,SAAK;AAAA,EACP,OAAO;AACL,SAAK,KAAK,IAAI;AACd,SAAK,KAAK,IAAI;AAAA,EAChB;AACA,SAAQ,IAAI,SAAS,EAAE,IAAI,IAAI,SAAS,EAAE,KAAM;AAClD;AAEA,SAAS,UAAU,SAAS,EAAC,QAAQ,OAAM,GAAG,kBAAkB,MAAM;AACpE,QAAM,QAAQ,QAAQ;AACtB,SAAO,MAAM,QAAQ,WAAW,MAAM,QAAQ,QAAQ,QAAQ,MAAM,gBAAgB;AACtF;AAEA,SAAS,8BAA8B,OAAO,YAAY,SAAS;AACjE,QAAM,cAAc,QAAQ;AAC5B,QAAM,UAAU,UAAU,QAAQ,OAAO;AACzC,QAAM,WAAW,iBAAiB,MAAM,KAAK,OAAO;AACpD,QAAM,QAAQ,SAAS,QAAQ,QAAQ,QAAQ;AAC/C,QAAM,SAAS,SAAS,SAAS,QAAQ,SAAS;AAClD,SAAO,uBAAuB,YAAY,SAAS,EAAC,OAAO,QAAQ,QAAO,GAAG,MAAM,SAAS;AAC9F;AAEA,SAAS,sBAAsB,YAAY;AACzC,QAAM,EAAC,GAAG,GAAG,IAAI,GAAE,IAAI;AACvB,QAAM,WAAW,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC;AAE1C,SAAO,WAAW,KAAK,IAAI,WAAW,KAAK,WAAW,KAAK,KAAK,WAAW,KAAK;AAClF;AAEA,SAAS,uBAAuB,YAAY,OAAO,OAAO,WAAW;AACnE,QAAM,EAAC,OAAO,QAAQ,QAAO,IAAI;AACjC,QAAM,EAAC,SAAS,QAAO,IAAI;AAC3B,QAAM,KAAK,EAAC,GAAG,WAAW,GAAG,GAAG,WAAW,EAAC;AAC5C,QAAM,KAAK,EAAC,GAAG,WAAW,IAAI,GAAG,WAAW,GAAE;AAC9C,QAAM,WAAW,MAAM,aAAa,SAAS,sBAAsB,UAAU,IAAI,UAAU,MAAM,QAAQ;AACzG,QAAM,OAAO,YAAY,OAAO,QAAQ,QAAQ;AAChD,QAAM,IAAI,WAAW,YAAY,OAAO,EAAC,WAAW,MAAM,QAAO,GAAG,SAAS;AAC7E,QAAM,KAAK,WAAW,KAAK,aAAa,IAAI,WAAW,IAAI,IAAI,CAAC,IAAI,YAAY,IAAI,IAAI,CAAC;AACzF,QAAM,mBAAmB,EAAC,MAAM,KAAK,GAAG,KAAK,UAAU,MAAM,KAAK,UAAU,OAAO,SAAS,QAAQ,KAAI;AACxG,QAAM,mBAAmB,EAAC,MAAM,KAAK,GAAG,KAAK,UAAU,KAAK,KAAK,UAAU,QAAQ,SAAS,QAAQ,IAAG;AACvG,QAAM,UAAU,sBAAsB,GAAG,GAAG,gBAAgB,IAAI;AAChE,QAAM,UAAU,sBAAsB,GAAG,GAAG,gBAAgB,IAAI;AAChE,SAAO;AAAA,IACL,GAAG,UAAW,QAAQ;AAAA,IACtB,GAAG,UAAW,SAAS;AAAA,IACvB,IAAI,UAAW,QAAQ;AAAA,IACvB,IAAI,UAAW,SAAS;AAAA,IACxB;AAAA,IACA;AAAA,IACA,QAAQ,GAAG;AAAA,IACX,QAAQ,GAAG;AAAA,IACX;AAAA,IACA;AAAA,IACA,UAAU,UAAU,QAAQ;AAAA,EAC9B;AACF;AAEA,SAAS,YAAY,OAAO,QAAQ,UAAU;AAC5C,QAAM,MAAM,KAAK,IAAI,QAAQ;AAC7B,QAAM,MAAM,KAAK,IAAI,QAAQ;AAC7B,SAAO;AAAA,IACL,GAAG,KAAK,IAAI,QAAQ,GAAG,IAAI,KAAK,IAAI,SAAS,GAAG;AAAA,IAChD,GAAG,KAAK,IAAI,QAAQ,GAAG,IAAI,KAAK,IAAI,SAAS,GAAG;AAAA,EAClD;AACF;AAEA,SAAS,WAAW,YAAY,OAAO,OAAO,WAAW;AACvD,MAAI;AACJ,QAAM,QAAQ,YAAY,YAAY,SAAS;AAC/C,MAAI,MAAM,aAAa,SAAS;AAC9B,QAAI,iBAAiB,EAAC,GAAG,WAAW,KAAK,WAAW,GAAG,GAAG,WAAW,KAAK,WAAW,EAAC,GAAG,OAAO,OAAO,KAAK;AAAA,EAC9G,WAAW,MAAM,aAAa,OAAO;AACnC,QAAI,IAAI,iBAAiB,EAAC,GAAG,WAAW,IAAI,WAAW,IAAI,GAAG,WAAW,IAAI,WAAW,GAAE,GAAG,OAAO,OAAO,KAAK;AAAA,EAClH,OAAO;AACL,QAAIH,qBAAoB,GAAG,MAAM,QAAQ;AAAA,EAC3C;AACA,SAAO;AACT;AAEA,SAAS,iBAAiB,UAAU,OAAO,OAAO,OAAO;AACvD,QAAM,EAAC,WAAW,QAAO,IAAI;AAC7B,QAAM,QAAQ,SAAS,IAAI,MAAM;AACjC,QAAM,QAAQ,SAAS,IAAI,MAAM;AACjC,QAAM,IAAK,QAAQ,MAAQ,UAAU,IAAI,IAAI,QAAQ,OAAO,MAAM,KAAK;AACvE,QAAM,IAAK,QAAQ,MAAQ,UAAU,IAAI,IAAI,QAAQ,MAAM,MAAM,KAAK;AACtE,SAAO,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI;AACtC;AAEA,SAAS,YAAY,YAAY,WAAW;AAC1C,QAAM,EAAC,GAAG,IAAI,GAAG,GAAE,IAAI;AACvB,QAAM,IAAI,KAAK,IAAI,GAAG,EAAE,IAAI,UAAU;AACtC,QAAM,IAAI,KAAK,IAAI,GAAG,EAAE,IAAI,UAAU;AACtC,QAAM,IAAI,UAAU,SAAS,KAAK,IAAI,GAAG,EAAE;AAC3C,QAAM,IAAI,UAAU,QAAQ,KAAK,IAAI,GAAG,EAAE;AAC1C,SAAO;AAAA,IACL,GAAG,KAAK,IAAI,GAAG,CAAC;AAAA,IAChB,GAAG,KAAK,IAAI,GAAG,CAAC;AAAA,IAChB,IAAI,KAAK,IAAI,IAAI;AAAA,IACjB,IAAI,KAAK,IAAI,IAAI;AAAA,EACnB;AACF;AAEA,SAAS,sBAAsB,YAAY,YAAY;AACrD,QAAM,EAAC,MAAM,KAAK,KAAK,QAAO,IAAI;AAClC,QAAM,WAAW,OAAO;AACxB,MAAI,OAAO,MAAM,KAAK;AAEpB,YAAQ,MAAM,OAAO;AAAA,EACvB;AACA,MAAI,OAAQ,aAAa,UAAU,UAAW;AAC5C,iBAAa,MAAM,UAAU;AAAA,EAC/B;AACA,MAAI,OAAQ,aAAa,UAAU,UAAW;AAC5C,iBAAa,MAAM,UAAU;AAAA,EAC/B;AACA,SAAO;AACT;AAEA,SAAS,cAAc,MAAM;AAC3B,QAAM,UAAU,KAAK;AACrB,QAAM,iBAAiB,QAAQ,cAAc,QAAQ,WAAW;AAChE,QAAM,eAAe,QAAQ,cAAc,QAAQ,WAAW;AAC9D,SAAO;AAAA,IACL,WAAW;AAAA,IACX,SAAS;AAAA,IACT,aAAa,cAAc,MAAM,cAAc;AAAA,IAC/C,WAAW,cAAc,MAAM,YAAY;AAAA,EAC7C;AACF;AAEA,SAAS,cAAc,MAAM,WAAW;AACtC,MAAI,CAAC,aAAa,CAAC,UAAU,SAAS;AACpC,WAAO;AAAA,EACT;AACA,QAAM,EAAC,QAAQ,MAAK,IAAI;AACxB,QAAM,SAAS,KAAK,QAAQ,cAAc;AAC1C,QAAM,KAAK,EAAC,GAAG,QAAQ,GAAG,QAAQ,OAAM;AACxC,QAAM,KAAK,EAAC,GAAG,GAAG,GAAG,OAAM;AAC3B,SAAO,KAAK,IAAI,aAAa,GAAG,IAAI,EAAE,CAAC;AACzC;AAEA,SAAS,cAAc,KAAK,QAAQ,QAAQ,WAAW;AACrD,MAAI,CAAC,aAAa,CAAC,UAAU,SAAS;AACpC;AAAA,EACF;AACA,QAAM,EAAC,QAAQ,OAAO,MAAM,iBAAiB,YAAW,IAAI;AAC5D,QAAM,eAAe,KAAK,IAAI,SAAS,MAAM,IAAI;AACjD,MAAI,UAAU;AACd,iBAAe,KAAK,SAAS;AAC7B,iBAAe,KAAK,SAAS;AAC7B,MAAI,OAAO,cAAc,CAAC,KAAK;AAC/B,MAAI,OAAO,SAAS,QAAQ,CAAC;AAC7B,MAAI,OAAO,cAAc,KAAK;AAC9B,MAAI,SAAS,MAAM;AACjB,QAAI,YAAY,mBAAmB;AACnC,QAAI,UAAU;AACd,QAAI,KAAK;AACT,QAAI,cAAc;AAAA,EACpB,OAAO;AACL,QAAI,cAAc,UAAU;AAAA,EAC9B;AACA,MAAI,OAAO;AACb;AAEA,SAAS,gBAAgB,YAAY,SAAS,UAAU;AACtD,QAAM,EAAC,GAAG,GAAG,IAAI,IAAI,SAAS,QAAO,IAAI;AACzC,QAAM,QAAQ,KAAK,MAAM,KAAK,GAAG,KAAK,CAAC;AACvC,QAAM,KAAK,WAAW,QAAQ,cAAc,CAAC;AAC7C,QAAM,QAAQ;AAAA,IACZ,GAAG,UAAU,QAAQ,UAAU,GAAG,GAAG,KAAK;AAAA,IAC1C,GAAG,UAAU,QAAQ,UAAU,GAAG,GAAG,KAAK;AAAA,EAC5C;AACA,SAAO,QAAQ,OAAO,EAAC,GAAG,SAAS,GAAG,QAAO,GAAG,KAAK;AACvD;AAEA,SAAS,qBAAqB,KAAK,EAAC,GAAG,EAAC,GAAG,EAAC,OAAO,OAAM,GAAG,WAAW;AACrE,MAAI,CAAC,aAAa,CAAC,UAAU,SAAS;AACpC;AAAA,EACF;AACA,MAAI,KAAK;AACT,MAAI,UAAU,GAAG,CAAC;AAClB,MAAI,OAAO,KAAK;AAChB,gBAAc,KAAK,GAAG,CAAC,QAAQ,SAAS;AACxC,MAAI,QAAQ;AACd;AAEA,SAAS,UAAU,KAAK,SAAS,IAAI,QAAQ;AAC3C,QAAM,EAAC,GAAG,GAAG,IAAI,IAAI,QAAO,IAAI;AAChC,QAAM,EAAC,WAAW,SAAS,aAAa,UAAS,IAAI,cAAc,OAAO;AAC1E,QAAM,KAAK,EAAC,GAAG,EAAC;AAChB,QAAM,KAAK,EAAC,GAAG,IAAI,GAAG,GAAE;AACxB,QAAM,aAAa,aAAa,IAAI,IAAI,IAAI,CAAC;AAC7C,QAAM,WAAW,aAAa,IAAI,IAAI,IAAI,CAAC,IAAI;AAC/C,QAAM,KAAK,aAAa,IAAI,IAAI,IAAI,cAAc,MAAM;AACxD,QAAM,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI,YAAY,MAAM;AAE1D,QAAM,OAAO,IAAI,OAAO;AACxB,MAAI,UAAU;AACd,OAAK,OAAO,GAAG,GAAG,GAAG,CAAC;AACtB,OAAK,iBAAiB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC5C,MAAI,cAAc,QAAQ;AAC1B,MAAI,OAAO,IAAI;AACf,UAAQ,OAAO;AACf,UAAQ,MAAM;AACd,uBAAqB,KAAK,IAAI,EAAC,OAAO,YAAY,QAAQ,YAAW,GAAG,SAAS;AACjF,uBAAqB,KAAK,IAAI,EAAC,OAAO,UAAU,QAAQ,UAAS,GAAG,OAAO;AAC7E;AAEA,IAAM,oBAAN,cAAgC,QAAQ;AAAA,EAEtC,QAAQ,QAAQ,QAAQ,MAAM,kBAAkB;AAC9C,UAAM,WAAW,KAAK,QAAQ;AAC9B,UAAM,WAAW,KAAK,QAAQ,cAAc,KAAK,QAAQ,gBAAgB;AACzE,QAAI,SAAS,OAAO,SAAS,KAAK;AAChC,aAAO,eAAe,EAAC,GAAG,QAAQ,GAAG,OAAM,GAAG,KAAK,SAAS,CAAC,SAAS,UAAU,WAAW,SAAS,GAAG,gBAAgB,GAAG,UAAU,OAAO;AAAA,IAC7I;AACA,UAAM,EAAC,GAAG,GAAG,IAAI,GAAE,IAAI,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,IAAI,GAAG,gBAAgB;AAC7E,UAAM,QAAQ,SAAS,MAAM,EAAC,OAAO,GAAG,KAAK,GAAE,IAAI,EAAC,OAAO,GAAG,KAAK,GAAE;AACrE,UAAM,eAAe,QAAQ,EAAC,GAAG,QAAQ,GAAG,OAAM,GAAG,KAAK,eAAe,gBAAgB,GAAG,UAAU,CAAC,QAAQ,CAAC;AAChH,WAAO,aAAa,IAAI,KAAK,MAAM,QAAQ,UAAU,WAAW,aAAa,IAAI,KAAK,MAAM,MAAM,UAAU;AAAA,EAC9G;AAAA,EAEA,eAAe,kBAAkB;AAC/B,WAAO,sBAAsB,MAAM,gBAAgB;AAAA,EACrD;AAAA,EAEA,KAAK,KAAK;AACR,UAAM,EAAC,OAAO,QAAQ,SAAS,SAAS,QAAO,IAAI;AACnD,QAAI,KAAK;AACT,cAAU,KAAK,KAAK,eAAe,GAAG,QAAQ,QAAQ;AACtD,mBAAe,KAAK,KAAK,OAAO;AAChC,QAAI,UAAU;AACd,QAAI,YAAY,QAAQ;AACxB,UAAM,SAAS,eAAe,KAAK,OAAO;AAC1C,QAAI,QAAQ,SAAS,SAAS,SAAS,GAAG,QAAQ,GAAG,KAAK,GAAG,GAAG,IAAI,EAAE;AACtE,QAAI,KAAK;AACT,QAAI,QAAQ;AACV,UAAI,cAAc,QAAQ;AAC1B,UAAI,OAAO;AAAA,IACb;AACA,QAAI,QAAQ;AAAA,EACd;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,YAAY,KAAK,SAAS,CAAC;AAAA,EACzC;AAAA,EAEA,yBAAyB,OAAO,SAAS;AACvC,WAAO,6BAA6B,OAAO,OAAO;AAAA,EACpD;AAEF;AAEA,kBAAkB,KAAK;AAEvB,kBAAkB,WAAW;AAAA,EAC3B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,YAAY,CAAC;AAAA,EACb,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,MAAM;AAAA,EACN,OAAO,OAAO,OAAO,CAAC,GAAG,cAAc,SAAS,KAAK;AAAA,EACrD,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,GAAG;AACL;AAEA,kBAAkB,gBAAgB;AAAA,EAChC,aAAa;AAAA,EACb,iBAAiB;AACnB;AAEA,kBAAkB,cAAc;AAAA,EAC9B,OAAO;AAAA,IACL,WAAW;AAAA,EACb;AACF;AAEA,SAAS,eAAe,GAAG,SAAS,UAAU,SAAS;AACrD,QAAM,EAAC,OAAO,QAAQ,SAAS,QAAO,IAAI;AAC1C,QAAM,UAAU,QAAQ;AACxB,QAAM,UAAU,SAAS;AAEzB,MAAI,WAAW,KAAK,WAAW,GAAG;AAChC,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,UAAU,YAAY,CAAC;AACrC,QAAM,WAAW,KAAK,IAAI,KAAK;AAC/B,QAAM,WAAW,KAAK,IAAI,KAAK;AAC/B,QAAM,IAAI,KAAK,IAAI,YAAY,EAAE,IAAI,WAAW,YAAY,EAAE,IAAI,UAAU,CAAC;AAC7E,QAAM,IAAI,KAAK,IAAI,YAAY,EAAE,IAAI,WAAW,YAAY,EAAE,IAAI,UAAU,CAAC;AAC7E,SAAQ,IAAI,KAAK,IAAI,UAAU,SAAS,CAAC,IAAM,IAAI,KAAK,IAAI,UAAU,SAAS,CAAC,KAAM;AACxF;AAEA,IAAM,kBAAN,cAA8B,QAAQ;AAAA,EAEpC,QAAQ,QAAQ,QAAQ,MAAM,kBAAkB;AAC9C,UAAM,EAAC,GAAG,GAAG,IAAI,IAAI,MAAK,IAAI,KAAK,SAAS,CAAC,KAAK,KAAK,MAAM,MAAM,OAAO,GAAG,gBAAgB;AAC7F,UAAM,WAAW,KAAK,QAAQ,cAAc,KAAK,QAAQ,gBAAgB;AACzE,QAAI,SAAS,OAAO,SAAS,KAAK;AAChC,aAAO,aAAa,EAAC,GAAG,QAAQ,GAAG,OAAM,GAAG,KAAK,eAAe,gBAAgB,GAAG,QAAQ,GAAG,OAAO;AAAA,IACvG;AACA,UAAM,QAAQ,SAAS,MAAM,EAAC,OAAO,GAAG,KAAK,IAAI,OAAO,OAAM,IAAI,EAAC,OAAO,GAAG,KAAK,IAAI,OAAO,OAAM;AACnG,WAAO,QAAQ,OAAO,OAAO;AAAA,EAC/B;AAAA,EAEA,eAAe,kBAAkB;AAC/B,WAAO,sBAAsB,MAAM,gBAAgB;AAAA,EACrD;AAAA,EAEA,KAAK,KAAK;AACR,UAAM,UAAU,KAAK;AACrB,UAAM,cAAc,QAAQ;AAC5B,QAAI,QAAQ,SAAS,KAAK;AACxB;AAAA,IACF;AACA,QAAI,KAAK;AACT,QAAI,YAAY,QAAQ;AACxB,mBAAe,KAAK,OAAO;AAC3B,UAAM,SAAS,eAAe,KAAK,OAAO;AAC1C,IAAAE,WAAU,KAAK,MAAM,KAAK,SAAS,KAAK,OAAO;AAC/C,QAAI,UAAU,CAAC,gBAAgB,QAAQ,UAAU,GAAG;AAClD,UAAI,cAAc,QAAQ;AAC1B,UAAI,OAAO;AAAA,IACb;AACA,QAAI,QAAQ;AACZ,YAAQ,cAAc;AAAA,EACxB;AAAA,EAEA,yBAAyB,OAAO,SAAS;AACvC,UAAM,aAAa,uBAAuB,OAAO,OAAO;AACxD,eAAW,iBAAiB,wBAAwB,OAAO,YAAY,OAAO;AAC9E,WAAO;AAAA,EACT;AACF;AAEA,gBAAgB,KAAK;AAErB,gBAAgB,WAAW;AAAA,EACzB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,YAAY,CAAC;AAAA,EACb,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,GAAG;AACL;AAEA,gBAAgB,gBAAgB;AAAA,EAC9B,aAAa;AAAA,EACb,iBAAiB;AACnB;AAEA,IAAM,oBAAN,cAAgC,QAAQ;AAAA,EAEtC,QAAQ,QAAQ,QAAQ,MAAM,kBAAkB;AAC9C,QAAI,SAAS,OAAO,SAAS,KAAK;AAChC,aAAO,KAAK,QAAQ,UAAU,OAAO,KAAK,SAAS,SAAS,KAAK,iBAAiB,KAAK,UAAU,QAAQ,QAAQ,gBAAgB;AAAA,IACnI;AACA,UAAM,eAAe,QAAQ,EAAC,GAAG,QAAQ,GAAG,OAAM,GAAG,KAAK,eAAe,gBAAgB,GAAG,UAAU,CAAC,KAAK,QAAQ,QAAQ,CAAC;AAC7H,UAAM,aAAa,KAAK,SAAS,IAAI,CAAC,UAAU,SAAS,MAAM,MAAM,KAAK,MAAM,EAAE;AAClF,UAAM,QAAQ,KAAK,IAAI,GAAG,UAAU;AACpC,UAAM,MAAM,KAAK,IAAI,GAAG,UAAU;AAClC,WAAO,aAAa,IAAI,KAAK,SAAS,aAAa,IAAI,KAAK;AAAA,EAC9D;AAAA,EAEA,eAAe,kBAAkB;AAC/B,WAAO,sBAAsB,MAAM,gBAAgB;AAAA,EACrD;AAAA,EAEA,KAAK,KAAK;AACR,UAAM,EAAC,UAAU,QAAO,IAAI;AAC5B,QAAI,KAAK;AACT,QAAI,UAAU;AACd,QAAI,YAAY,QAAQ;AACxB,mBAAe,KAAK,OAAO;AAC3B,UAAM,SAAS,eAAe,KAAK,OAAO;AAC1C,QAAI,QAAQ;AACZ,eAAW,MAAM,UAAU;AACzB,UAAI,OAAO;AACT,YAAI,OAAO,GAAG,GAAG,GAAG,CAAC;AACrB,gBAAQ;AAAA,MACV,OAAO;AACL,YAAI,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MACvB;AAAA,IACF;AACA,QAAI,UAAU;AACd,QAAI,KAAK;AAET,QAAI,QAAQ;AACV,UAAI,cAAc,QAAQ;AAC1B,UAAI,OAAO;AAAA,IACb;AACA,QAAI,QAAQ;AAAA,EACd;AAAA,EAEA,yBAAyB,OAAO,SAAS;AACvC,UAAM,aAAa,uBAAuB,OAAO,OAAO;AACxD,UAAM,EAAC,OAAO,SAAQ,IAAI;AAC1B,UAAM,WAAW,CAAC;AAClB,UAAM,QAAS,IAAI,KAAM;AACzB,QAAI,MAAM,WAAW;AACrB,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK,OAAO,OAAO;AAC5C,YAAM,UAAU,kBAAkB,YAAY,SAAS,GAAG;AAC1D,cAAQ,iBAAiB,wBAAwB,OAAO,YAAY,OAAO;AAC3E,eAAS,KAAK,OAAO;AAAA,IACvB;AACA,eAAW,WAAW;AACtB,WAAO;AAAA,EACT;AACF;AAEA,kBAAkB,KAAK;AAEvB,kBAAkB,WAAW;AAAA,EAC3B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,EAChB,YAAY,CAAC;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,IACL,QAAQ;AAAA,EACV;AAAA,EACA,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,eAAe;AAAA,EACf,OAAO;AAAA,EACP,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,GAAG;AACL;AAEA,kBAAkB,gBAAgB;AAAA,EAChC,aAAa;AAAA,EACb,iBAAiB;AACnB;AAEA,SAAS,kBAAkB,EAAC,SAAS,QAAO,GAAG,EAAC,QAAQ,aAAa,aAAY,GAAG,KAAK;AACvF,QAAM,WAAW,cAAc,gBAAgB;AAC/C,QAAM,MAAM,KAAK,IAAI,GAAG;AACxB,QAAM,MAAM,KAAK,IAAI,GAAG;AACxB,QAAM,QAAQ,EAAC,GAAG,UAAU,MAAM,QAAQ,GAAG,UAAU,MAAM,OAAM;AACnE,SAAO;AAAA,IACL,MAAM;AAAA,IACN,aAAa;AAAA,IACb,YAAY;AAAA,MACV,GAAG,MAAM;AAAA,MACT,GAAG,MAAM;AAAA,MACT,SAAS,MAAM;AAAA,MACf,SAAS,MAAM;AAAA,MACf,IAAI,UAAU,OAAO,SAAS;AAAA,MAC9B,IAAI,UAAU,OAAO,SAAS;AAAA,IAChC;AAAA,EACF;AACF;AAEA,SAAS,iBAAiB,QAAQ,GAAG,GAAG,kBAAkB;AACxD,MAAI,WAAW;AACf,MAAI,IAAI,OAAO,OAAO,SAAS,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,GAAG,gBAAgB;AACzE,aAAW,SAAS,QAAQ;AAC1B,UAAM,IAAI,MAAM,SAAS,CAAC,MAAM,IAAI,GAAG,gBAAgB;AACvD,QAAK,EAAE,KAAK,MAAQ,EAAE,KAAK,KAAM,KAAK,EAAE,KAAK,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI;AACtF,iBAAW,CAAC;AAAA,IACd;AACA,QAAI;AAAA,EACN;AACA,SAAO;AACT;AAEA,IAAM,kBAAkB;AAAA,EACtB,KAAK;AAAA,EACL,eAAe;AAAA,EACf,SAAS;AAAA,EACT,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AACX;AAUA,OAAO,KAAK,eAAe,EAAE,QAAQ,SAAO;AAC1C,WAAS,SAAS,YAAY,gBAAgB,GAAG,EAAE,EAAE,IAAI;AAAA,IACvD,WAAW;AAAA,EACb,CAAC;AACH,CAAC;AAED,IAAM,gBAAgB;AAAA,EACpB,QAAQ,OAAO;AACjB;AAEA,IAAM,UAAU,WAAW,OAAO,YAAY;AAC9C,IAAME,WAAU,CAAC,OAAO,YAAY,SAAS,OAAO,IAAI,WAAW,OAAO,OAAO,IAAI;AAarF,IAAM,cAAc,CAAC,SAAS,SAAS,WAAW,SAAS;AAO3D,SAAS,YAAY,OAAO,QAAQ;AAClC,MAAI,gBAAgB,IAAI,GAAG;AACzB,WAAO;AAAA,EACT;AACA,UAAQ,KAAK,6BAA6B,IAAI,yBAAyB;AACvE,SAAO;AACT;AAQA,SAAS,eAAe,OAAO,OAAO,SAAS,MAAM;AACnD,QAAM,aAAa,kBAAkB,OAAO,QAAQ,YAAY,IAAI;AAEpE,QAAM,cAAc,MAAM;AAC1B,QAAM,WAAW,eAAe,MAAM,UAAU,WAAW;AAE3D,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,UAAM,oBAAoB,YAAY,CAAC;AACvC,UAAM,UAAU,mBAAmB,UAAU,GAAG,kBAAkB,IAAI;AACtE,UAAM,WAAW,kBAAkB,WAAW,WAAW,OAAO,SAAS,UAAU,iBAAiB,CAAC;AACrG,UAAM,aAAa,QAAQ,yBAAyB,OAAO,QAAQ;AAEnE,eAAW,OAAO,OAAO,UAAU;AAEnC,QAAI,cAAc,YAAY;AAC5B,wBAAkB,SAAS,WAAW,UAAU,UAAU,UAAU;AAGpE,aAAO,WAAW;AAAA,IACpB;AAEA,QAAI,CAAC,QAAQ,QAAQ,CAAC,GAAG;AAKvB,aAAO,OAAO,SAAS,UAAU;AAAA,IACnC;AAEA,WAAO,OAAO,SAAS,WAAW,cAAc;AAChD,eAAW,UAAU,yBAAyB,QAAQ;AAEtD,eAAW,OAAO,SAAS,UAAU;AAAA,EACvC;AACF;AAEA,SAAS,OAAO,YAAY;AAC1B,SAAO,MAAM,WAAW,CAAC,KAAK,MAAM,WAAW,CAAC;AAClD;AAEA,SAAS,kBAAkB,OAAO,UAAU,MAAM;AAChD,MAAI,SAAS,WAAW,SAAS,UAAU,SAAS,UAAU;AAC5D,WAAO;AAAA,EACT;AACA,SAAO,IAAI,WAAW,OAAO,QAAQ;AACvC;AAEA,SAAS,kBAAkB,aAAa,UAAU,UAAU,YAAY;AACtE,QAAM,cAAc,YAAY,aAAa,YAAY,WAAW,CAAC;AACrE,cAAY,SAAS,SAAS;AAC9B,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,aAAa,SAAS,CAAC;AAC7B,UAAM,aAAa,WAAW;AAC9B,UAAM,aAAa,mBAAmB,aAAa,GAAG,WAAW,MAAM,WAAW,cAAc;AAChG,UAAM,cAAc,SAAS,WAAW,WAAW,EAAE,SAAS,UAAU;AACxE,eAAW,UAAU,yBAAyB,WAAW;AACzD,eAAW,OAAO,YAAY,UAAU;AAAA,EAC1C;AACF;AAEA,SAAS,mBAAmB,UAAU,OAAO,MAAM,gBAAgB;AACjE,QAAM,eAAe,gBAAgB,YAAY,IAAI,CAAC;AACtD,MAAI,UAAU,SAAS,KAAK;AAC5B,MAAI,CAAC,WAAW,EAAE,mBAAmB,eAAe;AAClD,cAAU,SAAS,KAAK,IAAI,IAAI,aAAa;AAC7C,WAAO,OAAO,SAAS,cAAc;AAAA,EACvC;AACA,SAAO;AACT;AAEA,SAAS,yBAAyB,UAAU;AAC1C,QAAM,eAAe,gBAAgB,YAAY,SAAS,IAAI,CAAC;AAC/D,QAAM,SAAS,CAAC;AAChB,SAAO,KAAK,SAAS;AACrB,SAAO,OAAO,SAAS;AACvB,SAAO,WAAW,SAAS;AAC3B,SAAO;AAAA,IAAO;AAAA,IACZ,WAAW,UAAU,aAAa,QAAQ;AAAA,IAC1C,WAAW,UAAU,aAAa,aAAa;AAAA,EAAC;AAClD,aAAW,QAAQ,SAAS;AAC1B,WAAO,IAAI,IAAI,SAAS,IAAI;AAAA,EAC9B;AACA,SAAO;AACT;AAEA,SAAS,WAAW,UAAU,MAAM;AAClC,QAAM,SAAS,CAAC;AAChB,aAAW,QAAQ,OAAO,KAAK,IAAI,GAAG;AACpC,UAAM,UAAU,KAAK,IAAI;AACzB,UAAM,QAAQ,SAAS,IAAI;AAC3B,QAAI,YAAY,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,aAAO,IAAI,IAAI,MAAM,IAAI,CAAC,SAASA,SAAQ,MAAM,OAAO,CAAC;AAAA,IAC3D,OAAO;AACL,aAAO,IAAI,IAAIA,SAAQ,OAAO,OAAO;AAAA,IACvC;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,WAAW,OAAO,SAAS,UAAUD,aAAY;AACxD,SAAO,QAAQ,aAAa,QAAQ,WAAW,OAAO,OAAO,OAAO,OAAO,MAAM,WAAW,CAAC,GAAG;AAAA,IAC9F;AAAA,IACA,IAAI,WAAW;AACb,aAAO,SAAS,OAAO,CAAC,OAAO,MAAM,GAAG,OAAO;AAAA,IACjD;AAAA,IACA,IAAIA,YAAW;AAAA,IACf,MAAM;AAAA,EACR,CAAC;AACH;AAEA,SAAS,eAAe,UAAU,aAAa;AAC7C,QAAM,QAAQ,YAAY;AAC1B,QAAM,QAAQ,SAAS;AAEvB,MAAI,QAAQ,OAAO;AACjB,UAAM,MAAM,QAAQ;AACpB,aAAS,OAAO,OAAO,GAAG,GAAG,IAAI,MAAM,GAAG,CAAC;AAAA,EAC7C,WAAW,QAAQ,OAAO;AACxB,aAAS,OAAO,OAAO,QAAQ,KAAK;AAAA,EACtC;AACA,SAAO;AACT;AAEA,IAAI,UAAU;AAEd,IAAM,cAAc,oBAAI,IAAI;AAC5B,IAAM,qBAAqB,CAAAA,gBAAcA,YAAW,SAAS;AAC7D,IAAM,QAAQ,WAAW,OAAO,YAAY;AAE5C,IAAI,aAAa;AAAA,EACf,IAAI;AAAA,EAEJ;AAAA,EAEA,iBAAiB;AACf,mBAAe,YAAY,OAAO,MAAM,OAAO;AAAA,EACjD;AAAA,EAEA,gBAAgB;AACd,UAAM,SAAS,eAAe;AAAA,EAChC;AAAA,EAEA,kBAAkB;AAChB,UAAM,WAAW,eAAe;AAAA,EAClC;AAAA,EAEA,WAAW,OAAO;AAChB,gBAAY,IAAI,OAAO;AAAA,MACrB,aAAa,CAAC;AAAA,MACd,UAAU,CAAC;AAAA,MACX,iBAAiB,CAAC;AAAA,MAClB,WAAW,CAAC;AAAA,MACZ,UAAU;AAAA,MACV,cAAc;AAAA,MACd,OAAO,CAAC;AAAA,MACR,QAAQ;AAAA,MACR,SAAS,CAAC;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EAEA,aAAa,OAAO,MAAM,SAAS;AACjC,UAAM,QAAQ,YAAY,IAAI,KAAK;AACnC,UAAM,cAAc,MAAM,cAAc,CAAC;AAEzC,QAAI,oBAAoB,QAAQ;AAChC,QAAI,SAAS,iBAAiB,GAAG;AAC/B,aAAO,KAAK,iBAAiB,EAAE,QAAQ,SAAO;AAC5C,cAAM,QAAQ,kBAAkB,GAAG;AACnC,YAAI,SAAS,KAAK,GAAG;AACnB,gBAAM,KAAK;AACX,sBAAY,KAAK,KAAK;AAAA,QACxB;AAAA,MACF,CAAC;AAAA,IACH,WAAW,QAAQ,iBAAiB,GAAG;AACrC,kBAAY,KAAK,GAAG,iBAAiB;AAAA,IACvC;AACA,uBAAmB,YAAY,OAAO,kBAAkB,GAAG,MAAM,MAAM;AAAA,EACzE;AAAA,EAEA,gBAAgB,OAAO,MAAM;AAC3B,UAAM,QAAQ,YAAY,IAAI,KAAK;AACnC,qBAAiB,OAAO,KAAK,OAAO,MAAM,YAAY,OAAO,kBAAkB,EAAE,OAAO,OAAK,EAAE,WAAW,EAAE,gBAAgB,CAAC;AAAA,EAC/H;AAAA,EAEA,YAAY,OAAO,MAAM,SAAS;AAChC,UAAM,QAAQ,YAAY,IAAI,KAAK;AACnC,oBAAgB,OAAO,OAAO,OAAO;AACrC,mBAAe,OAAO,OAAO,SAAS,KAAK,IAAI;AAC/C,UAAM,kBAAkB,MAAM,SAAS,OAAO,QAAM,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO;AAClF,gBAAY,OAAO,OAAO,OAAO;AAAA,EACnC;AAAA,EAEA,mBAAmB,OAAO,OAAO,SAAS;AACxC,SAAK,OAAO,sBAAsB,QAAQ,IAAI;AAAA,EAChD;AAAA,EAEA,kBAAkB,OAAO,OAAO,SAAS;AACvC,SAAK,OAAO,qBAAqB,QAAQ,IAAI;AAAA,EAC/C;AAAA,EAEA,kBAAkB,OAAO,OAAO,SAAS;AACvC,SAAK,OAAO,MAAM,OAAO,QAAQ,IAAI;AAAA,EACvC;AAAA,EAEA,WAAW,OAAO,OAAO,SAAS;AAChC,SAAK,OAAO,cAAc,QAAQ,IAAI;AAAA,EACxC;AAAA,EAEA,UAAU,OAAO,OAAO,SAAS;AAC/B,SAAK,OAAO,aAAa,QAAQ,IAAI;AAAA,EACvC;AAAA,EAEA,YAAY,OAAO,MAAM,SAAS;AAChC,UAAM,QAAQ,YAAY,IAAI,KAAK;AACnC,QAAI,YAAY,OAAO,KAAK,OAAO,OAAO,GAAG;AAC3C,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EAEA,aAAa,OAAO;AAClB,gBAAY,OAAO,KAAK;AAAA,EAC1B;AAAA,EAEA,eAAe,OAAO;AACpB,UAAM,QAAQ,YAAY,IAAI,KAAK;AACnC,WAAO,QAAQ,MAAM,WAAW,CAAC;AAAA,EACnC;AAAA;AAAA,EAGA,qCAAqC,iBAAiB,OAAO,SAAS;AACpE,WAAO,YAAY,iBAAiB,OAAO,OAAO;AAAA,EACpD;AAAA,EAEA,UAAU;AAAA,IACR,YAAY;AAAA,MACV,SAAS;AAAA,QACP,YAAY,CAAC,KAAK,KAAK,MAAM,MAAM,SAAS,UAAU,WAAW,WAAW,UAAU,UAAU,QAAQ;AAAA,QACxG,MAAM;AAAA,MACR;AAAA,MACA,QAAQ;AAAA,QACN,YAAY,CAAC,mBAAmB,aAAa;AAAA,QAC7C,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,IACN,aAAa;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,MAAM;AAAA,MACN,OAAO,CACP;AAAA,IACF;AAAA,EACF;AAAA,EAEA,aAAa;AAAA,IACX,YAAY;AAAA,IACZ,aAAa,CAAC,SAAS,CAAC,MAAM,SAAS,IAAI,KAAK,SAAS;AAAA,IACzD,aAAa;AAAA,MACX,UAAU;AAAA,MACV,WAAW,CAAC,MAAM,SAAS,YAAY,gBAAgB,YAAY,KAAK,IAAI,CAAC,EAAE,EAAE;AAAA,IACnF;AAAA,IACA,aAAa;AAAA,MACX,WAAW;AAAA,IACb;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,QACL,YAAY;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EAEA,wBAAwB,CAAC,EAAE;AAC7B;AAEA,SAAS,KAAK,OAAO,QAAQ,MAAM;AACjC,QAAM,EAAC,KAAK,UAAS,IAAI;AACzB,QAAM,QAAQ,YAAY,IAAI,KAAK;AAEnC,MAAI,MAAM;AACR,aAAS,KAAK,SAAS;AAAA,EACzB;AAEA,QAAM,mBAAmB,oBAAoB,MAAM,iBAAiB,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,QAAQ,IAAI,EAAE,QAAQ,QAAQ,CAAC;AACpI,aAAW,QAAQ,kBAAkB;AACnC,gBAAY,KAAK,WAAW,OAAO,IAAI;AAAA,EACzC;AAEA,MAAI,MAAM;AACR,eAAW,GAAG;AAAA,EAChB;AACF;AAEA,SAAS,oBAAoB,UAAU,QAAQ;AAC7C,QAAM,mBAAmB,CAAC;AAC1B,aAAW,MAAM,UAAU;AACzB,QAAI,GAAG,QAAQ,aAAa,QAAQ;AAClC,uBAAiB,KAAK,EAAC,SAAS,IAAI,MAAM,KAAI,CAAC;AAAA,IACjD;AACA,QAAI,GAAG,YAAY,GAAG,SAAS,QAAQ;AACrC,iBAAW,OAAO,GAAG,UAAU;AAC7B,YAAI,IAAI,QAAQ,WAAW,IAAI,QAAQ,aAAa,QAAQ;AAC1D,2BAAiB,KAAK,EAAC,SAAS,IAAG,CAAC;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,YAAY,KAAK,WAAW,OAAO,MAAM;AAChD,QAAM,KAAK,KAAK;AAChB,MAAI,KAAK,MAAM;AACb,eAAW,OAAO,IAAI,YAAY;AAClC,OAAG,KAAK,KAAK,SAAS;AACtB,eAAW,OAAO,IAAI,WAAW;AAAA,EACnC,OAAO;AACL,OAAG,KAAK,KAAK,SAAS;AAAA,EACxB;AACF;", "names": ["getRelativePosition", "hooks", "drawPoint", "annotation", "resolve"]}