import { TextField, MenuItem, Radio } from "@mui/material";
import { ExpandMore, ExpandLess } from "@mui/icons-material";
import theme from "../theme";
import { useApp } from "../hooks/AppHook";

const RegionGroupFilter = ({ regionGroups, value, onChange }) => {
    const handleChange = (e) => {
        onChange(e.target.value);
    };
    const { devMode } = useApp();

    return (
        <TextField
            value={value}
            onChange={handleChange}
            variant="standard"
            InputProps={{
                sx: {
                    height: "40px",
                },
                inputProps: {
                    sx: {
                        fontSize: "12px",
                        paddingLeft: 1,
                        color: "primary.contrastText",
                    },
                },
            }}
            select
            SelectProps={{
                MenuProps: {
                    PaperProps: {
                        sx: {
                            "& .MuiMenuItem-root": {
                                display: "flex",
                                alignItems: "center",
                            },
                            "& .MuiList-root": {
                                padding: 0,
                            },
                        },
                    },
                },
                IconComponent: (props) =>
                    value ? <ExpandMore {...props} sx={{ right: "10px !important" }} /> : <ExpandLess {...props} sx={{ right: "10px !important" }} />,
                renderValue: (selected) => {
                    if (selected === "all") {
                        return "All";
                    }
                    if (selected === "allUnregistered") {
                        return "All (Include Unregistered)";
                    }
                    const selectedGroup = regionGroups.find((rg) => rg._id === selected);
                    return selectedGroup ? `${selectedGroup.name} (UTC ${selectedGroup.timezone})` : "";
                },
            }}
            sx={{
                minWidth: 150,
                height: "30px",
                backgroundColor: "#464F59",
                borderRadius: "5px",
                "& .MuiInputBase-root": {
                    fontSize: "16px",
                    border: "none",
                    paddingX: "10px",
                    "&::after, &::before": {
                        borderBottom: "none !important",
                    },
                },
                "& .MuiSelect-select": {
                    borderBottom: "none",
                    paddingY: 0,
                },
                "& .MuiInputBase-root::before": {
                    border: 0,
                },
            }}
        >
            <MenuItem
                value={"all"}
                sx={{
                    borderBottom: regionGroups.length > 0 ? "1px solid " + theme.palette.custom.borderColor : "none",
                    padding: "10px 20px",
                }}
            >
                <Radio
                    checked={value === "all"}
                    value="all"
                    size="small"
                    sx={{
                        padding: 0,
                        marginRight: 1,
                        color: "white",
                        "&.Mui-checked": { color: "white" },
                    }}
                />
                All
            </MenuItem>
            {devMode && (
                <MenuItem
                    value={"allUnregistered"}
                    sx={{
                        borderBottom: regionGroups.length > 0 ? "1px solid " + theme.palette.custom.borderColor : "none",
                        padding: "10px 20px",
                    }}
                >
                    <Radio
                        checked={value === "allUnregistered"}
                        value="allUnregistered"
                        size="small"
                        sx={{
                            padding: 0,
                            marginRight: 1,
                            color: "white",
                            "&.Mui-checked": { color: "white" },
                        }}
                    />
                    All (Include Unregistered)
                </MenuItem>
            )}
            {regionGroups.map((rg, i) => (
                <MenuItem
                    key={i}
                    value={rg._id}
                    sx={{
                        borderBottom: i < regionGroups.length - 1 ? "1px solid " + theme.palette.custom.borderColor : "none",
                        padding: "10px 20px",
                    }}
                >
                    <Radio
                        checked={value === rg._id}
                        value={rg._id}
                        size="small"
                        sx={{
                            padding: 0,
                            marginRight: 1,
                            color: "white",
                            "&.Mui-checked": { color: "white" },
                        }}
                    />
                    {rg.name} (UTC {rg.timezone})
                </MenuItem>
            ))}
        </TextField>
    );
};

export default RegionGroupFilter;
