import { Button, Grid, InputAdornment, OutlinedInput, Tab, Tabs } from "@mui/material";
import { useEffect, useMemo, useState } from "react";
import { useUser } from "../../../hooks/UserHook";
import { permissions } from "../../../utils";
import { Search, Add } from "@mui/icons-material";
import { useApp } from "../../../hooks/AppHook";
import RegionGroups from "./RegionGroups";
import useVesselInfo from "../../../hooks/VesselInfoHook";

export default function RegionGroupManagement() {
    const { user } = useUser();
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();

    const { isMobile } = useApp();

    const [searchQuery, setSearchQuery] = useState("");
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [vessels, setVessels] = useState([]);

    useEffect(() => {
        if (!(user && user.hasPermissions([permissions.manageRegionsGroups]))) return;
        fetchVessels();
    }, [vesselInfo, user]);

    const fetchVessels = async () => {
        if (vesselInfo && Array.isArray(vesselInfo)) {
            setVessels(vesselInfo);
        } else {
            fetchVesselsInfo();
        }
    };

    const tabs = useMemo(
        () => [
            {
                value: "regionGroups",
                label: "Regions",
                component: (
                    <RegionGroups
                        searchQuery={searchQuery}
                        vessels={vessels}
                        managedVessels={vesselInfo}
                        showCreateModal={showCreateModal}
                        setShowCreateModal={setShowCreateModal}
                    />
                ),
                display: user?.hasPermissions([permissions.manageRegionsGroups]),
            },
        ],
        [user, searchQuery, showCreateModal, setShowCreateModal, vessels, vesselInfo],
    );

    const [tab, setTab] = useState(tabs[0].value);

    const handleSearchChange = (event) => {
        setSearchQuery(event.target.value);
    };

    return (
        user &&
        tabs.some((t) => t.display) &&
        tab && (
            <Grid
                container
                color={"#FFFFFF"}
                flexDirection={"column"}
                padding={2}
                gap={2}
                height={"100%"}
                overflow={"auto"}
                sx={{ backgroundColor: (theme) => theme.palette.custom.darkBlue }}
            >
                <Grid container display={"flex"} rowGap={2} justifyContent={"space-between"} alignItems={"center"} flexWrap={"wrap"}>
                    <Grid
                        size={{
                            xs: 12,
                            lg: 2.5,
                        }}
                    >
                        <Tabs
                            value={tab}
                            onChange={(e, v) => setTab(v)}
                            sx={{
                                width: "100%",
                                padding: "4px",
                                border: (theme) => `2px solid ${theme.palette.custom.borderColor}`,
                                borderRadius: "8px",
                                backgroundColor: "transparent",
                                "& .MuiTabs-flexContainer": {
                                    height: "100%",
                                },
                                "& .MuiButtonBase-root": {
                                    width: "100%",
                                    borderRadius: "8px",
                                },
                                "& .MuiButtonBase-root.Mui-selected": {
                                    backgroundColor: (theme) => theme.palette.custom.mainBlue,
                                },
                            }}
                        >
                            {tabs
                                .filter((t) => t.display)
                                .map((t) => (
                                    <Tab
                                        key={t.value}
                                        label={t.label}
                                        value={t.value}
                                        sx={{
                                            maxWidth: "none",
                                        }}
                                    />
                                ))}
                        </Tabs>
                    </Grid>
                    <Grid
                        container
                        columnGap={2}
                        justifyContent={"space-between"}
                        size={{
                            xs: 12,
                            lg: 9.4,
                        }}
                    >
                        <Grid
                            size={{
                                xs: "grow",
                                lg: 5.8,
                            }}
                        >
                            <OutlinedInput
                                type="text"
                                value={searchQuery}
                                onChange={handleSearchChange}
                                startAdornment={
                                    <InputAdornment position="start">
                                        <Search sx={{ color: "#FFFFFF" }} />
                                    </InputAdornment>
                                }
                                placeholder="Search by name  or created by"
                                sx={{
                                    color: "#FFFFFF",
                                    width: "100%",
                                    "& .MuiOutlinedInput-notchedOutline": {
                                        border: "2px solid",
                                        borderColor: (theme) => theme.palette.custom.borderColor + " !important",
                                        borderRadius: "8px",
                                    },
                                }}
                            />
                        </Grid>
                        <Grid alignItems={"center"} display={"flex"} justifyContent={"flex-end"} gap={2} size="auto">
                            <Button
                                variant="contained"
                                sx={{
                                    "&.MuiButtonBase-root": {
                                        color: "#FFFFFF",
                                        height: { xs: "100%", lg: "auto" },
                                        padding: { xs: 0, lg: "10px 20px" },
                                        backgroundColor: (theme) => theme.palette.custom.mainBlue,
                                        fontWeight: "bold",
                                    },
                                }}
                                startIcon={<Add />}
                                onClick={() => setShowCreateModal(true)}
                            >
                                {!isMobile && "Create New Region"}
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
                {tabs
                    .filter((t) => t.display)
                    .map((t) => (
                        <Grid key={t.value} display={tab !== t.value && "none"} width={"100%"} size="grow">
                            {t.component}
                        </Grid>
                    ))}
            </Grid>
        )
    );
}
