import { DateTimePicker } from "@mui/x-date-pickers";
import theme from "../theme";
import { useApp } from "../hooks/AppHook";
const CustomDateTimePicker = ({ ampm, value, onChange, minDateTime, maxDateTime, slots, sx }) => {
    const { timezone } = useApp();

    const defaultStyle = {
        width: "100%",
        backgroundColor: "transparent",
        borderRadius: "40px",
        border: "1px solid #E0E2E9",
        "& .MuiSvgIcon-root": {
            color: theme.palette.custom.mediumGrey,
            fontSize: "18px",
        },
        fontSize: "12px",
        height: "40px",
        "& .MuiInputBase-root": {
            border: "none",
            fontSize: "12px",
            color: theme.palette.custom.mediumGrey,
            marginTop: -0.9,
            paddingLeft: 0,
            paddingRight: 1.5,
            justifyContent: "space-between",
            "& input": {
                maxWidth: "120px",
            },
        },
    };

    const combinedStyles = { ...defaultStyle, ...sx };

    return (
        <DateTimePicker
            ampm={ampm}
            value={value}
            onChange={onChange}
            minDateTime={minDateTime}
            maxDateTime={maxDateTime}
            slots={slots}
            sx={combinedStyles}
            timezone={timezone}
        />
    );
};

export default CustomDateTimePicker;
