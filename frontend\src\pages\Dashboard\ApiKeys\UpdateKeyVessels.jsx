import { useEffect, useMemo, useState } from "react";
import MultiSelect from "../../../components/MultiSelect";
import { CircularProgress } from "@mui/material";
import apiKeyController from "../../../controllers/ApiKey.controller";

export default function UpdateKeyVessels({ api_key, vessels, regionGroups, disabled, fetchKeys }) {
    const [checkedVessels, setCheckedVessels] = useState([]);
    const [updating, setUpdating] = useState(false);

    const vesselsByRegionGroup = useMemo(() => {
        return vessels
            .filter((v) => v.region_group_id && v.is_active !== false)
            .map((v) => ({ ...v, region_group_object: regionGroups.find((rg) => rg._id === v.region_group_id) }))
            .sort((a, b) => {
                const groupA = a.region_group_object?.name?.toLowerCase() || "";
                const groupB = b.region_group_object?.name?.toLowerCase() || "";
                // Sort by group name first
                if (groupA < groupB) return -1;
                if (groupA > groupB) return 1;

                // Sort alphabetically by vessel name within the group
                // const vesselIds = a.region_group_object?.vessel_ids || [];
                // const indexA = vesselIds.indexOf(a.vessel_id);
                // const indexB = vesselIds.indexOf(b.vessel_id);
                // // Fallback to alphabetical if not found
                // if (indexA === -1 || indexB === -1) {
                const nameA = a.name?.toLowerCase() || a.vessel_id;
                const nameB = b.name?.toLowerCase() || b.vessel_id;
                return nameA.localeCompare(nameB);
                // }
                // return indexA - indexB;
            });
    }, [vessels, regionGroups]);

    useEffect(() => {
        initializeCheckedVessels();
    }, [api_key, vessels]);

    const initializeCheckedVessels = () => setCheckedVessels(api_key.allowed_vessels || []);

    const isChanged = useMemo(() => {
        if (checkedVessels.length !== (api_key.allowed_vessels || []).length) return true;
        const allowedVesselIds = new Set(api_key.allowed_vessels || []);
        return checkedVessels.some((vesselId) => !allowedVesselIds.has(vesselId));
    }, [checkedVessels, api_key]);

    const handleUpdate = async () => {
        try {
            setUpdating(true);
            const allowed_vessels = checkedVessels.filter((v) => vessels.find((o) => o.vessel_id === v));

            await apiKeyController.updateAllowedVessels({
                id: api_key._id,
                allowed_vessels,
            });
            fetchKeys();
        } catch (err) {
            initializeCheckedVessels();
            console.error(err);
        } finally {
            setUpdating(false);
            fetchKeys();
        }
    };

    return updating ? (
        <CircularProgress />
    ) : (
        <MultiSelect
            loading={vessels.length === 0}
            options={vesselsByRegionGroup}
            value={checkedVessels}
            disabled={disabled || updating}
            multiple
            disableCloseOnSelect
            groupBy={(o) => o.region_group_object?.name}
            label={`${(api_key.allowed_vessels || []).length} selected`}
            getOptionLabel={(o) => o.name}
            isOptionEqualToValue={(o, v) => v.includes(o.vessel_id)}
            renderTags={() => null}
            onChange={(e, v) => setCheckedVessels(v.map((o) => (typeof o === "string" ? o : o.vessel_id)))}
            onClose={() => {
                if (isChanged) {
                    handleUpdate();
                }
            }}
        />
    );
}
