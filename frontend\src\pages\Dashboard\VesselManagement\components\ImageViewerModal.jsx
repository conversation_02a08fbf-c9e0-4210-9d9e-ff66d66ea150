import { useState, useEffect } from "react";
import { alpha, CircularProgress, Typography, Box, Modal, IconButton, Backdrop } from "@mui/material";
import { ImageNotSupported, Close } from "@mui/icons-material";
import theme from "../../../../theme";

const ImageViewerModal = ({ open, onClose, imageUrl, vesselName }) => {
    const [imageLoading, setImageLoading] = useState(true);
    const [imageError, setImageError] = useState(false);

    const handleImageLoad = () => setImageLoading(false);
    const handleImageError = () => {
        setImageError(true);
        setImageLoading(false);
    };

    useEffect(() => {
        if (open && imageUrl) {
            setImageLoading(true);
            setImageError(false);
        }
    }, [open, imageUrl]);

    return (
        <Modal
            open={open}
            onClose={onClose}
            closeAfterTransition
            BackdropComponent={Backdrop}
            BackdropProps={{
                timeout: 500,
                sx: { backgroundColor: "rgba(0, 0, 0, 0.9)" },
            }}
        >
            <Box
                sx={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    maxWidth: "90vw",
                    maxHeight: "90vh",
                    outline: "none",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                }}
            >
                <IconButton
                    onClick={onClose}
                    sx={{
                        position: "absolute",
                        top: -50,
                        right: -50,
                        color: "#FFFFFF",
                        bgcolor: alpha(theme.palette.custom.darkBlue, 0.8),
                        "&:hover": {
                            bgcolor: alpha(theme.palette.custom.darkBlue, 1),
                        },
                        zIndex: 1,
                    }}
                >
                    <Close />
                </IconButton>

                <Box
                    sx={{
                        position: "relative",
                        borderRadius: 2,
                        overflow: "hidden",
                        boxShadow: "0 8px 32px rgba(0, 0, 0, 0.5)",
                        bgcolor: alpha(theme.palette.custom.darkBlue, 0.9),
                        border: `2px solid ${alpha(theme.palette.custom.mainBlue, 0.3)}`,
                    }}
                >
                    {imageLoading && (
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                minWidth: 400,
                                minHeight: 300,
                            }}
                        >
                            <CircularProgress size={60} sx={{ color: theme.palette.custom.mainBlue }} />
                        </Box>
                    )}

                    {imageError ? (
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                justifyContent: "center",
                                minWidth: 400,
                                minHeight: 300,
                                color: theme.palette.custom.offline,
                                p: 4,
                            }}
                        >
                            <ImageNotSupported sx={{ fontSize: 80, mb: 2 }} />
                            <Typography variant="h6" color="inherit">
                                Image not available
                            </Typography>
                            <Typography variant="body2" color="inherit" sx={{ mt: 1 }}>
                                {vesselName}
                            </Typography>
                        </Box>
                    ) : (
                        <img
                            src={imageUrl}
                            alt={vesselName}
                            onLoad={handleImageLoad}
                            onError={handleImageError}
                            style={{
                                maxWidth: "85vw",
                                maxHeight: "80vh",
                                objectFit: "contain",
                                display: imageLoading ? "none" : "block",
                            }}
                        />
                    )}
                </Box>

                {!imageError && (
                    <Typography
                        variant="h6"
                        sx={{
                            mt: 2,
                            color: "#FFFFFF",
                            textAlign: "center",
                            bgcolor: alpha(theme.palette.custom.darkBlue, 0.8),
                            px: 3,
                            py: 1,
                            borderRadius: 1,
                        }}
                    >
                        {vesselName}
                    </Typography>
                )}
            </Box>
        </Modal>
    );
};

export default ImageViewerModal;
