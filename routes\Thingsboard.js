const express = require("express");
const router = express.Router();
const { param } = require("express-validator");
const { endpointIds } = require("../utils/endpointIds");
const isAuthenticated = require("../middlewares/auth");
const { validateData } = require("../middlewares/validator");
const assignEndpointId = require("../middlewares/assignEndpointId");
const thingsboardService = require("../services/Thingsboard.service");
const { validateError } = require("../utils/functions");

router.get("/devices", assignEndpointId.bind(this, endpointIds.FETCH_THINGSBOARD_DEVICES), isAuthenticated, async (req, res) => {
    try {
        const devices = await thingsboardService.getAllDevices();
        res.json(devices);
    } catch (err) {
        validateError(err, res);
    }
});

router.get(
    "/device/:unitId",
    assignEndpointId.bind(this, endpointIds.FETCH_THINGSBOARD_DEVICE),
    isAuthenticated,
    validateData.bind(this, [
        param("unitId")
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const device = await thingsboardService.getDeviceByUnitId(req.params.unitId);
            if (!device) {
                return res.status(404).json({ message: "Device not found" });
            }
            res.json(device);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get("/reset-dashboards", assignEndpointId.bind(this, endpointIds.RESET_THINGSBOARD_DASHBOARDS), isAuthenticated, async (req, res) => {
    try {
        thingsboardService.resetDashboards();
        res.json({ message: "Dashboards reset successful" });
    } catch (err) {
        validateError(err, res);
    }
});

module.exports = router;
