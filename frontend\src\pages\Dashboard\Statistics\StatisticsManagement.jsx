import { alpha, CircularProgress, Grid, Icon<PERSON>utton, Tab, Tabs } from "@mui/material";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useUser } from "../../../hooks/UserHook";
import StatisticsPastWeeks from "./StatisticsPastWeeks";
import StatisticsPastDaylights from "./StatisticsPastDaylights";
import { handleDownload, isEnvironment, permissions } from "../../../utils";
import theme from "../../../theme";
import ReportDurationModal from "./ReportDurationModal.jsx";
import { useToaster } from "../../../hooks/ToasterHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";
import environment from "../../../../environment.js";

const prepareCsv = (user, weeklyStats, vesselInfo) => {
    if (!weeklyStats) {
        console.warn("No statistics data available for download.");
        return {};
    }

    const csvRows = ["vessel, sensorId, timestamp,total detections,total time online (h),total time at sea (h),distance travelled (miles)"];
    const vesselsRowsData = {};
    // TODO: remove reference to allowed_units
    const userAllowedVessels = user.hasPermissions([permissions.accessAllVessels]) ? "all" : user.allowed_vessels || [];

    for (const weeklyStatsData of weeklyStats) {
        const stats = weeklyStatsData.stats;
        const timestamp = `${weeklyStatsData.fromTimestamp.split("T")[0]} - ${weeklyStatsData.toTimestamp.split("T")[0]}`;

        const vesselIds = userAllowedVessels === "all" ? Object.keys(stats.totalVesselsDetectedbySensors) : userAllowedVessels;

        vesselIds.forEach((vesselId) => {
            const vessel = vesselInfo.find((vessel) => vessel.vessel_id === vesselId);

            const vesselName = vessel?.name ?? "N/A";
            const detections = stats.totalVesselsDetectedbySensors?.[vesselId] ?? "N/A";
            const onlineTime = stats.totalSensorsOnlineDuration?.[vesselId]
                ? (parseFloat(stats.totalSensorsOnlineDuration?.[vesselId] / 1000 / 60 / 60).toFixed(2) ?? "N/A")
                : 0;
            const seaTime = stats.totalSensorsDurationAtSea?.[vesselId]
                ? (parseFloat((stats.totalSensorsDurationAtSea?.[vesselId] / 1000 / 60 / 60).toFixed(2)) ?? "N/A")
                : 0;
            const distance = stats.totalSmartmastsDistanceTraveled?.[vesselId]
                ? (parseFloat((stats.totalSmartmastsDistanceTraveled?.[vesselId] * 0.00062137).toFixed(2)) ?? "N/A")
                : 0; // Format distance
            (vesselsRowsData[vesselId] ||= []).push(
                [vesselName, vessel?.vessel_id, timestamp, detections, onlineTime, seaTime, isNaN(distance) ? "N/A" : distance].join(","),
            );
        });
    }

    if (Object.keys(vesselsRowsData).length === 0) {
        return {};
    }

    for (const vesselId of Object.keys(vesselsRowsData)) {
        for (const rowData of vesselsRowsData[vesselId]) {
            csvRows.push(rowData);
        }
    }

    const toDate = weeklyStats[0].toTimestamp.split("T")[0];
    const fromDate = weeklyStats[weeklyStats.length - 1].fromTimestamp.split("T")[0];

    return {
        csvString: csvRows.join("\n"),
        filename: `smartmast_stats_${fromDate}_to_${toDate}.csv`,
    };
};

export default function StatisticsManagement() {
    const { user } = useUser();
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();
    const [downloadStats, setDownloadStats] = useState(false);
    const [tab, setTab] = useState("");
    const [showReportConfigDialog, setShowReportConfigDialog] = useState(false);
    const statsWeekRef = useRef(null);
    const toaster = useToaster();

    const tabs = useMemo(
        () => [
            {
                value: "past_daylights",
                label: "Past 24 Hours",
                component: <StatisticsPastDaylights />,
                display: user?.hasPermissions([permissions.viewStatistics]),
            },
            {
                value: "past_weeks",
                label: "Total",
                component: <StatisticsPastWeeks ref={statsWeekRef} />,
                display: user?.hasPermissions([permissions.viewStatistics]),
            },
        ],
        [user],
    );

    useEffect(() => {
        setTab(tabs.find((t) => t.display)?.value || "");
    }, [tabs]);

    const handleDownloadCsv = useCallback(
        async (timeLimitation) => {
            try {
                setDownloadStats(true);
                setShowReportConfigDialog(false);

                const allStats = statsWeekRef.current.getAllStats();
                timeLimitation = Number(timeLimitation);

                let vessels = vesselInfo;

                if (!vessels || vessels.length === 0) {
                    vessels = await fetchVesselsInfo();
                    console.log("vesselInfo", vessels);
                }

                const { csvString, filename } = prepareCsv(user, timeLimitation === 0 ? allStats : allStats.slice(0, timeLimitation), vessels);

                if (!csvString || csvString.length === 0) {
                    toaster("No statistics data available for download.", { variant: "success" });
                    setDownloadStats(false);
                    return;
                }

                const blob = new Blob([csvString], { type: "text/csv;charset=utf-8;" });
                handleDownload(blob, filename);
            } catch (error) {
                console.error("Error downloading CSV:", error);
                toaster("Error downloading CSV", { variant: "error" });
            } finally {
                setDownloadStats(false);
            }
        },
        [user],
    );

    return (
        user &&
        tabs.some((t) => t.display) &&
        tab && (
            <Grid
                container
                color={"#FFFFFF"}
                flexDirection={"column"}
                width={"100%"}
                height={"100%"}
                overflow={"auto"}
                sx={{ backgroundColor: theme.palette.custom.darkBlue }}
            >
                <Grid
                    size={12}
                    container
                    alignItems={"center"}
                    justifyContent={"space-between"}
                    wrap="nowrap"
                    borderRadius={"20px"}
                    paddingX={2}
                    paddingTop={2}
                    paddingRight={4}
                >
                    <Grid
                        size={9}
                        container
                        paddingBottom={0}
                        display={"flex"}
                        rowGap={2}
                        justifyContent={"space-between"}
                        alignItems={"center"}
                        flexWrap={"wrap"}
                    >
                        <Grid
                            size={{
                                xs: 12,
                                lg: 3.9,
                            }}
                        >
                            <Tabs
                                value={tab}
                                onChange={(e, v) => setTab(v)}
                                sx={{
                                    width: "100%",
                                    padding: "4px",
                                    border: `2px solid ${theme.palette.custom.borderColor}`,
                                    borderRadius: "8px",
                                    backgroundColor: "transparent",
                                    "& .MuiTabs-flexContainer": {
                                        height: "100%",
                                    },
                                    "& .MuiButtonBase-root": {
                                        width: "50%",
                                        borderRadius: "8px",
                                    },
                                    "& .MuiButtonBase-root.Mui-selected": {
                                        backgroundColor: theme.palette.custom.mainBlue,
                                    },
                                }}
                            >
                                {tabs
                                    .filter((t) => t.display)
                                    .map((t) => (
                                        <Tab
                                            key={t.value}
                                            label={t.label}
                                            value={t.value}
                                            sx={{
                                                maxWidth: "none",
                                                textTransform: "none",
                                            }}
                                        />
                                    ))}
                            </Tabs>
                        </Grid>
                    </Grid>

                    {!isEnvironment(environment.stagingAndProduction) && !downloadStats && (
                        <Grid size={3} sx={{ textAlign: "right" }}>
                            <IconButton
                                sx={{ p: "5px 10px", borderRadius: "16px", border: "1px solid white" }}
                                onClick={() => setShowReportConfigDialog(true)}
                                variant="contained"
                            >
                                <FileDownloadIcon />
                            </IconButton>
                        </Grid>
                    )}

                    {!isEnvironment(environment.stagingAndProduction) && downloadStats && (
                        <Grid item sx={{ width: "auto", paddingRight: "15px" }}>
                            <CircularProgress size={30} sx={{ color: alpha("#FFFFFF", 0.9), marginRight: 2 }} />
                        </Grid>
                    )}
                </Grid>
                <Grid container flexDirection={"column"} overflow={"auto"} width={"100%"} size="grow">
                    {tabs
                        .filter((t) => t.display)
                        .map((t) => (
                            <Grid key={t.value} display={tab !== t.value && "none"} padding={2} width={"100%"} size="auto">
                                {t.component}
                            </Grid>
                        ))}
                </Grid>
                <ReportDurationModal open={showReportConfigDialog} onClose={() => setShowReportConfigDialog(false)} onSubmit={handleDownloadCsv} />
            </Grid>
        )
    );
}
