import { Box, CircularProgress, Grid, IconButton, Typography } from "@mui/material";
import { useEffect, useMemo, useState } from "react";
import axiosInstance from "../../../axios";
import { generateRandomColorRGBA, isEnvironment, permissions, sortObject, userValues } from "../../../utils";
import dayjs from "dayjs";
import { ArrowBackIos, ArrowForwardIos } from "@mui/icons-material";
import BarChart from "../../../components/Charts/BarChart";
import KPICard from "../../../components/KPICard";
import environment from "../../../../environment";
import { useApp } from "../../../hooks/AppHook";
import { useUser } from "../../../hooks/UserHook";
import theme from "../../../theme";
import DoughnutChart from "../../../components/Charts/DoughnutChart";
import useVesselInfo from "../../../hooks/VesselInfoHook";

const graphsMinHeight = { xs: 200, sm: 300, lg: 400 };

export default function StatisticsPastDaylights() {
    const { devMode, timezone, showIDs } = useApp();
    const { user } = useUser();
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();
    const [focusedStats, setFocusedStats] = useState({});
    const [allStats, setAllStats] = useState([]);
    const [vesselsInfo, setVesselsInfo] = useState([]);

    useEffect(() => {
        fetchVessels();
    }, [vesselInfo]);

    const fetchVessels = async () => {
        if (vesselInfo && Array.isArray(vesselInfo)) {
            setVesselsInfo(vesselInfo);
        } else {
            fetchVesselsInfo();
        }
    };

    useEffect(() => {
        axiosInstance
            .get("/statistics", { params: { type: "daily" } })
            .then((res) => setAllStats(res.data))
            .catch(console.error);
    }, []);

    useEffect(() => {
        if (!allStats || allStats.length === 0) return;
        setFocusedStats(allStats[0]);
    }, [allStats]);

    const handleStatsChange = (op) => {
        setFocusedStats((focusedStats) => {
            var index = allStats.findIndex((o) => o._id === focusedStats._id);
            if (op === "increment") index += 1;
            else if (op === "decrement") index -= 1;
            if (!allStats[index]) return focusedStats;
            return allStats[index];
        });
    };

    const transformKeys = (data) => {
        return Object.fromEntries(
            Object.entries(data).map(([key, value]) => {
                const vessel = vesselsInfo.find((e) => e.vessel_id === key);
                if (vessel) {
                    if ((devMode || showIDs) && vessel.unit_id) {
                        return [`${vessel.name} (${vessel.unit_id})`, value];
                    } else {
                        return [vessel.name, value];
                    }
                }
                return [key, value];
            }),
        );
    };

    const stats = useMemo(() => {
        if (!focusedStats || !focusedStats.stats || vesselsInfo.length === 0 || !user) return null;
        let stats = focusedStats.stats;

        /** remove dev units */
        if (!devMode) {
            vesselsInfo.forEach((vessel) => {
                if (!vessel.is_active && vessel.vessel_id) {
                    delete stats.totalVesselsDetectedbySensors?.[vessel.vessel_id];
                }
            });
        }

        /** hide unwanted vessels */
        if (!user.hasPermissions([permissions.accessAllVessels])) {
            let personalizedStats = {
                totalVesselsDetectedbySensors: {},
            };

            const userVesselIds = user.allowed_vessels || [];

            vesselsInfo.forEach((vessel) => {
                const hasAccess = userVesselIds.some((allowedVessel) => allowedVessel.toString() === vessel.vessel_id);

                if (hasAccess && vessel.vessel_id && stats.totalVesselsDetectedbySensors?.[vessel.vessel_id]) {
                    personalizedStats.totalVesselsDetectedbySensors[vessel.vessel_id] = stats.totalVesselsDetectedbySensors?.[vessel.vessel_id];
                }
            });
            stats = { ...stats, ...personalizedStats };
        }

        stats.totalVesselsDetectedbySensors = sortObject(stats.totalVesselsDetectedbySensors || {});
        stats.totalVesselsSuperCategorized = sortObject(stats.totalVesselsSuperCategorized);
        stats.totalVesselsSubCategorized = sortObject(stats.totalVesselsSubCategorized);

        return stats;
    }, [focusedStats, devMode, vesselsInfo, user]);

    return !stats ? (
        <CircularProgress />
    ) : (
        <Grid container gap={2} color={"#FFFFFF"} flexDirection={"column"} wrap="nowrap" overflow={"auto"} width={"100%"}>
            <Grid
                container
                alignItems={"center"}
                justifyContent={"space-between"}
                wrap="nowrap"
                bgcolor={"primary.main"}
                borderRadius={"20px"}
                padding={2}
            >
                <Grid>
                    <IconButton sx={{ p: 0 }} disabled={focusedStats._id === allStats[0]._id} onClick={() => handleStatsChange("decrement")}>
                        <ArrowBackIos />
                    </IconButton>
                </Grid>
                <Grid>
                    <Typography sx={{ typography: { xs: "caption", sm: "body1" } }} fontWeight={"bold"} textAlign={"center"} display={"flex"}>
                        Data for the time period from:{" "}
                        {dayjs(focusedStats.fromTimestamp)
                            .tz(timezone)
                            .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}{" "}
                        to:{" "}
                        {dayjs(focusedStats.toTimestamp)
                            .tz(timezone)
                            .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}{" "}
                        ({timezone})
                    </Typography>
                </Grid>
                <Grid>
                    <IconButton
                        sx={{ p: 0 }}
                        disabled={focusedStats._id === allStats[allStats.length - 1]._id}
                        onClick={() => handleStatsChange("increment")}
                    >
                        <ArrowForwardIos />
                    </IconButton>
                </Grid>
            </Grid>
            <Grid width={"100%"} container spacing={2} display={isEnvironment(environment.stagingAndProduction) && !devMode ? "none" : "flex"}>
                <Grid
                    size={{
                        xs: 12,
                        md: 5.8,
                        lg: 4,
                    }}
                >
                    <KPICard title={"Vessels Detected"} value={stats.totalVesselsDetected} icon={"/stats-icon-4.svg"} />
                </Grid>
                <Grid
                    size={{
                        xs: 12,
                        md: 5.8,
                        lg: 4,
                    }}
                >
                    <KPICard title={"SmartMasts at Sea"} value={stats.totalSmartmastsAtSea} icon={"/stats-icon-1.svg"} />
                </Grid>
                <Grid
                    size={{
                        xs: 12,
                        md: 5.8,
                        lg: 4,
                    }}
                >
                    <KPICard title={"SmartMasts Online"} value={stats.totalSmartmastsOnline} icon={"/stats-icon-2.svg"} />
                </Grid>
            </Grid>
            {/* <Grid item>
                    <Typography>
                        Total vessels detected: {stats.totalVesselsDetected}
                    </Typography>
                </Grid> */}
            {/* <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total vessels detected by sensors
                        </Typography>
                    </Grid>
                    <Grid item container columnGap={2}>
                        {Object.keys(stats.totalVesselsDetectedbySensors).map(unit => (
                            <Grid item>
                                <Typography>
                                    {unit}: {stats.totalVesselsDetectedbySensors[unit]}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
                <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total vessels categorized by super category
                        </Typography>
                    </Grid>
                    <Grid item container columnGap={2}>
                        {Object.keys(stats.totalVesselsSuperCategorized).map(cat => (
                            <Grid item>
                                <Typography>
                                    {cat}: {stats.totalVesselsSuperCategorized[cat]}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                </Grid>
                {stats.experimental?.totalVesselsSuperCategorizedWithBoundingBoxOccupancy5 && (
                    <Grid item container flexDirection={'column'}>
                        <Grid item>
                            <Typography>
                                [Experimental] Total vessels categorized by super category (Bounding Box Occupancy {'>'} 5%)
                            </Typography>
                        </Grid>
                        <Grid item container columnGap={2}>
                            {Object.keys(stats.experimental.totalVesselsSuperCategorizedWithBoundingBoxOccupancy5).map(cat => (
                                <Grid item>
                                    <Typography>
                                        {cat}: {stats.experimental.totalVesselsSuperCategorizedWithBoundingBoxOccupancy5[cat]}
                                    </Typography>
                                </Grid>
                            ))}
                        </Grid>
                    </Grid>
                )} */}
            {/* <Grid item container flexDirection={'column'}>
                    <Grid item>
                        <Typography>
                            Total vessels categorized by sub category
                        </Typography>
                    </Grid>
                    <Grid item container gap={2}>
                        {Object.keys(stats.totalVesselsSubCategorized).map(cat => (
                            <Grid item>
                                <Typography>
                                    {cat}: {stats.totalVesselsSubCategorized[cat]}
                                </Typography>
                            </Grid>
                        ))}
                    </Grid>
                </Grid> */}
            {/* <Grid item>
                    <Typography>
                        Total SmartMasts at sea: {stats.totalSmartmastsAtSea}
                    </Typography>
                </Grid>
                <Grid item>
                    <Typography>
                        Total SmartMasts online: {stats.totalSmartmastsOnline}
                    </Typography>
                </Grid> */}
            <Grid container spacing={2} columns={12}>
                <Grid
                    display={"flex"}
                    flexDirection={"column"}
                    justifyContent={"flex-start"}
                    minHeight={graphsMinHeight}
                    maxHeight={"none"}
                    height={"auto"}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3, gap: 3 }}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                        Total Vessels Detected By Sensors
                    </Typography>
                    {Object.keys(stats.totalVesselsDetectedbySensors).length > 0 ? (
                        <Grid justifyContent={"center"} alignItems={"center"} display={"flex"} height={"90%"}>
                            <DoughnutChart
                                data={{
                                    labels: Object.keys(transformKeys(stats.totalVesselsDetectedbySensors)),
                                    datasets: [
                                        {
                                            label: "Vessels Detected",
                                            data: Object.values(stats.totalVesselsDetectedbySensors),
                                            backgroundColor: Array.from({ length: Object.keys(stats.totalVesselsDetectedbySensors).length }).map(
                                                (_, i) => generateRandomColorRGBA(10 + i, 1),
                                            ),
                                            borderColor: Array.from({ length: Object.keys(stats.totalVesselsDetectedbySensors).length }).map((_, i) =>
                                                generateRandomColorRGBA(10 + i, 1),
                                            ),
                                            borderWidth: 1,
                                        },
                                    ],
                                }}
                                options={{
                                    responsive: true,
                                }}
                                // fontSizes={
                                //     isEnvironment(environment.stagingAndProduction)
                                //         ? [
                                //               { size: "15rem", family: "Arial" },
                                //               { size: "20rem", family: "Arial" },
                                //           ]
                                //         : null
                                // }
                            />
                        </Grid>
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                height: "93% !important",
                            }}
                        >
                            <Typography>No data available for specified time range</Typography>
                        </Box>
                    )}
                </Grid>
                <Grid
                    display={isEnvironment(environment.stagingAndProduction) && !devMode ? "none" : "flex"}
                    flexDirection={"column"}
                    justifyContent={"flex-start"}
                    minHeight={graphsMinHeight}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3 }}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                        Total Vessels Categorized by Super Category
                    </Typography>
                    {Object.keys(stats.totalVesselsSuperCategorized).length > 0 ? (
                        <BarChart
                            data={{
                                labels: Object.keys(stats.totalVesselsSuperCategorized),
                                datasets: [
                                    {
                                        label: "Total Vessels",
                                        data: Object.values(stats.totalVesselsSuperCategorized),
                                        backgroundColor: theme.palette.custom.mainBlue,
                                    },
                                ].filter((o) => o),
                            }}
                            options={{
                                responsive: true,
                            }}
                        />
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                height: "93%",
                            }}
                        >
                            <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                                No data available for specified time range
                            </Typography>
                        </Box>
                    )}
                </Grid>
                <Grid
                    display={isEnvironment(environment.stagingAndProduction) && !devMode ? "none" : "flex"}
                    flexDirection={"column"}
                    justifyContent={"center"}
                    minHeight={graphsMinHeight}
                    backgroundColor={"primary.main"}
                    borderRadius={"20px"}
                    sx={{ padding: 3 }}
                    size={{
                        xs: 12,
                        lg: 6,
                    }}
                >
                    <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                        Total Vessels Categorized by Sub Category{" "}
                    </Typography>
                    {Object.keys(stats.totalVesselsSubCategorized).length > 0 ? (
                        <BarChart
                            data={{
                                labels: Object.keys(stats.totalVesselsSubCategorized),
                                datasets: [
                                    {
                                        label: "Total Vessels",
                                        data: Object.values(stats.totalVesselsSubCategorized),
                                        backgroundColor: theme.palette.custom.mainBlue,
                                    },
                                ].filter((o) => o),
                            }}
                            options={{
                                responsive: true,
                            }}
                        />
                    ) : (
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                height: "93%",
                            }}
                        >
                            <Typography fontWeight={"600"} fontSize={{ xs: "14px", md: "20px" }}>
                                No data available for specified time range
                            </Typography>
                        </Box>
                    )}
                </Grid>
            </Grid>
        </Grid>
    );
}
