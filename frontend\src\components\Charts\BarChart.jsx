// Import required libraries
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import theme from '../../theme';

// Register components in Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

export default function BarChart({ data, options }) {
    const defaultOptions = {
        responsive: true,
        scales: {
            x: {
                grid: {
                    display: false,
                },
                ticks: {
                    color: '#FFFFFF',
                    maxRotation: 90
                },
            },
            y: {
                grid: {
                    display: false,
                },
                ticks: {
                    color: '#FFFFFF',
                },
            },
        },
        plugins: {
            legend: {
                labels: {
                    color: '#FFFFFF',
                    usePointStyle: true,
                    pointStyle: 'circle',
                },
            },
        },
        // elements: {
        //     bar: {
        //         borderRadius: 10,
        //     },
        // },
    };

    const mergedOptions = { ...defaultOptions, ...options };

    return <Bar data={data} options={mergedOptions} />;
}