import { Box, CircularProgress, IconButton, Tooltip } from "@mui/material";
import StarBorderIcon from "@mui/icons-material/StarBorder";
import StarIcon from "@mui/icons-material/Star";
import DownloadIcon from "@mui/icons-material/Download";
import { Share as ShareIcon } from "@mui/icons-material";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import ImageIcon from "@mui/icons-material/Image";
import HideImageIcon from "@mui/icons-material/HideImage";
import { helperIconModes } from "../utils";

const HelperIcons = ({ buttonsToShow = [], buttonHandlers = {}, buttonStates = {}, containerStyle = {}, direction = "column", containerRef }) => {
    return (
        <Box
            sx={{
                position: "absolute",
                top: 8,
                right: 8,
                display: "flex",
                flexDirection: direction,
                flexWrap: "wrap",
                gap: 1,
                zIndex: 1000,
                ...containerStyle,
            }}
        >
            {buttonsToShow.includes(helperIconModes.FAVOURITE) && (
                <Tooltip
                    {...(containerRef ? { slotProps: { popper: { container: containerRef.current } } } : {})}
                    title={buttonStates.isFavourite ? "Remove from favorites" : "Add to favorites"}
                    arrow
                    placement="left"
                >
                    <IconButton
                        onClick={(e) => {
                            e.stopPropagation();
                            buttonStates.isFavourite ? buttonHandlers.removeFavourite() : buttonHandlers.addFavourite();
                        }}
                        sx={{
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: buttonStates.isFavourite ? "yellow" : "white",
                            backgroundColor: "rgba(0, 0, 0, 0.5)",
                            borderRadius: "50%",
                            "&:hover": {
                                backgroundColor: "rgba(0, 0, 0, 0.7)",
                            },
                            "&.Mui-disabled": {
                                backgroundColor: "rgba(0, 0, 0, 0.5)",
                            },
                        }}
                        disableRipple
                        disabled={buttonStates.isFavouriteLoading}
                    >
                        {buttonStates.isFavouriteLoading ? (
                            <CircularProgress sx={{ color: "white" }} size={18} />
                        ) : buttonStates.isFavourite ? (
                            <StarIcon sx={{ height: 18 }} />
                        ) : (
                            <StarBorderIcon sx={{ height: 18 }} />
                        )}
                    </IconButton>
                </Tooltip>
            )}
            {buttonsToShow.includes(helperIconModes.SHARE) && (
                <Tooltip
                    {...(containerRef ? { slotProps: { popper: { container: containerRef.current } } } : {})}
                    title="Share this event"
                    arrow
                    placement="left"
                >
                    <IconButton
                        className="icon-button"
                        onClick={buttonHandlers.toggleShare}
                        sx={{
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: "#fff",
                            backgroundColor: "rgba(0, 0, 0, 0.5)",
                            borderRadius: "50%",
                            "&:hover": {
                                backgroundColor: "rgba(0, 0, 0, 0.7)",
                            },
                        }}
                    >
                        <ShareIcon sx={{ height: 18 }} />
                    </IconButton>
                </Tooltip>
            )}
            {buttonsToShow.includes(helperIconModes.DOWNLOAD) && !buttonStates.isDownloading && (
                <Tooltip
                    {...(containerRef ? { slotProps: { popper: { container: containerRef.current } } } : {})}
                    title="Download"
                    arrow
                    placement="left"
                >
                    <IconButton
                        onClick={(e) => {
                            e.stopPropagation();
                            buttonHandlers.downloadArtifact();
                        }}
                        sx={{
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: "#fff",
                            backgroundColor: "rgba(0, 0, 0, 0.5)",
                            borderRadius: "50%",
                            "&:hover": {
                                backgroundColor: "rgba(0, 0, 0, 0.7)",
                            },
                        }}
                    >
                        <DownloadIcon sx={{ height: 18 }} />
                    </IconButton>
                </Tooltip>
            )}
            {buttonsToShow.includes(helperIconModes.DOWNLOAD) && buttonStates.isDownloading && (
                <CircularProgress
                    sx={{
                        fontSize: 18,
                        color: "white",
                        filter: "drop-shadow(0px 2px 3px rgba(0,0,0,0.5))",
                    }}
                />
            )}
            {buttonsToShow.includes(helperIconModes.FULLSCREEN) && buttonHandlers.handleFullscreenOpen && buttonStates.showFullscreenIconForMap && (
                <Tooltip
                    {...(containerRef ? { slotProps: { popper: { container: containerRef.current } } } : {})}
                    title="View Full Screen"
                    arrow
                    placement="left"
                >
                    <IconButton
                        onClick={buttonHandlers.handleFullscreenOpen}
                        sx={{
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: "#fff",
                            backgroundColor: "rgba(0, 0, 0, 0.5)",
                            borderRadius: "50%",
                            "&:hover": {
                                backgroundColor: "rgba(0, 0, 0, 0.7)",
                            },
                        }}
                    >
                        <FullscreenIcon sx={{ height: 18 }} />
                    </IconButton>
                </Tooltip>
            )}
            {buttonsToShow.includes(helperIconModes.ARCHIVE) && buttonHandlers.handleArchiveClick && (
                <Tooltip
                    {...(containerRef ? { slotProps: { popper: { container: containerRef.current } } } : {})}
                    title={!buttonStates.isArchiving ? (buttonStates.archived ? "Unarchive" : "Archive") : null}
                    arrow
                    placement="left"
                >
                    <IconButton
                        size="small"
                        sx={{
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: "#fff",
                            backgroundColor: "rgba(0,0,0,0.5)",
                            borderRadius: "50%",
                            "&:hover": {
                                backgroundColor: "rgba(0,0,0,0.7)",
                            },
                        }}
                        onClick={buttonHandlers.handleArchiveClick}
                        disabled={buttonStates.isArchiving}
                    >
                        {buttonStates.isArchiving ? (
                            <CircularProgress sx={{ color: "white" }} size={18} />
                        ) : buttonStates.archived ? (
                            <ImageIcon sx={{ height: 18, color: "#F59E0B" }} />
                        ) : (
                            <HideImageIcon sx={{ height: 18, color: "#F59E0B" }} />
                        )}
                    </IconButton>
                </Tooltip>
            )}
        </Box>
    );
};

export default HelperIcons;
