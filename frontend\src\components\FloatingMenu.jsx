import React, { useEffect, useState } from "react";
import { Box, IconButton, Tooltip, Menu, Typography, Grid, CircularProgress } from "@mui/material";
import { Close } from "@mui/icons-material";
import { useApp } from "../hooks/AppHook";
import { useLocation } from "react-router-dom";
import theme from "../theme";

const FloatingMenu = ({
    menuItems,
    menuBgColor = "#4F5968",
    children,
    activeComponentTitle,
    onMenuUpdate,
    withPadding = true,
    withMinContent = false,
    toggle = true,
    artifactIndicator,
    isProcessing = {},
    mosaicToggle,
    view,
}) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const { isMobile } = useApp();
    const { pathname } = useLocation();
    const [title, setTitle] = useState("");

    const isItemProcessing = (itemTitle) => {
        return isProcessing[itemTitle] === true;
    };

    const handleIconClick = (event, title) => {
        if (mosaicToggle && title === "Mosaic View") mosaicToggle();
        setTitle(title);
        if (activeComponentTitle === title && toggle) return handleClose();
        setAnchorEl(event.currentTarget);
        onMenuUpdate(title);
    };

    const handleClose = () => {
        setAnchorEl(null);
        onMenuUpdate(null);
    };

    useEffect(() => {
        if (!activeComponentTitle) {
            setAnchorEl(null);
        }
    }, [activeComponentTitle]);

    useEffect(() => {
        handleClose();
    }, [pathname]);

    return (
        <Box width={{ xs: "100%", md: "100%", lg: "auto" }}>
            {!isMobile && (
                <Box
                    sx={{
                        position: "fixed",
                        top: "10rem",
                        right: "1rem",
                        zIndex: 1200,
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        gap: 4,
                        backgroundColor: menuBgColor,
                        boxShadow: theme.shadows[4],
                        padding: "20px 10px",
                        borderRadius: 10,
                    }}
                >
                    {menuItems.map(({ title, icon: Icon, imgSrc, className, artifactIndicatorShow, selectable = true, tooltip, disabled }, index) => (
                        <Tooltip
                            title={tooltip || title}
                            placement="left"
                            key={index}
                            slotProps={{
                                tooltip: {
                                    sx: {
                                        backgroundColor: "#FFFFFF",
                                        color: "black",
                                        boxShadow: theme.shadows[1],
                                        fontSize: 14,
                                        padding: "5px 10px",
                                    },
                                },
                                arrow: {
                                    sx: {
                                        color: "#FFFFFF",
                                        transform: title === "Record" ? "translateY(38px) !important" : "translateY(8px) !important",
                                    },
                                },
                            }}
                            arrow
                        >
                            {isItemProcessing(title) ? (
                                <IconButton
                                    size="large"
                                    color="primary"
                                    className={className}
                                    sx={{
                                        position: "relative",
                                        backgroundColor: "primary.main",
                                        borderRadius: "5px",
                                        padding: "5px",
                                        cursor: "default",
                                        "&:hover": {
                                            backgroundColor: "primary.main",
                                        },
                                    }}
                                >
                                    <CircularProgress sx={{ color: "white" }} size={20} />
                                </IconButton>
                            ) : (
                                <IconButton
                                    onClick={(e) => handleIconClick(e, title)}
                                    size="large"
                                    color="primary"
                                    className={className}
                                    sx={{
                                        position: "relative",
                                        backgroundColor:
                                            title === "Mosaic View" && view === "Mosaic"
                                                ? theme.palette.custom.mainBlue
                                                : selectable && activeComponentTitle && activeComponentTitle === title
                                                  ? theme.palette.custom.mainBlue
                                                  : "primary.main",
                                        borderRadius: "5px",
                                        padding: "5px",
                                        transition: "background-color 0.3s ease",
                                        "&:hover": {
                                            backgroundColor: "#818994",
                                        },
                                    }}
                                    disabled={disabled}
                                >
                                    {imgSrc && !Icon && <img src={imgSrc} alt={title} width={20} height={20} />}
                                    {Icon && !imgSrc && <Icon />}

                                    {artifactIndicatorShow && artifactIndicator && Object.keys(artifactIndicator).length > 0 && (
                                        <Grid
                                            style={{
                                                position: "absolute",
                                                top: 0,
                                                right: "-3px",
                                                width: "12px",
                                                height: "12px",
                                                borderRadius: "50%",
                                                backgroundColor: "red",
                                                border: "2px solid ", // Optional, to make the circle stand out
                                            }}
                                        />
                                    )}
                                </IconButton>
                            )}
                        </Tooltip>
                    ))}
                </Box>
            )}
            {!isMobile && toggle && (
                <Menu
                    anchorEl={anchorEl}
                    open={Boolean(anchorEl)}
                    onClose={handleClose}
                    anchorOrigin={{
                        vertical: "top",
                        horizontal: "left",
                    }}
                    transformOrigin={{
                        vertical: "top",
                        horizontal: "right",
                    }}
                    sx={{
                        position: "static",
                        marginLeft: "-1rem",
                        "& .MuiPaper-root": {
                            padding: 0,
                            boxShadow: theme.shadows[4],
                        },
                        "& .MuiPaper-root.MuiPopover-paper": {
                            marginLeft: "-25px",
                        },
                        "& .MuiList-root": {
                            padding: 0,
                        },
                        display: title === "Mosaic View" ? "none" : "",
                    }}
                    hideBackdrop={true} // Hides the backdrop
                >
                    <Grid
                        container
                        color={"#FFFFFF"}
                        width={{
                            xs: "auto",
                            md: withMinContent ? "min-content" : "100%",
                            zIndex: 999,
                            display: title === "Mosaic View" ? "none" : "",
                        }}
                        flexDirection={"column"}
                    >
                        <Grid
                            size={{ xs: true }}
                            container
                            justifyContent={"space-between"}
                            alignItems={"center"}
                            sx={{
                                backgroundColor: "primary.main",
                                paddingX: 2,
                                zIndex: 999,
                            }}
                        >
                            <Grid>
                                <Typography>{activeComponentTitle}</Typography>
                            </Grid>
                            <Grid paddingY={"10px"}>
                                <IconButton
                                    onClick={() => handleClose()}
                                    size="large"
                                    sx={{
                                        padding: 0,
                                    }}
                                >
                                    <Close />
                                </IconButton>
                            </Grid>
                        </Grid>
                        <Grid container paddingX={withPadding ? 2 : 0} minWidth={300} sx={{ zIndex: 999, backgroundColor: "primary.light" }}>
                            {children}
                        </Grid>
                    </Grid>
                </Menu>
            )}

            {isMobile && <Box>{children}</Box>}
        </Box>
    );
};

export default FloatingMenu;
