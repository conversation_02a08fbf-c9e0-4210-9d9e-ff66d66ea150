import { Button, Grid, InputAdornment, OutlinedInput, Tab, Tabs } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { useUser } from "../../../hooks/UserHook";
import { permissions } from "../../../utils";
import Sessions from "./Sessions";
import { Search } from "@mui/icons-material";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";
import useDebounce from "../../../hooks/useDebounce.js";

export default function LogManagement() {
    const { user } = useUser();
    const { isMobile } = useApp();

    const [showFilterModal, setShowFilterModal] = useState(false);
    const [searchQuery, setSearchQuery] = useState({});

    const [searchTerm, setSearchTerm] = React.useState(searchQuery.full_name_or_browser_or_device || "");
    const debouncedInputValue = useDebounce(searchTerm, 600);

    useEffect(() => {
        if (debouncedInputValue !== undefined && debouncedInputValue !== searchQuery.full_name_or_browser_or_device) {
            handleSearchChange({ target: { value: debouncedInputValue } });
        }
    }, [debouncedInputValue]);

    useEffect(() => {
        if (searchQuery.full_name_or_browser_or_device !== searchTerm) {
            setSearchTerm(searchQuery.full_name_or_browser_or_device || "");
        }
    }, [searchQuery.full_name_or_browser_or_device]);

    const handleSearchChange = (event) => {
        setSearchQuery({ full_name_or_browser_or_device: event.target.value });
    };

    const handleInputChange = (event) => {
        setSearchTerm(event.target.value);
    };

    const tabs = useMemo(
        () => [
            {
                value: "sessions",
                label: "Sessions",
                component: <Sessions showFilterModal={showFilterModal} setShowFilterModal={setShowFilterModal} searchQuery={searchQuery} />,
                display: user?.hasPermissions([permissions.viewSessionLogs]),
            },
        ],
        [user, showFilterModal, searchQuery],
    );

    const [tab, setTab] = useState(tabs[0].value);

    return (
        user &&
        tabs.some((t) => t.display) &&
        tab && (
            <Grid
                container
                color={"#FFFFFF"}
                flexDirection={"column"}
                height={"100%"}
                overflow={"auto"}
                sx={{ backgroundColor: theme.palette.custom.darkBlue }}
            >
                <Grid container padding={2} display={"flex"} rowGap={2} justifyContent={"space-between"} alignItems={"center"} flexWrap={"wrap"}>
                    <Grid
                        size={{
                            xs: 12,
                            lg: 2.5,
                        }}
                    >
                        <Tabs
                            value={tab}
                            onChange={(e, v) => setTab(v)}
                            sx={{
                                width: "100%",
                                padding: "4px",
                                border: `2px solid ${theme.palette.custom.borderColor}`,
                                borderRadius: "8px",
                                backgroundColor: "transparent",
                                "& .MuiTabs-flexContainer": {
                                    height: "100%",
                                },
                                "& .MuiButtonBase-root": {
                                    width: "100%",
                                    borderRadius: "8px",
                                },
                                "& .MuiButtonBase-root.Mui-selected": {
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                        >
                            {tabs
                                .filter((t) => t.display)
                                .map((t) => (
                                    <Tab
                                        key={t.value}
                                        label={t.label}
                                        value={t.value}
                                        sx={{
                                            maxWidth: "none",
                                        }}
                                    />
                                ))}
                        </Tabs>
                    </Grid>
                    <Grid
                        container
                        columnGap={2}
                        justifyContent={"space-between"}
                        size={{
                            xs: 12,
                            lg: 9.4,
                        }}
                    >
                        <Grid
                            size={{
                                xs: "grow",
                                lg: 5.8,
                            }}
                        >
                            <OutlinedInput
                                type="text"
                                value={searchTerm}
                                onChange={handleInputChange}
                                startAdornment={
                                    <InputAdornment position="start">
                                        <Search sx={{ color: "#FFFFFF" }} />
                                    </InputAdornment>
                                }
                                placeholder="Search by name, browser or device"
                                sx={{
                                    color: "#FFFFFF",
                                    width: "100%",
                                    "& .MuiOutlinedInput-notchedOutline": {
                                        border: "2px solid",
                                        borderColor: theme.palette.custom.borderColor + " !important",
                                        borderRadius: "8px",
                                    },
                                }}
                            />
                        </Grid>
                        <Grid alignItems={"center"} display={"flex"} justifyContent={"flex-end"} gap={2} size="auto">
                            <Button
                                variant="outlined"
                                startIcon={<img src={"/icons/filter_icon.svg"} width={20} height={20} />}
                                sx={{
                                    "&.MuiButtonBase-root": {
                                        borderColor: theme.palette.custom.borderColor,
                                        color: "#FFFFFF",
                                        height: { xs: "100%", lg: "auto" },
                                        padding: { xs: "0", lg: "10px 20px" },
                                        fontWeight: "bold",
                                    },
                                    "& .MuiButton-icon": {
                                        marginRight: { xs: 0, lg: "10px" },
                                    },
                                }}
                                onClick={() => setShowFilterModal(true)}
                            >
                                {!isMobile && "Filter"}
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
                {tabs
                    .filter((t) => t.display)
                    .map((t) => (
                        <Grid key={t.value} display={tab !== t.value && "none"} paddingX={2} paddingBottom={2} width={"100%"} size="grow">
                            {t.component}
                        </Grid>
                    ))}
            </Grid>
        )
    );
}
