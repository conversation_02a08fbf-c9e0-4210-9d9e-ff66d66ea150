const express = require("express");
const isAuthenticated = require("../middlewares/auth");
const { validateError, canAccessVessel } = require("../utils/functions");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const router = express.Router();
const streamService = require("../services/Stream.service");
const { validateData } = require("../middlewares/validator");
const { query } = require("express-validator");
const Vessel = require("../models/Vessel");
const RegionGroup = require("../models/RegionGroup");
const { default: mongoose } = require("mongoose");

const authUserApiLimiter = rateLimit({
    windowMs: 10 * 1000,
    limit: 50,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

// Middleware to apply the correct limiter
function conditionalRateLimiter(req, res, next) {
    if (req.user) {
        authUserApiLimiter(req, res, next);
    } else {
        apiLimiter(req, res, next);
    }
}

router.use("/", conditionalRateLimiter);

router.get(
    "/info",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSELS_INFO),
    isAuthenticated,
    validateData.bind(this, [
        query("regions")
            .isString()
            .optional()
            .customSanitizer((v) => v.split(",").map((v) => v.trim())),
        query("region_groups")
            .optional()
            .customSanitizer((v) => {
                if (!v || v.trim() === "") return [];
                return v
                    .split(",")
                    .map((id) => id.trim())
                    .filter((id) => id.length > 0 && mongoose.Types.ObjectId.isValid(id));
            }),
    ]),
    async (req, res) => {
        try {
            const { regions, region_groups } = req.query;

            let vessels = await Vessel.find();
            const streams = await streamService.fetchAll();
            const regionGroups = await RegionGroup.find();

            // filter provisioned units
            vessels = vessels.filter((vessel) => canAccessVessel(req, vessel));

            // filter by regions
            if (regions && regions.length > 0) {
                vessels = vessels.filter((vessel) => {
                    const stream = streams.find((s) => s.unit_id === vessel.unit_id);
                    if (!stream) return false;
                    return regions.includes(stream.region);
                });
            }

            // filter by region groups
            // if (region_groups && region_groups.length > 0) {
            //     vessels = vessels.filter((vessel) => {
            //         const regionGroup = regionGroups.find((rg) => rg.vessel_ids.some((v) => v.toString() === vessel._id.toString()));
            //         if (!regionGroup) return false;
            //         return region_groups.includes(regionGroup._id.toString());
            //     });
            // }
            if (region_groups && region_groups.length > 0) {
                vessels = vessels.filter((vessel) => {
                    if (!vessel.region_group_id) return false;
                    return region_groups.includes(vessel.region_group_id.toString());
                });
            }

            // generate response data
            const data = vessels.map((vessel) => {
                const stream = streams.find((s) => s.unit_id === vessel.unit_id);
                // const regionGroup = regionGroups.find((rg) => rg.vessel_ids.some((v) => v.toString() === vessel._id.toString()));
                const regionGroup = vessel ? regionGroups.find((rg) => rg._id.toString() === vessel.region_group_id?.toString()) : null;

                return {
                    vessel_id: vessel._id.toString(),
                    unit_id: vessel.unit_id,
                    name: vessel.name,
                    thumbnail_s3_key: vessel.thumbnail_s3_key,
                    is_active: vessel.is_active,
                    region: stream?.region || null,
                    is_live: stream?.is_live || false,
                    timezone: regionGroup?.timezone || null,
                    // region_group_id: regionGroup?._id.toString() || null,
                    region_group_id: vessel?.region_group_id || null,
                };
            });

            res.json(data);
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Vessels
 *   description: Fetch vessel information
 */

/**
 * @swagger
 * /vessels/info:
 *   get:
 *     summary: Fetch list of vessels information
 *     description: Fetches a list of vessels with their respective details including vessel ID, unit ID, name, and other metadata.
 *     tags: [Vessels]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: regions
 *         in: query
 *         required: false
 *         description: Comma-separated AWS regions to fetch vessels for
 *         schema:
 *           type: string
 *           example: ap-southeast-1,us-east-1
 *       - name: region_groups
 *         in: query
 *         required: false
 *         description: Comma-separated region group IDs to fetch vessels for
 *         schema:
 *           type: string
 *           example: 67db2068a64a865006d065f7, 67dc2e586a89af04d143d471, 681c253f9f43051a7748b2c1
 *     responses:
 *       200:
 *         description: An array of vessels information
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   vessel_id:
 *                     type: string
 *                     description: "The unique identifier of the vessel."
 *                     example: "507f1f77bcf86cd799439011"
 *                   unit_id:
 *                     type: string
 *                     description: "The unit identifier of the vessel."
 *                     example: "prototype-30"
 *                   name:
 *                     type: string
 *                     nullable: true
 *                     description: "The name of the unit. Can be null if not provided."
 *                     example: "BRP Teresa Magbanua MRRV-9701"
 *                   thumbnail_s3_key:
 *                     type: string
 *                     nullable: true
 *                     description: "The S3 key of the vessel's thumbnail image."
 *                     example: "vessels/thumbnail/507f1f77bcf86cd799439011.jpg"
 *                   is_active:
 *                     type: boolean
 *                     description: "Whether the vessel is active."
 *                     example: true
 *                   region:
 *                     type: string
 *                     nullable: true
 *                     description: "AWS region the vessel is listed in."
 *                     example: "ap-southeast-1"
 *                   is_live:
 *                     type: boolean
 *                     description: "Whether the vessel's camera is currently live."
 *                     example: true
 *                   timezone:
 *                     type: string
 *                     nullable: true
 *                     description: "The timezone of the vessel from its region group."
 *                     example: "+08:00"
 *                   region_group_id:
 *                     type: string
 *                     nullable: true
 *                     description: "The ID of the region group this vessel belongs to."
 *                     example: "507f1f77bcf86cd799439011"
 *       401:
 *         description: Unauthorized, the user must be authenticated.
 *       500:
 *         description: "Internal server error"
 */
