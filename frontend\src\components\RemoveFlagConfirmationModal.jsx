import { Grid, Modal, Typography, Button } from "@mui/material";
import ModalContainer from "./ModalContainer";
import theme from "../theme";

const RemoveFlagConfirmationModal = ({ open, onClose, onConfirm }) => {
    return (
        <Modal open={open} onClose={onClose}>
            <ModalContainer title={"Remove from Flagged"} onClose={onClose} headerPosition="center">
                <Grid container flexDirection={"column"} gap={2} width={{ xs: "auto", sm: 400 }}>
                    <Grid display={"flex"} justifyContent={"center"}>
                        <Typography fontWeight={"100"} textAlign={"center"}>
                            Do you really want to remove this artifact from flagged artifacts.
                        </Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button
                                variant="contained"
                                onClick={onClose}
                                backgroundColor={"#FFFFFF"}
                                sx={{ background: "#FFFFFF !important", color: theme.palette.primary.main }}
                            >
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" onClick={onConfirm}>
                                Confirm
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default RemoveFlagConfirmationModal;
