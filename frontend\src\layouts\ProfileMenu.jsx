import { Logout, Settings, KeyboardArrowUp } from "@mui/icons-material";
import { alpha, Avatar, Badge, Box, Button, Divider, Grid, CircularProgress, IconButton, Menu, MenuItem, Typography } from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { useNavigate } from "react-router-dom";
import { useUser } from "../hooks/UserHook";
import { useEffect, useState } from "react";
import theme from "../theme";
import NotificationsIcon from "@mui/icons-material/Notifications";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import axiosInstance from "../axios";
import dayjs from "dayjs";
import { displayCoordinates, userValues } from "../utils";

import gps_socket from "../gps_socket";
import s3Controller from "../controllers/S3.controller.js";

const ProfileMenu = ({ logoutOnly, avatarOnly }) => {
    const { logout, user, timezone } = useUser();
    const navigation = useNavigate();
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);
    const [notificationEle, setNotificaitonEle] = useState(null);
    const notificationShow = Boolean(notificationEle);
    const [notifications, setNotifications] = useState([]);
    const [badgeShow, setBadgeShow] = useState(false);
    // eslint-disable-next-line no-unused-vars
    const [pageSize, setPageSize] = useState(50);
    const [page, setPage] = useState(1);
    const [loader, setLoader] = useState(false);

    const fetchNotification = async () => {
        setLoader(true);
        const { data } = await axiosInstance
            .get(`/inAppNotifications?page=${page}&page_size=${pageSize}`)
            .then((res) => res.data)
            .catch(console.error);
        const batch = [];
        const detailData = data.filter((d) => d.artifact_details);
        detailData.map((artifact) => {
            batch.push({
                key: artifact.artifact_details.image_path,
                bucket_name: artifact.artifact_details.bucket_name,
                region: artifact.artifact_details.aws_region,
            });
        });
        await axiosInstance.post("/S3/signedUrl/batch", { batch }).then((response) => {
            const signedUrls = response.data.signedUrls;
            detailData.forEach((artifact, index) => {
                artifact.artifact_details.image_path && (artifact.artifact_details.image_path = signedUrls[index].signedUrl);
            });
        });
        const isReadAvailable = detailData.some((s) => s.is_read == false);
        setBadgeShow(isReadAvailable);
        const concatenatedData = notifications.concat(detailData);
        const sortedData = concatenatedData.sort((a, b) => new Date(b.artifact_details.timestamp) - new Date(a.artifact_details.timestamp));
        setNotifications(sortedData);
        setLoader(false);
    };

    const updateNotificaition = async (id) => {
        await axiosInstance.patch(`/inAppNotifications/markRead/${id}`).catch(console.error);
    };
    const markAllAsReadNotificaiton = async () => {
        const updateNotifications = notifications.filter((v) => v.is_read == false);
        if (updateNotifications.length === 0 || !updateNotifications) return;
        const ids = updateNotifications.map((v) => v._id);
        await axiosInstance
            .patch(`/inAppNotifications/bulkMarkRead`, { ids: ids })
            .then(() => {
                fetchNotification();
            })
            .catch(console.error);
    };
    useEffect(() => {
        gps_socket.on("in_app_notification", fetchNotification);
        return () => gps_socket.off("in_app_notification", fetchNotification);
    }, []);
    useEffect(() => {
        fetchNotification();
    }, [page]);
    const notificationOpen = (event) => {
        setNotificaitonEle(event.currentTarget);
    };
    const closeNotification = () => {
        setNotificaitonEle(null);
    };

    const notificationNavigation = (artifact_id, notification_id, notification) => {
        if (!notification.is_read) {
            updateNotificaition(notification_id);
        }
        navigation(`/dashboard/events/${artifact_id}`);
        closeNotification();
        fetchNotification();
    };

    const loadMoreNotification = () => {
        // if (pageSize >= 50) {
        //     setPage(page + 1);
        // }
        // setPageSize(pageSize + 10);
        // testing
        setPage(page + 1);
    };

    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleLogout = () => {
        logout();
        handleClose();
    };
    const handleNavigation = () => {
        navigation("/dashboard/settings");
        handleClose();
    };

    const LogoutButton = () => <Logout onClick={handleLogout} sx={{ fontSize: "24px", color: "red", cursor: "pointer" }} />;

    const AvatarButton = () => <Avatar sx={{ width: "35px", height: "35px" }} />;
    const NotificationMenu = () => (
        <>
            <IconButton
                onClick={notificationOpen}
                sx={{
                    fontSize: "30px",
                    color: "#FFFFFF",
                    cursor: "pointer",
                    display: "flex",
                }}
            >
                <Badge color="error" variant={badgeShow ? "dot" : "standard"} overlap="circular">
                    <NotificationsIcon />
                </Badge>
            </IconButton>

            <Menu
                anchorEl={notificationEle}
                open={notificationShow}
                onClose={closeNotification}
                anchorOrigin={{
                    vertical: 50,
                    horizontal: -60,
                }}
            >
                <Grid container sx={{ width: { xs: "320px", lg: "400px" } }}>
                    <Grid
                        width={"100%"}
                        container
                        justifyContent="space-between"
                        alignItems="center"
                        sx={{
                            backgroundColor: "primary.main",
                            padding: 1,
                            marginTop: -1,
                        }}
                    >
                        <Grid>
                            <Typography sx={{ color: "white" }}>Alerts</Typography>
                        </Grid>
                        <Grid>
                            <IconButton onClick={() => markAllAsReadNotificaiton()} size="small" disabled={!badgeShow}>
                                <Typography sx={{ color: "white", paddingRight: "5px" }} fontSize={"14px"} fontWeight={300}>
                                    Mark all as read
                                </Typography>
                                <CheckCircleOutlineIcon sx={{ color: "white" }} />
                            </IconButton>
                        </Grid>
                    </Grid>

                    {notifications.length > 0 &&
                        notifications.map((noti, index) => (
                            <Grid
                                width={"100%"}
                                container
                                sx={{
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    padding: 1,
                                    flexWrap: "nowrap",
                                    backgroundColor: noti.is_read ? "#343B44" : "#464F59",
                                    cursor: "pointer",
                                }}
                                key={index}
                                onClick={() => notificationNavigation(noti.artifact_details._id, noti._id, noti)}
                            >
                                <Grid sx={{ flex: 1, overflow: "hidden" }}>
                                    <Typography sx={{ color: "white" }}>
                                        <Typography component="span" fontSize={"14px"} fontWeight={500}>
                                            {noti.artifact_details.onboard_vessel_name}
                                        </Typography>
                                        <Typography component="span" fontSize={"14px"} fontWeight={300}>
                                            {" "}
                                            has detected a vessel {noti.is_read}
                                        </Typography>
                                    </Typography>

                                    <Typography sx={{ color: "white" }}>
                                        <Typography component="span" fontSize={"14px"} fontWeight={300}>
                                            Category:{" "}
                                        </Typography>
                                        <Typography component="span" fontSize={"14px"} fontWeight={500}>
                                            {noti.artifact_details.category}
                                        </Typography>
                                    </Typography>

                                    <Typography sx={{ color: "white" }}>
                                        <Typography component="span" fontSize={"14px"} fontWeight={300}>
                                            Location:{" "}
                                        </Typography>
                                        <Typography component="span" fontSize={"14px"} fontWeight={500}>
                                            {displayCoordinates(noti.artifact_details.location.coordinates, !!user?.use_MGRS) || ""}
                                        </Typography>
                                    </Typography>

                                    <Typography sx={{ color: "white" }}>
                                        <Typography component="span" fontSize={"14px"} fontWeight={300}>
                                            Detection Time:{" "}
                                        </Typography>
                                        <Typography component="span" fontSize={"14px"} fontWeight={500}>
                                            {dayjs(noti.artifact_details.timestamp)
                                                .tz(timezone)
                                                .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                                        </Typography>
                                    </Typography>
                                </Grid>

                                {/* Image & Timestamp */}
                                <Grid sx={{ minWidth: 60, textAlign: "right" }}>
                                    <img
                                        src={s3Controller.fetchPreviewUrl(noti.artifact_details)}
                                        loading="lazy"
                                        alt="Artifact"
                                        style={{
                                            borderRadius: "8px",
                                            maxWidth: "100px",
                                            height: "60px",
                                            objectFit: "cover",
                                        }}
                                    />
                                    {/* <Typography sx={{ fontSize: "10px", color: "white", marginTop: "4px" }}>
                            Today at 12:00
                        </Typography> */}
                                </Grid>
                            </Grid>
                        ))}

                    {notifications.length === 0 ? (
                        <Grid container justifyContent="center" width={"100%"}>
                            <Typography item sx={{ color: "white" }}>
                                No Notification is Available
                            </Typography>
                        </Grid>
                    ) : (
                        <Grid container justifyContent="center" alignItems="center" sx={{ padding: 1, width: "100%" }}>
                            <Box width="100%" display="flex" justifyContent="center">
                                {loader ? (
                                    <CircularProgress size={18} sx={{ color: "white" }} />
                                ) : (
                                    <Button variant="text" sx={{ color: "white" }} size="small" onClick={loadMoreNotification}>
                                        Load More
                                    </Button>
                                )}
                            </Box>
                        </Grid>
                    )}
                </Grid>
            </Menu>
        </>
    );
    return logoutOnly ? (
        <LogoutButton />
    ) : avatarOnly ? (
        <Grid container>
            {NotificationMenu()}
            <AvatarButton />
        </Grid>
    ) : (
        <Grid container gap={"10px"}>
            <Box
                className="dashboard-step-1"
                sx={{
                    padding: "6px 10px",
                    borderRadius: 20,
                    backgroundColor: alpha(theme.palette.background.default, 0.25),
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    gap: "10px",
                }}
            >
                {NotificationMenu()}
                <Typography
                    color={"#FFFFFF"}
                    fontWeight={"bold"}
                    sx={{ display: "inline", padding: "6px 15px", borderRadius: "20px", backgroundColor: "#FFFFFF", color: "#000", fontSize: "14px" }}
                >
                    {user ? user.name : "Anonymous"}
                </Typography>
                <AvatarButton />
                <IconButton onClick={handleClick} sx={{ fontSize: "24px", color: "#FFFFFF", cursor: "pointer" }}>
                    {anchorEl ? <KeyboardArrowUp /> : <KeyboardArrowDownIcon />}
                </IconButton>
                <Menu
                    id="basic-menu"
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleClose}
                    MenuListProps={{
                        "aria-labelledby": "basic-button",
                    }}
                    sx={{
                        "& .MuiList-root": {
                            backgroundColor: "#4F5968",
                        },
                    }}
                    transformOrigin={{
                        vertical: 100,
                        horizontal: "center",
                    }}
                    anchorOrigin={{
                        vertical: 150,
                        horizontal: "left",
                    }}
                >
                    <MenuItem
                        onClick={handleNavigation}
                        sx={{ minWidth: "250px", display: "flex", justifyContent: "space-between", backgroundColor: "#4F5968 !important" }}
                    >
                        Settings
                        <Settings sx={{ fontSize: "24px", color: "#FFFFFF", cursor: "pointer" }} />
                    </MenuItem>
                    <Divider variant="middle" sx={{ backgroundColor: alpha("#FFFFFF", 0.3) }} component="li" />
                    <MenuItem
                        onClick={handleLogout}
                        sx={{ minWidth: "250px", display: "flex", justifyContent: "space-between", backgroundColor: "#4F5968 !important" }}
                    >
                        Logout
                        <LogoutButton />
                    </MenuItem>
                </Menu>
            </Box>
        </Grid>
    );
};

export default ProfileMenu;
