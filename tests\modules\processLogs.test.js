// const { logCpuAndMemoryUsage } = require('../../modules/processLogs'); // Update with your module path

describe('setInterval conditional based on NODE_ENV', () => {
    beforeEach(() => {
        jest.spyOn(global, 'setInterval').mockImplementation(() => { }); // Mock setInterval
        jest.resetModules()
    });

    afterEach(() => {
        jest.restoreAllMocks(); // Clean up the mock
    });

    it('should call setInterval when NODE_ENV is not "test"', () => {
        process.env.NODE_ENV = 'dev'; // Simulate a non-test environment

        // Require the module again to trigger the setInterval logic
        require('../../modules/processLogs'); // Update with your module path

        // Check that setInterval was called with the correct arguments
        expect(setInterval).toHaveBeenCalledTimes(1);
    });

    it('should not call setInterval when NODE_ENV is "test"', () => {
        process.env.NODE_ENV = 'test'; // Simulate the "test" environment

        // Require the module again to trigger the setInterval logic
        require('../../modules/processLogs'); // Update with your module path

        // Ensure setInterval is not called
        expect(setInterval).not.toHaveBeenCalled();
    });

    it('should successfully execute the function without throwing errors', () => {
        process.env.NODE_ENV = 'test'; // Simulate the "test" environment
        const { logCpuAndMemoryUsage } = require('../../modules/processLogs')
        console.log('logCpuAndMemoryUsage is', logCpuAndMemoryUsage)
        expect(() => logCpuAndMemoryUsage()).not.toThrow();
    });
});
