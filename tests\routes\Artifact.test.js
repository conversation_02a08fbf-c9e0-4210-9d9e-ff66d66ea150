const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Role = require('../../models/Role');
const { default: mongoose } = require('mongoose');
const crypto = require('crypto');
const { sendEmail } = require('../../modules/email');
const Region = require('../../models/Region');
const { regionsList } = require('../data/Regions');
const { permissionsList } = require('../data/Permissions');
const Permission = require('../../models/Permission');
const { streamsList } = require('../data/Kinesis');
const { VesselLocations } = require('../data/VesselLocations');
const { artifactsList } = require('../data/Artifacts');
const awsS3 = require('../../modules/awsS3');
const db = require('../../modules/db');
const request = require("supertest");
const app = require('../../server');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('Artifacts API', () => {

    describe('POST /api/artifacts/', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts')
                        .send({});
                    expect(res.status).toBe(401);
                });

                it('should return 400 if payload is invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ excludeIds: 'invalid' });

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch artifacts successfully with valid data', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            skip: jest.fn().mockReturnValue({
                                limit: jest.fn().mockReturnValue({
                                    toArray: jest.fn().mockResolvedValue(null),
                                }),
                            }),
                            toArray: jest.fn().mockResolvedValue(null),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(50),
                    });

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .set('Referer', '/docs')
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], page: 1, pageSize: 10, filters: {} });

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockRejectedValue(new Error('Database error')),
                        }),
                    });

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });

                    expect(res.status).toBe(500);
                });

                it('should return paginated results when page and pageSize are provided', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            skip: jest.fn().mockReturnValue({
                                limit: jest.fn().mockReturnValue({
                                    toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10)),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(50),
                    });

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('artifacts');
                    expect(res.body.artifacts).toHaveLength(10);
                    expect(res.body).toHaveProperty('totalCount', 50);
                });

                it('should return filtered results based on provided filters', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            skip: jest.fn().mockReturnValue({
                                limit: jest.fn().mockReturnValue({
                                    toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10)),
                                }),
                            }),
                            toArray: jest.fn().mockResolvedValue(artifactsList.filter(a => a.category === 'metal')),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(50),
                    });

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ filters: { category: ['metal'], startTime: 1622547800, endTime: 1622547900 }, page: 1, pageSize: 10 });

                    expect(res.status).toBe(200);
                    expect(res.body.artifacts).toBeInstanceOf(Array);
                });

                it('should return filter items if getFilterItems is true', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            skip: jest.fn().mockReturnValue({
                                limit: jest.fn().mockReturnValue({
                                    toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10)),
                                }),
                            }),
                            toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10)),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(50),
                    });

                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });

                    expect(res.status).toBe(200);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('POST /api/artifacts/:vesselName', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts/vesselA')
                        .send({});
                    expect(res.status).toBe(401);
                });

                it('should return 400 if payload is invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: 'invalid', startTimestamp: 'invalid', endTimestamp: 'invalid' });

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch artifacts successfully with valid data', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(null),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(50),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .set('Referer', '/docs')
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], startTimestamp: 1622547800, endTimestamp: 1622547900 });

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockRejectedValue(new Error('Database error')),
                        }),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({});

                    expect(res.status).toBe(500);
                });

                it('should return paginated results when page and pageSize are provided', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10)),
                        }),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], startTimestamp: 1622547800, endTimestamp: 1622547900 });

                    expect(res.status).toBe(200);
                    expect(res.body.length).toBe(10);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });

    describe('GET /api/artifacts/filters', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/filters')
                        .send({});
                    expect(res.status).toBe(401);
                });

                it('should return 200 and fetch filters', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.qmai.collection.mockReturnValue({
                        distinct: jest.fn()
                            .mockResolvedValueOnce(['metal', 'plastic'])
                            .mockResolvedValueOnce(['US', 'UK'])
                            .mockResolvedValueOnce(['Vessel A', 'Vessel B'])
                            .mockResolvedValueOnce(['Category 1', 'Category 2']),
                    });

                    const res = await request(app)
                        .get('/api/artifacts/filters')
                        .set('Authorization', authToken)

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.qmai.collection.mockReturnValue({
                        distinct: jest.fn().mockRejectedValue(new Error('Database error'))
                    });

                    const res = await request(app)
                        .get('/api/artifacts/filters')
                        .set('Authorization', authToken)

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });
});
