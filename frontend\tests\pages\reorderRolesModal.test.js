import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ReorderRolesModal from '../../src/pages/Dashboard/User/Roles/ReorderRolesModal';
import { DndContext } from '@dnd-kit/core';
import { SortableContext } from '@dnd-kit/sortable';
import axiosInstance from "../../src/axios";

jest.mock('../../src/axios', () => ({
    patch: jest.fn(),
}));

jest.mock('@dnd-kit/core', () => ({
    ...jest.requireActual('@dnd-kit/core'),
    DndContext: jest.fn(({ children, onDragEnd }) => {
        onDragEnd({ active: { id: 'r1' }, over: { id: 'r1' } });
        onDragEnd({ active: { id: 'r1' }, over: { id: 'r2' } });
        onDragEnd({ active: { id: 'r2' }, over: { id: 'r1' } });

        return <div>{children}</div>
    }),
    closestCenter: jest.fn(),
}));

jest.mock('../../src/pages/Dashboard/User/Roles/SortableItem', () => ({ role }) => <div>{role.role_name}</div>);

jest.mock('@dnd-kit/sortable', () => ({
    SortableContext: jest.fn(({ children }) => <div>{children}</div>),
    useSortable: jest.fn(),
    arrayMove: jest.fn((arr, from, to) => {
        const newArr = [...arr];
        const [movedItem] = newArr.splice(from, 1);
        newArr.splice(to, 0, movedItem);
        return newArr;
    }),
}));

describe('ReorderRolesModal', () => {
    let mockOnClose, mockOnReorder, mockFetchRoles, user;

    beforeEach(() => {
        mockOnClose = jest.fn();
        mockOnReorder = jest.fn();
        mockFetchRoles = jest.fn().mockResolvedValue([
            { _id: 'r1', role_name: 'Admin', hierarchy_number: 1 },
            { _id: 'r2', role_name: 'User', hierarchy_number: 2 },
        ]);

        user = {
            role: {
                hierarchy_number: 2,
            },
        };
    });

    it('should render roles correctly', () => {
        const roles = [
            { _id: 'r1', role_name: 'Admin', hierarchy_number: 1 },
            { _id: 'r2', role_name: 'User', hierarchy_number: 2 },
        ];

        const { rerender } = render(
            <ReorderRolesModal
                open={false}
                onClose={mockOnClose}
                roles={roles}
                onReorder={mockOnReorder}
                fetchRoles={mockFetchRoles}
                user={user}
            />
        );

        rerender(
            <ReorderRolesModal
                open={true}
                onClose={mockOnClose}
                roles={roles}
                onReorder={mockOnReorder}
                fetchRoles={mockFetchRoles}
                user={user}
            />
        );

        expect(screen.getByText('Admin')).toBeInTheDocument();
        expect(screen.getByText('User')).toBeInTheDocument();
    });


    it('should call onReorder after saving the order', async () => {
        const roles = [
            { _id: 'r1', role_name: 'Admin', hierarchy_number: 1 },
            { _id: 'r2', role_name: 'User', hierarchy_number: 2 },
        ];

        axiosInstance.patch.mockResolvedValue({});

        render(
            <ReorderRolesModal
                open={true}
                onClose={mockOnClose}
                roles={roles}
                onReorder={mockOnReorder}
                fetchRoles={mockFetchRoles}
                user={user}
            />
        );

        const saveButton = screen.getByText('Save Order');
        fireEvent.click(saveButton);

        await waitFor(() => {
            expect(axiosInstance.patch).toHaveBeenCalled();
        });
    });

    it('should not call onReorder after fails in saving the order', async () => {
        const roles = [
            { _id: 'r1', role_name: 'Admin', hierarchy_number: 1 },
            { _id: 'r2', role_name: 'User', hierarchy_number: 2 },
        ];

        axiosInstance.patch.mockRejectedValue({});

        render(
            <ReorderRolesModal
                open={true}
                onClose={mockOnClose}
                roles={roles}
                onReorder={mockOnReorder}
                fetchRoles={mockFetchRoles}
                user={user}
            />
        );

        const saveButton = screen.getByText('Save Order');
        fireEvent.click(saveButton);

        await waitFor(() => {
            expect(axiosInstance.patch).toHaveBeenCalled();
            expect(mockOnReorder).not.toHaveBeenCalled();
        });
    });

    it('should call onClose when Cancel button is clicked', () => {
        const roles = [
            { _id: 'r1', role_name: 'Admin', hierarchy_number: 1 },
            { _id: 'r2', role_name: 'User', hierarchy_number: 2 },
        ];

        render(
            <ReorderRolesModal
                open={true}
                onClose={mockOnClose}
                roles={roles}
                onReorder={mockOnReorder}
                fetchRoles={mockFetchRoles}
                user={user}
            />
        );

        const cancelButton = screen.getByText('Cancel');
        fireEvent.click(cancelButton);

        expect(mockOnClose).toHaveBeenCalled();
    });
});
