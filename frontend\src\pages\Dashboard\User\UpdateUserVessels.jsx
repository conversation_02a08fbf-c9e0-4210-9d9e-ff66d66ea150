import { useEffect, useMemo, useState } from "react";
import MultiSelect from "../../../components/MultiSelect";
import { CircularProgress } from "@mui/material";
import axiosInstance from "../../../axios";

export default function UpdateUserVessels({ user, vessels, regionGroups, disabled }) {
    const [checkedVessels, setCheckedVessels] = useState([]);
    const [updating, setUpdating] = useState(false);

    const vesselsByRegionGroup = useMemo(() => {
        return vessels
            .filter((v) => v.region_group_id && v.is_active !== false)
            .map((v) => ({ ...v, region_group_object: regionGroups.find((rg) => rg._id === v.region_group_id) }))
            .sort((a, b) => {
                const groupA = a.region_group_object?.name?.toLowerCase() || "";
                const groupB = b.region_group_object?.name?.toLowerCase() || "";
                // Sort by group name first
                if (groupA < groupB) return -1;
                if (groupA > groupB) return 1;
                // Then sort within the group based on region_group_object.vessel_ids order
                // const vesselIds = a.region_group_object?.vessel_ids || [];
                // const indexA = vesselIds.indexOf(a.vessel_id);
                // const indexB = vesselIds.indexOf(b.vessel_id);
                // // Fallback to alphabetical if not found
                // if (indexA === -1 || indexB === -1) {
                const nameA = a.name?.toLowerCase() || a.vessel_id;
                const nameB = b.name?.toLowerCase() || b.vessel_id;
                return nameA.localeCompare(nameB);
                // }

                // return indexA - indexB;
            });
    }, [vessels, regionGroups]);

    useEffect(() => {
        initializeCheckedVessels();
    }, [user, vessels]);

    const initializeCheckedVessels = () => setCheckedVessels(user.allowed_vessels || []);

    const isChanged = useMemo(() => {
        if (checkedVessels.length !== (user.allowed_vessels || []).length) return true;
        const allowedVesselIds = new Set(user.allowed_vessels || []);
        return checkedVessels.some((vesselId) => !allowedVesselIds.has(vesselId));
    }, [checkedVessels, user]);

    const handleUpdate = () => {
        setUpdating(true);
        const allowed_vessels = checkedVessels.filter((v) => vessels.find((o) => o.vessel_id === v));
        axiosInstance
            .patch(`/users/${user._id}/allowedVessels`, { allowed_vessels }, { meta: { showSnackbar: true } })
            .catch((err) => {
                initializeCheckedVessels();
                console.error(err);
            })
            .finally(() => {
                setUpdating(false);
            });
    };

    return updating ? (
        <CircularProgress />
    ) : (
        <MultiSelect
            loading={vessels.length === 0}
            options={vesselsByRegionGroup}
            value={checkedVessels}
            disabled={disabled || updating}
            multiple
            disableCloseOnSelect
            groupBy={(o) => o.region_group_object?.name}
            label={`${(user.allowed_vessels || []).length} selected`}
            getOptionLabel={(o) => o.name}
            isOptionEqualToValue={(o, v) => v.includes(o.vessel_id)}
            renderTags={() => null}
            onChange={(e, v) => setCheckedVessels(v.map((o) => (typeof o === "string" ? o : o.vessel_id)))}
            onClose={() => {
                if (isChanged) {
                    handleUpdate();
                }
            }}
        />
    );
}
