{"version": 3, "sources": ["../../tslib/tslib.es6.mjs", "../../size-sensor/lib/id.js", "../../size-sensor/lib/debounce.js", "../../size-sensor/lib/constant.js", "../../size-sensor/lib/sensors/object.js", "../../size-sensor/lib/sensors/resizeObserver.js", "../../size-sensor/lib/sensors/index.js", "../../size-sensor/lib/sensorPool.js", "../../size-sensor/lib/index.js", "../../echarts-for-react/src/helper/pick.ts", "../../echarts-for-react/src/helper/is-function.ts", "../../echarts-for-react/src/helper/is-string.ts", "../../echarts-for-react/src/helper/is-equal.ts", "../../echarts-for-react/src/core.tsx"], "sourcesContent": ["/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\nvar id = 1;\n\n/**\n * generate unique id in application\n * @return {string}\n */\nvar _default = function _default() {\n  return \"\".concat(id++);\n};\nexports[\"default\"] = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\nvar _default = function _default(fn) {\n  var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 60;\n  var timer = null;\n  return function () {\n    var _this = this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    clearTimeout(timer);\n    timer = setTimeout(function () {\n      fn.apply(_this, args);\n    }, delay);\n  };\n};\nexports[\"default\"] = _default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.SizeSensorId = exports.SensorTabIndex = exports.SensorClassName = void 0;\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\nvar SizeSensorId = 'size-sensor-id';\nexports.SizeSensorId = SizeSensorId;\nvar SensorClassName = 'size-sensor-object';\nexports.SensorClassName = SensorClassName;\nvar SensorTabIndex = '-1';\nexports.SensorTabIndex = SensorTabIndex;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _debounce = _interopRequireDefault(require(\"../debounce\"));\nvar _constant = require(\"../constant\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\nvar createSensor = function createSensor(element, whenDestroy) {\n  var sensor = undefined;\n  // callback\n  var listeners = [];\n\n  /**\n   * create object DOM of sensor\n   * @returns {HTMLObjectElement}\n   */\n  var newSensor = function newSensor() {\n    // adjust style\n    if (getComputedStyle(element).position === 'static') {\n      element.style.position = 'relative';\n    }\n    var obj = document.createElement('object');\n    obj.onload = function () {\n      obj.contentDocument.defaultView.addEventListener('resize', resizeListener);\n      // 直接触发一次 resize\n      resizeListener();\n    };\n    obj.style.display = 'block';\n    obj.style.position = 'absolute';\n    obj.style.top = '0';\n    obj.style.left = '0';\n    obj.style.height = '100%';\n    obj.style.width = '100%';\n    obj.style.overflow = 'hidden';\n    obj.style.pointerEvents = 'none';\n    obj.style.zIndex = '-1';\n    obj.style.opacity = '0';\n    obj.setAttribute('class', _constant.SensorClassName);\n    obj.setAttribute('tabindex', _constant.SensorTabIndex);\n    obj.type = 'text/html';\n\n    // append into dom\n    element.appendChild(obj);\n    // for ie, should set data attribute delay, or will be white screen\n    obj.data = 'about:blank';\n    return obj;\n  };\n\n  /**\n   * trigger listeners\n   */\n  var resizeListener = (0, _debounce[\"default\"])(function () {\n    // trigger all listener\n    listeners.forEach(function (listener) {\n      listener(element);\n    });\n  });\n\n  /**\n   * listen with one callback function\n   * @param cb\n   */\n  var bind = function bind(cb) {\n    // if not exist sensor, then create one\n    if (!sensor) {\n      sensor = newSensor();\n    }\n    if (listeners.indexOf(cb) === -1) {\n      listeners.push(cb);\n    }\n  };\n\n  /**\n   * destroy all\n   */\n  var destroy = function destroy() {\n    if (sensor && sensor.parentNode) {\n      if (sensor.contentDocument) {\n        // remote event\n        sensor.contentDocument.defaultView.removeEventListener('resize', resizeListener);\n      }\n      // remove dom\n      sensor.parentNode.removeChild(sensor);\n      // initial variable\n      element.removeAttribute(_constant.SizeSensorId);\n      sensor = undefined;\n      listeners = [];\n      whenDestroy && whenDestroy();\n    }\n  };\n\n  /**\n   * cancel listener bind\n   * @param cb\n   */\n  var unbind = function unbind(cb) {\n    var idx = listeners.indexOf(cb);\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    }\n\n    // no listener, and sensor is exist\n    // then destroy the sensor\n    if (listeners.length === 0 && sensor) {\n      destroy();\n    }\n  };\n  return {\n    element: element,\n    bind: bind,\n    destroy: destroy,\n    unbind: unbind\n  };\n};\nexports.createSensor = createSensor;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _constant = require(\"../constant\");\nvar _debounce = _interopRequireDefault(require(\"../debounce\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n/**\n * Created by hustcc on 18/7/5.\n * Contract: <EMAIL>\n */\n\nvar createSensor = function createSensor(element, whenDestroy) {\n  var sensor = undefined;\n  // callback\n  var listeners = [];\n\n  /**\n   * trigger listeners\n   */\n  var resizeListener = (0, _debounce[\"default\"])(function () {\n    // trigger all\n    listeners.forEach(function (listener) {\n      listener(element);\n    });\n  });\n\n  /**\n   * create ResizeObserver sensor\n   * @returns\n   */\n  var newSensor = function newSensor() {\n    var s = new ResizeObserver(resizeListener);\n    // listen element\n    s.observe(element);\n\n    // trigger once\n    resizeListener();\n    return s;\n  };\n\n  /**\n   * listen with callback\n   * @param cb\n   */\n  var bind = function bind(cb) {\n    if (!sensor) {\n      sensor = newSensor();\n    }\n    if (listeners.indexOf(cb) === -1) {\n      listeners.push(cb);\n    }\n  };\n\n  /**\n   * destroy\n   */\n  var destroy = function destroy() {\n    sensor.disconnect();\n    listeners = [];\n    sensor = undefined;\n    element.removeAttribute(_constant.SizeSensorId);\n    whenDestroy && whenDestroy();\n  };\n\n  /**\n   * cancel bind\n   * @param cb\n   */\n  var unbind = function unbind(cb) {\n    var idx = listeners.indexOf(cb);\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    }\n\n    // no listener, and sensor is exist\n    // then destroy the sensor\n    if (listeners.length === 0 && sensor) {\n      destroy();\n    }\n  };\n  return {\n    element: element,\n    bind: bind,\n    destroy: destroy,\n    unbind: unbind\n  };\n};\nexports.createSensor = createSensor;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _object = require(\"./object\");\nvar _resizeObserver = require(\"./resizeObserver\");\n/**\n * Created by hustcc on 18/7/5.\n * Contract: <EMAIL>\n */\n\n/**\n * sensor strategies\n */\n// export const createSensor = createObjectSensor;\nvar createSensor = typeof ResizeObserver !== 'undefined' ? _resizeObserver.createSensor : _object.createSensor;\nexports.createSensor = createSensor;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.removeSensor = exports.getSensor = exports.Sensors = void 0;\nvar _id = _interopRequireDefault(require(\"./id\"));\nvar _sensors = require(\"./sensors\");\nvar _constant = require(\"./constant\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\n\n/**\n * all the sensor objects.\n * sensor pool\n */\nvar Sensors = {};\n\n/**\n * When destroy the sensor, remove it from the pool\n */\nexports.Sensors = Sensors;\nfunction clean(sensorId) {\n  // exist, then remove from pool\n  if (sensorId && Sensors[sensorId]) {\n    delete Sensors[sensorId];\n  }\n}\n\n/**\n * get one sensor\n * @param element\n * @returns {*}\n */\nvar getSensor = function getSensor(element) {\n  var sensorId = element.getAttribute(_constant.SizeSensorId);\n\n  // 1. if the sensor exists, then use it\n  if (sensorId && Sensors[sensorId]) {\n    return Sensors[sensorId];\n  }\n\n  // 2. not exist, then create one\n  var newId = (0, _id[\"default\"])();\n  element.setAttribute(_constant.SizeSensorId, newId);\n  var sensor = (0, _sensors.createSensor)(element, function () {\n    return clean(newId);\n  });\n  // add sensor into pool\n  Sensors[newId] = sensor;\n  return sensor;\n};\n\n/**\n * 移除 sensor\n * @param sensor\n */\nexports.getSensor = getSensor;\nvar removeSensor = function removeSensor(sensor) {\n  var sensorId = sensor.element.getAttribute(_constant.SizeSensorId);\n  // remove event, dom of the sensor used\n  sensor.destroy();\n  clean(sensorId);\n};\nexports.removeSensor = removeSensor;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ver = exports.clear = exports.bind = void 0;\nvar _sensorPool = require(\"./sensorPool\");\n/**\n * Created by hustcc on 18/6/9.[高考时间]\n * Contract: <EMAIL>\n */\n\n/**\n * bind an element with resize callback function\n * @param {*} element\n * @param {*} cb\n */\nvar bind = function bind(element, cb) {\n  var sensor = (0, _sensorPool.getSensor)(element);\n\n  // listen with callback\n  sensor.bind(cb);\n\n  // return unbind function\n  return function () {\n    sensor.unbind(cb);\n  };\n};\n\n/**\n * clear all the listener and sensor of an element\n * @param element\n */\nexports.bind = bind;\nvar clear = function clear(element) {\n  var sensor = (0, _sensorPool.getSensor)(element);\n  (0, _sensorPool.removeSensor)(sensor);\n};\nexports.clear = clear;\nvar ver = \"1.0.2\";\nexports.ver = ver;", "/**\n * 保留 object 中的部分内容\n * @param obj\n * @param keys\n */\nexport function pick(obj: Record<string, unknown>, keys: string[]): Record<string, unknown> {\n  const r = {};\n  keys.forEach((key) => {\n    r[key] = obj[key];\n  });\n  return r;\n}\n", "export function isFunction(v: any): boolean {\n  return typeof v === 'function';\n}\n", "export function isString(v: any): boolean {\n  return typeof v === 'string';\n}\n", "import isEqual from 'fast-deep-equal';\n\nexport { isEqual };\n", "import type { ECharts } from 'echarts';\nimport React, { PureComponent } from 'react';\nimport { bind, clear } from 'size-sensor';\nimport { pick } from './helper/pick';\nimport { isFunction } from './helper/is-function';\nimport { isString } from './helper/is-string';\nimport { isEqual } from './helper/is-equal';\nimport { EChartsReactProps, EChartsInstance } from './types';\n\n/**\n * core component for echarts binding\n */\nexport default class EChartsReactCore extends PureComponent<EChartsReactProps> {\n  /**\n   * echarts render container\n   */\n  public ele: HTMLElement;\n\n  /**\n   * if this is the first time we are resizing\n   */\n  private isInitialResize: boolean;\n\n  /**\n   * echarts library entry\n   */\n  protected echarts: any;\n\n  constructor(props: EChartsReactProps) {\n    super(props);\n\n    this.echarts = props.echarts;\n    this.ele = null;\n    this.isInitialResize = true;\n  }\n\n  componentDidMount() {\n    this.renderNewEcharts();\n  }\n\n  // update\n  componentDidUpdate(prevProps: EChartsReactProps) {\n    /**\n     * if shouldSetOption return false, then return, not update echarts options\n     * default is true\n     */\n    const { shouldSetOption } = this.props;\n    if (isFunction(shouldSetOption) && !shouldSetOption(prevProps, this.props)) {\n      return;\n    }\n\n    // 以下属性修改的时候，需要 dispose 之后再新建\n    // 1. 切换 theme 的时候\n    // 2. 修改 opts 的时候\n    // 3. 修改 onEvents 的时候，这样可以取消所有之前绑定的事件 issue #151\n    if (\n      !isEqual(prevProps.theme, this.props.theme) ||\n      !isEqual(prevProps.opts, this.props.opts) ||\n      !isEqual(prevProps.onEvents, this.props.onEvents)\n    ) {\n      this.dispose();\n\n      this.renderNewEcharts(); // 重建\n      return;\n    }\n\n    // when these props are not isEqual, update echarts\n    const pickKeys = ['option', 'notMerge', 'lazyUpdate', 'showLoading', 'loadingOption'];\n    if (!isEqual(pick(this.props, pickKeys), pick(prevProps, pickKeys))) {\n      this.updateEChartsOption();\n    }\n\n    /**\n     * when style or class name updated, change size.\n     */\n    if (!isEqual(prevProps.style, this.props.style) || !isEqual(prevProps.className, this.props.className)) {\n      this.resize();\n    }\n  }\n\n  componentWillUnmount() {\n    this.dispose();\n  }\n\n  /**\n   * return the echart object\n   * 1. if exist, return the existed instance\n   * 2. or new one instance\n   */\n  public getEchartsInstance(): ECharts {\n    return this.echarts.getInstanceByDom(this.ele) || this.echarts.init(this.ele, this.props.theme, this.props.opts);\n  }\n\n  /**\n   * dispose echarts and clear size-sensor\n   */\n  private dispose() {\n    if (this.ele) {\n      try {\n        clear(this.ele);\n      } catch (e) {\n        console.warn(e);\n      }\n      // dispose echarts instance\n      this.echarts.dispose(this.ele);\n    }\n  }\n\n  /**\n   * render a new echarts instance\n   */\n  private renderNewEcharts() {\n    const { onEvents, onChartReady } = this.props;\n\n    // 1. new echarts instance\n    const echartsInstance = this.updateEChartsOption();\n\n    // 2. bind events\n    this.bindEvents(echartsInstance, onEvents || {});\n\n    // 3. on chart ready\n    if (isFunction(onChartReady)) onChartReady(echartsInstance);\n\n    // 4. on resize\n    if (this.ele) {\n      bind(this.ele, () => {\n        this.resize();\n      });\n    }\n  }\n\n  // bind the events\n  private bindEvents(instance, events: EChartsReactProps['onEvents']) {\n    function _bindEvent(eventName: string, func: Function) {\n      // ignore the event config which not satisfy\n      if (isString(eventName) && isFunction(func)) {\n        // binding event\n        instance.on(eventName, (param) => {\n          func(param, instance);\n        });\n      }\n    }\n\n    // loop and bind\n    for (const eventName in events) {\n      if (Object.prototype.hasOwnProperty.call(events, eventName)) {\n        _bindEvent(eventName, events[eventName]);\n      }\n    }\n  }\n\n  /**\n   * render the echarts\n   */\n  private updateEChartsOption(): EChartsInstance {\n    const { option, notMerge = false, lazyUpdate = false, showLoading, loadingOption = null } = this.props;\n    // 1. get or initial the echarts object\n    const echartInstance = this.getEchartsInstance();\n    // 2. set the echarts option\n    echartInstance.setOption(option, notMerge, lazyUpdate);\n    // 3. set loading mask\n    if (showLoading) echartInstance.showLoading(loadingOption);\n    else echartInstance.hideLoading();\n\n    return echartInstance;\n  }\n\n  /**\n   * resize wrapper\n   */\n  private resize() {\n    // 1. get the echarts object\n    const echartsInstance = this.getEchartsInstance();\n\n    // 2. call echarts instance resize if not the initial resize\n    // resize should not happen on first render as it will cancel initial echarts animations\n    if (!this.isInitialResize) {\n      try {\n        echartsInstance.resize();\n      } catch (e) {\n        console.warn(e);\n      }\n    }\n\n    // 3. update variable for future calls\n    this.isInitialResize = false;\n  }\n\n  render(): JSX.Element {\n    const { style, className = '' } = this.props;\n    // default height = 300\n    const newStyle = { height: 300, ...style };\n\n    return (\n      <div\n        ref={(e: HTMLElement) => {\n          this.ele = e;\n        }}\n        style={newStyle}\n        className={`echarts-for-react ${className}`}\n      />\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBO,SAAS,UAAU,GAAG,GAAG;AAC9B,MAAI,OAAO,MAAM,cAAc,MAAM;AACjC,UAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACpF;AAaO,SAAS,OAAO,GAAG,GAAG;AAC3B,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACT;AAEO,SAAS,WAAW,YAAY,QAAQ,KAAK,MAAM;AACxD,MAAI,IAAI,UAAU,QAAQ,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAM;AAC3H,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa,WAAY,KAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;AAAA,MACxH,UAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,IAAK,KAAI,IAAI,WAAW,CAAC,EAAG,MAAK,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,QAAQ,KAAK,CAAC,IAAI,EAAE,QAAQ,GAAG,MAAM;AAChJ,SAAO,IAAI,KAAK,KAAK,OAAO,eAAe,QAAQ,KAAK,CAAC,GAAG;AAC9D;AAEO,SAAS,QAAQ,YAAY,WAAW;AAC7C,SAAO,SAAU,QAAQ,KAAK;AAAE,cAAU,QAAQ,KAAK,UAAU;AAAA,EAAG;AACtE;AAEO,SAAS,aAAa,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACvG,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAG;AACtH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI,CAAC;AACtG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAC;AACf,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAC,IAAI,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAG;AAC5K,QAAI,UAAU,GAAG,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAI,IAAI,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IACvD,WACS,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IAC3B;AAAA,EACJ;AACA,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACT;AAEO,SAAS,kBAAkB,SAAS,cAAc,OAAO;AAC9D,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC1F;AACA,SAAO,WAAW,QAAQ;AAC5B;AAEO,SAAS,UAAU,GAAG;AAC3B,SAAO,OAAO,MAAM,WAAW,IAAI,GAAG,OAAO,CAAC;AAChD;AAEO,SAAS,kBAAkB,GAAG,MAAM,QAAQ;AACjD,MAAI,OAAO,SAAS,SAAU,QAAO,KAAK,cAAc,IAAI,OAAO,KAAK,aAAa,GAAG,IAAI;AAC5F,SAAO,OAAO,eAAe,GAAG,QAAQ,EAAE,cAAc,MAAM,OAAO,SAAS,GAAG,OAAO,QAAQ,KAAK,IAAI,IAAI,KAAK,CAAC;AACrH;AAEO,SAAS,WAAW,aAAa,eAAe;AACrD,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa,WAAY,QAAO,QAAQ,SAAS,aAAa,aAAa;AAC/H;AAEO,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AAC3D,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACH;AAEO,SAAS,YAAY,SAAS,MAAM;AACzC,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,OAAO,QAAQ,OAAO,aAAa,aAAa,WAAW,QAAQ,SAAS;AAC/L,SAAO,EAAE,OAAO,KAAK,CAAC,GAAG,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE,QAAQ,IAAI,KAAK,CAAC,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AAC1J,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,EAAG,KAAI;AAC1C,UAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,UAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAG;AAAA,QACX,KAAK;AAAA,QAAG,KAAK;AAAG,cAAI;AAAI;AAAA,QACxB,KAAK;AAAG,YAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,QACtD,KAAK;AAAG,YAAE;AAAS,cAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;AAAA,QACxC,KAAK;AAAG,eAAK,EAAE,IAAI,IAAI;AAAG,YAAE,KAAK,IAAI;AAAG;AAAA,QACxC;AACI,cAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,gBAAI;AAAG;AAAA,UAAU;AAC3G,cAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,cAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,UAAO;AACrF,cAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,gBAAI;AAAI;AAAA,UAAO;AACpE,cAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,cAAE,IAAI,KAAK,EAAE;AAAG;AAAA,UAAO;AAClE,cAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,YAAE,KAAK,IAAI;AAAG;AAAA,MACtB;AACA,WAAK,KAAK,KAAK,SAAS,CAAC;AAAA,IAC7B,SAAS,GAAG;AAAE,WAAK,CAAC,GAAG,CAAC;AAAG,UAAI;AAAA,IAAG,UAAE;AAAU,UAAI,IAAI;AAAA,IAAG;AACzD,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACF;AAcO,SAAS,aAAa,GAAG,GAAG;AACjC,WAAS,KAAK,EAAG,KAAI,MAAM,aAAa,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,iBAAgB,GAAG,GAAG,CAAC;AAC9G;AAEO,SAAS,SAAS,GAAG;AAC1B,MAAI,IAAI,OAAO,WAAW,cAAc,OAAO,UAAU,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI;AAC5E,MAAI,EAAG,QAAO,EAAE,KAAK,CAAC;AACtB,MAAI,KAAK,OAAO,EAAE,WAAW,SAAU,QAAO;AAAA,IAC1C,MAAM,WAAY;AACd,UAAI,KAAK,KAAK,EAAE,OAAQ,KAAI;AAC5B,aAAO,EAAE,OAAO,KAAK,EAAE,GAAG,GAAG,MAAM,CAAC,EAAE;AAAA,IAC1C;AAAA,EACJ;AACA,QAAM,IAAI,UAAU,IAAI,4BAA4B,iCAAiC;AACvF;AAEO,SAAS,OAAO,GAAG,GAAG;AAC3B,MAAI,IAAI,OAAO,WAAW,cAAc,EAAE,OAAO,QAAQ;AACzD,MAAI,CAAC,EAAG,QAAO;AACf,MAAI,IAAI,EAAE,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;AAC/B,MAAI;AACA,YAAQ,MAAM,UAAU,MAAM,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,KAAM,IAAG,KAAK,EAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,QAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAI,KAAK,CAAC,EAAE,SAAS,IAAI,EAAE,QAAQ,GAAI,GAAE,KAAK,CAAC;AAAA,IACnD,UACA;AAAU,UAAI,EAAG,OAAM,EAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAO;AACT;AAGO,SAAS,WAAW;AACzB,WAAS,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ;AAC3C,SAAK,GAAG,OAAO,OAAO,UAAU,CAAC,CAAC,CAAC;AACvC,SAAO;AACT;AAGO,SAAS,iBAAiB;AAC/B,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,IAAK,MAAK,UAAU,CAAC,EAAE;AAC7E,WAAS,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI;AACzC,aAAS,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,EAAE,QAAQ,IAAI,IAAI,KAAK;AAC1D,QAAE,CAAC,IAAI,EAAE,CAAC;AAClB,SAAO;AACT;AAEO,SAAS,cAAc,IAAI,MAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,QAAI,MAAM,EAAE,KAAK,OAAO;AACpB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;AAEO,SAAS,QAAQ,GAAG;AACzB,SAAO,gBAAgB,WAAW,KAAK,IAAI,GAAG,QAAQ,IAAI,QAAQ,CAAC;AACrE;AAEO,SAAS,iBAAiB,SAAS,YAAY,WAAW;AAC/D,MAAI,CAAC,OAAO,cAAe,OAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAI,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;AAC5D,SAAO,IAAI,OAAO,QAAQ,OAAO,kBAAkB,aAAa,gBAAgB,QAAQ,SAAS,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,UAAU,WAAW,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AACtN,WAAS,YAAY,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,QAAQ,QAAQ,CAAC,EAAE,KAAK,GAAG,MAAM;AAAA,IAAG;AAAA,EAAG;AAC9F,WAAS,KAAK,GAAG,GAAG;AAAE,QAAI,EAAE,CAAC,GAAG;AAAE,QAAE,CAAC,IAAI,SAAU,GAAG;AAAE,eAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;AAAE,YAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,OAAO,GAAG,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAAG,UAAI,EAAG,GAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IAAG;AAAA,EAAE;AACvK,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI;AAAE,WAAK,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,IAAG,SAAS,GAAG;AAAE,aAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,IAAG;AAAA,EAAE;AACjF,WAAS,KAAK,GAAG;AAAE,MAAE,iBAAiB,UAAU,QAAQ,QAAQ,EAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,EAAG;AACvH,WAAS,QAAQ,OAAO;AAAE,WAAO,QAAQ,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,OAAO;AAAE,WAAO,SAAS,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,GAAG,GAAG;AAAE,QAAI,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG,EAAE,OAAQ,QAAO,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AACnF;AAEO,SAAS,iBAAiB,GAAG;AAClC,MAAI,GAAG;AACP,SAAO,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,SAAS,SAAU,GAAG;AAAE,UAAM;AAAA,EAAG,CAAC,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,QAAQ,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AAC1I,WAAS,KAAK,GAAG,GAAG;AAAE,MAAE,CAAC,IAAI,EAAE,CAAC,IAAI,SAAU,GAAG;AAAE,cAAQ,IAAI,CAAC,KAAK,EAAE,OAAO,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,MAAM,IAAI,IAAI,EAAE,CAAC,IAAI;AAAA,IAAG,IAAI;AAAA,EAAG;AACvI;AAEO,SAAS,cAAc,GAAG;AAC/B,MAAI,CAAC,OAAO,cAAe,OAAM,IAAI,UAAU,sCAAsC;AACrF,MAAI,IAAI,EAAE,OAAO,aAAa,GAAG;AACjC,SAAO,IAAI,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO,aAAa,aAAa,SAAS,CAAC,IAAI,EAAE,OAAO,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,EAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAG;AAC9M,WAAS,KAAK,GAAG;AAAE,MAAE,CAAC,IAAI,EAAE,CAAC,KAAK,SAAU,GAAG;AAAE,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAAE,YAAI,EAAE,CAAC,EAAE,CAAC,GAAG,OAAO,SAAS,QAAQ,EAAE,MAAM,EAAE,KAAK;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAG;AAC/J,WAAS,OAAO,SAAS,QAAQ,GAAG,GAAG;AAAE,YAAQ,QAAQ,CAAC,EAAE,KAAK,SAASA,IAAG;AAAE,cAAQ,EAAE,OAAOA,IAAG,MAAM,EAAE,CAAC;AAAA,IAAG,GAAG,MAAM;AAAA,EAAG;AAC7H;AAEO,SAAS,qBAAqB,QAAQ,KAAK;AAChD,MAAI,OAAO,gBAAgB;AAAE,WAAO,eAAe,QAAQ,OAAO,EAAE,OAAO,IAAI,CAAC;AAAA,EAAG,OAAO;AAAE,WAAO,MAAM;AAAA,EAAK;AAC9G,SAAO;AACT;AAiBO,SAAS,aAAa,KAAK;AAChC,MAAI,OAAO,IAAI,WAAY,QAAO;AAClC,MAAI,SAAS,CAAC;AACd,MAAI,OAAO;AAAM,aAAS,IAAI,QAAQ,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAK,KAAI,EAAE,CAAC,MAAM,UAAW,iBAAgB,QAAQ,KAAK,EAAE,CAAC,CAAC;AAAA;AAC/H,qBAAmB,QAAQ,GAAG;AAC9B,SAAO;AACT;AAEO,SAAS,gBAAgB,KAAK;AACnC,SAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,SAAS,IAAI;AACxD;AAEO,SAAS,uBAAuB,UAAU,OAAO,MAAM,GAAG;AAC/D,MAAI,SAAS,OAAO,CAAC,EAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,0EAA0E;AACjL,SAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,KAAK,QAAQ,IAAI,IAAI,EAAE,QAAQ,MAAM,IAAI,QAAQ;AAC9F;AAEO,SAAS,uBAAuB,UAAU,OAAO,OAAO,MAAM,GAAG;AACtE,MAAI,SAAS,IAAK,OAAM,IAAI,UAAU,gCAAgC;AACtE,MAAI,SAAS,OAAO,CAAC,EAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,yEAAyE;AAChL,SAAQ,SAAS,MAAM,EAAE,KAAK,UAAU,KAAK,IAAI,IAAI,EAAE,QAAQ,QAAQ,MAAM,IAAI,UAAU,KAAK,GAAI;AACtG;AAEO,SAAS,sBAAsB,OAAO,UAAU;AACrD,MAAI,aAAa,QAAS,OAAO,aAAa,YAAY,OAAO,aAAa,WAAa,OAAM,IAAI,UAAU,wCAAwC;AACvJ,SAAO,OAAO,UAAU,aAAa,aAAa,QAAQ,MAAM,IAAI,QAAQ;AAC9E;AAEO,SAAS,wBAAwB,KAAK,OAAO,OAAO;AACzD,MAAI,UAAU,QAAQ,UAAU,QAAQ;AACtC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAY,OAAM,IAAI,UAAU,kBAAkB;AACpG,QAAI,SAAS;AACb,QAAI,OAAO;AACT,UAAI,CAAC,OAAO,aAAc,OAAM,IAAI,UAAU,qCAAqC;AACnF,gBAAU,MAAM,OAAO,YAAY;AAAA,IACrC;AACA,QAAI,YAAY,QAAQ;AACtB,UAAI,CAAC,OAAO,QAAS,OAAM,IAAI,UAAU,gCAAgC;AACzE,gBAAU,MAAM,OAAO,OAAO;AAC9B,UAAI,MAAO,SAAQ;AAAA,IACrB;AACA,QAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/E,QAAI,MAAO,WAAU,WAAW;AAAE,UAAI;AAAE,cAAM,KAAK,IAAI;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,QAAQ,OAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AACpG,QAAI,MAAM,KAAK,EAAE,OAAc,SAAkB,MAAa,CAAC;AAAA,EACjE,WACS,OAAO;AACd,QAAI,MAAM,KAAK,EAAE,OAAO,KAAK,CAAC;AAAA,EAChC;AACA,SAAO;AACT;AAOO,SAAS,mBAAmB,KAAK;AACtC,WAAS,KAAK,GAAG;AACf,QAAI,QAAQ,IAAI,WAAW,IAAI,iBAAiB,GAAG,IAAI,OAAO,0CAA0C,IAAI;AAC5G,QAAI,WAAW;AAAA,EACjB;AACA,MAAI,GAAG,IAAI;AACX,WAAS,OAAO;AACd,WAAO,IAAI,IAAI,MAAM,IAAI,GAAG;AAC1B,UAAI;AACF,YAAI,CAAC,EAAE,SAAS,MAAM,EAAG,QAAO,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,GAAG,QAAQ,QAAQ,EAAE,KAAK,IAAI;AACrF,YAAI,EAAE,SAAS;AACb,cAAI,SAAS,EAAE,QAAQ,KAAK,EAAE,KAAK;AACnC,cAAI,EAAE,MAAO,QAAO,KAAK,GAAG,QAAQ,QAAQ,MAAM,EAAE,KAAK,MAAM,SAAS,GAAG;AAAE,iBAAK,CAAC;AAAG,mBAAO,KAAK;AAAA,UAAG,CAAC;AAAA,QACxG,MACK,MAAK;AAAA,MACZ,SACO,GAAG;AACR,aAAK,CAAC;AAAA,MACR;AAAA,IACF;AACA,QAAI,MAAM,EAAG,QAAO,IAAI,WAAW,QAAQ,OAAO,IAAI,KAAK,IAAI,QAAQ,QAAQ;AAC/E,QAAI,IAAI,SAAU,OAAM,IAAI;AAAA,EAC9B;AACA,SAAO,KAAK;AACd;AAEO,SAAS,iCAAiC,MAAM,aAAa;AAClE,MAAI,OAAO,SAAS,YAAY,WAAW,KAAK,IAAI,GAAG;AACnD,WAAO,KAAK,QAAQ,oDAAoD,SAAU,GAAG,KAAK,GAAG,KAAK,IAAI;AAClG,aAAO,MAAM,cAAc,SAAS,QAAQ,MAAM,CAAC,OAAO,CAAC,MAAM,IAAK,IAAI,MAAM,MAAM,GAAG,YAAY,IAAI;AAAA,IAC7G,CAAC;AAAA,EACL;AACA,SAAO;AACT;AA7WA,IAgBI,eAeO,UAyHA,iBA2GP,oBAMA,SA8DA,kBAwCG;AA/WP;AAAA;AAgBA,IAAI,gBAAgB,SAAS,GAAG,GAAG;AACjC,sBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,QAAAD,GAAE,YAAYC;AAAA,MAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,iBAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,MAAG;AACpG,aAAO,cAAc,GAAG,CAAC;AAAA,IAC3B;AAUO,IAAI,WAAW,WAAW;AAC/B,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACvC;AAgHO,IAAI,kBAAkB,OAAO,SAAU,SAAS,GAAG,GAAG,GAAG,IAAI;AAClE,UAAI,OAAO,OAAW,MAAK;AAC3B,UAAI,OAAO,OAAO,yBAAyB,GAAG,CAAC;AAC/C,UAAI,CAAC,SAAS,SAAS,OAAO,CAAC,EAAE,aAAa,KAAK,YAAY,KAAK,eAAe;AAC/E,eAAO,EAAE,YAAY,MAAM,KAAK,WAAW;AAAE,iBAAO,EAAE,CAAC;AAAA,QAAG,EAAE;AAAA,MAChE;AACA,aAAO,eAAe,GAAG,IAAI,IAAI;AAAA,IACnC,IAAM,SAAS,GAAG,GAAG,GAAG,IAAI;AAC1B,UAAI,OAAO,OAAW,MAAK;AAC3B,QAAE,EAAE,IAAI,EAAE,CAAC;AAAA,IACb;AAiGA,IAAI,qBAAqB,OAAO,SAAU,SAAS,GAAG,GAAG;AACvD,aAAO,eAAe,GAAG,WAAW,EAAE,YAAY,MAAM,OAAO,EAAE,CAAC;AAAA,IACpE,IAAK,SAAS,GAAG,GAAG;AAClB,QAAE,SAAS,IAAI;AAAA,IACjB;AAEA,IAAI,UAAU,SAAS,GAAG;AACxB,gBAAU,OAAO,uBAAuB,SAAUC,IAAG;AACnD,YAAI,KAAK,CAAC;AACV,iBAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,IAAG,GAAG,MAAM,IAAI;AACjF,eAAO;AAAA,MACT;AACA,aAAO,QAAQ,CAAC;AAAA,IAClB;AAuDA,IAAI,mBAAmB,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,OAAO,YAAY,SAAS;AACrH,UAAI,IAAI,IAAI,MAAM,OAAO;AACzB,aAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AAAA,IACjF;AAqCA,IAAO,oBAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AChZA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AAMrB,QAAI,KAAK;AAMT,QAAI,WAAW,SAASC,YAAW;AACjC,aAAO,GAAG,OAAO,IAAI;AAAA,IACvB;AACA,YAAQ,SAAS,IAAI;AAAA;AAAA;;;ACpBrB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AAKrB,QAAI,WAAW,SAASC,UAAS,IAAI;AACnC,UAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,UAAI,QAAQ;AACZ,aAAO,WAAY;AACjB,YAAI,QAAQ;AACZ,iBAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,eAAK,IAAI,IAAI,UAAU,IAAI;AAAA,QAC7B;AACA,qBAAa,KAAK;AAClB,gBAAQ,WAAW,WAAY;AAC7B,aAAG,MAAM,OAAO,IAAI;AAAA,QACtB,GAAG,KAAK;AAAA,MACV;AAAA,IACF;AACA,YAAQ,SAAS,IAAI;AAAA;AAAA;;;ACxBrB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,eAAe,QAAQ,iBAAiB,QAAQ,kBAAkB;AAM1E,QAAI,eAAe;AACnB,YAAQ,eAAe;AACvB,QAAI,kBAAkB;AACtB,YAAQ,kBAAkB;AAC1B,QAAI,iBAAiB;AACrB,YAAQ,iBAAiB;AAAA;AAAA;;;AChBzB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,eAAe;AACvB,QAAI,YAAY,uBAAuB,kBAAsB;AAC7D,QAAI,YAAY;AAChB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAMhG,QAAI,eAAe,SAASC,cAAa,SAAS,aAAa;AAC7D,UAAI,SAAS;AAEb,UAAI,YAAY,CAAC;AAMjB,UAAI,YAAY,SAASC,aAAY;AAEnC,YAAI,iBAAiB,OAAO,EAAE,aAAa,UAAU;AACnD,kBAAQ,MAAM,WAAW;AAAA,QAC3B;AACA,YAAI,MAAM,SAAS,cAAc,QAAQ;AACzC,YAAI,SAAS,WAAY;AACvB,cAAI,gBAAgB,YAAY,iBAAiB,UAAU,cAAc;AAEzE,yBAAe;AAAA,QACjB;AACA,YAAI,MAAM,UAAU;AACpB,YAAI,MAAM,WAAW;AACrB,YAAI,MAAM,MAAM;AAChB,YAAI,MAAM,OAAO;AACjB,YAAI,MAAM,SAAS;AACnB,YAAI,MAAM,QAAQ;AAClB,YAAI,MAAM,WAAW;AACrB,YAAI,MAAM,gBAAgB;AAC1B,YAAI,MAAM,SAAS;AACnB,YAAI,MAAM,UAAU;AACpB,YAAI,aAAa,SAAS,UAAU,eAAe;AACnD,YAAI,aAAa,YAAY,UAAU,cAAc;AACrD,YAAI,OAAO;AAGX,gBAAQ,YAAY,GAAG;AAEvB,YAAI,OAAO;AACX,eAAO;AAAA,MACT;AAKA,UAAI,kBAAkB,GAAG,UAAU,SAAS,GAAG,WAAY;AAEzD,kBAAU,QAAQ,SAAU,UAAU;AACpC,mBAAS,OAAO;AAAA,QAClB,CAAC;AAAA,MACH,CAAC;AAMD,UAAI,OAAO,SAASC,MAAK,IAAI;AAE3B,YAAI,CAAC,QAAQ;AACX,mBAAS,UAAU;AAAA,QACrB;AACA,YAAI,UAAU,QAAQ,EAAE,MAAM,IAAI;AAChC,oBAAU,KAAK,EAAE;AAAA,QACnB;AAAA,MACF;AAKA,UAAI,UAAU,SAASC,WAAU;AAC/B,YAAI,UAAU,OAAO,YAAY;AAC/B,cAAI,OAAO,iBAAiB;AAE1B,mBAAO,gBAAgB,YAAY,oBAAoB,UAAU,cAAc;AAAA,UACjF;AAEA,iBAAO,WAAW,YAAY,MAAM;AAEpC,kBAAQ,gBAAgB,UAAU,YAAY;AAC9C,mBAAS;AACT,sBAAY,CAAC;AACb,yBAAe,YAAY;AAAA,QAC7B;AAAA,MACF;AAMA,UAAI,SAAS,SAASC,QAAO,IAAI;AAC/B,YAAI,MAAM,UAAU,QAAQ,EAAE;AAC9B,YAAI,QAAQ,IAAI;AACd,oBAAU,OAAO,KAAK,CAAC;AAAA,QACzB;AAIA,YAAI,UAAU,WAAW,KAAK,QAAQ;AACpC,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,YAAQ,eAAe;AAAA;AAAA;;;ACzHvB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,eAAe;AACvB,QAAI,YAAY;AAChB,QAAI,YAAY,uBAAuB,kBAAsB;AAC7D,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAMhG,QAAI,eAAe,SAASC,cAAa,SAAS,aAAa;AAC7D,UAAI,SAAS;AAEb,UAAI,YAAY,CAAC;AAKjB,UAAI,kBAAkB,GAAG,UAAU,SAAS,GAAG,WAAY;AAEzD,kBAAU,QAAQ,SAAU,UAAU;AACpC,mBAAS,OAAO;AAAA,QAClB,CAAC;AAAA,MACH,CAAC;AAMD,UAAI,YAAY,SAASC,aAAY;AACnC,YAAI,IAAI,IAAI,eAAe,cAAc;AAEzC,UAAE,QAAQ,OAAO;AAGjB,uBAAe;AACf,eAAO;AAAA,MACT;AAMA,UAAI,OAAO,SAASC,MAAK,IAAI;AAC3B,YAAI,CAAC,QAAQ;AACX,mBAAS,UAAU;AAAA,QACrB;AACA,YAAI,UAAU,QAAQ,EAAE,MAAM,IAAI;AAChC,oBAAU,KAAK,EAAE;AAAA,QACnB;AAAA,MACF;AAKA,UAAI,UAAU,SAASC,WAAU;AAC/B,eAAO,WAAW;AAClB,oBAAY,CAAC;AACb,iBAAS;AACT,gBAAQ,gBAAgB,UAAU,YAAY;AAC9C,uBAAe,YAAY;AAAA,MAC7B;AAMA,UAAI,SAAS,SAASC,QAAO,IAAI;AAC/B,YAAI,MAAM,UAAU,QAAQ,EAAE;AAC9B,YAAI,QAAQ,IAAI;AACd,oBAAU,OAAO,KAAK,CAAC;AAAA,QACzB;AAIA,YAAI,UAAU,WAAW,KAAK,QAAQ;AACpC,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,YAAQ,eAAe;AAAA;AAAA;;;AC1FvB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,eAAe;AACvB,QAAI,UAAU;AACd,QAAI,kBAAkB;AAUtB,QAAI,eAAe,OAAO,mBAAmB,cAAc,gBAAgB,eAAe,QAAQ;AAClG,YAAQ,eAAe;AAAA;AAAA;;;AClBvB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,eAAe,QAAQ,YAAY,QAAQ,UAAU;AAC7D,QAAI,MAAM,uBAAuB,YAAe;AAChD,QAAI,WAAW;AACf,QAAI,YAAY;AAChB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAUhG,QAAI,UAAU,CAAC;AAKf,YAAQ,UAAU;AAClB,aAAS,MAAM,UAAU;AAEvB,UAAI,YAAY,QAAQ,QAAQ,GAAG;AACjC,eAAO,QAAQ,QAAQ;AAAA,MACzB;AAAA,IACF;AAOA,QAAI,YAAY,SAASC,WAAU,SAAS;AAC1C,UAAI,WAAW,QAAQ,aAAa,UAAU,YAAY;AAG1D,UAAI,YAAY,QAAQ,QAAQ,GAAG;AACjC,eAAO,QAAQ,QAAQ;AAAA,MACzB;AAGA,UAAI,SAAS,GAAG,IAAI,SAAS,GAAG;AAChC,cAAQ,aAAa,UAAU,cAAc,KAAK;AAClD,UAAI,UAAU,GAAG,SAAS,cAAc,SAAS,WAAY;AAC3D,eAAO,MAAM,KAAK;AAAA,MACpB,CAAC;AAED,cAAQ,KAAK,IAAI;AACjB,aAAO;AAAA,IACT;AAMA,YAAQ,YAAY;AACpB,QAAI,eAAe,SAASC,cAAa,QAAQ;AAC/C,UAAI,WAAW,OAAO,QAAQ,aAAa,UAAU,YAAY;AAEjE,aAAO,QAAQ;AACf,YAAM,QAAQ;AAAA,IAChB;AACA,YAAQ,eAAe;AAAA;AAAA;;;ACnEvB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,MAAM,QAAQ,QAAQ,QAAQ,OAAO;AAC7C,QAAI,cAAc;AAWlB,QAAI,OAAO,SAASC,MAAK,SAAS,IAAI;AACpC,UAAI,UAAU,GAAG,YAAY,WAAW,OAAO;AAG/C,aAAO,KAAK,EAAE;AAGd,aAAO,WAAY;AACjB,eAAO,OAAO,EAAE;AAAA,MAClB;AAAA,IACF;AAMA,YAAQ,OAAO;AACf,QAAI,QAAQ,SAASC,OAAM,SAAS;AAClC,UAAI,UAAU,GAAG,YAAY,WAAW,OAAO;AAC/C,OAAC,GAAG,YAAY,cAAc,MAAM;AAAA,IACtC;AACA,YAAQ,QAAQ;AAChB,QAAI,MAAM;AACV,YAAQ,MAAM;AAAA;AAAA;;;;;;;;ACnCd,aAAgB,KAAK,KAA8B,MAAc;AAC/D,UAAM,IAAI,CAAA;AACV,WAAK,QAAQ,SAAC,KAAG;AACf,UAAE,GAAG,IAAI,IAAI,GAAG;MAClB,CAAC;AACD,aAAO;IACT;AANA,YAAA,OAAA;;;;;;;;;;ACLA,aAAgB,WAAW,GAAM;AAC/B,aAAO,OAAO,MAAM;IACtB;AAFA,YAAA,aAAA;;;;;;;;;;ACAA,aAAgB,SAAS,GAAM;AAC7B,aAAO,OAAO,MAAM;IACtB;AAFA,YAAA,WAAA;;;;;;;;;;;ACAA,QAAA,qBAAA,GAAA,QAAA,iBAAA,yBAAA;AAES,YAAA,UAFF,kBAAA;;;;;;;;;ACCP,QAAA,WAAA,GAAA,QAAA,cAAA,eAAA;AACA,QAAA,gBAAA;AACA,QAAA,SAAA;AACA,QAAA,gBAAA;AACA,QAAA,cAAA;AACA,QAAA,aAAA;AAMA,QAAA;;MAAA,SAAA,QAAA;AAA8C,SAAA,GAAA,QAAA,WAAAC,mBAAA,MAAA;AAgB5C,iBAAAA,kBAAY,OAAwB;AAApC,cAAA,QACE,OAAA,KAAA,MAAM,KAAK,KAAC;AAEZ,gBAAK,UAAU,MAAM;AACrB,gBAAK,MAAM;AACX,gBAAK,kBAAkB;;QACzB;AAEA,QAAAA,kBAAA,UAAA,oBAAA,WAAA;AACE,eAAK,iBAAgB;QACvB;AAGA,QAAAA,kBAAA,UAAA,qBAAA,SAAmB,WAA4B;AAKrC,cAAA,kBAAoB,KAAK,MAAK;AACtC,eAAI,GAAA,cAAA,YAAW,eAAe,KAAK,CAAC,gBAAgB,WAAW,KAAK,KAAK,GAAG;AAC1E;;AAOF,cACE,EAAC,GAAA,WAAA,SAAQ,UAAU,OAAO,KAAK,MAAM,KAAK,KAC1C,EAAC,GAAA,WAAA,SAAQ,UAAU,MAAM,KAAK,MAAM,IAAI,KACxC,EAAC,GAAA,WAAA,SAAQ,UAAU,UAAU,KAAK,MAAM,QAAQ,GAChD;AACA,iBAAK,QAAO;AAEZ,iBAAK,iBAAgB;AACrB;;AAIF,cAAM,WAAW,CAAC,UAAU,YAAY,cAAc,eAAe,eAAe;AACpF,cAAI,EAAC,GAAA,WAAA,UAAQ,GAAA,OAAA,MAAK,KAAK,OAAO,QAAQ,IAAG,GAAA,OAAA,MAAK,WAAW,QAAQ,CAAC,GAAG;AACnE,iBAAK,oBAAmB;;AAM1B,cAAI,EAAC,GAAA,WAAA,SAAQ,UAAU,OAAO,KAAK,MAAM,KAAK,KAAK,EAAC,GAAA,WAAA,SAAQ,UAAU,WAAW,KAAK,MAAM,SAAS,GAAG;AACtG,iBAAK,OAAM;;QAEf;AAEA,QAAAA,kBAAA,UAAA,uBAAA,WAAA;AACE,eAAK,QAAO;QACd;AAOO,QAAAA,kBAAA,UAAA,qBAAP,WAAA;AACE,iBAAO,KAAK,QAAQ,iBAAiB,KAAK,GAAG,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,KAAK,MAAM,OAAO,KAAK,MAAM,IAAI;QACjH;AAKQ,QAAAA,kBAAA,UAAA,UAAR,WAAA;AACE,cAAI,KAAK,KAAK;AACZ,gBAAI;AACF,eAAA,GAAA,cAAA,OAAM,KAAK,GAAG;qBACP,GAAG;AACV,sBAAQ,KAAK,CAAC;;AAGhB,iBAAK,QAAQ,QAAQ,KAAK,GAAG;;QAEjC;AAKQ,QAAAA,kBAAA,UAAA,mBAAR,WAAA;AAAA,cAAA,QAAA;AACQ,cAAA,KAA6B,KAAK,OAAhC,WAAQ,GAAA,UAAE,eAAY,GAAA;AAG9B,cAAM,kBAAkB,KAAK,oBAAmB;AAGhD,eAAK,WAAW,iBAAiB,YAAY,CAAA,CAAE;AAG/C,eAAI,GAAA,cAAA,YAAW,YAAY;AAAG,yBAAa,eAAe;AAG1D,cAAI,KAAK,KAAK;AACZ,aAAA,GAAA,cAAA,MAAK,KAAK,KAAK,WAAA;AACb,oBAAK,OAAM;YACb,CAAC;;QAEL;AAGQ,QAAAA,kBAAA,UAAA,aAAR,SAAmB,UAAU,QAAqC;AAChE,mBAAS,WAAWC,YAAmB,MAAc;AAEnD,iBAAI,GAAA,YAAA,UAASA,UAAS,MAAK,GAAA,cAAA,YAAW,IAAI,GAAG;AAE3C,uBAAS,GAAGA,YAAW,SAAC,OAAK;AAC3B,qBAAK,OAAO,QAAQ;cACtB,CAAC;;UAEL;AAGA,mBAAW,aAAa,QAAQ;AAC9B,gBAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,SAAS,GAAG;AAC3D,yBAAW,WAAW,OAAO,SAAS,CAAC;;;QAG7C;AAKQ,QAAAD,kBAAA,UAAA,sBAAR,WAAA;AACQ,cAAA,KAAsF,KAAK,OAAzF,SAAM,GAAA,QAAE,KAAA,GAAA,UAAA,WAAQ,OAAA,SAAG,QAAK,IAAE,KAAA,GAAA,YAAA,aAAU,OAAA,SAAG,QAAK,IAAE,cAAW,GAAA,aAAE,KAAA,GAAA,eAAA,gBAAa,OAAA,SAAG,OAAI;AAEvF,cAAM,iBAAiB,KAAK,mBAAkB;AAE9C,yBAAe,UAAU,QAAQ,UAAU,UAAU;AAErD,cAAI;AAAa,2BAAe,YAAY,aAAa;;AACpD,2BAAe,YAAW;AAE/B,iBAAO;QACT;AAKQ,QAAAA,kBAAA,UAAA,SAAR,WAAA;AAEE,cAAM,kBAAkB,KAAK,mBAAkB;AAI/C,cAAI,CAAC,KAAK,iBAAiB;AACzB,gBAAI;AACF,8BAAgB,OAAM;qBACf,GAAG;AACV,sBAAQ,KAAK,CAAC;;;AAKlB,eAAK,kBAAkB;QACzB;AAEA,QAAAA,kBAAA,UAAA,SAAA,WAAA;AAAA,cAAA,QAAA;AACQ,cAAA,KAA4B,KAAK,OAA/B,QAAK,GAAA,OAAE,KAAA,GAAA,WAAA,YAAS,OAAA,SAAG,KAAE;AAE7B,cAAM,YAAQ,GAAA,QAAA,UAAA,EAAK,QAAQ,IAAG,GAAK,KAAK;AAExC,iBACE,QAAA,QAAA,cAAA,OAAA,EACE,KAAK,SAAC,GAAc;AAClB,kBAAK,MAAM;UACb,GACA,OAAO,UACP,WAAW,uBAAqB,UAAW,CAAA;QAGjD;AACF,eAAAA;MAAA,EA/L8C,QAAA,aAAa;;;;;", "names": ["v", "d", "b", "__assign", "o", "_default", "_default", "createSensor", "newSensor", "bind", "destroy", "unbind", "createSensor", "newSensor", "bind", "destroy", "unbind", "getSensor", "removeSensor", "bind", "clear", "EChartsReactCore", "eventName"]}