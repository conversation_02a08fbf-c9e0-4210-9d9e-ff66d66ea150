import { <PERSON>ton, <PERSON>rid, <PERSON>dal, <PERSON><PERSON><PERSON>, Chip, FormControl, FormControlLabel, Radio, RadioGroup, alpha } from "@mui/material";
import { useState } from "react";
import ModalContainer from "../../../components/ModalContainer";
import theme from "../../../theme";
import { useUser } from "../../../hooks/UserHook";
import dayjs from "dayjs";

const FilterUserModal = ({ showFilterModal, setShowFilterModal, roles, organizations, setFilters }) => {
    const { user } = useUser();
    const [selectedRoles, setSelectedRoles] = useState([]);
    const [selectedOrganizations, setSelectedOrganizations] = useState([]);
    const [emailFilter, setEmailFilter] = useState("both");
    const [timeFilter, setTimeFilter] = useState("all_time");
    const [changed, setChanged] = useState({
        isChanged: false,
        changedRoles: [],
        changedOrganizations: [],
        changedTime: false,
        changedEmail: false,
        oldEmail: "",
        oldTime: "",
    });

    const handleChange = () => {
        let created_after;

        switch (timeFilter) {
            case "today": {
                created_after = dayjs().startOf("day").valueOf();
                break;
            }
            case "last_3_days": {
                created_after = dayjs().subtract(3, "days").valueOf();
                break;
            }
            case "last_week": {
                created_after = dayjs().subtract(7, "days").valueOf();
                break;
            }
            case "last_month": {
                created_after = dayjs().subtract(30, "days").valueOf();
                break;
            }
            case "last_year": {
                created_after = dayjs().subtract(365, "days").valueOf();
                break;
            }
            default:
                created_after = null;
        }

        setFilters((old) => ({
            ...old,
            roles: selectedRoles?.length ? selectedRoles : null,
            organizations: selectedOrganizations?.length ? selectedOrganizations : null,
            hasEmail: emailFilter === "both" || !emailFilter ? null : emailFilter === "email",
            created_after,
        }));
    };

    const handleClose = () => {
        setShowFilterModal(false);
    };

    const handleClear = () => {
        setSelectedRoles([]);
        setSelectedOrganizations([]);
        setEmailFilter("both");
        setTimeFilter("all_time");
        setChanged({
            isChanged: false,
            changedRoles: [],
            changedOrganizations: [],
            changedTime: false,
            changedEmail: false,
            oldEmail: "",
            oldTime: "",
        });

        setFilters((old) => ({
            ...old,
            roles: null,
            organizations: null,
            hasEmail: null,
            created_after: null,
        }));
    };

    const toggleRoleSelection = (roleId) => {
        setSelectedRoles((prev) => (prev.includes(roleId) ? prev.filter((role) => role !== roleId) : [...prev, roleId]));
        setChanged({ isChanged: true, changedRoles: [...changed.changedRoles, roleId] });
    };

    const toggleOrganizationSelection = (orgId) => {
        setSelectedOrganizations((prev) => (prev.includes(orgId) ? prev.filter((org) => org !== orgId) : [...prev, orgId]));
        setChanged({ isChanged: true, changedOrganizations: [...changed.changedOrganizations, orgId] });
    };

    const handleSubmit = () => {
        handleChange();

        setChanged({
            isChanged: false,
            changedRoles: [],
            changedOrganizations: [],
            changedTime: false,
            changedEmail: false,
            oldEmail: "",
            oldTime: "",
        });
        setShowFilterModal(false);
    };

    // useEffect(() => {
    //     handleSubmit();
    // }, [roles]);

    return (
        <Modal open={Boolean(showFilterModal)} onClose={handleClose}>
            <ModalContainer title={"Filter"} onClose={handleClose} showDivider={true}>
                <Grid
                    container
                    flexDirection={"column"}
                    gap={2}
                    width={{ xs: 300, sm: 500 }}
                    maxHeight={"70vh"}
                    overflow={"auto"}
                    flexWrap={"nowrap"}
                >
                    <Grid item>
                        <Typography variant="h6" fontSize={"16px !important"} marginBottom={2} fontWeight={500}>
                            Roles
                        </Typography>
                        <Grid container spacing={1}>
                            {roles.map((role) => (
                                <Grid item key={role.role_id}>
                                    <Chip
                                        label={role.role_name}
                                        disableRipple
                                        onClick={() => toggleRoleSelection(role.role_id)}
                                        sx={{
                                            border: `1px solid ${selectedRoles.includes(role.role_id) ? alpha("#FFFFFF", 0.5) : theme.palette.custom.borderColor}`,
                                            borderRadius: "8px",
                                            color: selectedRoles.includes(role.role_id) ? "#FFFFFF" : "#737791",
                                            backgroundColor: "transparent",
                                            "&:hover": { backgroundColor: "transparent" },
                                        }}
                                    />
                                </Grid>
                            ))}
                        </Grid>
                    </Grid>

                    {user.organization.is_internal && (
                        <Grid item>
                            <Typography variant="h6" fontSize={"16px !important"} marginBottom={2} fontWeight={500}>
                                Organizations
                            </Typography>
                            <Grid container spacing={1}>
                                {organizations.map((org) => (
                                    <Grid item key={org._id}>
                                        <Chip
                                            label={org.name}
                                            disableRipple
                                            onClick={() => toggleOrganizationSelection(org._id)}
                                            sx={{
                                                border: `1px solid ${selectedOrganizations.includes(org._id) ? alpha("#FFFFFF", 0.5) : theme.palette.custom.borderColor}`,
                                                borderRadius: "8px",
                                                color: selectedOrganizations.includes(org._id) ? "#FFFFFF" : "#737791",
                                                backgroundColor: "transparent",
                                                "&:hover": { backgroundColor: "transparent" },
                                            }}
                                        />
                                    </Grid>
                                ))}
                            </Grid>
                        </Grid>
                    )}

                    <Grid item>
                        <Typography variant="h6" fontSize={"16px !important"} marginBottom={2} fontWeight={500}>
                            Filter by Email
                        </Typography>
                        <Grid container border={`1px solid ${theme.palette.custom.borderColor}`} borderRadius={"8px"} padding={"3px"}>
                            {["email", "no_email", "both"].map((value) => (
                                <Grid item xs={4} key={value}>
                                    <Button
                                        onClick={() => {
                                            setChanged({ ...changed, changedEmail: true, oldEmail: emailFilter });
                                            setEmailFilter(value);
                                        }}
                                        sx={{
                                            width: "100%",
                                            borderRadius: "8px",
                                            color: emailFilter === value ? "#FFFFFF" : "#737791",
                                            outline: "none !important",
                                            textTransform: "capitalize",
                                            backgroundColor: "transparent",
                                            border: emailFilter === value ? `1px solid ${theme.palette.custom.borderColor}` : "none",
                                            "&:hover": {
                                                backgroundColor: "transparent",
                                            },
                                        }}
                                    >
                                        {value.charAt(0).toUpperCase() + value.slice(1).replace(/_/g, " ")}
                                    </Button>
                                </Grid>
                            ))}
                        </Grid>
                    </Grid>

                    <Grid item>
                        <Typography variant="h6" fontSize={"16px !important"} marginBottom={2} fontWeight={500}>
                            Time
                        </Typography>
                        <FormControl component="fieldset">
                            <RadioGroup
                                value={timeFilter}
                                onChange={(e) => {
                                    setChanged({ ...changed, changedTime: true, oldTime: timeFilter });
                                    setTimeFilter(e.target.value);
                                }}
                            >
                                {["today", "last_3_days", "last_week", "last_month", "last_year", "all_time"].map((value) => (
                                    <FormControlLabel
                                        key={value}
                                        value={value}
                                        control={
                                            <Radio sx={{ color: "#737791", opacity: 0.3, "&.Mui-checked": { color: "#FFFFFF", opacity: 0.5 } }} />
                                        }
                                        label={value.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase())}
                                        sx={{
                                            margin: 0,
                                            marginBottom: 1,
                                            width: "fit-content",
                                            padding: "0 15px 0 0",
                                            border: `1px solid ${timeFilter === value ? alpha("#FFFFFF", 0.5) : theme.palette.custom.borderColor}`,
                                            borderRadius: "8px",
                                            "& .MuiTypography-root": {
                                                color: timeFilter === value ? "#FFFFFF" : "#737791",
                                                fontWeight: 300,
                                            },
                                        }}
                                    />
                                ))}
                            </RadioGroup>
                        </FormControl>
                    </Grid>
                </Grid>
                <Grid item container gap={2} justifyContent={"space-between"}>
                    <Grid item>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                textTransform: "none",
                            }}
                            onClick={handleClear}
                        >
                            Clear filters
                        </Button>
                    </Grid>
                    <Grid item>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                backgroundColor: theme.palette.custom.mainBlue,
                                "&:hover": {
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                            variant="contained"
                            onClick={handleSubmit}
                        >
                            Apply
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default FilterUserModal;
