import React from "react";

const splashStyle = {
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    width: "100vw",
    height: "100vh",
    backgroundColor: "#f8f8f8",
    color: "#333",
    fontSize: 24,
    lineHeight: "normal",
    fontWeight: "bold",
    fontFamily: "Arial, sans-serif",
};

const imgStyle = {
    animation: "rotate 2s linear infinite",
    width: "100px",
};

const keyframes = `
@keyframes rotate {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}
`;

export default function SplashScreen() {
    return (
        <div style={splashStyle}>
            <style>{keyframes}</style>
            <img src="/quartermaster-logo-dark.svg" style={imgStyle} alt="Quartermaster Logo" />
            <p>Loading...</p>
        </div>
    );
}
