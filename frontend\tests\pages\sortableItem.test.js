import { render, screen } from '@testing-library/react';
import SortableItem from '../../src/pages/Dashboard/User/Roles/SortableItem';
import { useSortable } from '@dnd-kit/sortable';
import { DragHandle } from '@mui/icons-material';
import LockIcon from '@mui/icons-material/Lock';
import theme from "../../src/theme";

jest.mock('@dnd-kit/sortable', () => ({
    useSortable: jest.fn(),
}));

describe('SortableItem', () => {
    let role;

    beforeEach(() => {
        role = {
            _id: 'r1',
            role_name: 'Admin',
        };
    });

    it('should render the role name correctly', () => {
        useSortable.mockReturnValue({
            attributes: {},
            listeners: {},
            setNodeRef: jest.fn(),
            transform: {},
            transition: '',
        });

        render(<SortableItem role={role} isDraggable={true} />);

        expect(screen.getByText('Admin')).toBeInTheDocument();
    });

    it('should apply correct styles when not draggable', () => {
        useSortable.mockReturnValue({
            attributes: {},
            listeners: {},
            setNodeRef: jest.fn(),
            transform: {},
            transition: '',
        });

        render(<SortableItem role={role} isDraggable={false} />);

        const roleElement = screen.getByText('Admin').parentElement;
        expect(roleElement).not.toHaveStyle({});
    });

    it('should pass the ref to the grid container', () => {
        const setNodeRef = jest.fn();
        useSortable.mockReturnValue({
            attributes: {},
            listeners: {},
            setNodeRef,
            transform: {},
            transition: '',
        });

        render(<SortableItem role={role} isDraggable={true} />);

        expect(setNodeRef).toHaveBeenCalled();
    });
});
