import React, { useState } from "react";
import { Grid, Modal, Typography, IconButton, FormControl, OutlinedInput, InputAdornment, alpha } from "@mui/material";
import { ContentCopy, Check } from "@mui/icons-material";
import ModalContainer from "./ModalContainer";
import { useToaster } from "../hooks/ToasterHook";

const ShareModal = ({ state, onClose, shareLink, isImageLink, shareEventLink }) => {
    const [isCopied, setIsCopied] = useState(false);
    const toaster = useToaster();

    const handleCopy = (e) => {
        e.stopPropagation();
        navigator.clipboard.writeText(shareEventLink ? shareEventLink : shareLink);
        toaster("Link copied to clipboard!", { variant: "success" });
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 5000);
    };

    return (
        <Modal open={state} onClose={onClose}>
            <ModalContainer title="Share Link" onClose={onClose}>
                <Grid container flexDirection="column" gap={2} width={{ xs: 300, sm: 500 }}>
                    <Grid container justifyContent="center" height={"100%"} size={"grow"}>
                        {isImageLink ? (
                            <img src={shareLink} alt="share" loading="lazy" style={{ width: "100%", height: "100%", borderRadius: 8 }} />
                        ) : (
                            <video
                                src={shareLink}
                                controls
                                autoPlay
                                muted
                                preload="auto"
                                style={{ width: "100%", height: "100%", borderRadius: 8 }}
                            />
                        )}
                    </Grid>
                    <Grid>
                        <Typography fontWeight={"100"}>Copy the link below to share:</Typography>
                    </Grid>
                    <Grid container alignItems="center" gap={1}>
                        <FormControl sx={{ m: 1, width: "100%" }} variant="outlined">
                            <OutlinedInput
                                value={shareEventLink ? shareEventLink : shareLink}
                                sx={{
                                    color: alpha("#FFFFFF", 0.5),
                                    fontWeight: 300,
                                }}
                                endAdornment={
                                    <InputAdornment position="end">
                                        <IconButton edge="end" onClick={handleCopy} disabled={isCopied}>
                                            {isCopied ? <Check sx={{ color: "#FFFFFF" }} /> : <ContentCopy sx={{ color: "#FFFFFF" }} />}
                                        </IconButton>
                                    </InputAdornment>
                                }
                            />
                        </FormControl>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default ShareModal;
