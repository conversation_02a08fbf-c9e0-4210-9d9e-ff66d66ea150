{"version": 3, "sources": ["../../@mui/icons-material/esm/FiberManualRecord.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"8\"\n}), 'FiberManualRecord');"], "mappings": ";;;;;;;;;;;AAGA,yBAA4B;AAC5B,IAAO,4BAAQ,kBAA2B,mBAAAA,KAAK,UAAU;AAAA,EACvD,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,GAAG;AACL,CAAC,GAAG,mBAAmB;", "names": ["_jsx"]}