{"version": 3, "sources": ["../../echarts/lib/export/api/helper.js", "../../echarts/lib/export/api/number.js", "../../echarts/lib/export/api/time.js", "../../echarts/lib/export/api/graphic.js", "../../echarts/lib/export/api/format.js", "../../echarts/lib/export/api/util.js", "../../echarts/lib/export/api.js", "../../echarts/lib/label/LabelManager.js", "../../echarts/lib/label/installLabelLayout.js", "../../echarts/lib/export/core.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * This module exposes helper functions for developing extensions.\r\n */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport createSeriesData from '../../chart/helper/createSeriesData.js';\n// import createGraphFromNodeEdge from './chart/helper/createGraphFromNodeEdge.js';\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport { AxisModelCommonMixin } from '../../coord/axisModelCommonMixin.js';\nimport Model from '../../model/Model.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport { enableDataStack, isDimensionStacked, getStackedDimension } from '../../data/helper/dataStackHelper.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle as innerCreateTextStyle } from '../../label/labelStyle.js';\n/**\r\n * Create a multi dimension List structure from seriesModel.\r\n */\nexport function createList(seriesModel) {\n  return createSeriesData(null, seriesModel);\n}\n// export function createGraph(seriesModel) {\n//     let nodes = seriesModel.get('data');\n//     let links = seriesModel.get('links');\n//     return createGraphFromNodeEdge(nodes, links, seriesModel);\n// }\nexport { getLayoutRect };\nexport { createDimensions } from '../../data/helper/createDimensions.js';\nexport var dataStack = {\n  isDimensionStacked: isDimensionStacked,\n  enableDataStack: enableDataStack,\n  getStackedDimension: getStackedDimension\n};\n/**\r\n * Create a symbol element with given symbol configuration: shape, x, y, width, height, color\r\n * @param {string} symbolDesc\r\n * @param {number} x\r\n * @param {number} y\r\n * @param {number} w\r\n * @param {number} h\r\n * @param {string} color\r\n */\nexport { createSymbol } from '../../util/symbol.js';\n/**\r\n * Create scale\r\n * @param {Array.<number>} dataExtent\r\n * @param {Object|module:echarts/Model} option If `optoin.type`\r\n *        is secified, it can only be `'value'` currently.\r\n */\nexport function createScale(dataExtent, option) {\n  var axisModel = option;\n  if (!(option instanceof Model)) {\n    axisModel = new Model(option);\n    // FIXME\n    // Currently AxisModelCommonMixin has nothing to do with the\n    // the requirements of `axisHelper.createScaleByModel`. For\n    // example the methods `getCategories` and `getOrdinalMeta`\n    // are required for `'category'` axis, and ecModel is required\n    // for `'time'` axis. But occasionally echarts-gl happened\n    // to only use `'value'` axis.\n    // zrUtil.mixin(axisModel, AxisModelCommonMixin);\n  }\n  var scale = axisHelper.createScaleByModel(axisModel);\n  scale.setExtent(dataExtent[0], dataExtent[1]);\n  axisHelper.niceScaleExtent(scale, axisModel);\n  return scale;\n}\n/**\r\n * Mixin common methods to axis model,\r\n *\r\n * Include methods\r\n * `getFormattedLabels() => Array.<string>`\r\n * `getCategories() => Array.<string>`\r\n * `getMin(origin: boolean) => number`\r\n * `getMax(origin: boolean) => number`\r\n * `getNeedCrossZero() => boolean`\r\n */\nexport function mixinAxisModelCommonMethods(Model) {\n  zrUtil.mixin(Model, AxisModelCommonMixin);\n}\nexport { getECData };\nexport { enableHoverEmphasis } from '../../util/states.js';\nexport function createTextStyle(textStyleModel, opts) {\n  opts = opts || {};\n  return innerCreateTextStyle(textStyleModel, null, null, opts.state !== 'normal');\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport { linearMap, round, asc, getPrecision, getPrecisionSafe, getPixelPrecision, getPercentWithPrecision, MAX_SAFE_INTEGER, remRadian, isRadianAroundZero, parseDate, quantity, quantityExponent, nice, quantile, reformIntervals, isNumeric, numericToNumber } from '../../util/number.js';", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport { parseDate as parse } from '../../util/number.js';\nexport { format } from '../../util/time.js';", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport { extendShape, extendPath, makePath, makeImage, mergePath, resizePath, createIcon, updateProps, initProps, getTransform, clipPointsByRect, clipRectByRect, registerShape, getShapeClass, Group, Image, Text, Circle, Ellipse, Sector, Ring, Polygon, Polyline, Rect, Line, BezierCurve, Arc, IncrementalDisplayable, CompoundPath, LinearGradient, RadialGradient, BoundingRect } from '../../util/graphic.js';", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport { addCommas, toCamelCase, normalizeCssArray, encodeHTML, formatTpl, getTooltipMarker, formatTime, capitalFirst, truncateText, getTextRect } from '../../util/format.js';", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport { map, each, indexOf, inherits, reduce, filter, bind, curry, isArray, isString, isObject, isFunction, extend, defaults, clone, merge } from 'zrender/lib/core/util.js';", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// These APIs are for more advanced usages\n// For example extend charts and components, creating graphic elements, formatting.\nimport ComponentModel from '../model/Component.js';\nimport ComponentView from '../view/Component.js';\nimport SeriesModel from '../model/Series.js';\nimport ChartView from '../view/Chart.js';\nimport SeriesData from '../data/SeriesData.js';\nimport * as zrender_1 from 'zrender/lib/zrender.js';\nexport { zrender_1 as zrender };\nimport * as matrix_1 from 'zrender/lib/core/matrix.js';\nexport { matrix_1 as matrix };\nimport * as vector_1 from 'zrender/lib/core/vector.js';\nexport { vector_1 as vector };\nimport * as zrUtil_1 from 'zrender/lib/core/util.js';\nexport { zrUtil_1 as zrUtil };\nimport * as color_1 from 'zrender/lib/tool/color.js';\nexport { color_1 as color };\nexport { throttle } from '../util/throttle.js';\nimport * as helper_1 from './api/helper.js';\nexport { helper_1 as helper };\nexport { use } from '../extension.js';\nexport { setPlatformAPI } from 'zrender/lib/core/platform.js';\n// --------------------- Helper Methods ---------------------\nexport { default as parseGeoJSON } from '../coord/geo/parseGeoJson.js';\nexport { default as parseGeoJson } from '../coord/geo/parseGeoJson.js';\nimport * as number_1 from './api/number.js';\nexport { number_1 as number };\nimport * as time_1 from './api/time.js';\nexport { time_1 as time };\nimport * as graphic_1 from './api/graphic.js';\nexport { graphic_1 as graphic };\nimport * as format_1 from './api/format.js';\nexport { format_1 as format };\nimport * as util_1 from './api/util.js';\nexport { util_1 as util };\nexport { default as env } from 'zrender/lib/core/env.js';\n// --------------------- Export for Extension Usage ---------------------\n// export {SeriesData};\nexport { SeriesData as List }; // TODO: Compatitable with exists echarts-gl code\nexport { default as Model } from '../model/Model.js';\nexport { default as Axis } from '../coord/Axis.js';\nexport { ComponentModel, ComponentView, SeriesModel, ChartView };\n// Only for GL\nexport { brushSingle as innerDrawElementOnCanvas } from 'zrender/lib/canvas/graphic.js';\n// --------------------- Deprecated Extension Methods ---------------------\n// Should use `ComponentModel.extend` or `class XXXX extend ComponentModel` to create class.\n// Then use `registerComponentModel` in `install` parameter when `use` this extension. For example:\n// class Bar3DModel extends ComponentModel {}\n// export function install(registers) { registers.registerComponentModel(Bar3DModel); }\n// echarts.use(install);\nexport function extendComponentModel(proto) {\n  var Model = ComponentModel.extend(proto);\n  ComponentModel.registerClass(Model);\n  return Model;\n}\nexport function extendComponentView(proto) {\n  var View = ComponentView.extend(proto);\n  ComponentView.registerClass(View);\n  return View;\n}\nexport function extendSeriesModel(proto) {\n  var Model = SeriesModel.extend(proto);\n  SeriesModel.registerClass(Model);\n  return Model;\n}\nexport function extendChartView(proto) {\n  var View = ChartView.extend(proto);\n  ChartView.registerClass(View);\n  return View;\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// TODO: move labels out of viewport.\nimport { BoundingRect, updateProps, initProps, isElementRemoved } from '../util/graphic.js';\nimport { getECData } from '../util/innerStore.js';\nimport { parsePercent } from '../util/number.js';\nimport Transformable from 'zrender/lib/core/Transformable.js';\nimport { updateLabelLinePoints, setLabelLineStyle, getLabelLineStatesModels } from './labelGuideHelper.js';\nimport { makeInner } from '../util/model.js';\nimport { retrieve2, each, keys, isFunction, filter, indexOf } from 'zrender/lib/core/util.js';\nimport { prepareLayoutList, hideOverlap, shiftLayoutOnX, shiftLayoutOnY } from './labelLayoutHelper.js';\nimport { labelInner, animateLabelValue } from './labelStyle.js';\nimport { normalizeRadian } from 'zrender/lib/contain/util.js';\nfunction cloneArr(points) {\n  if (points) {\n    var newPoints = [];\n    for (var i = 0; i < points.length; i++) {\n      newPoints.push(points[i].slice());\n    }\n    return newPoints;\n  }\n}\nfunction prepareLayoutCallbackParams(labelItem, hostEl) {\n  var label = labelItem.label;\n  var labelLine = hostEl && hostEl.getTextGuideLine();\n  return {\n    dataIndex: labelItem.dataIndex,\n    dataType: labelItem.dataType,\n    seriesIndex: labelItem.seriesModel.seriesIndex,\n    text: labelItem.label.style.text,\n    rect: labelItem.hostRect,\n    labelRect: labelItem.rect,\n    // x: labelAttr.x,\n    // y: labelAttr.y,\n    align: label.style.align,\n    verticalAlign: label.style.verticalAlign,\n    labelLinePoints: cloneArr(labelLine && labelLine.shape.points)\n  };\n}\nvar LABEL_OPTION_TO_STYLE_KEYS = ['align', 'verticalAlign', 'width', 'height', 'fontSize'];\nvar dummyTransformable = new Transformable();\nvar labelLayoutInnerStore = makeInner();\nvar labelLineAnimationStore = makeInner();\nfunction extendWithKeys(target, source, keys) {\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (source[key] != null) {\n      target[key] = source[key];\n    }\n  }\n}\nvar LABEL_LAYOUT_PROPS = ['x', 'y', 'rotation'];\nvar LabelManager = /** @class */function () {\n  function LabelManager() {\n    this._labelList = [];\n    this._chartViewList = [];\n  }\n  LabelManager.prototype.clearLabels = function () {\n    this._labelList = [];\n    this._chartViewList = [];\n  };\n  /**\r\n   * Add label to manager\r\n   */\n  LabelManager.prototype._addLabel = function (dataIndex, dataType, seriesModel, label, layoutOption) {\n    var labelStyle = label.style;\n    var hostEl = label.__hostTarget;\n    var textConfig = hostEl.textConfig || {};\n    // TODO: If label is in other state.\n    var labelTransform = label.getComputedTransform();\n    var labelRect = label.getBoundingRect().plain();\n    BoundingRect.applyTransform(labelRect, labelRect, labelTransform);\n    if (labelTransform) {\n      dummyTransformable.setLocalTransform(labelTransform);\n    } else {\n      // Identity transform.\n      dummyTransformable.x = dummyTransformable.y = dummyTransformable.rotation = dummyTransformable.originX = dummyTransformable.originY = 0;\n      dummyTransformable.scaleX = dummyTransformable.scaleY = 1;\n    }\n    dummyTransformable.rotation = normalizeRadian(dummyTransformable.rotation);\n    var host = label.__hostTarget;\n    var hostRect;\n    if (host) {\n      hostRect = host.getBoundingRect().plain();\n      var transform = host.getComputedTransform();\n      BoundingRect.applyTransform(hostRect, hostRect, transform);\n    }\n    var labelGuide = hostRect && host.getTextGuideLine();\n    this._labelList.push({\n      label: label,\n      labelLine: labelGuide,\n      seriesModel: seriesModel,\n      dataIndex: dataIndex,\n      dataType: dataType,\n      layoutOption: layoutOption,\n      computedLayoutOption: null,\n      rect: labelRect,\n      hostRect: hostRect,\n      // Label with lower priority will be hidden when overlapped\n      // Use rect size as default priority\n      priority: hostRect ? hostRect.width * hostRect.height : 0,\n      // Save default label attributes.\n      // For restore if developers want get back to default value in callback.\n      defaultAttr: {\n        ignore: label.ignore,\n        labelGuideIgnore: labelGuide && labelGuide.ignore,\n        x: dummyTransformable.x,\n        y: dummyTransformable.y,\n        scaleX: dummyTransformable.scaleX,\n        scaleY: dummyTransformable.scaleY,\n        rotation: dummyTransformable.rotation,\n        style: {\n          x: labelStyle.x,\n          y: labelStyle.y,\n          align: labelStyle.align,\n          verticalAlign: labelStyle.verticalAlign,\n          width: labelStyle.width,\n          height: labelStyle.height,\n          fontSize: labelStyle.fontSize\n        },\n        cursor: label.cursor,\n        attachedPos: textConfig.position,\n        attachedRot: textConfig.rotation\n      }\n    });\n  };\n  LabelManager.prototype.addLabelsOfSeries = function (chartView) {\n    var _this = this;\n    this._chartViewList.push(chartView);\n    var seriesModel = chartView.__model;\n    var layoutOption = seriesModel.get('labelLayout');\n    /**\r\n     * Ignore layouting if it's not specified anything.\r\n     */\n    if (!(isFunction(layoutOption) || keys(layoutOption).length)) {\n      return;\n    }\n    chartView.group.traverse(function (child) {\n      if (child.ignore) {\n        return true; // Stop traverse descendants.\n      }\n      // Only support label being hosted on graphic elements.\n      var textEl = child.getTextContent();\n      var ecData = getECData(child);\n      // Can only attach the text on the element with dataIndex\n      if (textEl && !textEl.disableLabelLayout) {\n        _this._addLabel(ecData.dataIndex, ecData.dataType, seriesModel, textEl, layoutOption);\n      }\n    });\n  };\n  LabelManager.prototype.updateLayoutConfig = function (api) {\n    var width = api.getWidth();\n    var height = api.getHeight();\n    function createDragHandler(el, labelLineModel) {\n      return function () {\n        updateLabelLinePoints(el, labelLineModel);\n      };\n    }\n    for (var i = 0; i < this._labelList.length; i++) {\n      var labelItem = this._labelList[i];\n      var label = labelItem.label;\n      var hostEl = label.__hostTarget;\n      var defaultLabelAttr = labelItem.defaultAttr;\n      var layoutOption = void 0;\n      // TODO A global layout option?\n      if (isFunction(labelItem.layoutOption)) {\n        layoutOption = labelItem.layoutOption(prepareLayoutCallbackParams(labelItem, hostEl));\n      } else {\n        layoutOption = labelItem.layoutOption;\n      }\n      layoutOption = layoutOption || {};\n      labelItem.computedLayoutOption = layoutOption;\n      var degreeToRadian = Math.PI / 180;\n      // TODO hostEl should always exists.\n      // Or label should not have parent because the x, y is all in global space.\n      if (hostEl) {\n        hostEl.setTextConfig({\n          // Force to set local false.\n          local: false,\n          // Ignore position and rotation config on the host el if x or y is changed.\n          position: layoutOption.x != null || layoutOption.y != null ? null : defaultLabelAttr.attachedPos,\n          // Ignore rotation config on the host el if rotation is changed.\n          rotation: layoutOption.rotate != null ? layoutOption.rotate * degreeToRadian : defaultLabelAttr.attachedRot,\n          offset: [layoutOption.dx || 0, layoutOption.dy || 0]\n        });\n      }\n      var needsUpdateLabelLine = false;\n      if (layoutOption.x != null) {\n        // TODO width of chart view.\n        label.x = parsePercent(layoutOption.x, width);\n        label.setStyle('x', 0); // Ignore movement in style. TODO: origin.\n        needsUpdateLabelLine = true;\n      } else {\n        label.x = defaultLabelAttr.x;\n        label.setStyle('x', defaultLabelAttr.style.x);\n      }\n      if (layoutOption.y != null) {\n        // TODO height of chart view.\n        label.y = parsePercent(layoutOption.y, height);\n        label.setStyle('y', 0); // Ignore movement in style.\n        needsUpdateLabelLine = true;\n      } else {\n        label.y = defaultLabelAttr.y;\n        label.setStyle('y', defaultLabelAttr.style.y);\n      }\n      if (layoutOption.labelLinePoints) {\n        var guideLine = hostEl.getTextGuideLine();\n        if (guideLine) {\n          guideLine.setShape({\n            points: layoutOption.labelLinePoints\n          });\n          // Not update\n          needsUpdateLabelLine = false;\n        }\n      }\n      var labelLayoutStore = labelLayoutInnerStore(label);\n      labelLayoutStore.needsUpdateLabelLine = needsUpdateLabelLine;\n      label.rotation = layoutOption.rotate != null ? layoutOption.rotate * degreeToRadian : defaultLabelAttr.rotation;\n      label.scaleX = defaultLabelAttr.scaleX;\n      label.scaleY = defaultLabelAttr.scaleY;\n      for (var k = 0; k < LABEL_OPTION_TO_STYLE_KEYS.length; k++) {\n        var key = LABEL_OPTION_TO_STYLE_KEYS[k];\n        label.setStyle(key, layoutOption[key] != null ? layoutOption[key] : defaultLabelAttr.style[key]);\n      }\n      if (layoutOption.draggable) {\n        label.draggable = true;\n        label.cursor = 'move';\n        if (hostEl) {\n          var hostModel = labelItem.seriesModel;\n          if (labelItem.dataIndex != null) {\n            var data = labelItem.seriesModel.getData(labelItem.dataType);\n            hostModel = data.getItemModel(labelItem.dataIndex);\n          }\n          label.on('drag', createDragHandler(hostEl, hostModel.getModel('labelLine')));\n        }\n      } else {\n        // TODO Other drag functions?\n        label.off('drag');\n        label.cursor = defaultLabelAttr.cursor;\n      }\n    }\n  };\n  LabelManager.prototype.layout = function (api) {\n    var width = api.getWidth();\n    var height = api.getHeight();\n    var labelList = prepareLayoutList(this._labelList);\n    var labelsNeedsAdjustOnX = filter(labelList, function (item) {\n      return item.layoutOption.moveOverlap === 'shiftX';\n    });\n    var labelsNeedsAdjustOnY = filter(labelList, function (item) {\n      return item.layoutOption.moveOverlap === 'shiftY';\n    });\n    shiftLayoutOnX(labelsNeedsAdjustOnX, 0, width);\n    shiftLayoutOnY(labelsNeedsAdjustOnY, 0, height);\n    var labelsNeedsHideOverlap = filter(labelList, function (item) {\n      return item.layoutOption.hideOverlap;\n    });\n    hideOverlap(labelsNeedsHideOverlap);\n  };\n  /**\r\n   * Process all labels. Not only labels with layoutOption.\r\n   */\n  LabelManager.prototype.processLabelsOverall = function () {\n    var _this = this;\n    each(this._chartViewList, function (chartView) {\n      var seriesModel = chartView.__model;\n      var ignoreLabelLineUpdate = chartView.ignoreLabelLineUpdate;\n      var animationEnabled = seriesModel.isAnimationEnabled();\n      chartView.group.traverse(function (child) {\n        if (child.ignore && !child.forceLabelAnimation) {\n          return true; // Stop traverse descendants.\n        }\n        var needsUpdateLabelLine = !ignoreLabelLineUpdate;\n        var label = child.getTextContent();\n        if (!needsUpdateLabelLine && label) {\n          needsUpdateLabelLine = labelLayoutInnerStore(label).needsUpdateLabelLine;\n        }\n        if (needsUpdateLabelLine) {\n          _this._updateLabelLine(child, seriesModel);\n        }\n        if (animationEnabled) {\n          _this._animateLabels(child, seriesModel);\n        }\n      });\n    });\n  };\n  LabelManager.prototype._updateLabelLine = function (el, seriesModel) {\n    // Only support label being hosted on graphic elements.\n    var textEl = el.getTextContent();\n    // Update label line style.\n    var ecData = getECData(el);\n    var dataIndex = ecData.dataIndex;\n    // Only support labelLine on the labels represent data.\n    if (textEl && dataIndex != null) {\n      var data = seriesModel.getData(ecData.dataType);\n      var itemModel = data.getItemModel(dataIndex);\n      var defaultStyle = {};\n      var visualStyle = data.getItemVisual(dataIndex, 'style');\n      if (visualStyle) {\n        var visualType = data.getVisual('drawType');\n        // Default to be same with main color\n        defaultStyle.stroke = visualStyle[visualType];\n      }\n      var labelLineModel = itemModel.getModel('labelLine');\n      setLabelLineStyle(el, getLabelLineStatesModels(itemModel), defaultStyle);\n      updateLabelLinePoints(el, labelLineModel);\n    }\n  };\n  LabelManager.prototype._animateLabels = function (el, seriesModel) {\n    var textEl = el.getTextContent();\n    var guideLine = el.getTextGuideLine();\n    // Animate\n    if (textEl\n    // `forceLabelAnimation` has the highest priority\n    && (el.forceLabelAnimation || !textEl.ignore && !textEl.invisible && !el.disableLabelAnimation && !isElementRemoved(el))) {\n      var layoutStore = labelLayoutInnerStore(textEl);\n      var oldLayout = layoutStore.oldLayout;\n      var ecData = getECData(el);\n      var dataIndex = ecData.dataIndex;\n      var newProps = {\n        x: textEl.x,\n        y: textEl.y,\n        rotation: textEl.rotation\n      };\n      var data = seriesModel.getData(ecData.dataType);\n      if (!oldLayout) {\n        textEl.attr(newProps);\n        // Disable fade in animation if value animation is enabled.\n        if (!labelInner(textEl).valueAnimation) {\n          var oldOpacity = retrieve2(textEl.style.opacity, 1);\n          // Fade in animation\n          textEl.style.opacity = 0;\n          initProps(textEl, {\n            style: {\n              opacity: oldOpacity\n            }\n          }, seriesModel, dataIndex);\n        }\n      } else {\n        textEl.attr(oldLayout);\n        // Make sure the animation from is in the right status.\n        var prevStates = el.prevStates;\n        if (prevStates) {\n          if (indexOf(prevStates, 'select') >= 0) {\n            textEl.attr(layoutStore.oldLayoutSelect);\n          }\n          if (indexOf(prevStates, 'emphasis') >= 0) {\n            textEl.attr(layoutStore.oldLayoutEmphasis);\n          }\n        }\n        updateProps(textEl, newProps, seriesModel, dataIndex);\n      }\n      layoutStore.oldLayout = newProps;\n      if (textEl.states.select) {\n        var layoutSelect = layoutStore.oldLayoutSelect = {};\n        extendWithKeys(layoutSelect, newProps, LABEL_LAYOUT_PROPS);\n        extendWithKeys(layoutSelect, textEl.states.select, LABEL_LAYOUT_PROPS);\n      }\n      if (textEl.states.emphasis) {\n        var layoutEmphasis = layoutStore.oldLayoutEmphasis = {};\n        extendWithKeys(layoutEmphasis, newProps, LABEL_LAYOUT_PROPS);\n        extendWithKeys(layoutEmphasis, textEl.states.emphasis, LABEL_LAYOUT_PROPS);\n      }\n      animateLabelValue(textEl, dataIndex, data, seriesModel, seriesModel);\n    }\n    if (guideLine && !guideLine.ignore && !guideLine.invisible) {\n      var layoutStore = labelLineAnimationStore(guideLine);\n      var oldLayout = layoutStore.oldLayout;\n      var newLayout = {\n        points: guideLine.shape.points\n      };\n      if (!oldLayout) {\n        guideLine.setShape(newLayout);\n        guideLine.style.strokePercent = 0;\n        initProps(guideLine, {\n          style: {\n            strokePercent: 1\n          }\n        }, seriesModel);\n      } else {\n        guideLine.attr({\n          shape: oldLayout\n        });\n        updateProps(guideLine, {\n          shape: newLayout\n        }, seriesModel);\n      }\n      layoutStore.oldLayout = newLayout;\n    }\n  };\n  return LabelManager;\n}();\nexport default LabelManager;", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner } from '../util/model.js';\nimport LabelManager from './LabelManager.js';\nvar getLabelManager = makeInner();\nexport function installLabelLayout(registers) {\n  registers.registerUpdateLifecycle('series:beforeupdate', function (ecModel, api, params) {\n    // TODO api provide an namespace that can save stuff per instance\n    var labelManager = getLabelManager(api).labelManager;\n    if (!labelManager) {\n      labelManager = getLabelManager(api).labelManager = new LabelManager();\n    }\n    labelManager.clearLabels();\n  });\n  registers.registerUpdateLifecycle('series:layoutlabels', function (ecModel, api, params) {\n    var labelManager = getLabelManager(api).labelManager;\n    params.updatedSeries.forEach(function (series) {\n      labelManager.addLabelsOfSeries(api.getViewOfSeriesModel(series));\n    });\n    labelManager.updateLayoutConfig(api);\n    labelManager.layout(api);\n    labelManager.processLabelsOverall();\n  });\n}", "\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Core API from echarts/src/echarts\nexport * from '../core/echarts.js';\nexport * from './api.js';\nimport { use } from '../extension.js';\n// Import label layout by default.\n// TODO will be treeshaked.\nimport { installLabelLayout } from '../label/installLabelLayout.js';\nuse(installLabelLayout);"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2DO,SAAS,WAAW,aAAa;AACtC,SAAO,yBAAiB,MAAM,WAAW;AAC3C;AAQO,IAAI,YAAY;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AACF;AAiBO,SAAS,YAAY,YAAY,QAAQ;AAC9C,MAAI,YAAY;AAChB,MAAI,EAAE,kBAAkB,gBAAQ;AAC9B,gBAAY,IAAI,cAAM,MAAM;AAAA,EAS9B;AACA,MAAI,QAAmB,mBAAmB,SAAS;AACnD,QAAM,UAAU,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAC5C,EAAW,gBAAgB,OAAO,SAAS;AAC3C,SAAO;AACT;AAWO,SAAS,4BAA4B,OAAO;AACjD,EAAO,MAAM,OAAO,oBAAoB;AAC1C;AAGO,SAASC,iBAAgB,gBAAgB,MAAM;AACpD,SAAO,QAAQ,CAAC;AAChB,SAAO,gBAAqB,gBAAgB,MAAM,MAAM,KAAK,UAAU,QAAQ;AACjF;;;AC9HA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAC,gBAAA;AAAA,SAAAA,eAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AC6FO,SAAS,qBAAqB,OAAO;AAC1C,MAAI,QAAQ,kBAAe,OAAO,KAAK;AACvC,oBAAe,cAAc,KAAK;AAClC,SAAO;AACT;AACO,SAAS,oBAAoB,OAAO;AACzC,MAAI,OAAOC,mBAAc,OAAO,KAAK;AACrC,EAAAA,mBAAc,cAAc,IAAI;AAChC,SAAO;AACT;AACO,SAAS,kBAAkB,OAAO;AACvC,MAAI,QAAQ,eAAY,OAAO,KAAK;AACpC,iBAAY,cAAc,KAAK;AAC/B,SAAO;AACT;AACO,SAAS,gBAAgB,OAAO;AACrC,MAAI,OAAO,cAAU,OAAO,KAAK;AACjC,gBAAU,cAAc,IAAI;AAC5B,SAAO;AACT;;;AC1DA,SAAS,SAAS,QAAQ;AACxB,MAAI,QAAQ;AACV,QAAI,YAAY,CAAC;AACjB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAU,KAAK,OAAO,CAAC,EAAE,MAAM,CAAC;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,4BAA4B,WAAW,QAAQ;AACtD,MAAI,QAAQ,UAAU;AACtB,MAAI,YAAY,UAAU,OAAO,iBAAiB;AAClD,SAAO;AAAA,IACL,WAAW,UAAU;AAAA,IACrB,UAAU,UAAU;AAAA,IACpB,aAAa,UAAU,YAAY;AAAA,IACnC,MAAM,UAAU,MAAM,MAAM;AAAA,IAC5B,MAAM,UAAU;AAAA,IAChB,WAAW,UAAU;AAAA;AAAA;AAAA,IAGrB,OAAO,MAAM,MAAM;AAAA,IACnB,eAAe,MAAM,MAAM;AAAA,IAC3B,iBAAiB,SAAS,aAAa,UAAU,MAAM,MAAM;AAAA,EAC/D;AACF;AACA,IAAI,6BAA6B,CAAC,SAAS,iBAAiB,SAAS,UAAU,UAAU;AACzF,IAAI,qBAAqB,IAAI,sBAAc;AAC3C,IAAI,wBAAwB,UAAU;AACtC,IAAI,0BAA0B,UAAU;AACxC,SAAS,eAAe,QAAQ,QAAQC,OAAM;AAC5C,WAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,KAAK;AACpC,QAAI,MAAMA,MAAK,CAAC;AAChB,QAAI,OAAO,GAAG,KAAK,MAAM;AACvB,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACF;AACA,IAAI,qBAAqB,CAAC,KAAK,KAAK,UAAU;AAC9C,IAAI;AAAA;AAAA,EAA4B,WAAY;AAC1C,aAASC,gBAAe;AACtB,WAAK,aAAa,CAAC;AACnB,WAAK,iBAAiB,CAAC;AAAA,IACzB;AACA,IAAAA,cAAa,UAAU,cAAc,WAAY;AAC/C,WAAK,aAAa,CAAC;AACnB,WAAK,iBAAiB,CAAC;AAAA,IACzB;AAIA,IAAAA,cAAa,UAAU,YAAY,SAAU,WAAW,UAAU,aAAa,OAAO,cAAc;AAClG,UAAI,aAAa,MAAM;AACvB,UAAI,SAAS,MAAM;AACnB,UAAI,aAAa,OAAO,cAAc,CAAC;AAEvC,UAAI,iBAAiB,MAAM,qBAAqB;AAChD,UAAI,YAAY,MAAM,gBAAgB,EAAE,MAAM;AAC9C,2BAAa,eAAe,WAAW,WAAW,cAAc;AAChE,UAAI,gBAAgB;AAClB,2BAAmB,kBAAkB,cAAc;AAAA,MACrD,OAAO;AAEL,2BAAmB,IAAI,mBAAmB,IAAI,mBAAmB,WAAW,mBAAmB,UAAU,mBAAmB,UAAU;AACtI,2BAAmB,SAAS,mBAAmB,SAAS;AAAA,MAC1D;AACA,yBAAmB,WAAW,gBAAgB,mBAAmB,QAAQ;AACzE,UAAI,OAAO,MAAM;AACjB,UAAI;AACJ,UAAI,MAAM;AACR,mBAAW,KAAK,gBAAgB,EAAE,MAAM;AACxC,YAAI,YAAY,KAAK,qBAAqB;AAC1C,6BAAa,eAAe,UAAU,UAAU,SAAS;AAAA,MAC3D;AACA,UAAI,aAAa,YAAY,KAAK,iBAAiB;AACnD,WAAK,WAAW,KAAK;AAAA,QACnB;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,QACtB,MAAM;AAAA,QACN;AAAA;AAAA;AAAA,QAGA,UAAU,WAAW,SAAS,QAAQ,SAAS,SAAS;AAAA;AAAA;AAAA,QAGxD,aAAa;AAAA,UACX,QAAQ,MAAM;AAAA,UACd,kBAAkB,cAAc,WAAW;AAAA,UAC3C,GAAG,mBAAmB;AAAA,UACtB,GAAG,mBAAmB;AAAA,UACtB,QAAQ,mBAAmB;AAAA,UAC3B,QAAQ,mBAAmB;AAAA,UAC3B,UAAU,mBAAmB;AAAA,UAC7B,OAAO;AAAA,YACL,GAAG,WAAW;AAAA,YACd,GAAG,WAAW;AAAA,YACd,OAAO,WAAW;AAAA,YAClB,eAAe,WAAW;AAAA,YAC1B,OAAO,WAAW;AAAA,YAClB,QAAQ,WAAW;AAAA,YACnB,UAAU,WAAW;AAAA,UACvB;AAAA,UACA,QAAQ,MAAM;AAAA,UACd,aAAa,WAAW;AAAA,UACxB,aAAa,WAAW;AAAA,QAC1B;AAAA,MACF,CAAC;AAAA,IACH;AACA,IAAAA,cAAa,UAAU,oBAAoB,SAAU,WAAW;AAC9D,UAAI,QAAQ;AACZ,WAAK,eAAe,KAAK,SAAS;AAClC,UAAI,cAAc,UAAU;AAC5B,UAAI,eAAe,YAAY,IAAI,aAAa;AAIhD,UAAI,EAAE,WAAW,YAAY,KAAK,KAAK,YAAY,EAAE,SAAS;AAC5D;AAAA,MACF;AACA,gBAAU,MAAM,SAAS,SAAU,OAAO;AACxC,YAAI,MAAM,QAAQ;AAChB,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,MAAM,eAAe;AAClC,YAAI,SAAS,UAAU,KAAK;AAE5B,YAAI,UAAU,CAAC,OAAO,oBAAoB;AACxC,gBAAM,UAAU,OAAO,WAAW,OAAO,UAAU,aAAa,QAAQ,YAAY;AAAA,QACtF;AAAA,MACF,CAAC;AAAA,IACH;AACA,IAAAA,cAAa,UAAU,qBAAqB,SAAU,KAAK;AACzD,UAAI,QAAQ,IAAI,SAAS;AACzB,UAAI,SAAS,IAAI,UAAU;AAC3B,eAAS,kBAAkB,IAAI,gBAAgB;AAC7C,eAAO,WAAY;AACjB,gCAAsB,IAAI,cAAc;AAAA,QAC1C;AAAA,MACF;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,YAAI,YAAY,KAAK,WAAW,CAAC;AACjC,YAAI,QAAQ,UAAU;AACtB,YAAI,SAAS,MAAM;AACnB,YAAI,mBAAmB,UAAU;AACjC,YAAI,eAAe;AAEnB,YAAI,WAAW,UAAU,YAAY,GAAG;AACtC,yBAAe,UAAU,aAAa,4BAA4B,WAAW,MAAM,CAAC;AAAA,QACtF,OAAO;AACL,yBAAe,UAAU;AAAA,QAC3B;AACA,uBAAe,gBAAgB,CAAC;AAChC,kBAAU,uBAAuB;AACjC,YAAI,iBAAiB,KAAK,KAAK;AAG/B,YAAI,QAAQ;AACV,iBAAO,cAAc;AAAA;AAAA,YAEnB,OAAO;AAAA;AAAA,YAEP,UAAU,aAAa,KAAK,QAAQ,aAAa,KAAK,OAAO,OAAO,iBAAiB;AAAA;AAAA,YAErF,UAAU,aAAa,UAAU,OAAO,aAAa,SAAS,iBAAiB,iBAAiB;AAAA,YAChG,QAAQ,CAAC,aAAa,MAAM,GAAG,aAAa,MAAM,CAAC;AAAA,UACrD,CAAC;AAAA,QACH;AACA,YAAI,uBAAuB;AAC3B,YAAI,aAAa,KAAK,MAAM;AAE1B,gBAAM,IAAI,aAAa,aAAa,GAAG,KAAK;AAC5C,gBAAM,SAAS,KAAK,CAAC;AACrB,iCAAuB;AAAA,QACzB,OAAO;AACL,gBAAM,IAAI,iBAAiB;AAC3B,gBAAM,SAAS,KAAK,iBAAiB,MAAM,CAAC;AAAA,QAC9C;AACA,YAAI,aAAa,KAAK,MAAM;AAE1B,gBAAM,IAAI,aAAa,aAAa,GAAG,MAAM;AAC7C,gBAAM,SAAS,KAAK,CAAC;AACrB,iCAAuB;AAAA,QACzB,OAAO;AACL,gBAAM,IAAI,iBAAiB;AAC3B,gBAAM,SAAS,KAAK,iBAAiB,MAAM,CAAC;AAAA,QAC9C;AACA,YAAI,aAAa,iBAAiB;AAChC,cAAI,YAAY,OAAO,iBAAiB;AACxC,cAAI,WAAW;AACb,sBAAU,SAAS;AAAA,cACjB,QAAQ,aAAa;AAAA,YACvB,CAAC;AAED,mCAAuB;AAAA,UACzB;AAAA,QACF;AACA,YAAI,mBAAmB,sBAAsB,KAAK;AAClD,yBAAiB,uBAAuB;AACxC,cAAM,WAAW,aAAa,UAAU,OAAO,aAAa,SAAS,iBAAiB,iBAAiB;AACvG,cAAM,SAAS,iBAAiB;AAChC,cAAM,SAAS,iBAAiB;AAChC,iBAAS,IAAI,GAAG,IAAI,2BAA2B,QAAQ,KAAK;AAC1D,cAAI,MAAM,2BAA2B,CAAC;AACtC,gBAAM,SAAS,KAAK,aAAa,GAAG,KAAK,OAAO,aAAa,GAAG,IAAI,iBAAiB,MAAM,GAAG,CAAC;AAAA,QACjG;AACA,YAAI,aAAa,WAAW;AAC1B,gBAAM,YAAY;AAClB,gBAAM,SAAS;AACf,cAAI,QAAQ;AACV,gBAAI,YAAY,UAAU;AAC1B,gBAAI,UAAU,aAAa,MAAM;AAC/B,kBAAI,OAAO,UAAU,YAAY,QAAQ,UAAU,QAAQ;AAC3D,0BAAY,KAAK,aAAa,UAAU,SAAS;AAAA,YACnD;AACA,kBAAM,GAAG,QAAQ,kBAAkB,QAAQ,UAAU,SAAS,WAAW,CAAC,CAAC;AAAA,UAC7E;AAAA,QACF,OAAO;AAEL,gBAAM,IAAI,MAAM;AAChB,gBAAM,SAAS,iBAAiB;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AACA,IAAAA,cAAa,UAAU,SAAS,SAAU,KAAK;AAC7C,UAAI,QAAQ,IAAI,SAAS;AACzB,UAAI,SAAS,IAAI,UAAU;AAC3B,UAAI,YAAY,kBAAkB,KAAK,UAAU;AACjD,UAAI,uBAAuB,OAAO,WAAW,SAAU,MAAM;AAC3D,eAAO,KAAK,aAAa,gBAAgB;AAAA,MAC3C,CAAC;AACD,UAAI,uBAAuB,OAAO,WAAW,SAAU,MAAM;AAC3D,eAAO,KAAK,aAAa,gBAAgB;AAAA,MAC3C,CAAC;AACD,qBAAe,sBAAsB,GAAG,KAAK;AAC7C,qBAAe,sBAAsB,GAAG,MAAM;AAC9C,UAAI,yBAAyB,OAAO,WAAW,SAAU,MAAM;AAC7D,eAAO,KAAK,aAAa;AAAA,MAC3B,CAAC;AACD,kBAAY,sBAAsB;AAAA,IACpC;AAIA,IAAAA,cAAa,UAAU,uBAAuB,WAAY;AACxD,UAAI,QAAQ;AACZ,WAAK,KAAK,gBAAgB,SAAU,WAAW;AAC7C,YAAI,cAAc,UAAU;AAC5B,YAAI,wBAAwB,UAAU;AACtC,YAAI,mBAAmB,YAAY,mBAAmB;AACtD,kBAAU,MAAM,SAAS,SAAU,OAAO;AACxC,cAAI,MAAM,UAAU,CAAC,MAAM,qBAAqB;AAC9C,mBAAO;AAAA,UACT;AACA,cAAI,uBAAuB,CAAC;AAC5B,cAAI,QAAQ,MAAM,eAAe;AACjC,cAAI,CAAC,wBAAwB,OAAO;AAClC,mCAAuB,sBAAsB,KAAK,EAAE;AAAA,UACtD;AACA,cAAI,sBAAsB;AACxB,kBAAM,iBAAiB,OAAO,WAAW;AAAA,UAC3C;AACA,cAAI,kBAAkB;AACpB,kBAAM,eAAe,OAAO,WAAW;AAAA,UACzC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,IAAAA,cAAa,UAAU,mBAAmB,SAAU,IAAI,aAAa;AAEnE,UAAI,SAAS,GAAG,eAAe;AAE/B,UAAI,SAAS,UAAU,EAAE;AACzB,UAAI,YAAY,OAAO;AAEvB,UAAI,UAAU,aAAa,MAAM;AAC/B,YAAI,OAAO,YAAY,QAAQ,OAAO,QAAQ;AAC9C,YAAI,YAAY,KAAK,aAAa,SAAS;AAC3C,YAAI,eAAe,CAAC;AACpB,YAAI,cAAc,KAAK,cAAc,WAAW,OAAO;AACvD,YAAI,aAAa;AACf,cAAI,aAAa,KAAK,UAAU,UAAU;AAE1C,uBAAa,SAAS,YAAY,UAAU;AAAA,QAC9C;AACA,YAAI,iBAAiB,UAAU,SAAS,WAAW;AACnD,0BAAkB,IAAI,yBAAyB,SAAS,GAAG,YAAY;AACvE,8BAAsB,IAAI,cAAc;AAAA,MAC1C;AAAA,IACF;AACA,IAAAA,cAAa,UAAU,iBAAiB,SAAU,IAAI,aAAa;AACjE,UAAI,SAAS,GAAG,eAAe;AAC/B,UAAI,YAAY,GAAG,iBAAiB;AAEpC,UAAI,WAEA,GAAG,uBAAuB,CAAC,OAAO,UAAU,CAAC,OAAO,aAAa,CAAC,GAAG,yBAAyB,CAAC,iBAAiB,EAAE,IAAI;AACxH,YAAI,cAAc,sBAAsB,MAAM;AAC9C,YAAI,YAAY,YAAY;AAC5B,YAAI,SAAS,UAAU,EAAE;AACzB,YAAI,YAAY,OAAO;AACvB,YAAI,WAAW;AAAA,UACb,GAAG,OAAO;AAAA,UACV,GAAG,OAAO;AAAA,UACV,UAAU,OAAO;AAAA,QACnB;AACA,YAAI,OAAO,YAAY,QAAQ,OAAO,QAAQ;AAC9C,YAAI,CAAC,WAAW;AACd,iBAAO,KAAK,QAAQ;AAEpB,cAAI,CAAC,WAAW,MAAM,EAAE,gBAAgB;AACtC,gBAAI,aAAa,UAAU,OAAO,MAAM,SAAS,CAAC;AAElD,mBAAO,MAAM,UAAU;AACvB,sBAAU,QAAQ;AAAA,cAChB,OAAO;AAAA,gBACL,SAAS;AAAA,cACX;AAAA,YACF,GAAG,aAAa,SAAS;AAAA,UAC3B;AAAA,QACF,OAAO;AACL,iBAAO,KAAK,SAAS;AAErB,cAAI,aAAa,GAAG;AACpB,cAAI,YAAY;AACd,gBAAI,QAAQ,YAAY,QAAQ,KAAK,GAAG;AACtC,qBAAO,KAAK,YAAY,eAAe;AAAA,YACzC;AACA,gBAAI,QAAQ,YAAY,UAAU,KAAK,GAAG;AACxC,qBAAO,KAAK,YAAY,iBAAiB;AAAA,YAC3C;AAAA,UACF;AACA,sBAAY,QAAQ,UAAU,aAAa,SAAS;AAAA,QACtD;AACA,oBAAY,YAAY;AACxB,YAAI,OAAO,OAAO,QAAQ;AACxB,cAAI,eAAe,YAAY,kBAAkB,CAAC;AAClD,yBAAe,cAAc,UAAU,kBAAkB;AACzD,yBAAe,cAAc,OAAO,OAAO,QAAQ,kBAAkB;AAAA,QACvE;AACA,YAAI,OAAO,OAAO,UAAU;AAC1B,cAAI,iBAAiB,YAAY,oBAAoB,CAAC;AACtD,yBAAe,gBAAgB,UAAU,kBAAkB;AAC3D,yBAAe,gBAAgB,OAAO,OAAO,UAAU,kBAAkB;AAAA,QAC3E;AACA,0BAAkB,QAAQ,WAAW,MAAM,aAAa,WAAW;AAAA,MACrE;AACA,UAAI,aAAa,CAAC,UAAU,UAAU,CAAC,UAAU,WAAW;AAC1D,YAAI,cAAc,wBAAwB,SAAS;AACnD,YAAI,YAAY,YAAY;AAC5B,YAAI,YAAY;AAAA,UACd,QAAQ,UAAU,MAAM;AAAA,QAC1B;AACA,YAAI,CAAC,WAAW;AACd,oBAAU,SAAS,SAAS;AAC5B,oBAAU,MAAM,gBAAgB;AAChC,oBAAU,WAAW;AAAA,YACnB,OAAO;AAAA,cACL,eAAe;AAAA,YACjB;AAAA,UACF,GAAG,WAAW;AAAA,QAChB,OAAO;AACL,oBAAU,KAAK;AAAA,YACb,OAAO;AAAA,UACT,CAAC;AACD,sBAAY,WAAW;AAAA,YACrB,OAAO;AAAA,UACT,GAAG,WAAW;AAAA,QAChB;AACA,oBAAY,YAAY;AAAA,MAC1B;AAAA,IACF;AACA,WAAOA;AAAA,EACT,EAAE;AAAA;AACF,IAAO,uBAAQ;;;ACpYf,IAAI,kBAAkB,UAAU;AACzB,SAAS,mBAAmB,WAAW;AAC5C,YAAU,wBAAwB,uBAAuB,SAAU,SAAS,KAAK,QAAQ;AAEvF,QAAI,eAAe,gBAAgB,GAAG,EAAE;AACxC,QAAI,CAAC,cAAc;AACjB,qBAAe,gBAAgB,GAAG,EAAE,eAAe,IAAI,qBAAa;AAAA,IACtE;AACA,iBAAa,YAAY;AAAA,EAC3B,CAAC;AACD,YAAU,wBAAwB,uBAAuB,SAAU,SAAS,KAAK,QAAQ;AACvF,QAAI,eAAe,gBAAgB,GAAG,EAAE;AACxC,WAAO,cAAc,QAAQ,SAAU,QAAQ;AAC7C,mBAAa,kBAAkB,IAAI,qBAAqB,MAAM,CAAC;AAAA,IACjE,CAAC;AACD,iBAAa,mBAAmB,GAAG;AACnC,iBAAa,OAAO,GAAG;AACvB,iBAAa,qBAAqB;AAAA,EACpC,CAAC;AACH;;;ACdA,IAAI,kBAAkB;", "names": ["createTextStyle", "createTextStyle", "util_exports", "Component_default", "keys", "LabelManager"]}