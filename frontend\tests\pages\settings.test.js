import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import Settings from '../../src/pages/Settings/Settings';
import { useUser } from '../../src/hooks/UserHook';
import axiosInstance from '../../src/axios';
import ConfirmModal from "../../src/components/ConfirmModal";
import { ThemeProvider } from '@mui/material';
import theme from '../../src/theme';

const mockConfirmModal = {
    shouldConfirm: true
};

jest.mock('../../src/hooks/UserHook', () => ({
    useUser: jest.fn(),
}));

jest.mock('../../src/axios', () => ({
    patch: jest.fn().mockResolvedValueOnce({}),
}));

jest.mock("../../src/components/ConfirmModal", () => ({
    __esModule: true,
    default: ({ onConfirm, onClose }) => {
        Promise.resolve().then(() => {
            if (mockConfirmModal.shouldConfirm) {
                onConfirm?.();
            } else {
                onClose?.();
            }
        });
        return null;
    }
}));

describe('Settings Component', () => {
    const mockFetchUser = jest.fn();

    beforeEach(() => {
        jest.useFakeTimers();
        jest.clearAllMocks();
        mockConfirmModal.shouldConfirm = true;
    });

    afterEach(() => {
        jest.useRealTimers();
    });


    it('should render the settings page', () => {
        useUser.mockReturnValue({
            user: { email_verification_enabled: false, email: '<EMAIL>' },
            fetchUser: mockFetchUser,
        });

        render(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        expect(screen.getByText(/Profile Settings/i)).toBeInTheDocument();
        expect(screen.getByText(/Update your profile/i)).toBeInTheDocument();
    });

    it('should correctly toggle the two-factor authentication switch', async () => {
        useUser.mockReturnValue({
            user: { email_verification_enabled: false, email: '<EMAIL>' },
            fetchUser: mockFetchUser,
        });

        render(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        const switchElement = screen.getByLabelText(/Enable Two Factor Authentication/i);
        expect(switchElement).not.toBeChecked();

        fireEvent.click(switchElement);

        expect(switchElement).toBeChecked();
    });

    it('should not save changes if user cancels confirmation', async () => {
        useUser.mockReturnValue({
            user: { email_verification_enabled: false, email: '<EMAIL>' },
            fetchUser: mockFetchUser,
        });

        axiosInstance.patch.mockResolvedValueOnce({});

        render(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        const switchElement = screen.getByLabelText(/Enable Two Factor Authentication/i);
        fireEvent.click(switchElement);

        const saveButton = screen.getByText(/Save/i);
        fireEvent.click(saveButton);

        const cancelButton = screen.getByText(/Cancel/i);
        fireEvent.click(cancelButton);

        await waitFor(() => expect(axiosInstance.patch).not.toHaveBeenCalled());
    });

    it('should disable the switch if no email is provided', () => {
        useUser.mockReturnValue({
            user: { email_verification_enabled: false, email: '' },
            fetchUser: mockFetchUser,
        });

        render(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        const switchElement = screen.getByLabelText(/Enable Two Factor Authentication/i);
        expect(switchElement).toBeDisabled();
    });

    it('should cancel changes and revert the switch to its initial state', () => {
        useUser.mockReturnValue({
            user: { email_verification_enabled: true, email: '<EMAIL>' },
            fetchUser: mockFetchUser,
        });

        render(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        const switchElement = screen.getByLabelText(/Enable Two Factor Authentication/i);
        fireEvent.click(switchElement);

        const cancelButton = screen.getByText(/Cancel/i);
        fireEvent.click(cancelButton);

        expect(switchElement).toBeChecked();
    });

    it('should update switch state when user prop changes', async () => {
        const { rerender } = render(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        useUser.mockReturnValue({
            user: { email_verification_enabled: false, email: '<EMAIL>' },
            fetchUser: mockFetchUser,
        });

        rerender(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        const switchElement = screen.getByLabelText(/Enable Two Factor Authentication/i);
        expect(switchElement).not.toBeChecked();

        useUser.mockReturnValue({
            user: { email_verification_enabled: true, email: '<EMAIL>' },
            fetchUser: mockFetchUser,
        });

        rerender(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        expect(switchElement).toBeChecked();
    });

    it('should handle null user state', () => {
        useUser.mockReturnValue({
            user: null,
            fetchUser: mockFetchUser,
        });

        render(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        const switchElement = screen.getByLabelText(/Enable Two Factor Authentication/i);
        expect(switchElement).not.toBeChecked();
        expect(switchElement).toBeDisabled();
    });

    it('should only show action buttons when state is modified', async () => {
        const mockUser = { email_verification_enabled: false, email: '<EMAIL>' };
        useUser.mockReturnValue({
            user: mockUser,
            fetchUser: mockFetchUser,
        });

        render(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        expect(screen.queryByText(/Save/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/Cancel/i)).not.toBeInTheDocument();

        fireEvent.click(screen.getByLabelText(/Enable Two Factor Authentication/i));

        expect(screen.getByText(/Save/i)).toBeInTheDocument();
        expect(screen.getByText(/Cancel/i)).toBeInTheDocument();
    });

    it('should show correct confirmation message for disable action', async () => {
        useUser.mockReturnValue({
            user: { email_verification_enabled: true, email: '<EMAIL>' },
            fetchUser: mockFetchUser,
        });

        render(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        fireEvent.click(screen.getByLabelText(/Enable Two Factor Authentication/i));
        fireEvent.click(screen.getByText('Save'));

        expect(axiosInstance.patch).not.toHaveBeenCalled();
    });

    it('should log confirmation status and handle save flow', async () => {
        useUser.mockReturnValue({
            user: { email_verification_enabled: false, email: '<EMAIL>' },
            fetchUser: mockFetchUser,
        });
        axiosInstance.patch.mockResolvedValueOnce({});

        render(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        fireEvent.click(screen.getByLabelText(/Enable Two Factor Authentication/i));
        fireEvent.click(screen.getByText('Save'));

        await waitFor(() => {
            expect(axiosInstance.patch).toHaveBeenCalledWith(
                '/users/userEmailVerification',
                null,
                expect.any(Object)
            );
        });
    });

    it('should handle cancellation and revert state', async () => {
        mockConfirmModal.shouldConfirm = false;

        const mockUser = {
            email_verification_enabled: false,
            email: '<EMAIL>'
        };
        useUser.mockReturnValue({
            user: mockUser,
            fetchUser: mockFetchUser,
        });

        render(
            <ThemeProvider theme={theme}>
                <Settings />
            </ThemeProvider>
        );

        const switchElement = screen.getByLabelText(/Enable Two Factor Authentication/i);
        fireEvent.click(switchElement);
        expect(switchElement).toBeChecked();

        await act(async () => {
            fireEvent.click(screen.getByText('Save'));
            await Promise.resolve();
            jest.runAllTimers();
        });

        await waitFor(() => {
            expect(switchElement).not.toBeChecked();
            expect(axiosInstance.patch).not.toHaveBeenCalled();
        });
    });
});
