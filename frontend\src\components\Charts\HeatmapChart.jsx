import React from "react";
import ReactEChartsCore from "echarts-for-react/lib/core";
import * as echarts from "echarts/core";
import { HeatmapChart as HeatmapChartType } from "echarts/charts";
import { TooltipComponent, VisualMapComponent, GridComponent } from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
import theme from "../../theme";

// Register the required components
echarts.use([HeatmapChartType, TooltipComponent, VisualMapComponent, GridComponent, CanvasRenderer]);

const HeatmapChart = ({ chartSeries }) => {
    const chartOptions = {
        tooltip: {
            position: "top",
            backgroundColor: "rgba(50, 50, 50, 0.7)",
            textStyle: {
                color: "#FFFFFF",
            },
        },
        grid: {
            height: "50%",
            top: "10%",
        },
        xAxis: {
            type: "category",
            data: chartSeries[0].data.map((item) => item.x),
            splitArea: {
                show: false,
            },
            axisLabel: {
                color: "#FFFFFF",
            },
            axisTick: {
                show: false,
            },
        },
        yAxis: {
            type: "category",
            data: chartSeries.map((series) => series.name),
            splitArea: {
                show: false,
            },
            axisLabel: {
                color: "#FFFFFF",
            },
            axisTick: {
                show: false,
            },
        },
        visualMap: {
            min: 0,
            max: 100,
            calculable: true,
            orient: "horizontal",
            left: "center",
            bottom: "15%",
            inRange: {
                color: ["rgba(79, 89, 104, 0.5)", theme.palette.custom.mainBlue],
            },
        },
        series: [
            {
                name: "",
                type: "heatmap",
                data: chartSeries.flatMap((series, seriesIndex) => series.data.map((item, itemIndex) => [itemIndex, seriesIndex, item.y])),
                label: {
                    show: false,
                    color: "#FFFFFF",
                },
                itemStyle: {
                    borderColor: theme.palette.primary.main,
                    borderWidth: 2,
                    borderRadius: 4,
                },
                emphasis: {
                    focus: "series",
                    label: {
                        show: false,
                    },
                    itemStyle: {
                        shadowBlur: 10,
                        shadowColor: "rgba(0, 0, 0, 0.5)",
                    },
                },
            },
        ],
    };

    return <ReactEChartsCore echarts={echarts} option={chartOptions} style={{ height: 350 }} opts={{ renderer: "canvas" }} />;
};

export default HeatmapChart;
