require("dotenv").config();
const Organization = require("../models/Organization");

const organizations = [
    {
        name: "Quartermaster",
        domain: "quartermaster.us",
    },
    {
        name: "Mail Mil",
        domain: "mail.mil",
    },
    {
        name: "USCG",
        domain: "uscg.mil",
    },
    {
        name: "Tesla",
        domain: "teslagovernment.com",
    },
    {
        name: "Plantir",
        domain: "palantir.com",
    },
    {
        name: "Honey Badger Labs",
        domain: "honeybadgerlabs.co",
    },
    {
        name: "MSP",
        domain: "msp.go.cr",
    },
    {
        name: "Forcoda",
        domain: "forcoda.com",
    },
];

async function addOrganizations() {
    try {
        for (const org of organizations) {
            const existingOrg = await Organization.findOne({ domain: org.domain });
            if (existingOrg) {
                console.log(`Organization with domain '${org.domain}' already exists.`);
                continue;
            }

            const newOrg = await Organization.create({
                name: org.name,
                domain: org.domain,
            });

            console.log(`Organization '${newOrg.name}' added successfully.`);
        }

        console.log("All organizations added successfully.");
    } catch (err) {
        console.error("Error adding organizations:", err);
        process.exit(0);
    }
}

addOrganizations();
