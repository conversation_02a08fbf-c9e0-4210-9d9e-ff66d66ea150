import React, { useEffect, useState } from "react";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { SortableContext, arrayMove } from "@dnd-kit/sortable";
import { Grid, Button } from "@mui/material";
import SortableMosaicItem from "./SortableMosaicItem";

const MosaicReorder = ({ streams, onSave }) => {
    const [streamList, setStreamList] = useState(streams);
    const [disable, setDisable] = useState(true);

    useEffect(() => {
        setStreamList(streams);
    }, [streams]);

    const handleDragEnd = ({ active, over }) => {
        if (!over || active.id === over.id) return;
        const oldIndex = streamList.findIndex((stream) => stream.StreamName === active.id);
        const newIndex = streamList.findIndex((stream) => stream.StreamName === over.id);
        setStreamList(arrayMove(streamList, oldIndex, newIndex));
        setDisable(false);
    };

    const handleSave = () => {
        onSave(streamList);
        setDisable(true);
    };

    return (
        <Grid>
            <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                <SortableContext items={streamList.map((stream) => stream.StreamName)}>
                    {streamList.map((stream) => (
                        <SortableMosaicItem key={stream.StreamName} stream={stream} />
                    ))}
                </SortableContext>
            </DndContext>
            <Grid>
                <Button onClick={handleSave} variant="contained" disabled={disable}>
                    Save Order
                </Button>
            </Grid>
        </Grid>
    );
};

export default MosaicReorder;
