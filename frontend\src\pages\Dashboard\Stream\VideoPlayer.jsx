import { useEffect, useRef, useState } from "react";
import { MediaPlayer as DashJSMediaPlayer } from "dashjs";
import { Box, Grid, IconButton, Slider, Tooltip, Typography, alpha, CircularProgress } from "@mui/material";
import { Fullscreen, Pause, PlayArrow } from "@mui/icons-material";
import { useApp } from "../../../hooks/AppHook";
import { useLocation } from "react-router-dom";
import SensorDetails from "./SensorDetails";
import dayjs from "dayjs";
import theme from "../../../theme";
import { userValues } from "../../../utils.js";
import { useUser } from "../../../hooks/UserHook.jsx";

const seaTheme = () => {
    return {
        playerWrapper: {
            position: "relative",
            backgroundColor: "#000000",
            overflow: "hidden",
            width: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
        },
        controlsWrapper: {
            overflow: "hidden",
            background: "#FFFFFF4D",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            paddingX: 1,
        },
        playButton: {
            color: "white",
            borderRadius: 0,
            "&:hover": {
                color: theme.palette.custom.mainBlue,
                backgroundColor: "#FFFFFF",
            },
        },
        progressBar: {
            flexGrow: 1,
            height: "100%",
        },
        sliderStyles: {
            color: theme.palette.custom.mainBlue,
            padding: "4px 0",
            height: "100%",
            "& .MuiSlider-thumb": {
                width: 8,
                height: "100%",
                borderRadius: 0,
                backgroundColor: "#FFFFFF",
                transform: "translate(-50%, -65%)",
                "&:hover, &.Mui-focusVisible": {
                    boxShadow: "none",
                },
            },
            "& .MuiSlider-track": {
                height: "100%",
                borderRadius: 0,
                color: theme.palette.custom.mainBlue,
            },
            "& .MuiSlider-rail": {
                height: "100%",
                borderRadius: 0,
                opacity: 0.28,
                backgroundColor: "#FFFFFF4D",
            },
            "& .MuiSlider-mark": { height: "100%", width: 4 },
        },
        fullscreenButton: {
            color: "white",
            borderRadius: 0,
            "&:hover": {
                color: theme.palette.custom.mainBlue,
                backgroundColor: "#FFFFFF",
            },
        },
        timeDisplay: {
            fontSize: "0.8rem",
            color: "white",
            marginLeft: 1,
            marginRight: 1,
        },
        loadingIndicator: {
            position: "absolute",
            inset: 0, // covers the parent
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: theme.palette.custom.mainBlue,
        },
        centerPlayButton: {
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            backgroundColor: alpha(theme.palette.custom.mainBlue, 0.7),
            color: "white",
            width: 80,
            height: 80,
            "&:hover": {
                backgroundColor: theme.palette.custom.mainBlue,
            },
            boxShadow: "0px 0px 15px rgba(0, 0, 0, 0.3)",
        },
    };
};

const VideoPlayer = ({
    streamUrl,
    setStreamUrl,
    streamMode,
    totalDuration,
    playBack,
    setPlayBack,
    setScrubBarSlotInterval,
    selectedStream,
    referenceTime,
    // handleReplay,
    view = "Single",
    sharedPlayback,
    lockSlider,
    hlsUrlError,
    marks,
    setTimestamp,
}) => {
    const { isTabActive, timezone } = useApp();
    const { user } = useUser();
    const videoNode = useRef(null);
    const videoContainerRef = useRef(null);
    const player = useRef(null);
    const [isPlayingVideo, setIsPlayingVideo] = useState(true);
    const [hoverTime, setHoverTime] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const { pathname } = useLocation();
    const scrubBarRef = useRef();
    const allowDelay = 4;
    const [scrubBarValue, setScrubBarValue] = useState(0);

    const resetPlayer = (resetHlsUrl = true) => {
        if (player.current) {
            player.current.reset();
            player.current = null;
            if (resetHlsUrl) setStreamUrl("");
        }
    };
    const seaThemeStyles = seaTheme();
    const initializePlayer = () => {
        if (videoNode.current && streamUrl) {
            setIsLoading(true);
            player.current = DashJSMediaPlayer().create();
            player.current.initialize(videoNode.current, streamUrl, true);
            player.current.setAutoPlay(true);
            player.current.updateSettings({
                streaming: {
                    delay: {
                        liveDelay: allowDelay,
                    },
                    liveCatchup: {
                        maxDrift: 0,
                        playbackRate: {
                            max: 1,
                            min: -0.5,
                        },
                    },
                },
            });

            player.current.on(DashJSMediaPlayer.events.PLAYBACK_STARTED, () => {
                setTimeout(() => {
                    setIsPlayingVideo(true);
                }, 200);
            });

            player.current.on(DashJSMediaPlayer.events.PLAYBACK_PAUSED, () => {
                setIsPlayingVideo(false);
            });

            player.current.on(DashJSMediaPlayer.events.BUFFER_LEVEL_STATE_CHANGED, function (e) {
                if (e.state === "bufferLoaded") {
                    setIsLoading(false);
                } else if (e.state === "bufferStalled") {
                    setIsLoading(true);
                } else if (e.state === "bufferEmpty") {
                    setIsLoading(true);
                }
            });

            // player.current.on(DashJSMediaPlayer.events.ERROR, (error) => {
            //     if (error.error.code === 25 || error.error.code === 27) {
            //         handleReplay({ title: "Last 1 Hour", interval: 60, time: 60, tag: "1H" });
            //     }
            // });
        }
    };

    const handleHover = (event) => {
        const scrubBar = scrubBarRef.current;
        if (!scrubBar) return;

        const rect = scrubBar.getBoundingClientRect();
        const relativeX = event.clientX - rect.left;
        const clampedX = Math.max(0, Math.min(relativeX, rect.width));
        const hoveredTime = (clampedX / rect.width) * totalDuration;

        setHoverTime(hoveredTime);
    };

    const handlePause = () => {
        if (lockSlider) return;
        if (player.current) {
            if (!player.current.isPaused()) {
                player.current.pause();
                setIsPlayingVideo(false);
            } else {
                // On play, for LIVE mode, check latency and seek if needed
                if (streamMode === "LIVE") {
                    const currentLatency = player.current.getCurrentLiveLatency?.() || player.current.getLiveLatency?.();
                    if (currentLatency && currentLatency > allowDelay) {
                        player.current.seek(player.current.duration() - (currentLatency - allowDelay));
                    }
                }
                player.current.play();
                setIsPlayingVideo(true);
            }
        }
    };

    const handleFullscreen = () => {
        if (videoContainerRef.current) {
            if (!document.fullscreenElement) {
                videoContainerRef.current.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
    };

    const formatTime = (user, timeInSeconds) => {
        const startDate = dayjs(referenceTime.current - totalDuration * 1000).tz(timezone);
        const pastDate = startDate.add(timeInSeconds, "second");
        const formattedDate = pastDate.format(userValues.dateTimeFormat(user));
        return (
            <Grid sx={{ textAlign: "center" }}>
                <Grid>({formattedDate})</Grid>
            </Grid>
        );
    };

    const formatTimePart = (part) => part.toString().padStart(2, "0");

    const formatTimeStr = (millis) => {
        const days = Math.floor(millis / 86400);
        const hours = Math.floor((millis % 86400) / 3600);
        const minutes = Math.floor((millis % 3600) / 60);
        const seconds = Math.floor(millis % 60);

        let res = `${formatTimePart(minutes)}:${formatTimePart(seconds)}`;

        if (hours > 0) {
            res = `${formatTimePart(hours)}:${res}`;
        }

        if (days > 0) {
            res = `${formatTimePart(days)}:${res}`;
        }

        return res;
    };

    // Format current time in MM:SS format - updated to reflect actual position in the buffer
    const formatCurrentTime = () => {
        const actualPosition = playBack.offset + playBack.currentVideoPlayTime;
        return formatTimeStr(actualPosition);
    };

    // Format total duration in MM:SS format
    const formatDuration = () => {
        return formatTimeStr(totalDuration);
    };
    const handleScrub = (event, newValue) => {
        setScrubBarValue(newValue);
    };
    const handleScrubCommitted = (event, newValue) => {
        const video = videoNode.current;
        if (video) {
            const clickedTime = (newValue / 100) * totalDuration;
            setPlayBack(() => ({
                offset: clickedTime,
                currentVideoPlayTime: 0,
                latency: 0,
            }));
            // setScrubBarSlotInterval(totalDuration / 60 - clickedTime / 60);
            const startDate = dayjs(referenceTime.current - totalDuration * 1000).tz(timezone);
            const pastDate = startDate.add(hoverTime, "second");
            // ISO string with timezone offset
            // const isoDateWithOffset = pastDate.format(); // e.g. "2025-07-24T15:30:00+05:30"
            // ISO string in UTC
            const isoDateUTC = pastDate.toISOString();
            setTimestamp({ current: isoDateUTC, scrubBarSlotInterval: isoDateUTC });
            setScrubBarSlotInterval(isoDateUTC);
        }
    };

    const handleTimeUpdate = () => {
        const video = videoNode.current;
        if (video) {
            const currentTime = video.currentTime;
            setTimestamp((prev) => {
                if (prev) {
                    const startDate = dayjs(prev.scrubBarSlotInterval).tz(timezone);
                    const pastDate = startDate.add(currentTime + 2, "second");
                    const isoDateUTC = pastDate.toISOString();
                    return { current: isoDateUTC, scrubBarSlotInterval: prev?.scrubBarSlotInterval };
                }
            });
            setPlayBack((prev) => ({
                offset: prev.offset,
                currentVideoPlayTime: currentTime,
                latency: player.current && (player.current.getCurrentLiveLatency?.() || player.current.getLiveLatency?.()),
            }));
        }
    };

    useEffect(() => {
        const video = videoNode.current;
        if (video && streamMode === "ON_DEMAND" && playBack.currentVideoPlayTime + playBack.offset >= totalDuration) {
            video.pause();
            setIsPlayingVideo(false);
        }
    }, [playBack.currentVideoPlayTime, playBack.offset, totalDuration, streamMode]);

    useEffect(() => {
        if (hlsUrlError && hlsUrlError === "No streams found in the specified timestamp range.") {
            setIsLoading(false);
            if (player.current) {
                player.current.reset();
                player.current = null;
            }
        }
    }, [hlsUrlError]);
    useEffect(() => {
        const video = videoNode.current;
        if (video) {
            video.addEventListener("timeupdate", handleTimeUpdate);
        }
        return () => {
            if (video) {
                video.removeEventListener("timeupdate", handleTimeUpdate);
            }
        };
    }, [streamMode]);

    useEffect(() => {
        if (!pathname.includes("/stream") || !isTabActive) {
            resetPlayer(false);
            return;
        }
        if (streamUrl && videoNode.current) {
            if (player.current) {
                setIsLoading(true);
            }
            resetPlayer(false);
            initializePlayer();
        }
    }, [streamUrl, videoNode.current, pathname, isTabActive]);

    useEffect(() => {
        const playerCurr = player.current;

        return () => {
            if (playerCurr) {
                playerCurr.reset();
                player.current = null;
            }
        };
    }, []);

    useEffect(() => {
        if (streamUrl && videoNode.current) {
            if (player.current) {
                // Reinitialize the player with the new stream URL
                player.current.attachSource(streamUrl);
                player.current.setAutoPlay(true);
            } else {
                initializePlayer();
            }
        }
    }, [streamUrl]);

    useEffect(() => {
        if (sharedPlayback) {
            if (sharedPlayback.isPaused) {
                player.current?.pause();
            } else {
                player.current?.play();
            }
        }
    }, [sharedPlayback]);
    useEffect(() => {
        const currentPositionPercent = ((playBack.offset + playBack.currentVideoPlayTime) / totalDuration) * 100;
        setScrubBarValue(currentPositionPercent);
    }, [playBack.offset, totalDuration]);
    // Calculate the current position as a percentage for the slider
    // const currentPositionPercent = ((playBack.offset + playBack.currentVideoPlayTime) / totalDuration) * 100;
    const endTime = Date.now();
    const startTime = endTime - totalDuration * 1000;
    const computedMarks = marks?.map((mark) => {
        const artifactTime = new Date(mark.timestamp).getTime();
        const secondsFromStart = (artifactTime - startTime) / 1000;
        // Map seconds into percentage relative to totalDuration (0 to 100)
        const percentageValue = (secondsFromStart / totalDuration) * 100;
        return { value: percentageValue };
        // label: mark.count.toString()
    });

    return (
        <Grid
            container
            minHeight={{ xs: 300, lg: "auto" }}
            flexDirection={{ xs: "column", lg: "column" }}
            sx={{ height: "100%" }}
            ref={videoContainerRef}
        >
            <Grid container flexDirection={"column"} size={"grow"} sx={{ ...seaThemeStyles.playerWrapper }} onDoubleClick={handleFullscreen}>
                <video
                    ref={videoNode}
                    id="video"
                    preload="auto"
                    autoPlay
                    muted
                    style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "contain", // Ensures it fits nicely
                    }}
                    onClick={handlePause}
                />
                {hlsUrlError && hlsUrlError === "No streams found in the specified timestamp range." ? (
                    <Box
                        sx={{
                            position: "absolute",
                            top: 0,
                            right: 0,
                            bottom: 0,
                            left: 0,
                            width: "fit-content",
                            height: "fit-content",
                            margin: "auto",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <Typography sx={{ textAlign: "center", color: "white", fontSize: "1.5rem" }}>{hlsUrlError}</Typography>
                    </Box>
                ) : (
                    isLoading && (
                        <Box sx={seaThemeStyles.loadingIndicator}>
                            <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={50} />
                        </Box>
                    )
                )}

                {!isPlayingVideo && !isLoading && (
                    <IconButton
                        sx={{
                            ...seaThemeStyles.centerPlayButton,
                            opacity: 1,
                            transition: "opacity 0.3s",
                        }}
                        onClick={handlePause}
                        aria-label="play video"
                    >
                        <PlayArrow sx={{ fontSize: 32 }} />
                    </IconButton>
                )}
            </Grid>
            <Grid
                sx={{
                    ...seaThemeStyles.controlsWrapper,
                    display: lockSlider ? "none" : "flex",
                }}
                size="auto"
            >
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        width: "100%",
                        height: "100%",
                    }}
                >
                    <IconButton onClick={handlePause} sx={seaThemeStyles.playButton}>
                        {isPlayingVideo ? <Pause sx={{ fontSize: 20 }} /> : <PlayArrow sx={{ fontSize: 20 }} />}
                    </IconButton>

                    <Box
                        ref={scrubBarRef}
                        sx={{
                            ...seaThemeStyles.progressBar,
                            flexGrow: 1,
                            mx: 0,
                        }}
                        onMouseMove={streamMode === "ON_DEMAND" ? handleHover : undefined}
                        onMouseLeave={() => setHoverTime(null)}
                    >
                        {streamMode === "ON_DEMAND" && (
                            <Tooltip
                                open={hoverTime !== null}
                                title={formatTime(user, hoverTime || 0)}
                                placement="top"
                                arrow
                                followCursor
                                PopperProps={{
                                    container: videoContainerRef.current,
                                }}
                            >
                                <Slider
                                    value={scrubBarValue}
                                    onChange={handleScrub}
                                    onChangeCommitted={handleScrubCommitted}
                                    sx={seaThemeStyles.sliderStyles}
                                    marks={computedMarks}
                                />
                            </Tooltip>
                        )}
                    </Box>

                    {(streamMode === "LIVE" || streamMode === "ON_DEMAND") && (
                        <Box sx={{ display: "flex", justifyContent: "center" }}>
                            {streamMode === "LIVE" && (
                                <Typography
                                    sx={{
                                        ...seaThemeStyles.timeDisplay,
                                        color: theme.palette.custom.mainBlue,
                                        fontWeight: "bold",
                                    }}
                                >
                                    LIVE
                                </Typography>
                            )}

                            {streamMode === "ON_DEMAND" && (
                                <Typography sx={seaThemeStyles.timeDisplay}>
                                    {formatCurrentTime()} / {formatDuration()}
                                </Typography>
                            )}
                        </Box>
                    )}

                    <IconButton onClick={handleFullscreen} sx={seaThemeStyles.fullscreenButton}>
                        <Fullscreen sx={{ fontSize: 20 }} />
                    </IconButton>
                </Box>
            </Grid>
            {streamMode !== "ON_DEMAND" && (
                <Grid position={"absolute"} bottom={45} right={10} display={{ xs: "none", lg: view === "Mosaic" ? "none" : "block" }}>
                    <Grid
                        container
                        sx={{
                            backgroundColor: (theme) => alpha(theme.palette.primary.light, 0.5),
                            borderRadius: "20px",
                            width: "auto",
                            padding: "25px 20px",
                            minWidth: "350px",
                        }}
                    >
                        <Grid
                            container
                            width={"100%"}
                            flexDirection={"column"}
                            spacing={1}
                            color={"primary.contrastText"}
                            className="dashboard-step-6"
                        >
                            <Grid>
                                <Typography fontSize={"14px"} fontWeight={"600"}>
                                    Details
                                </Typography>
                            </Grid>
                            <Grid>
                                <SensorDetails selectedStream={selectedStream} />
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            )}
        </Grid>
    );
};

export default VideoPlayer;
