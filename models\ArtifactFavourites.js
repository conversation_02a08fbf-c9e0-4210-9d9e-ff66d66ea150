const mongoose = require("mongoose");
const db = require("../modules/db");
const User = require("./User");
const ioEmitter = require("../modules/ioEmitter");
const schema = mongoose.Schema;
const ArtifactFavouritesSchema = new schema(
    {
        user_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: User,
            required: true,
        },
        artifact_id: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
    },
    {
        timestamps: true,
    },
);

ArtifactFavouritesSchema.post("save", emitChangedEvent);
ArtifactFavouritesSchema.post("findOneAndDelete", emitChangedEvent);
function emitChangedEvent(favourite) {
    if (!favourite) return;
    ioEmitter.emit("notifyAll", { name: `favourites/changed`, data: favourite.toObject() });
}

const ArtifactFavourites = db.qm.model("ArtifactFavourites", ArtifactFavouritesSchema, "artifact_favourites");

module.exports = ArtifactFavourites;
module.exports.emitChangedEvent = process.env.NODE_ENV === "test" && emitChangedEvent;
