import React, { useEffect, useMemo, useState } from "react";
import { UserProvider } from "../../../providers/UserProvider";
import dayjs from "dayjs";
import { defaultValues, handleVesselTimezone, simplifyTimezone, permissions, displayCoordinates } from "../../../utils";
import s3Controller from "../../../controllers/S3.controller";
import { Grid, Typography, IconButton, Skeleton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import PreviewMedia from "../../../components/PreviewMedia";
import favouriteArtifactsController from "../../../controllers/FavouriteArtifacts.controller";
import { getSocket } from "../../../socket";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";

const ClusterInfoWindow = ({ markers, currentClusterInfoWindow, vesselInfo, user }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [archivedIds, setArchivedIds] = useState([]);
    const artifacts = markers.map((marker) => marker.artifactData).filter((artifact) => !archivedIds.includes(artifact._id));

    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);
    const artifact = artifacts[currentIndex];
    const [src, setSrc] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);
    const [loading, setLoading] = useState(true);
    const [favouriteArtifacts, setFavouriteArtifacts] = useState([]);
    const [modalExapand, setModalExpand] = useState(false);
    const [flaggedArtifact, setFlaggedArtifact] = useState(false);

    useEffect(() => {
        if (artifact) {
            // setLoading(true);
            setSrc(s3Controller.fetchUrl(artifact, artifact.video_path ? "video" : "image"));
            setThumbnail(s3Controller.fetchPreviewUrl(artifact));
            setLoading(false);
            setModalExpand(false);
        }
    }, [artifact]);
    const vesselTimezone = useMemo(() => {
        const timezone = handleVesselTimezone(artifact, vesselInfo);
        return timezone;
    }, [artifact, vesselInfo]);

    const handleNavigation = (direction) => {
        setLoading(true);
        setCurrentIndex((prevIndex) => {
            if (direction === "prev") {
                return prevIndex > 0 ? prevIndex - 1 : artifacts.length - 1;
            } else {
                return (prevIndex + 1) % artifacts.length;
            }
        });
    };

    const handleClose = () => {
        currentClusterInfoWindow.close();
    };

    const fetchFavouriteArtifacts = async () => {
        const { favourites } = await favouriteArtifactsController.getUserFavouriteArtifacts();
        setFavouriteArtifacts(favourites);
    };
    const fetchUserFlaggedArtifacts = async () => {
        await artifactFlagController.getUserFlaggedArtifactIds();
        setFlaggedArtifact(artifactFlagController.isArtifactFlaggedByUser(artifact._id));
    };

    useEffect(() => {
        fetchFavouriteArtifacts();
        fetchUserFlaggedArtifacts();
    }, [currentIndex]);

    const showClick = () => {
        setModalExpand((prev) => !prev);
    };

    useEffect(() => {
        const socket = getSocket();
        const handleLocalArchive = (e) => {
            const updatedArtifact = e?.artifact;
            if (!updatedArtifact) return;
            setArchivedIds((prev) => [...prev, updatedArtifact._id]);
        };
        socket.on("artifact/changed", handleLocalArchive);
        socket.on("artifacts_flagged/changed", fetchUserFlaggedArtifacts);
        return () => {
            socket.off("artifact/changed", handleLocalArchive);
            socket.off("artifacts_flagged/changed", fetchUserFlaggedArtifacts);
        };
    }, []);

    return (
        <Grid
            container
            direction="column"
            style={{ color: "white", padding: "20px", background: "#343B44", maxWidth: "330px", height: modalExapand ? "auto" : "570px" }}
        >
            <style>
                {`
                    .gm-style-iw-chr, .gm-style-iw-tc {
                        display: none !important;
                    }
                    .gm-style .gm-style-iw-c {
                        background-color: #343B44 !important;
                        outline: none;
                        padding: 0;
                    }
                    .gm-style .gm-style-iw-d {
                        overflow: auto !important;
                    }
                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {
                        background-color: #fff !important;
                    }
                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {
                        background: #343B44 !important;
                    }
                `}
            </style>
            <Grid sx={{ height: modalExapand ? "auto" : "230px" }}>
                <Grid container justifyContent="space-between" alignItems="center" style={{ marginBottom: "10px" }}>
                    <Typography variant="h6">Artifact {artifacts.length > 1 ? `${currentIndex + 1} / ${artifacts.length}` : ""}</Typography>
                    <IconButton
                        onClick={handleClose}
                        sx={{
                            color: "white",
                            border: "1px solid white",
                            "&:hover": {
                                backgroundColor: "white",
                                color: "#4F5968",
                            },
                        }}
                    >
                        <CloseIcon sx={{ fontSize: "16px" }} />
                    </IconButton>
                </Grid>
                {/* Image/Video Section */}
                <Grid
                    sx={{
                        position: "relative",
                        backgroundColor: "#343B44",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        height: 200,
                        borderRadius: 1,
                    }}
                >
                    {loading ? (
                        <Skeleton
                            variant="rectangular"
                            width="100%"
                            height="100%"
                            sx={{
                                borderRadius: 1,
                                minHeight: 200,
                                minWidth: 290,
                            }}
                        />
                    ) : (
                        <PreviewMedia
                            thumbnailLink={thumbnail}
                            originalLink={src}
                            cardId={artifact._id}
                            isImage={!artifact.video_path}
                            style={{ borderRadius: 8 }}
                            favouriteArtifacts={favouriteArtifacts}
                            showFullscreenIcon={true}
                            showFullscreenIconForMap={!artifact.video_path}
                            // userTest={user}
                            showVideoThumbnail={artifact.video_path}
                            showArchiveButton={hasManageArtifacts}
                            isArchived={artifact.is_archived}
                            vesselId={artifact?.onboard_vessel_id}
                            skeletonStyle={{
                                minHeight: 200,
                                minWidth: 290,
                            }}
                            flaggedArtifact={flaggedArtifact}
                        />
                    )}
                </Grid>
            </Grid>
            <Grid sx={{ height: modalExapand ? "auto" : "265px", display: "flex", flexDirection: "column" }}>
                <Typography sx={{ marginTop: "20px" }}>
                    <strong>Super Category</strong>: {artifact.super_category || "Not available"}
                </Typography>
                <Typography>
                    <strong>Category</strong>: {artifact.category || "Not available"}
                </Typography>
                <Typography>
                    <strong>Size</strong>: {artifact.size || "Not available"}
                </Typography>
                <Typography>
                    <strong>Color</strong>: {artifact.color || "Not available"}
                </Typography>
                <Typography>
                    <strong>Location:</strong> {displayCoordinates([artifact.lng, artifact.lat], !!user?.use_MGRS)}
                    <br />
                </Typography>
                <Typography>
                    <strong>Timestamp</strong>:{" "}
                    {artifact.timestamp ? dayjs(artifact.timestamp).tz(vesselTimezone).format(defaultValues.dateTimeFormat()) : "Not available"}{" "}
                    {vesselTimezone && simplifyTimezone(vesselTimezone)}
                </Typography>
                {artifact.others ? (
                    <Typography sx={{ textTransform: "lowercase" }}>
                        {artifact.others && (modalExapand || artifact.others.length <= 90 ? artifact.others : artifact.others.slice(0, 90) + "...")}
                        {artifact.others.length > 90 && (
                            <span onClick={showClick} style={{ color: "#007bff", cursor: "pointer", marginLeft: "5px" }}>
                                {modalExapand ? "Show Less" : "Read More"}
                            </span>
                        )}
                    </Typography>
                ) : (
                    <Typography>{"Not available"}</Typography>
                )}
            </Grid>
            <Grid sx={{ height: modalExapand ? "auto" : "35px" }}>
                {artifacts.length > 1 && (
                    <Grid container justifyContent="space-between" sx={{ marginBottom: "1px" }}>
                        <IconButton
                            onClick={() => handleNavigation("prev")}
                            sx={{
                                color: "white",
                                fontSize: "24px",
                                fontWeight: "bold",
                                "&:hover": {
                                    backgroundColor: "white",
                                    color: "#4F5968",
                                },
                            }}
                        >
                            <ChevronLeftIcon sx={{ fontSize: "24px" }} />
                        </IconButton>
                        <IconButton
                            onClick={() => handleNavigation("next")}
                            sx={{
                                color: "white",
                                fontWeight: "bold",
                                "&:hover": {
                                    backgroundColor: "white",
                                    color: "#4F5968",
                                },
                            }}
                        >
                            <KeyboardArrowRightIcon sx={{ fontSize: "24px" }} />
                        </IconButton>
                    </Grid>
                )}
            </Grid>
        </Grid>
    );
};

const WrappedClusterInfoWindow = (props) => {
    return (
        <UserProvider>
            <ClusterInfoWindow {...props} />
        </UserProvider>
    );
};

export default WrappedClusterInfoWindow;
