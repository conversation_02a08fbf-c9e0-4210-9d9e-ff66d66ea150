import globals from "globals";
import pluginJs from "@eslint/js";
import prettier from "eslint-plugin-prettier";
import prettierConfig from "eslint-config-prettier";
import unusedImports from "eslint-plugin-unused-imports";

/** @type {import('eslint').Linter.Config[]} */
export default [
    { files: ["**/*.js"], languageOptions: { sourceType: "commonjs" } },
    {
        languageOptions: { globals: { ...globals.node, ...globals.browser } },
        plugins: {
            prettier,
            "unused-imports": unusedImports,
        },
        rules: {
            "no-unused-vars": "error",
            "unused-imports/no-unused-imports": "error",
            "prettier/prettier": [
                "error",
                {
                    endOfLine: "auto",
                    semi: true,
                    trailingComma: "all",
                    arrowParens: "always",
                    printWidth: 150,
                    tabWidth: 4,
                },
            ],
        },
        settings: {
            prettier: prettierConfig,
        },
    },
    pluginJs.configs.recommended,
    {
        ignores: ["node_modules/", ".github/", "tests/", "coverage/", "frontend/", "*.config.js"],
    },
];
