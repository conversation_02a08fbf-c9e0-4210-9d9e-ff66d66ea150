import gps_socket from '../src/gps_socket';

jest.mock('socket.io-client', () => ({
    io: jest.fn(() => ({
        on: jest.fn(),
        emit: jest.fn(),
        connect: jest.fn(),
        disconnect: jest.fn(),
    })),
}));

describe('Mock gps_socket usage', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should call "on" method when listening to events', () => {
        gps_socket.on('test-event', jest.fn());
        expect(gps_socket.on).toHaveBeenCalledWith('test-event', expect.any(Function));
    });

    it('should call "emit" method when sending events', () => {
        gps_socket.emit('test-event', { key: 'value' });
        expect(gps_socket.emit).toHaveBeenCalledWith('test-event', { key: 'value' });
    });

    it('should call "connect" method', () => {
        gps_socket.connect();
        expect(gps_socket.connect).toHaveBeenCalled();
    });

    it('should call "disconnect" method', () => {
        gps_socket.disconnect();
        expect(gps_socket.disconnect).toHaveBeenCalled();
    });
});
