require("dotenv").config();
const mongoose = require("mongoose");
const RegionGroup = require("../models/RegionGroup");
const Vessel = require("../models/Vessel");

async function updateVesselsRegionGroup() {
    try {
        // Fetch all region groups & vessels as plain JS objects
        const regionGroups = await RegionGroup.find().lean();
        console.log(`Found ${regionGroups.length} region groups.`);

        const vessels = await Vessel.find().lean();
        console.log(`Found ${vessels.length} vessels.`);

        for (const vessel of vessels) {
            const vesselIdStr = vessel._id.toString();

            const matchingRegionGroup = regionGroups.find((rg) => (rg.vessel_ids || []).some((id) => id && id.toString() === vesselIdStr));

            if (matchingRegionGroup) {
                await Vessel.updateOne({ _id: vessel._id }, { $set: { region_group_id: matchingRegionGroup._id } });

                console.log(`Updated vessel '${vessel.name}' with region_group_id '${matchingRegionGroup.name}' (${matchingRegionGroup._id})`);
            } else {
                console.log(`Vessel '${vessel.name}' has no matching region group.`);
            }
        }

        console.log("Update process completed.");
        return true;
    } catch (error) {
        console.error("Error updating vessels:", error);
    } finally {
        mongoose.connection.close();
    }
}

updateVesselsRegionGroup();
