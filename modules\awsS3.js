const AWS = require("aws-sdk");
const sharp = require("sharp");
const db = require("../modules/db");
const { Readable } = require("node:stream");
const { getSignedUrl } = require("@aws-sdk/cloudfront-signer");

const s3 = new AWS.S3({
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
});

const s3Config = {
    buckets: {
        assets: {
            name: "quartermaster-assets",
            region: "us-east-1",
        },
        compressedItems: {
            name: process.env.AWS_COMPRESSED_ITEMS_BUCKET,
            region: process.env.AWS_COMPRESSED_ITEMS_REGION,
        },
    },
    settings: {
        signatureVersion: "v4",
    },
};

AWS.config.update({
    maxRetries: 3,

    httpOptions: {
        timeout: 30000,
        connectTimeout: 10000,
    },
});

// const rawRegions = {
//     '': 'us-east-1',
//     'EU': 'eu-west-1'
// }

// const buckets = {}
const bucketKeys = {};

// s3.listBuckets().promise().then(({ Buckets }) => {
//     Promise.all(Buckets.map(b => (
//         s3.getBucketLocation({ Bucket: b.Name }).promise()
//     ))).then(locations => {
//         locations.forEach((loc, i) => {
//             const region = rawRegions[loc.LocationConstraint] || loc.LocationConstraint
//             const bucket = Buckets[i]
//             buckets[bucket.Name] = {
//                 ...bucket,
//                 region,
//                 object_url_prefix: `https://${bucket.Name}.s3.${region}.amazonaws.com/`
//             }
//         })
//     }).catch(console.error)
// }).catch(console.error)

// async function getBucketKeys(bucket) {
//     let isTruncated = true; // Flag to check if there are more keys to retrieve
//     let continuationToken; // Token for pagination
//     bucketKeys[bucket] = []

//     try {
//         while (isTruncated) {
//             // Fetch objects from S3 bucket
//             const params = {
//                 Bucket: bucket,
//                 MaxKeys: 1000, // Maximum number of keys to return (default is 1000)
//                 ContinuationToken: continuationToken, // Token for next set of results
//             };

//             const data = await s3.listObjectsV2(params).promise();

//             // Add keys to the array
//             data.Contents.forEach(item => bucketKeys[bucket].push(item.Key));

//             console.log(bucket, 'bucket length', bucketKeys[bucket].length)

//             // Check if there are more keys to fetch
//             isTruncated = data.IsTruncated;
//             continuationToken = data.NextContinuationToken; // Update token for the next batch
//         }
//         console.log(`Finished getting keys for bucket ${bucket}`)
//         return bucketKeys[bucket]; // Return all keys
//     } catch (error) {
//         console.error(`Error listing keys: ${error}`);
//         throw error;
//     }
// }

async function getItemObject(bucketName, region, key, sync = false) {
    s3.config.update({ region });
    const params = {
        Bucket: bucketName,
        Key: key,
    };

    try {
        const obj = s3.getObject(params);
        return sync ? obj.promise() : obj; // No await here
    } catch (err) {
        console.error("Error getting object from S3:", err);
        throw err; // Re-throw to be handled by caller
    }
}

async function getItemObjectPart(req, res, bucketName, region, key) {
    s3.config.update({ region });
    const params = {
        Bucket: bucketName,
        Key: key,
    };

    try {
        const headData = await s3.headObject(params).promise();
        const fileSize = headData.ContentLength;
        const contentType = "video/mp4"; //headData.ContentType || "application/octet-stream";
        const etag = headData.ETag;

        res.setHeader("Accept-Ranges", "bytes");
        res.setHeader("Content-Type", contentType);

        const range = req.headers.range;
        let responseStatusCode = 200; // Default to 200 OK
        let responseHeaders = {
            "Content-Type": contentType,
            "Accept-Ranges": "bytes",
            "Content-Length": fileSize, // Default to full size
        };
        if (etag) {
            responseHeaders["ETag"] = etag;
        }

        if (range) {
            const parts = range.replace(/bytes=/, "").split("-");
            const start = parseInt(parts[0], 10);
            const end = parts[1] ? parseInt(parts[1], 10) : start > 0 ? fileSize - 1 : Math.round(fileSize / 4); //fileSize - 1;

            if (start >= fileSize || end >= fileSize || start > end) {
                console.warn(`v2: Invalid range requested: ${range} for file size ${fileSize}`);
                res.setHeader("Content-Range", `bytes */${fileSize}`);
                return res.status(416).send("Range Not Satisfiable");
            }

            const chunkSize = end - start + 1;
            params.Range = `bytes=${start}-${end}`; // Set Range for S3 GetObject

            console.log(`Serving S3 chunk: bytes ${start}-${end}/${fileSize}, size: ${chunkSize}`);

            responseStatusCode = 206; // 206 Partial Content
            responseHeaders["Content-Range"] = `bytes ${start}-${end}/${fileSize}`;
            responseHeaders["Content-Length"] = chunkSize;
        } else {
            console.log(`Serving full S3 file: ${key}, size: ${fileSize}`);
            // Headers already set for 200 OK defaults
        }

        console.log("v2: Calling s3.getObject(params).createReadStream()...");
        const s3Stream = s3.getObject(params).createReadStream();

        if (!(s3Stream instanceof Readable)) {
            throw new Error("S3 getObject().createReadStream() did not return a readable stream.");
        }

        // Write status code and headers *before* piping
        res.writeHead(responseStatusCode, responseHeaders);

        // Abort S3 download if client disconnects (Same logic)
        res.on("close", () => {
            console.log(`Client disconnected for ${key}. Aborting S3 stream.`);
            // createReadStream returns a standard Node Readable stream which has destroy()
            if (typeof s3Stream.destroy === "function") {
                s3Stream.destroy();
            } else {
                console.warn("S3 stream does not have a destroy method.");
            }
        });

        // Handle errors during the S3 stream (Same logic)
        s3Stream.on("error", (streamErr) => {
            console.error(`v2: Error during S3 stream for ${key}:`, streamErr);
            if (!res.headersSent) {
                // Check common v2 error codes
                if (streamErr.code === "NoSuchKey") {
                    res.status(404).send("File not found in S3");
                } else if (streamErr.code === "AccessDenied") {
                    res.status(403).send("Access denied to S3 resource");
                } else {
                    res.status(500).send("Error streaming file from S3");
                }
            } else {
                res.end();
            }
        });

        // 7. Pipe the S3 stream to the client response (Same logic)
        console.log("v2: Piping S3 stream to response.");
        s3Stream.pipe(res);
    } catch (err) {
        console.error(`v2: Error processing S3 request for ${key}:`, err);
        if (!res.headersSent) {
            // Check common v2 error codes from headObject or initial getObject call
            if (err.code === "NotFound" || err.code === "NoSuchKey") {
                // headObject might return NotFound
                res.status(404).send("File not found in S3");
            } else if (err.code === "NoSuchBucket") {
                res.status(500).send("Server configuration error (Bucket not found)");
            } else if (err.code === "AccessDenied") {
                res.status(403).send("Access denied to S3 resource");
            } else {
                res.status(500).send("Failed to retrieve file from S3");
            }
        } else {
            res.end();
        }
    }
}

async function getObjectStream(bucketName, region, key) {
    try {
        return (await getItemObject(bucketName, region, key)).createReadStream(); // Return the stream directly
    } catch (err) {
        console.error("Error getting object from S3:", err);
        throw err; // Re-throw to be handled by caller
    }
}

async function checkKeyExists(bucket, key) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
        console.log("checkKeyExists has actually been called");
        const ts = new Date().getTime();
        try {
            if (!bucketKeys[bucket]) bucketKeys[bucket] = [];
            if (bucketKeys[bucket].includes(key)) {
                resolve(true);
            } else {
                await s3.headObject({ Bucket: bucket, Key: key }).promise();
                bucketKeys[bucket].push(key);
                console.log(`added key ${key} to bucket ${bucket}. time taken ${new Date().getTime() - ts}`);
                resolve(true);
            }
        } catch (err) {
            if (err.statusCode === 404) resolve(false);
            else reject(err);
        }
    });
}

/**
 * Uploads a file buffer to the specified AWS S3 bucket.
 *
 * @param {Object} file - The file object containing a buffer and metadata.
 * @param {Buffer} file.buffer - The file content as a Buffer.
 * @param {string} file.originalname - The original file name (used for extension).
 * @param {Object} bucketType - The bucket configuration object with name and region properties.
 * @param {string} bucketType.name - The S3 bucket name.
 * @param {string} bucketType.region - The AWS region for the bucket.
 * @param {string} filePath - The destination path/key in the S3 bucket.
 * @param {import("aws-sdk/clients/s3").PutObjectRequest} [options] - Optional settings for the upload.
 */
async function uploadFileToS3(file, bucketType, filePath, options = {}) {
    try {
        if (!file || !file.buffer) {
            throw new Error("File buffer is required");
        }

        if (!bucketType || typeof bucketType !== "object") {
            throw new Error("bucketType must be an object with name and region properties");
        }

        if (!bucketType.name || !bucketType.region) {
            throw new Error("bucketType must have both name and region properties");
        }

        const { name: bucket, region } = bucketType;

        let finalPath = filePath;
        if (!filePath.includes(".")) {
            const timestamp = Date.now();
            const randomString = Math.random().toString(36).substring(2, 15);
            const extension = file.originalname ? file.originalname.split(".").pop() : "dat";
            finalPath = `${filePath}/${timestamp}-${randomString}.${extension}`;
        }

        s3.config.update({
            region: region,
            signatureVersion: s3Config.settings.signatureVersion,
        });

        const uploadParams = {
            Bucket: bucket,
            Key: finalPath,
            Body: file.buffer,
            ContentType: file.mimetype || "application/octet-stream",
            ...options,
        };

        const result = await s3.upload(uploadParams).promise();
        return result.Key;
    } catch (error) {
        console.error("[awsS3.uploadFileToS3] Error:", error);
        throw new Error(`Failed to upload file to S3: ${error.message}`);
    }
}

async function deleteFileFromS3(bucketType, key) {
    try {
        if (!key) {
            console.log("[awsS3.deleteFileFromS3] No key provided, skipping deletion");
            return;
        }

        if (!bucketType || typeof bucketType !== "object") {
            throw new Error("bucketType must be an object with name and region properties");
        }

        if (!bucketType.name || !bucketType.region) {
            throw new Error("bucketType must have both name and region properties");
        }

        const { name: bucket, region } = bucketType;

        s3.config.update({
            region: region,
            signatureVersion: s3Config.settings.signatureVersion,
        });

        const deleteParams = {
            Bucket: bucket,
            Key: key,
        };

        await s3.deleteObject(deleteParams).promise();
        console.log(`[awsS3.deleteFileFromS3] Successfully deleted file: ${key} from bucket: ${bucket}`);
    } catch (error) {
        console.error("[awsS3.deleteFileFromS3] Error:", error);
        console.warn(`[awsS3.deleteFileFromS3] Failed to delete file ${key}, but continuing with database update`);
    }
}

async function buildThumbnailImage(bucketName, region, key, unitName) {
    try {
        const originalBuffer = (await getItemObject(bucketName, region, key, true)).Body;
        const resizedBuffer = await sharp(originalBuffer).resize(null, 320).jpeg({ quality: 100 }).toBuffer();

        const keyParts = key.split("/");
        const name = keyParts.pop();
        const uploadKey = `images/${unitName}/${name}`;
        const { name: bucket, region: bucketRegion } = s3Config.buckets.compressedItems;

        s3.config.update({ region: bucketRegion });
        const putObjectParams = {
            Bucket: bucket,
            Key: uploadKey,
            Body: resizedBuffer,
            ContentType: "image/jpeg",
        };

        const uploadResult = await s3.putObject(putObjectParams).promise();
        if (uploadResult.ETag?.length > 0) {
            await db.qmai.collection("analysis_results").updateMany({ image_path: key }, { $set: { thumbnail_image_path: uploadKey } });
            return {
                createReadStream: () => Readable.from(resizedBuffer),
                ContentLength: resizedBuffer.length,
                ContentType: "image/jpeg",
            };
        } else {
            return undefined;
        }
    } catch (error) {
        console.error("Error processing image:", error);
        throw error;
    }
}

const CLOUDFRONT_URL = process.env.CLOUDFRONT_URL;
const CLOUDFRONT_KEY_PAIR_ID = process.env.CLOUDFRONT_KEY_PAIR_ID;
const CLOUDFRONT_PRIVATE_KEY = process.env.CLOUDFRONT_PRIVATE_KEY;

if (!CLOUDFRONT_URL || !CLOUDFRONT_KEY_PAIR_ID || !CLOUDFRONT_PRIVATE_KEY) {
    throw new Error("[awsS3.js] CloudFront configuration missing in .env");
}

function getCloudfrontSignedUrl({ fileName }) {
    if (!fileName) {
        throw new Error("fileName is required");
    }

    const signedUrl = getSignedUrl({
        url: CLOUDFRONT_URL + fileName,
        keyPairId: CLOUDFRONT_KEY_PAIR_ID,
        privateKey: CLOUDFRONT_PRIVATE_KEY,
        dateLessThan: new Date(Date.now() + 3600 * 1000),
    });

    console.log("signedUrl", signedUrl);

    return signedUrl;
}

// db.qmai.once('open', async () => {
//     try {
//         const buckets = []
//         const result = await db.qmai.collection('artifact_processor_results').find().toArray()
//         result.forEach(artifact => {
//             if (!buckets.includes(artifact.bucket_name))
//                 buckets.push(artifact.bucket_name)
//         })
//         console.log(`getting keys for buckets ${buckets}`)
//         buckets.forEach(bucket => {
//             getBucketKeys(bucket)
//         })
//     } catch (err) {
//         console.log(`(FATAL ERROR) in S3 module ${err?.message || err}`)
//         console.error('(FATAL ERROR) in S3 module', err)
//     }
// })

module.exports = {
    s3,
    getObjectStream,
    checkKeyExists,
    buildThumbnailImage,
    uploadFileToS3,
    deleteFileFromS3,
    getS3Object: getItemObject,
    getItemObjectPart,
    bucketKeys: process.env.NODE_ENV === "test" && bucketKeys,
    s3Config,
    getCloudfrontSignedUrl,
};
