import { Grid, Modal, Typography, Button } from "@mui/material";
import ModalContainer from "./ModalContainer";
import theme from "../theme";

const ArchiveConfirmModal = ({ initialState, onClose, onConfirm, isArchive = true }) => {
    return (
        <Modal open={initialState} onClose={onClose}>
            <ModalContainer title={isArchive ? "Move to Archive" : "Unarchive"} onClose={onClose} headerPosition="center">
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: "auto" }}>
                    <Grid>
                        <Typography fontWeight={"100"}>
                            {isArchive ? "Are you sure you want to move to archive this event?" : "Are you sure you want to unarchive this event?"}
                        </Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button
                                variant="contained"
                                sx={{ background: "#FFFFFF !important", color: theme.palette.primary.main }}
                                onClick={onClose}
                            >
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" onClick={onConfirm} sx={{ background: "#F59E0B !important" }}>
                                {isArchive ? "Archive" : "Unarchive"}
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default ArchiveConfirmModal;
