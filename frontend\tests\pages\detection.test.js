import { render, screen, waitFor, fireEvent } from "@testing-library/react";
import Detection from "../../src/pages/Dashboard/Stream/Detection";
import { useApp } from "../../src/hooks/AppHook";
import { getLocation } from "../../src/utils";
import axiosInstance from "../../src/axios";

jest.mock("../../src/utils", () => ({
    getLocation: jest.fn(),
    defaultValues: {
        dateTimeFormat: jest.fn(() => 'YYYY-MM-DD HH:mm:ss'),
        timezone: 'UTC'
    }
}));

jest.mock('@mui/material', () => ({
    ...jest.requireActual('@mui/material'),
    Modal: ({ children, onClose }) => {
        if (onClose) onClose();
        return <div>{children}</div>
    }
}));

jest.mock("../../src/hooks/AppHook");
jest.mock("../../src/axios");
jest.mock('../../src/components/VideoPlayer', () => ({
    __esModule: true,
    default: ({ onFullscreen, isFullscreen }) => (
        <div
            data-testid={isFullscreen ? "video-player-fullscreen" : "video-player-regular"}
            onClick={onFullscreen}
        >
            Mock Video Player {isFullscreen ? '(Fullscreen)' : ''}
        </div>
    )
}));

describe("Detection", () => {
    const mockArtifact = {
        location: {
            coordinates: [1.234, 5.678]
        },
        video_path: "video123.mp4",
        image_path: null,
        bucket_name: "test-bucket",
        others: "Test description"
    };

    beforeEach(() => {
        useApp.mockReturnValueOnce({
            screenSize: { xs: false }
        });

        axiosInstance.post.mockResolvedValueOnce({
            data: { signedUrl: 'https://mock-signed-url.com' }
        });
        getLocation.mockResolvedValue('Mock Location Name');
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("renders video player when video_path is provided", async () => {
        useApp.mockReturnValue({
            screenSize: { xs: true }
        });
        const newMock = JSON.parse(JSON.stringify(mockArtifact));
        newMock.others = null;
        newMock.location.coordinates = [1];
        render(<Detection artifact={newMock} />);

        await waitFor(() => {
            expect(screen.getByTestId('video-player-regular')).toBeInTheDocument();
        });
    });

    it("renders image when only image_path is provided", async () => {
        const imageArtifact = {
            ...mockArtifact,
            video_path: null,
            image_path: "image123.jpg",
            location: { coordinates: [1.234, 5.678] }
        };

        render(<Detection artifact={imageArtifact} />);

        await waitFor(() => {
            const imageElement = screen.getByRole("img");
            expect(imageElement).toBeInTheDocument();
            expect(imageElement).toHaveAttribute("src", "https://mock-signed-url.com");
        });
    });

    it("displays location and description", async () => {
        render(<Detection artifact={mockArtifact} />);

        await waitFor(() => {
            expect(screen.getByText('Location')).toBeInTheDocument();
            expect(screen.getByText('Description')).toBeInTheDocument();
            expect(screen.getByText('Mock Location Name')).toBeInTheDocument();
            expect(screen.getByText('Test description')).toBeInTheDocument();
        });
    });

    it("truncates long descriptions", async () => {
        const longDescription = "This is a very long description that should be truncated at 45 characters";
        const artifactWithLongDesc = {
            ...mockArtifact,
            others: longDescription
        };

        render(<Detection artifact={artifactWithLongDesc} />);

        await waitFor(() => {
            expect(screen.getByText(/^This is a very long description that should b.../)).toBeInTheDocument();
        });
    });

    it("handles geolocation fetch error gracefully", async () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        getLocation.mockRejectedValueOnce(new Error('Geolocation fetch failed'));

        render(<Detection artifact={mockArtifact} />);

        await waitFor(() => {
            expect(consoleSpy).toHaveBeenCalledWith(
                'Error fetching geolocation:',
                expect.any(Error)
            );
            expect(screen.getByText('Loading...')).toBeInTheDocument();
        });

        consoleSpy.mockRestore();
    });

    it("handles signed URL fetch error gracefully", async () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        axiosInstance.post.mockRejectedValueOnce(new Error('Failed to fetch signed URL'));

        render(<Detection artifact={mockArtifact} />);

        await waitFor(() => expect(consoleSpy).not.toHaveBeenCalled());

        consoleSpy.mockRestore();
    });

    it("handles both fetch errors occurring simultaneously", async () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        getLocation.mockRejectedValueOnce(new Error('Geolocation fetch failed'));
        axiosInstance.post.mockRejectedValueOnce(new Error('Failed to fetch signed URL'));

        render(<Detection artifact={mockArtifact} />);

        await waitFor(() => expect(consoleSpy).toHaveBeenCalled());

        consoleSpy.mockRestore();
    });

    it("closes modal when fullscreen video player triggers exit", async () => {
        render(<Detection artifact={mockArtifact} />);

        const videoPlayer = await screen.findByTestId('video-player-regular');
        videoPlayer.click();

        const fullscreenPlayer = await screen.findByTestId('video-player-fullscreen');
        fireEvent.click(fullscreenPlayer);

        await waitFor(() => {
            expect(screen.queryByTestId('video-player-fullscreen')).toBeInTheDocument();
        });
    });

    it("opens and closes modal with fullscreen video player", async () => {
        render(<Detection artifact={mockArtifact} />);

        const regularPlayer = await screen.findByTestId('video-player-regular');
        fireEvent.click(regularPlayer);

        const fullscreenPlayer = await screen.findByTestId('video-player-fullscreen');
        expect(fullscreenPlayer).toBeInTheDocument();

        fireEvent.click(fullscreenPlayer);

        await waitFor(() => {
            expect(screen.queryByTestId('video-player-fullscreen')).toBeInTheDocument();
        });
    });
});
